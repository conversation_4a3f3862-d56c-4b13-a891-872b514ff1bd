# Enhanced Program Overview: Replace Mock Data with Real Database Queries

## Executive Summary

The `EnhancedProgramOverview` component currently contains significant hardcoded mock data that displays inaccurate workout statistics to users. This implementation plan details the complete replacement of all mock data with real database queries while maintaining the existing UI structure and user experience.

### Key Changes Required:
- Replace hardcoded workout statistics (12 workouts, 85% completion, 3 achievements)
- Remove mock cycle progress calculations (65% progress, hardcoded dates)
- Integrate with existing `workoutLoggingService` for real data
- Add proper loading states and error handling
- Maintain visual design and component structure

### Impact:
- **Users**: Will see accurate, real-time workout statistics
- **Performance**: Minimal impact with proper caching implementation
- **Maintenance**: Eliminates confusion from mock data in production

---

## Current Mock Data Analysis

### 🚨 Critical Mock Data Locations:

#### 1. Quick Stats Function (Lines 258-279)
```typescript
// CURRENT MOCK DATA
<Text style={[styles.statValue, { color: colors.text }]}>12</Text>     // Hardcoded workouts
<Text style={[styles.statValue, { color: colors.text }]}>85%</Text>    // Hardcoded completion
<Text style={[styles.statValue, { color: colors.text }]}>3</Text>      // Hardcoded achievements
```

#### 2. Cycle Info Function (Lines 91-102)
```typescript
// CURRENT MOCK DATA
const cycleInfo: ProgramCycleInfo = {
  currentCycle: program.cycle_number || 1,
  totalCycles: 4,                    // Hardcoded
  cycleProgress: 65,                 // Hardcoded 65%
  daysInCycle: 28,                   // Hardcoded
  daysCompleted: 18,                 // Hardcoded
  nextTransitionDate: '2025-02-01',  // Hardcoded date
};
```

---

## Database Schema Requirements

### Tables to Query:
1. **`workout_logs`** - Workout completion tracking
2. **`workout_programs`** - Program details and start dates
3. **`user_achievements`** - Achievement system data
4. **`profiles`** - User-specific information

### Required Queries:

#### Workout Statistics Query:
```sql
-- Get workout completion statistics
SELECT 
  COUNT(*) as total_workouts,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_workouts,
  ROUND(
    (COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)), 
    0
  ) as completion_rate
FROM workout_logs 
WHERE user_id = $1 
  AND created_at >= NOW() - INTERVAL '30 days';
```

#### Achievement Count Query:
```sql
-- Get user achievement count
SELECT COUNT(*) as achievement_count
FROM user_achievements 
WHERE user_id = $1;
```

#### Current Streak Query:
```sql
-- Calculate current workout streak
WITH daily_workouts AS (
  SELECT DISTINCT date_trunc('day', completed_at) as workout_date
  FROM workout_logs 
  WHERE user_id = $1 
    AND status = 'completed'
    AND completed_at >= CURRENT_DATE - INTERVAL '30 days'
  ORDER BY workout_date DESC
)
SELECT COUNT(*) as current_streak
FROM daily_workouts;
```

---

## Phase 1: Data Service Integration

### Task 1.1: Create Enhanced Workout Statistics Service
**File**: `src/services/workoutStatsService.ts` (new file)
**Priority**: High
**Estimated Time**: 2 hours

#### Interface Definitions:
```typescript
export interface WorkoutStatistics {
  totalWorkouts: number;
  completionRate: number;
  achievements: number;
  currentStreak: number;
  weeklyAverage: number;
  lastWorkoutDate?: string;
}

export interface CycleProgress {
  currentCycle: number;
  totalCycles: number;
  cycleProgress: number; // 0-100 percentage
  daysInCycle: number;
  daysCompleted: number;
  nextTransitionDate: string;
  weekProgress: number; // Current week within cycle (1-4)
  isActive: boolean;
}
```

#### Service Implementation:
```typescript
import { supabase } from './supabaseClient';

export const workoutStatsService = {
  async getDetailedWorkoutStats(userId?: string): Promise<WorkoutStatistics> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;
    
    if (!targetUserId) {
      throw new Error('No user ID provided');
    }

    // Get workout statistics
    const { data: workoutData, error: workoutError } = await supabase
      .from('workout_logs')
      .select('status, completed_at, created_at')
      .eq('user_id', targetUserId)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

    if (workoutError) throw workoutError;

    // Get achievements
    const { data: achievementData, error: achievementError } = await supabase
      .from('user_achievements')
      .select('id')
      .eq('user_id', targetUserId);

    if (achievementError) throw achievementError;

    // Calculate statistics
    const totalWorkouts = workoutData?.length || 0;
    const completedWorkouts = workoutData?.filter(w => w.status === 'completed').length || 0;
    const completionRate = totalWorkouts > 0 ? Math.round((completedWorkouts / totalWorkouts) * 100) : 0;
    const achievements = achievementData?.length || 0;

    // Calculate current streak
    const currentStreak = this.calculateCurrentStreak(workoutData || []);
    
    // Calculate weekly average
    const weeklyAverage = Math.round(totalWorkouts / 4); // Last 4 weeks

    // Get last workout date
    const lastWorkout = workoutData
      ?.filter(w => w.status === 'completed')
      ?.sort((a, b) => new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime())[0];

    return {
      totalWorkouts,
      completionRate,
      achievements,
      currentStreak,
      weeklyAverage,
      lastWorkoutDate: lastWorkout?.completed_at
    };
  },

  calculateCurrentStreak(workoutData: any[]): number {
    const completedWorkouts = workoutData
      .filter(w => w.status === 'completed' && w.completed_at)
      .map(w => new Date(w.completed_at).toDateString())
      .filter((date, index, arr) => arr.indexOf(date) === index) // Remove duplicates
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let streak = 0;
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();

    // Check if user worked out today or yesterday to start streak
    if (completedWorkouts.includes(today) || completedWorkouts.includes(yesterday)) {
      for (let i = 0; i < completedWorkouts.length; i++) {
        const currentDate = new Date(completedWorkouts[i]);
        const expectedDate = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        
        if (currentDate.toDateString() === expectedDate.toDateString()) {
          streak++;
        } else {
          break;
        }
      }
    }

    return streak;
  }
};
```

### Task 1.2: Create Cycle Calculation Service
**File**: `src/services/cycleCalculationService.ts` (new file)
**Priority**: High
**Estimated Time**: 2 hours

```typescript
import { WorkoutProgram } from './workoutService';

export const cycleCalculationService = {
  calculateRealCycleInfo(program: WorkoutProgram): CycleProgress {
    const startDate = new Date(program.client_start_date || program.created_at);
    const now = new Date();
    
    // Calculate days since program start
    const daysSinceStart = Math.floor(
      (now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    // Ensure we don't have negative days
    const safeDaysSinceStart = Math.max(0, daysSinceStart);
    
    // Calculate cycle information
    const daysInCycle = 28; // 4 weeks per cycle
    const currentCycleDay = safeDaysSinceStart % daysInCycle;
    const currentCycle = Math.floor(safeDaysSinceStart / daysInCycle) + 1;
    const totalCycles = Math.ceil((program.duration_weeks || 12) / 4);
    
    // Calculate progress percentage within current cycle
    const cycleProgress = Math.min(100, Math.round((currentCycleDay / daysInCycle) * 100));
    
    // Calculate next transition date
    const nextTransitionDate = new Date(startDate);
    nextTransitionDate.setDate(startDate.getDate() + (currentCycle * daysInCycle));
    
    // Calculate current week within cycle (1-4)
    const weekProgress = Math.floor(currentCycleDay / 7) + 1;
    
    // Check if program is still active
    const isActive = currentCycle <= totalCycles && program.status === 'active_by_client';

    return {
      currentCycle: Math.min(currentCycle, totalCycles),
      totalCycles,
      cycleProgress,
      daysInCycle,
      daysCompleted: currentCycleDay,
      nextTransitionDate: nextTransitionDate.toISOString().split('T')[0],
      weekProgress: Math.min(weekProgress, 4),
      isActive
    };
  },

  formatTransitionDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays > 0) return `In ${diffDays} days`;
    return date.toLocaleDateString();
  }
};
```

---

## Phase 2: Component State Management

### Task 2.1: Add State Variables
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: High
**Estimated Time**: 1 hour

#### Add New Imports:
```typescript
import { workoutStatsService, WorkoutStatistics } from '../services/workoutStatsService';
import { cycleCalculationService, CycleProgress } from '../services/cycleCalculationService';
```

#### Add State Variables:
```typescript
// Add after existing state declarations
const [workoutStats, setWorkoutStats] = useState<WorkoutStatistics | null>(null);
const [isLoadingStats, setIsLoadingStats] = useState(false);
const [realCycleInfo, setRealCycleInfo] = useState<CycleProgress | null>(null);
const [statsError, setStatsError] = useState<string | null>(null);
const [lastStatsUpdate, setLastStatsUpdate] = useState<number>(0);
```

### Task 2.2: Create Data Fetching Functions
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: High
**Estimated Time**: 1 hour

```typescript
// Add these functions before the existing calculateCycleInfo function

const STATS_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

const fetchWorkoutStatistics = async () => {
  if (!profile?.id) return;
  
  // Check cache
  if (workoutStats && Date.now() - lastStatsUpdate < STATS_CACHE_DURATION) {
    return;
  }
  
  setIsLoadingStats(true);
  setStatsError(null);
  
  try {
    const stats = await workoutStatsService.getDetailedWorkoutStats(profile.id);
    setWorkoutStats(stats);
    setLastStatsUpdate(Date.now());
  } catch (error) {
    console.error('Error fetching workout statistics:', error);
    setStatsError('Failed to load workout statistics');
  } finally {
    setIsLoadingStats(false);
  }
};

const calculateRealCycleInfo = (program: WorkoutProgram) => {
  try {
    const realCycle = cycleCalculationService.calculateRealCycleInfo(program);
    setRealCycleInfo(realCycle);
    
    // Update existing cycleInfo for backward compatibility
    setCycleInfo({
      currentCycle: realCycle.currentCycle,
      totalCycles: realCycle.totalCycles,
      cycleProgress: realCycle.cycleProgress,
      daysInCycle: realCycle.daysInCycle,
      daysCompleted: realCycle.daysCompleted,
      nextTransitionDate: realCycle.nextTransitionDate,
    });
  } catch (error) {
    console.error('Error calculating cycle info:', error);
    // Fallback to basic info
    setRealCycleInfo({
      currentCycle: 1,
      totalCycles: Math.ceil((program.duration_weeks || 12) / 4),
      cycleProgress: 0,
      daysInCycle: 28,
      daysCompleted: 0,
      nextTransitionDate: new Date().toISOString().split('T')[0],
      weekProgress: 1,
      isActive: program.status === 'active_by_client'
    });
  }
};

const refreshAllData = async () => {
  await Promise.all([
    fetchWorkoutStatistics(),
    currentProgram ? Promise.resolve(calculateRealCycleInfo(currentProgram)) : Promise.resolve()
  ]);
};
```

---

## Phase 3: Update Render Functions

### Task 3.1: Replace renderQuickStats Function
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: High
**Estimated Time**: 1.5 hours

#### Complete Function Replacement:
```typescript
const renderQuickStats = () => {
  // Loading state
  if (isLoadingStats) {
    return (
      <View style={styles.quickStats}>
        {[1, 2, 3].map((index) => (
          <View key={index} style={[styles.statCard, { backgroundColor: colors.accent }]}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={[styles.statValue, { color: colors.text + '60' }]}>--</Text>
            <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
              {index === 1 ? 'Workouts' : index === 2 ? 'Completion' : 'Achievements'}
            </Text>
          </View>
        ))}
      </View>
    );
  }

  // Error state
  if (statsError) {
    return (
      <View style={styles.quickStats}>
        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <AlertCircle size={20} color={colors.error} />
          <Text style={[styles.statLabel, { color: colors.error, textAlign: 'center' }]}>
            Failed to load stats
          </Text>
          <TouchableOpacity onPress={fetchWorkoutStatistics}>
            <Text style={[styles.statLabel, { color: colors.primary }]}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // No data state
  if (!workoutStats) {
    return (
      <View style={styles.quickStats}>
        {[1, 2, 3].map((index) => (
          <View key={index} style={[styles.statCard, { backgroundColor: colors.accent }]}>
            <View style={{ opacity: 0.5 }}>
              {index === 1 && <TrendingUp size={20} color={colors.success} />}
              {index === 2 && <Target size={20} color={colors.primary} />}
              {index === 3 && <Award size={20} color={colors.warning} />}
            </View>
            <Text style={[styles.statValue, { color: colors.text + '60' }]}>--</Text>
            <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
              {index === 1 ? 'Workouts' : index === 2 ? 'Completion' : 'Achievements'}
            </Text>
          </View>
        ))}
      </View>
    );
  }

  // Real data display
  return (
    <View style={styles.quickStats}>
      <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
        <TrendingUp size={20} color={colors.success} />
        <Text style={[styles.statValue, { color: colors.text }]}>
          {workoutStats.totalWorkouts}
        </Text>
        <Text style={[styles.statLabel, { color: colors.text + '80' }]}>Workouts</Text>
      </View>
      
      <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
        <Target size={20} color={colors.primary} />
        <Text style={[styles.statValue, { color: colors.text }]}>
          {workoutStats.completionRate}%
        </Text>
        <Text style={[styles.statLabel, { color: colors.text + '80' }]}>Completion</Text>
      </View>
      
      <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
        <Award size={20} color={colors.warning} />
        <Text style={[styles.statValue, { color: colors.text }]}>
          {workoutStats.achievements}
        </Text>
        <Text style={[styles.statLabel, { color: colors.text + '80' }]}>Achievements</Text>
      </View>
    </View>
  );
};
```

### Task 3.2: Update calculateCycleInfo Function
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: High
**Estimated Time**: 0.5 hours

#### Replace Existing Function:
```typescript
const calculateCycleInfo = (program: WorkoutProgram) => {
  // Remove the mock calculation comment and replace with real calculation
  calculateRealCycleInfo(program);
};
```

### Task 3.3: Update renderCycleProgress Function
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: Medium
**Estimated Time**: 1 hour

#### Enhanced Function with Real Data:
```typescript
const renderCycleProgress = () => {
  if (!realCycleInfo) return null;

  const progressWidth = `${realCycleInfo.cycleProgress}%`;
  const transitionText = cycleCalculationService.formatTransitionDate(realCycleInfo.nextTransitionDate);

  return (
    <View style={[styles.cycleCard, { backgroundColor: colors.accent }]}>
      <View style={styles.cycleHeader}>
        <View style={styles.cycleInfo}>
          <Text style={[styles.cycleTitle, { color: colors.text }]}>
            Cycle {realCycleInfo.currentCycle} of {realCycleInfo.totalCycles}
          </Text>
          <Text style={[styles.cycleSubtitle, { color: colors.text + '80' }]}>
            Week {realCycleInfo.weekProgress} • Day {realCycleInfo.daysCompleted} of {realCycleInfo.daysInCycle}
          </Text>
        </View>
        <View style={styles.cycleStats}>
          <Text style={[styles.progressPercentage, { color: colors.primary }]}>
            {realCycleInfo.cycleProgress}%
          </Text>
        </View>
      </View>
      
      <View style={[styles.progressTrack, { backgroundColor: colors.text + '20' }]}>
        <View 
          style={[
            styles.progressFill, 
            { 
              backgroundColor: realCycleInfo.isActive ? colors.primary : colors.text + '40',
              width: progressWidth
            }
          ]} 
        />
      </View>
      
      <View style={styles.transitionInfo}>
        <Calendar size={12} color={colors.text + '60'} />
        <Text style={[styles.transitionText, { color: colors.text + '60' }]}>
          Next transition: {transitionText}
        </Text>
      </View>
    </View>
  );
};
```

---

## Phase 4: Add useEffect Hooks

### Task 4.1: Add Data Fetching Effects
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: High
**Estimated Time**: 0.5 hours

#### Add After Existing useEffect:
```typescript
// Add after the existing useEffect for currentProgram
useEffect(() => {
  if (profile?.id) {
    fetchWorkoutStatistics();
  }
}, [profile?.id]);

useEffect(() => {
  if (currentProgram) {
    calculateRealCycleInfo(currentProgram);
  }
}, [currentProgram]);

// Optional: Add refresh interval for real-time updates
useEffect(() => {
  const interval = setInterval(() => {
    if (profile?.id && !isLoadingStats) {
      fetchWorkoutStatistics();
    }
  }, 60000); // Refresh every minute

  return () => clearInterval(interval);
}, [profile?.id, isLoadingStats]);
```

---

## Phase 5: Error Handling and Loading States

### Task 5.1: Add Error Boundary Support
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: Medium
**Estimated Time**: 1 hour

#### Add Error Handling Wrapper:
```typescript
const renderWithErrorBoundary = (renderFunction: () => React.ReactNode, fallback: React.ReactNode) => {
  try {
    return renderFunction();
  } catch (error) {
    console.error('Render error in EnhancedProgramOverview:', error);
    return fallback;
  }
};

// Update main return statement to use error boundary
return (
  <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
    {programStatus === 'program_generating' ? (
      renderProgramGeneratingState()
    ) : programStatus === 'intake_needed' ? (
      renderEmptyState()
    ) : hasPrograms ? (
      <>
        {renderWithErrorBoundary(
          () => renderCycleProgress(),
          <View style={styles.errorFallback}>
            <Text style={[styles.errorText, { color: colors.error }]}>
              Unable to load cycle progress
            </Text>
          </View>
        )}
        {renderWithErrorBoundary(
          () => renderActiveProgram(),
          null
        )}
        {renderWithErrorBoundary(
          () => renderQuickStats(),
          <View style={styles.errorFallback}>
            <Text style={[styles.errorText, { color: colors.error }]}>
              Unable to load workout stats
            </Text>
          </View>
        )}
        {renderPendingPrograms()}
        {/* Show waiting state for programs under review (regular users only) */}
        {programsUnderReview.length > 0 && (
          <View style={styles.waitingStateContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Program Under Review
            </Text>
            <ProgramWaitingState
              programId={programsUnderReview[0].id}
              submittedDate={programsUnderReview[0].created_at}
              onRefresh={refreshAllData}
              onContactSupport={() => {
                console.log('Contact support requested');
              }}
            />
          </View>
        )}
      </>
    ) : (
      renderEmptyState()
    )}
  </ScrollView>
);
```

### Task 5.2: Add Error Styles
**File**: `src/components/EnhancedProgramOverview.tsx`
**Priority**: Low
**Estimated Time**: 0.5 hours

#### Add to StyleSheet:
```typescript
// Add to existing styles object
errorFallback: {
  padding: 20,
  alignItems: 'center',
  backgroundColor: colors.error + '10',
  borderRadius: 12,
  marginBottom: 20,
},
errorText: {
  fontSize: 14,
  textAlign: 'center',
},
```

---

## Phase 6: Testing Requirements

### Unit Tests
**File**: `src/components/__tests__/EnhancedProgramOverview.test.tsx`

#### Test Cases to Implement:
1. **Data Fetching Tests**:
   - Test successful workout stats fetching
   - Test error handling for failed API calls
   - Test loading states during data fetch

2. **Cycle Calculation Tests**:
   - Test cycle progress calculation with various start dates
   - Test edge cases (negative days, future dates)
   - Test transition date calculations

3. **Component Rendering Tests**:
   - Test rendering with real data
   - Test loading state rendering
   - Test error state rendering
   - Test empty state handling

#### Sample Test Implementation:
```typescript
import React from 'react';
import { render, waitFor, fireEvent } from '@testing-library/react-native';
import { EnhancedProgramOverview } from '../EnhancedProgramOverview';
import { workoutStatsService } from '../../services/workoutStatsService';

// Mock the services
jest.mock('../../services/workoutStatsService');
jest.mock('../../services/cycleCalculationService');

describe('EnhancedProgramOverview', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should display real workout statistics', async () => {
    const mockStats = {
      totalWorkouts: 15,
      completionRate: 87,
      achievements: 5,
      currentStreak: 3,
      weeklyAverage: 4
    };

    (workoutStatsService.getDetailedWorkoutStats as jest.Mock).mockResolvedValue(mockStats);

    const { getByText } = render(<EnhancedProgramOverview />);

    await waitFor(() => {
      expect(getByText('15')).toBeTruthy();
      expect(getByText('87%')).toBeTruthy();
      expect(getByText('5')).toBeTruthy();
    });
  });

  it('should handle loading states correctly', () => {
    const { getAllByTestId } = render(<EnhancedProgramOverview />);
    
    // Should show loading indicators
    expect(getAllByTestId('loading-indicator')).toHaveLength(3);
  });

  it('should handle error states gracefully', async () => {
    (workoutStatsService.getDetailedWorkoutStats as jest.Mock).mockRejectedValue(
      new Error('Network error')
    );

    const { getByText } = render(<EnhancedProgramOverview />);

    await waitFor(() => {
      expect(getByText('Failed to load stats')).toBeTruthy();
    });
  });
});
```

### Integration Tests
1. **Database Integration**: Test with real database queries
2. **Service Integration**: Test service layer interactions
3. **UI Integration**: Test complete user workflows

### Manual Testing Checklist
- [ ] Verify workout statistics display real data
- [ ] Confirm cycle progress shows accurate percentages
- [ ] Test loading states appear during data fetch
- [ ] Verify error handling works for network failures
- [ ] Check that UI layout remains consistent
- [ ] Test refresh functionality
- [ ] Verify caching works correctly
- [ ] Test with different user data scenarios

---

## Implementation Timeline

| Phase | Tasks | Estimated Time | Priority | Dependencies |
|-------|-------|----------------|----------|--------------|
| **Phase 1** | Data Service Integration | 4 hours | High | Database schema |
| **Phase 2** | Component State Management | 2 hours | High | Phase 1 |
| **Phase 3** | Update Render Functions | 3 hours | High | Phase 2 |
| **Phase 4** | Add useEffect Hooks | 0.5 hours | High | Phase 3 |
| **Phase 5** | Error Handling & Loading | 1.5 hours | Medium | Phase 4 |
| **Phase 6** | Testing Implementation | 3 hours | Medium | All phases |
| **Total** | **6 phases, 18 tasks** | **14 hours** | | |

### Critical Path:
1. Phase 1 → Phase 2 → Phase 3 → Phase 4 (Core functionality)
2. Phase 5 → Phase 6 (Polish and validation)

---

## Success Criteria

### Functional Requirements:
- [ ] **No hardcoded values** in workout statistics display
- [ ] **Real cycle progress** calculated from actual program start dates
- [ ] **Accurate completion rates** derived from workout logs
- [ ] **Real achievement counts** from user_achievements table
- [ ] **Proper loading states** during all data fetching operations
- [ ] **Comprehensive error handling** for all failure scenarios
- [ ] **Maintained UI structure** and visual design consistency
- [ ] **Performance optimization** with 5-minute caching implementation

### Technical Requirements:
- [ ] **Type safety** maintained throughout implementation
- [ ] **Error boundaries** implemented for graceful failure handling
- [ ] **Unit tests** covering all major functions (>80% coverage)
- [ ] **Integration tests** for service layer interactions
- [ ] **Performance benchmarks** meet existing standards
- [ ] **Accessibility** features preserved
- [ ] **Code documentation** updated for all new functions

### User Experience Requirements:
- [ ] **Seamless transition** from mock to real data (no UI changes)
- [ ] **Fast loading times** (<2 seconds for cached data)
- [ ] **Clear error messages** for user-facing failures
- [ ] **Consistent data updates** across app sessions
- [ ] **Offline graceful degradation** when network unavailable

### Validation Steps:
1. **Code Review**: All changes reviewed by senior developer
2. **QA Testing**: Manual testing of all user scenarios
3. **Performance Testing**: Load testing with realistic data volumes
4. **User Acceptance**: Stakeholder approval of real data accuracy
5. **Production Deployment**: Gradual rollout with monitoring

---

## Risk Mitigation

### Potential Risks:
1. **Performance Impact**: Large data queries affecting load times
2. **Data Accuracy**: Incorrect calculations showing wrong progress
3. **Error Handling**: Unhandled edge cases causing crashes
4. **User Experience**: Loading states disrupting user flow

### Mitigation Strategies:
1. **Caching Implementation**: 5-minute cache for workout statistics
2. **Fallback Values**: Graceful degradation to basic info on errors
3. **Comprehensive Testing**: Unit and integration tests for edge cases
4. **Progressive Enhancement**: Maintain existing UI while loading real data

This implementation plan provides a complete roadmap for replacing all mock data with real database queries while maintaining the existing user experience and ensuring robust error handling throughout the process.
