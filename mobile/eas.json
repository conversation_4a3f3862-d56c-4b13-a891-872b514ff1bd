{"cli": {"version": ">= 5.4.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"resourceClass": "medium", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true}, "android": {"resourceClass": "medium", "buildType": "apk"}}, "production": {"ios": {"resourceClass": "m-medium", "autoIncrement": true}, "android": {"resourceClass": "medium", "autoIncrement": "versionCode"}, "env": {"NODE_ENV": "production"}}, "production-ios": {"extends": "production", "ios": {"resourceClass": "m-medium", "autoIncrement": true, "bundleIdentifier": "com.fitnessapp.ios"}}, "production-android": {"extends": "production", "android": {"resourceClass": "medium", "autoIncrement": "versionCode", "applicationId": "com.fitnessapp.android"}}}, "submit": {"production": {}, "production-ios": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "ABCDEFGHIJ"}}, "production-android": {"android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "production"}}}}