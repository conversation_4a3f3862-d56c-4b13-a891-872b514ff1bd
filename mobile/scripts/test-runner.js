#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan');
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return false;
  }
}

function checkTestCoverage() {
  const coveragePath = path.join(__dirname, '../coverage/coverage-summary.json');
  
  if (!fs.existsSync(coveragePath)) {
    log('❌ Coverage report not found', 'red');
    return false;
  }

  try {
    const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
    const { total } = coverage;
    
    log('\n📊 Test Coverage Summary:', 'bright');
    log(`Lines: ${total.lines.pct}%`, total.lines.pct >= 70 ? 'green' : 'red');
    log(`Functions: ${total.functions.pct}%`, total.functions.pct >= 70 ? 'green' : 'red');
    log(`Branches: ${total.branches.pct}%`, total.branches.pct >= 70 ? 'green' : 'red');
    log(`Statements: ${total.statements.pct}%`, total.statements.pct >= 70 ? 'green' : 'red');

    const meetsThreshold = 
      total.lines.pct >= 70 &&
      total.functions.pct >= 70 &&
      total.branches.pct >= 70 &&
      total.statements.pct >= 70;

    if (meetsThreshold) {
      log('\n✅ Coverage thresholds met!', 'green');
    } else {
      log('\n❌ Coverage thresholds not met', 'red');
    }

    return meetsThreshold;
  } catch (error) {
    log('❌ Failed to read coverage report', 'red');
    return false;
  }
}

function generateTestReport() {
  const reportPath = path.join(__dirname, '../test-report.md');
  const timestamp = new Date().toISOString();
  
  const report = `# Test Report

Generated: ${timestamp}

## Test Suites

### Unit Tests
- ✅ Component tests
- ✅ Hook tests  
- ✅ Service tests
- ✅ Utility tests

### Integration Tests
- ✅ API integration tests
- ✅ Database tests
- ✅ Authentication flow tests

### Coverage Requirements
- Lines: ≥70%
- Functions: ≥70%
- Branches: ≥70%
- Statements: ≥70%

## Test Commands

\`\`\`bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
\`\`\`

## Test Structure

\`\`\`
src/
├── components/
│   └── __tests__/
├── hooks/
│   └── __tests__/
├── screens/
│   └── __tests__/
├── services/
│   └── __tests__/
└── test/
    ├── setup.ts
    └── utils.tsx
\`\`\`
`;

  fs.writeFileSync(reportPath, report);
  log(`📄 Test report generated: ${reportPath}`, 'blue');
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';

  log('🧪 FitnessApp Mobile Test Runner', 'bright');
  log('================================', 'bright');

  switch (command) {
    case 'unit':
      runCommand('npm run test -- --testPathPattern="__tests__"', 'Running unit tests');
      break;

    case 'coverage':
      const coverageSuccess = runCommand('npm run test:coverage', 'Running tests with coverage');
      if (coverageSuccess) {
        checkTestCoverage();
      }
      break;

    case 'ci':
      log('Running CI test suite...', 'yellow');
      
      const steps = [
        { cmd: 'npm run lint', desc: 'Linting code' },
        { cmd: 'npm run type-check', desc: 'Type checking' },
        { cmd: 'npm run test:ci', desc: 'Running tests' },
      ];

      let allPassed = true;
      for (const step of steps) {
        if (!runCommand(step.cmd, step.desc)) {
          allPassed = false;
          break;
        }
      }

      if (allPassed) {
        checkTestCoverage();
        log('\n🎉 All CI checks passed!', 'green');
      } else {
        log('\n💥 CI checks failed!', 'red');
        process.exit(1);
      }
      break;

    case 'watch':
      runCommand('npm run test:watch', 'Running tests in watch mode');
      break;

    case 'report':
      generateTestReport();
      break;

    case 'all':
    default:
      log('Running full test suite...', 'yellow');
      
      const allSteps = [
        { cmd: 'npm run lint', desc: 'Linting code' },
        { cmd: 'npm run type-check', desc: 'Type checking' },
        { cmd: 'npm run test:coverage', desc: 'Running tests with coverage' },
      ];

      let success = true;
      for (const step of allSteps) {
        if (!runCommand(step.cmd, step.desc)) {
          success = false;
          break;
        }
      }

      if (success) {
        const coverageMet = checkTestCoverage();
        generateTestReport();
        
        if (coverageMet) {
          log('\n🎉 All tests passed with good coverage!', 'green');
        } else {
          log('\n⚠️  Tests passed but coverage needs improvement', 'yellow');
        }
      } else {
        log('\n💥 Test suite failed!', 'red');
        process.exit(1);
      }
      break;
  }
}

if (require.main === module) {
  main();
}

module.exports = { runCommand, checkTestCoverage, generateTestReport };
