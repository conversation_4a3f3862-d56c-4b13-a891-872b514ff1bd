#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan');
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return false;
  }
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  const checks = [
    { cmd: 'eas --version', name: 'EAS CLI' },
    { cmd: 'expo --version', name: 'Expo CLI' },
    { cmd: 'node --version', name: 'Node.js' },
    { cmd: 'npm --version', name: 'npm' },
  ];

  for (const check of checks) {
    try {
      execSync(check.cmd, { stdio: 'pipe' });
      log(`✅ ${check.name} is installed`, 'green');
    } catch (error) {
      log(`❌ ${check.name} is not installed or not in PATH`, 'red');
      return false;
    }
  }

  return true;
}

function validateEnvironment() {
  log('🔧 Validating environment...', 'blue');
  
  const requiredEnvVars = [
    'EXPO_PUBLIC_SUPABASE_URL',
    'EXPO_PUBLIC_SUPABASE_ANON_KEY',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    log(`❌ Missing environment variables: ${missingVars.join(', ')}`, 'red');
    return false;
  }

  log('✅ Environment variables validated', 'green');
  return true;
}

function updateVersion() {
  log('📝 Updating version...', 'blue');
  
  const packagePath = path.join(__dirname, '../package.json');
  const appConfigPath = path.join(__dirname, '../app.json');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const appConfig = JSON.parse(fs.readFileSync(appConfigPath, 'utf8'));
    
    // Get current version
    const currentVersion = packageJson.version;
    log(`Current version: ${currentVersion}`, 'yellow');
    
    // Update build number in app.json
    if (appConfig.expo.ios) {
      appConfig.expo.ios.buildNumber = (parseInt(appConfig.expo.ios.buildNumber || '1') + 1).toString();
    }
    if (appConfig.expo.android) {
      appConfig.expo.android.versionCode = (appConfig.expo.android.versionCode || 1) + 1;
    }
    
    fs.writeFileSync(appConfigPath, JSON.stringify(appConfig, null, 2));
    log('✅ Build numbers updated', 'green');
    
    return true;
  } catch (error) {
    log(`❌ Failed to update version: ${error.message}`, 'red');
    return false;
  }
}

function buildApp(platform, profile = 'production') {
  log(`🏗️ Building ${platform} app with ${profile} profile...`, 'blue');
  
  const command = `eas build --platform ${platform} --profile ${profile} --non-interactive`;
  return runCommand(command, `Building ${platform} app`);
}

function submitApp(platform, profile = 'production') {
  log(`📤 Submitting ${platform} app...`, 'blue');
  
  const command = `eas submit --platform ${platform} --profile ${profile} --non-interactive`;
  return runCommand(command, `Submitting ${platform} app`);
}

function generateBuildReport(platform, profile) {
  const timestamp = new Date().toISOString();
  const reportPath = path.join(__dirname, `../build-report-${platform}-${timestamp.split('T')[0]}.md`);
  
  const report = `# Build Report - ${platform}

**Date:** ${timestamp}
**Platform:** ${platform}
**Profile:** ${profile}
**Status:** ✅ Success

## Build Configuration

- EAS Build Profile: ${profile}
- Platform: ${platform}
- Auto-increment: Enabled
- Resource Class: ${platform === 'ios' ? 'm-medium' : 'medium'}

## Environment

- Node.js: ${process.version}
- Expo SDK: 49.0.0
- EAS CLI: Latest

## Next Steps

${platform === 'ios' ? `
1. Download the .ipa file from EAS Build dashboard
2. Test on TestFlight
3. Submit for App Store review
4. Monitor crash reports and user feedback
` : `
1. Download the .aab file from EAS Build dashboard
2. Test on Google Play Console internal testing
3. Submit for Google Play review
4. Monitor crash reports and user feedback
`}

## Links

- [EAS Build Dashboard](https://expo.dev/accounts/[account]/projects/fitnessapp/builds)
- [App Store Connect](https://appstoreconnect.apple.com) (iOS)
- [Google Play Console](https://play.google.com/console) (Android)
`;

  fs.writeFileSync(reportPath, report);
  log(`📄 Build report generated: ${reportPath}`, 'blue');
}

function main() {
  const args = process.argv.slice(2);
  const platform = args[0]; // 'ios', 'android', or 'all'
  const profile = args[1] || 'production';
  const shouldSubmit = args.includes('--submit');

  log('🚀 FitnessApp Mobile Build Script', 'bright');
  log('==================================', 'bright');

  if (!platform || !['ios', 'android', 'all'].includes(platform)) {
    log('❌ Please specify platform: ios, android, or all', 'red');
    log('Usage: node build.js <platform> [profile] [--submit]', 'yellow');
    process.exit(1);
  }

  // Check prerequisites
  if (!checkPrerequisites()) {
    process.exit(1);
  }

  // Validate environment
  if (!validateEnvironment()) {
    process.exit(1);
  }

  // Update version
  if (!updateVersion()) {
    process.exit(1);
  }

  // Run tests before building
  if (!runCommand('npm run test:ci', 'Running tests')) {
    log('❌ Tests failed. Aborting build.', 'red');
    process.exit(1);
  }

  // Build apps
  const platforms = platform === 'all' ? ['ios', 'android'] : [platform];
  let allBuildsSuccessful = true;

  for (const plt of platforms) {
    const buildProfile = profile === 'production' ? `production-${plt}` : profile;
    
    if (!buildApp(plt, buildProfile)) {
      allBuildsSuccessful = false;
      break;
    }

    generateBuildReport(plt, buildProfile);

    // Submit if requested and build was successful
    if (shouldSubmit && profile === 'production') {
      if (!submitApp(plt, `production-${plt}`)) {
        log(`⚠️ Build succeeded but submission failed for ${plt}`, 'yellow');
      }
    }
  }

  if (allBuildsSuccessful) {
    log('\n🎉 All builds completed successfully!', 'green');
    log('Check the EAS Build dashboard for download links.', 'blue');
  } else {
    log('\n💥 Build process failed!', 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { buildApp, submitApp, checkPrerequisites, validateEnvironment };
