#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan');
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return false;
  }
}

function checkPrerequisites() {
  log('🔍 Checking app store submission prerequisites...', 'blue');
  
  const checks = [
    { cmd: 'eas --version', name: 'EAS CLI' },
    { cmd: 'fastlane --version', name: 'Fastlane' },
  ];

  for (const check of checks) {
    try {
      execSync(check.cmd, { stdio: 'pipe' });
      log(`✅ ${check.name} is installed`, 'green');
    } catch (error) {
      log(`❌ ${check.name} is not installed`, 'red');
      if (check.name === 'Fastlane') {
        log('Install with: gem install fastlane', 'yellow');
      }
      return false;
    }
  }

  return true;
}

function validateAppStoreAssets() {
  log('📱 Validating app store assets...', 'blue');
  
  const requiredAssets = [
    'app-store/screenshots/ios/iphone-6.5/1.png',
    'app-store/screenshots/ios/iphone-6.5/2.png',
    'app-store/screenshots/ios/iphone-6.5/3.png',
    'app-store/screenshots/ios/iphone-6.5/4.png',
    'app-store/screenshots/ios/iphone-6.5/5.png',
    'app-store/screenshots/android/phone/1.png',
    'app-store/screenshots/android/phone/2.png',
    'app-store/screenshots/android/phone/3.png',
    'app-store/screenshots/android/phone/4.png',
    'app-store/screenshots/android/phone/5.png',
    'app-store/app-store-listing.md',
    'app-store/privacy-policy.md',
    'app-store/terms-of-service.md',
  ];

  const missingAssets = requiredAssets.filter(asset => 
    !fs.existsSync(path.join(__dirname, '..', asset))
  );

  if (missingAssets.length > 0) {
    log('❌ Missing required assets:', 'red');
    missingAssets.forEach(asset => log(`  - ${asset}`, 'red'));
    return false;
  }

  log('✅ All required assets are present', 'green');
  return true;
}

function validateAppConfiguration() {
  log('⚙️ Validating app configuration...', 'blue');
  
  try {
    const appConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '../app.json'), 'utf8'));
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
    
    const requiredFields = [
      'expo.name',
      'expo.slug',
      'expo.version',
      'expo.orientation',
      'expo.icon',
      'expo.splash',
      'expo.ios.bundleIdentifier',
      'expo.android.package',
    ];

    const missingFields = requiredFields.filter(field => {
      const keys = field.split('.');
      let obj = appConfig;
      for (const key of keys) {
        if (!obj || !obj[key]) return true;
        obj = obj[key];
      }
      return false;
    });

    if (missingFields.length > 0) {
      log('❌ Missing required app configuration fields:', 'red');
      missingFields.forEach(field => log(`  - ${field}`, 'red'));
      return false;
    }

    // Check version consistency
    if (appConfig.expo.version !== packageJson.version) {
      log('❌ Version mismatch between app.json and package.json', 'red');
      return false;
    }

    log('✅ App configuration is valid', 'green');
    return true;
  } catch (error) {
    log(`❌ Failed to validate app configuration: ${error.message}`, 'red');
    return false;
  }
}

function buildForSubmission(platform) {
  log(`🏗️ Building ${platform} app for submission...`, 'blue');
  
  const profile = `production-${platform}`;
  return runCommand(
    `eas build --platform ${platform} --profile ${profile} --non-interactive`,
    `Building ${platform} app`
  );
}

function submitToAppStore(platform) {
  log(`📤 Submitting ${platform} app to store...`, 'blue');
  
  const profile = `production-${platform}`;
  return runCommand(
    `eas submit --platform ${platform} --profile ${profile} --latest --non-interactive`,
    `Submitting ${platform} app`
  );
}

function generateSubmissionReport(platform, success) {
  const timestamp = new Date().toISOString();
  const reportPath = path.join(__dirname, `../submission-report-${platform}-${timestamp.split('T')[0]}.md`);
  
  const report = `# App Store Submission Report - ${platform}

**Date:** ${timestamp}
**Platform:** ${platform}
**Status:** ${success ? '✅ Success' : '❌ Failed'}
**Version:** ${JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8')).version}

## Submission Details

- Build Profile: production-${platform}
- Submission Method: EAS Submit
- Auto-submission: Enabled

## Pre-submission Checklist

- ✅ Prerequisites checked
- ✅ App store assets validated
- ✅ App configuration validated
- ✅ Build completed successfully
- ${success ? '✅' : '❌'} Submission completed

## Next Steps

${platform === 'ios' ? `
1. Monitor App Store Connect for review status
2. Respond to any review feedback promptly
3. Prepare for release once approved
4. Monitor crash reports and user feedback
` : `
1. Monitor Google Play Console for review status
2. Check for any policy violations
3. Prepare for release once approved
4. Monitor crash reports and user feedback
`}

## Important Links

${platform === 'ios' ? `
- [App Store Connect](https://appstoreconnect.apple.com)
- [iOS App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
` : `
- [Google Play Console](https://play.google.com/console)
- [Google Play Policy Center](https://play.google.com/about/developer-content-policy/)
`}

## Review Timeline

${platform === 'ios' ? `
- Expected review time: 24-48 hours
- Holiday periods may extend review time
- First submission may take longer
` : `
- Expected review time: 1-3 days
- Policy violations may extend review time
- Updates typically reviewed faster
`}

## Monitoring

- Set up alerts for review status changes
- Monitor crash reports and user feedback
- Track download and conversion metrics
- Prepare for post-launch updates

---

Generated by FitnessApp submission script
`;

  fs.writeFileSync(reportPath, report);
  log(`📄 Submission report generated: ${reportPath}`, 'blue');
}

function setupFastlane(platform) {
  log(`⚡ Setting up Fastlane for ${platform}...`, 'blue');
  
  const fastlaneDir = path.join(__dirname, '../fastlane');
  if (!fs.existsSync(fastlaneDir)) {
    fs.mkdirSync(fastlaneDir);
  }

  // Create Fastfile
  const fastfileContent = platform === 'ios' ? `
default_platform(:ios)

platform :ios do
  desc "Upload to App Store"
  lane :release do
    upload_to_app_store(
      skip_metadata: false,
      skip_screenshots: false,
      submit_for_review: true,
      automatic_release: false,
      force: true
    )
  end

  desc "Upload metadata only"
  lane :metadata do
    upload_to_app_store(
      skip_binary_upload: true,
      skip_screenshots: false
    )
  end
end
` : `
default_platform(:android)

platform :android do
  desc "Upload to Google Play"
  lane :release do
    upload_to_play_store(
      track: 'production',
      skip_upload_metadata: false,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end

  desc "Upload metadata only"
  lane :metadata do
    upload_to_play_store(
      skip_upload_apk: true,
      skip_upload_aab: true,
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
  end
end
`;

  fs.writeFileSync(path.join(fastlaneDir, 'Fastfile'), fastfileContent);
  log(`✅ Fastlane configured for ${platform}`, 'green');
}

function main() {
  const args = process.argv.slice(2);
  const platform = args[0]; // 'ios', 'android', or 'all'
  const skipBuild = args.includes('--skip-build');
  const metadataOnly = args.includes('--metadata-only');

  log('📱 FitnessApp Store Submission Script', 'bright');
  log('====================================', 'bright');

  if (!platform || !['ios', 'android', 'all'].includes(platform)) {
    log('❌ Please specify platform: ios, android, or all', 'red');
    log('Usage: node app-store-submit.js <platform> [--skip-build] [--metadata-only]', 'yellow');
    process.exit(1);
  }

  // Check prerequisites
  if (!checkPrerequisites()) {
    process.exit(1);
  }

  // Validate assets and configuration
  if (!validateAppStoreAssets() || !validateAppConfiguration()) {
    process.exit(1);
  }

  const platforms = platform === 'all' ? ['ios', 'android'] : [platform];
  let allSubmissionsSuccessful = true;

  for (const plt of platforms) {
    log(`\n🚀 Starting ${plt} submission process...`, 'bright');
    
    // Setup Fastlane
    setupFastlane(plt);
    
    let success = true;

    // Build app (unless skipped)
    if (!skipBuild && !metadataOnly) {
      if (!buildForSubmission(plt)) {
        success = false;
        allSubmissionsSuccessful = false;
        generateSubmissionReport(plt, false);
        continue;
      }
    }

    // Submit to store
    if (!metadataOnly) {
      if (!submitToAppStore(plt)) {
        success = false;
        allSubmissionsSuccessful = false;
      }
    } else {
      // Upload metadata only using Fastlane
      if (!runCommand(`cd fastlane && fastlane ${plt} metadata`, `Uploading ${plt} metadata`)) {
        success = false;
        allSubmissionsSuccessful = false;
      }
    }

    generateSubmissionReport(plt, success);

    if (success) {
      log(`\n🎉 ${plt} submission completed successfully!`, 'green');
    } else {
      log(`\n💥 ${plt} submission failed!`, 'red');
    }
  }

  if (allSubmissionsSuccessful) {
    log('\n🎉 All submissions completed successfully!', 'green');
    log('Monitor the respective app store consoles for review status.', 'blue');
  } else {
    log('\n💥 Some submissions failed!', 'red');
    log('Check the submission reports for details.', 'yellow');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { 
  buildForSubmission, 
  submitToAppStore, 
  validateAppStoreAssets, 
  validateAppConfiguration 
};
