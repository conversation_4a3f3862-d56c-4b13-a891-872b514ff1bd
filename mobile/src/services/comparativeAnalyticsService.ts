import { supabase } from '@/lib/supabase';

export interface PeerGroup {
  id: string;
  name: string;
  criteria: {
    age_range: [number, number];
    experience_level: string[];
    primary_goals: string[];
    gender?: string;
    body_weight_range?: [number, number];
  };
  member_count: number;
  avg_metrics: Record<string, number>;
}

export interface BenchmarkData {
  metric: string;
  user_value: number;
  peer_group_average: number;
  peer_group_median: number;
  percentile_rank: number;
  top_10_percent_threshold: number;
  top_25_percent_threshold: number;
  improvement_needed_for_next_quartile: number;
  comparison_context: string;
}

export interface CompetitiveAnalysis {
  user_rank: number;
  total_users: number;
  percentile: number;
  metrics_comparison: {
    metric: string;
    user_value: number;
    rank: number;
    percentile: number;
    above_average: boolean;
    improvement_potential: number;
  }[];
  strengths: string[];
  improvement_areas: string[];
  peer_insights: {
    similar_users: {
      user_id: string;
      similarity_score: number;
      better_metrics: string[];
    }[];
    top_performers: {
      user_id: string;
      standout_metrics: string[];
      performance_gap: Record<string, number>;
    }[];
  };
}

export interface PopulationAnalytics {
  total_active_users: number;
  demographic_breakdown: {
    age_groups: Record<string, number>;
    experience_levels: Record<string, number>;
    primary_goals: Record<string, number>;
    geographic_distribution: Record<string, number>;
  };
  performance_distributions: {
    metric: string;
    distribution: {
      percentile: number;
      value: number;
    }[];
    mean: number;
    median: number;
    standard_deviation: number;
  }[];
  trends: {
    metric: string;
    monthly_averages: {
      month: string;
      average: number;
      user_count: number;
    }[];
    growth_rate: number;
  }[];
  insights: {
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
  }[];
}

class ComparativeAnalyticsService {
  async getUserBenchmarks(metrics: string[]): Promise<BenchmarkData[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const userProfile = await this.getUserProfile(user.id);
      const peerGroup = await this.findPeerGroup(userProfile);
      const userMetrics = await this.getUserMetrics(user.id, metrics);

      const benchmarks: BenchmarkData[] = [];

      for (const metric of metrics) {
        const benchmark = await this.calculateBenchmark(
          metric,
          userMetrics[metric] || 0,
          peerGroup
        );
        benchmarks.push(benchmark);
      }

      return benchmarks;
    } catch (error) {
      console.error('Error getting user benchmarks:', error);
      throw error;
    }
  }

  async getCompetitiveAnalysis(): Promise<CompetitiveAnalysis> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const userProfile = await this.getUserProfile(user.id);
      const userMetrics = await this.getUserMetrics(user.id, [
        'total_volume',
        'workout_frequency',
        'strength_score',
        'consistency_score'
      ]);

      // Get peer group for comparison
      const peerGroup = await this.findPeerGroup(userProfile);
      const peerMetrics = await this.getPeerGroupMetrics(peerGroup);

      // Calculate rankings and percentiles
      const metricsComparison = await this.calculateMetricsComparison(
        userMetrics,
        peerMetrics
      );

      // Find similar users and top performers
      const peerInsights = await this.analyzePeerInsights(user.id, peerGroup);

      // Determine strengths and improvement areas
      const strengths = metricsComparison
        .filter(m => m.percentile >= 75)
        .map(m => m.metric);
      
      const improvementAreas = metricsComparison
        .filter(m => m.percentile < 50)
        .map(m => m.metric);

      return {
        user_rank: Math.floor(Math.random() * peerGroup.member_count) + 1, // Simplified
        total_users: peerGroup.member_count,
        percentile: metricsComparison.reduce((sum, m) => sum + m.percentile, 0) / metricsComparison.length,
        metrics_comparison: metricsComparison,
        strengths,
        improvement_areas: improvementAreas,
        peer_insights: peerInsights,
      };
    } catch (error) {
      console.error('Error getting competitive analysis:', error);
      throw error;
    }
  }

  async getPopulationAnalytics(): Promise<PopulationAnalytics> {
    try {
      // Get overall population statistics
      const [demographics, distributions, trends] = await Promise.all([
        this.getDemographicBreakdown(),
        this.getPerformanceDistributions(),
        this.getPopulationTrends(),
      ]);

      const insights = this.generatePopulationInsights(demographics, distributions, trends);

      return {
        total_active_users: demographics.total_users,
        demographic_breakdown: demographics,
        performance_distributions: distributions,
        trends,
        insights,
      };
    } catch (error) {
      console.error('Error getting population analytics:', error);
      throw error;
    }
  }

  async findSimilarUsers(limit: number = 10): Promise<{
    user_id: string;
    similarity_score: number;
    shared_characteristics: string[];
    performance_comparison: Record<string, { user: number; similar_user: number }>;
  }[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const userProfile = await this.getUserProfile(user.id);
      const userMetrics = await this.getUserMetrics(user.id, ['total_volume', 'workout_frequency']);

      // Find users with similar profiles
      const { data: similarProfiles } = await supabase
        .from('user_profiles')
        .select('user_id, age, experience_level, primary_goals')
        .neq('user_id', user.id)
        .limit(100);

      if (!similarProfiles) return [];

      // Calculate similarity scores
      const similarUsers = similarProfiles
        .map(profile => ({
          ...profile,
          similarity_score: this.calculateSimilarityScore(userProfile, profile),
        }))
        .filter(u => u.similarity_score > 0.5)
        .sort((a, b) => b.similarity_score - a.similarity_score)
        .slice(0, limit);

      // Get performance data for similar users
      const results = [];
      for (const similarUser of similarUsers) {
        const similarUserMetrics = await this.getUserMetrics(similarUser.user_id, ['total_volume', 'workout_frequency']);
        
        results.push({
          user_id: similarUser.user_id,
          similarity_score: similarUser.similarity_score,
          shared_characteristics: this.getSharedCharacteristics(userProfile, similarUser),
          performance_comparison: {
            total_volume: { user: userMetrics.total_volume || 0, similar_user: similarUserMetrics.total_volume || 0 },
            workout_frequency: { user: userMetrics.workout_frequency || 0, similar_user: similarUserMetrics.workout_frequency || 0 },
          },
        });
      }

      return results;
    } catch (error) {
      console.error('Error finding similar users:', error);
      throw error;
    }
  }

  private async getUserProfile(userId: string) {
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('age, experience_level, primary_goals, gender')
      .eq('user_id', userId)
      .single();

    return profile || {
      age: 30,
      experience_level: 'intermediate',
      primary_goals: ['strength'],
      gender: null,
    };
  }

  private async findPeerGroup(userProfile: any): Promise<PeerGroup> {
    // Define age ranges
    const ageRanges = [
      [18, 25], [26, 35], [36, 45], [46, 55], [56, 65]
    ];
    
    const userAgeRange = ageRanges.find(range => 
      userProfile.age >= range[0] && userProfile.age <= range[1]
    ) || [26, 35];

    // Create peer group criteria
    const criteria = {
      age_range: userAgeRange as [number, number],
      experience_level: [userProfile.experience_level],
      primary_goals: userProfile.primary_goals || ['general_fitness'],
      gender: userProfile.gender,
    };

    // Get member count (simplified)
    const memberCount = Math.floor(Math.random() * 500) + 100;

    return {
      id: 'peer_group_1',
      name: `${criteria.experience_level[0]} ${userAgeRange[0]}-${userAgeRange[1]}`,
      criteria,
      member_count: memberCount,
      avg_metrics: {
        total_volume: 15000,
        workout_frequency: 3.5,
        strength_score: 72,
        consistency_score: 68,
      },
    };
  }

  private async getUserMetrics(userId: string, metrics: string[]): Promise<Record<string, number>> {
    const result: Record<string, number> = {};

    // Get recent workout data
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select(`
        *,
        exercise_logs(*)
      `)
      .eq('user_id', userId)
      .order('completed_at', { ascending: false })
      .limit(20);

    if (!workouts || workouts.length === 0) {
      return result;
    }

    // Calculate metrics
    if (metrics.includes('total_volume')) {
      result.total_volume = this.calculateTotalVolume(workouts);
    }

    if (metrics.includes('workout_frequency')) {
      result.workout_frequency = this.calculateWorkoutFrequency(workouts);
    }

    if (metrics.includes('strength_score')) {
      result.strength_score = this.calculateStrengthScore(workouts);
    }

    if (metrics.includes('consistency_score')) {
      result.consistency_score = this.calculateConsistencyScore(workouts);
    }

    return result;
  }

  private calculateTotalVolume(workouts: any[]): number {
    return workouts.reduce((total, workout) => {
      const workoutVolume = workout.exercise_logs?.reduce((sum: number, exercise: any) => {
        return sum + (exercise.sets?.reduce((setSum: number, set: any) => {
          return setSum + (set.weight || 0) * (set.reps || 0);
        }, 0) || 0);
      }, 0) || 0;
      return total + workoutVolume;
    }, 0);
  }

  private calculateWorkoutFrequency(workouts: any[]): number {
    if (workouts.length === 0) return 0;
    
    const firstWorkout = new Date(workouts[workouts.length - 1].completed_at);
    const lastWorkout = new Date(workouts[0].completed_at);
    const daysDiff = Math.ceil((lastWorkout.getTime() - firstWorkout.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysDiff > 0 ? (workouts.length / daysDiff) * 7 : 0; // Workouts per week
  }

  private calculateStrengthScore(workouts: any[]): number {
    // Simplified strength score calculation
    const totalVolume = this.calculateTotalVolume(workouts);
    return Math.min(100, (totalVolume / 1000) + 50);
  }

  private calculateConsistencyScore(workouts: any[]): number {
    if (workouts.length < 2) return 0;
    
    const frequency = this.calculateWorkoutFrequency(workouts);
    return Math.min(100, (frequency / 4) * 100); // Target: 4 workouts per week
  }

  private async calculateBenchmark(
    metric: string,
    userValue: number,
    peerGroup: PeerGroup
  ): Promise<BenchmarkData> {
    // In a real implementation, this would query actual peer data
    // For now, use mock data based on peer group averages
    const peerAverage = peerGroup.avg_metrics[metric] || 0;
    const peerMedian = peerAverage * 0.9; // Simplified
    
    // Calculate percentile (simplified)
    const percentile = userValue > peerAverage ? 
      Math.min(95, 50 + ((userValue - peerAverage) / peerAverage) * 30) :
      Math.max(5, 50 - ((peerAverage - userValue) / peerAverage) * 30);

    const top10Threshold = peerAverage * 1.5;
    const top25Threshold = peerAverage * 1.2;

    return {
      metric,
      user_value: userValue,
      peer_group_average: peerAverage,
      peer_group_median: peerMedian,
      percentile_rank: percentile,
      top_10_percent_threshold: top10Threshold,
      top_25_percent_threshold: top25Threshold,
      improvement_needed_for_next_quartile: Math.max(0, top25Threshold - userValue),
      comparison_context: this.getComparisonContext(percentile),
    };
  }

  private getComparisonContext(percentile: number): string {
    if (percentile >= 90) return 'Exceptional performance - top 10%';
    if (percentile >= 75) return 'Above average performance - top 25%';
    if (percentile >= 50) return 'Average performance';
    if (percentile >= 25) return 'Below average - room for improvement';
    return 'Significant improvement opportunity';
  }

  private async getPeerGroupMetrics(peerGroup: PeerGroup): Promise<Record<string, number[]>> {
    // In a real implementation, this would query actual peer data
    // For now, generate mock data based on normal distribution
    const metrics: Record<string, number[]> = {};
    
    Object.entries(peerGroup.avg_metrics).forEach(([metric, average]) => {
      const values = [];
      for (let i = 0; i < peerGroup.member_count; i++) {
        // Generate normal distribution around average
        const value = average + (Math.random() - 0.5) * average * 0.4;
        values.push(Math.max(0, value));
      }
      metrics[metric] = values.sort((a, b) => a - b);
    });

    return metrics;
  }

  private async calculateMetricsComparison(
    userMetrics: Record<string, number>,
    peerMetrics: Record<string, number[]>
  ) {
    const comparisons = [];

    for (const [metric, userValue] of Object.entries(userMetrics)) {
      const peerValues = peerMetrics[metric] || [];
      if (peerValues.length === 0) continue;

      const rank = peerValues.filter(v => v < userValue).length + 1;
      const percentile = (rank / peerValues.length) * 100;
      const average = peerValues.reduce((a, b) => a + b, 0) / peerValues.length;

      comparisons.push({
        metric,
        user_value: userValue,
        rank,
        percentile,
        above_average: userValue > average,
        improvement_potential: Math.max(0, peerValues[Math.floor(peerValues.length * 0.9)] - userValue),
      });
    }

    return comparisons;
  }

  private async analyzePeerInsights(userId: string, peerGroup: PeerGroup) {
    // Mock data for peer insights
    return {
      similar_users: [
        {
          user_id: 'user_123',
          similarity_score: 0.85,
          better_metrics: ['workout_frequency', 'consistency_score'],
        },
        {
          user_id: 'user_456',
          similarity_score: 0.78,
          better_metrics: ['total_volume'],
        },
      ],
      top_performers: [
        {
          user_id: 'user_789',
          standout_metrics: ['strength_score', 'total_volume'],
          performance_gap: {
            strength_score: 15,
            total_volume: 5000,
          },
        },
      ],
    };
  }

  private async getDemographicBreakdown() {
    // Mock demographic data
    return {
      total_users: 10000,
      age_groups: {
        '18-25': 2000,
        '26-35': 3500,
        '36-45': 2800,
        '46-55': 1200,
        '56+': 500,
      },
      experience_levels: {
        'beginner': 3000,
        'intermediate': 4500,
        'advanced': 2000,
        'expert': 500,
      },
      primary_goals: {
        'weight_loss': 3500,
        'muscle_gain': 2800,
        'strength': 2200,
        'endurance': 1000,
        'general_fitness': 500,
      },
      geographic_distribution: {
        'North America': 4500,
        'Europe': 3000,
        'Asia': 1800,
        'Other': 700,
      },
    };
  }

  private async getPerformanceDistributions() {
    // Mock performance distribution data
    return [
      {
        metric: 'total_volume',
        distribution: Array.from({ length: 100 }, (_, i) => ({
          percentile: i + 1,
          value: 5000 + (i * 200),
        })),
        mean: 15000,
        median: 14000,
        standard_deviation: 5000,
      },
      {
        metric: 'workout_frequency',
        distribution: Array.from({ length: 100 }, (_, i) => ({
          percentile: i + 1,
          value: 1 + (i * 0.05),
        })),
        mean: 3.5,
        median: 3.2,
        standard_deviation: 1.2,
      },
    ];
  }

  private async getPopulationTrends() {
    // Mock trend data
    return [
      {
        metric: 'total_volume',
        monthly_averages: Array.from({ length: 12 }, (_, i) => ({
          month: new Date(2024, i).toLocaleDateString('en-US', { month: 'short' }),
          average: 14000 + (i * 500),
          user_count: 8000 + (i * 100),
        })),
        growth_rate: 0.15,
      },
    ];
  }

  private generatePopulationInsights(demographics: any, distributions: any, trends: any) {
    return [
      {
        title: 'Growing User Base',
        description: 'The platform has seen 15% growth in active users over the past year',
        impact: 'high' as const,
      },
      {
        title: 'Strength Training Popularity',
        description: 'Strength-focused goals are increasingly popular among intermediate users',
        impact: 'medium' as const,
      },
    ];
  }

  private calculateSimilarityScore(userProfile: any, otherProfile: any): number {
    let score = 0;
    let factors = 0;

    // Age similarity
    const ageDiff = Math.abs(userProfile.age - otherProfile.age);
    score += Math.max(0, 1 - (ageDiff / 20));
    factors++;

    // Experience level similarity
    if (userProfile.experience_level === otherProfile.experience_level) {
      score += 1;
    }
    factors++;

    // Goal similarity
    const userGoals = new Set(userProfile.primary_goals || []);
    const otherGoals = new Set(otherProfile.primary_goals || []);
    const commonGoals = [...userGoals].filter(g => otherGoals.has(g));
    score += commonGoals.length / Math.max(userGoals.size, otherGoals.size);
    factors++;

    return score / factors;
  }

  private getSharedCharacteristics(userProfile: any, otherProfile: any): string[] {
    const shared = [];

    if (userProfile.experience_level === otherProfile.experience_level) {
      shared.push(`Both ${userProfile.experience_level} level`);
    }

    const ageDiff = Math.abs(userProfile.age - otherProfile.age);
    if (ageDiff <= 5) {
      shared.push('Similar age');
    }

    const userGoals = new Set(userProfile.primary_goals || []);
    const otherGoals = new Set(otherProfile.primary_goals || []);
    const commonGoals = [...userGoals].filter(g => otherGoals.has(g));
    if (commonGoals.length > 0) {
      shared.push(`Shared goals: ${commonGoals.join(', ')}`);
    }

    return shared;
  }
}

export const comparativeAnalyticsService = new ComparativeAnalyticsService();
