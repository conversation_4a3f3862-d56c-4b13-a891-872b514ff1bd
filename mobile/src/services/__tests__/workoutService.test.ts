import { workoutService } from '../workoutService';
import { supabase } from '@/lib/supabase';
import { createMockWorkout, createMockWorkoutProgram, createMockWorkoutLog } from '@test/utils';

// Mock Supabase
jest.mock('@/lib/supabase');
const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('WorkoutService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getWorkouts', () => {
    it('fetches workouts successfully', async () => {
      const mockWorkouts = [
        createMockWorkout(),
        createMockWorkout({ id: '2', name: 'Workout 2' }),
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockWorkouts,
          error: null,
        }),
      } as any);

      const result = await workoutService.getWorkouts();

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(result.data).toEqual(mockWorkouts);
      expect(result.error).toBeNull();
    });

    it('handles fetch workouts error', async () => {
      const errorMessage = 'Failed to fetch workouts';
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: null,
          error: { message: errorMessage },
        }),
      } as any);

      const result = await workoutService.getWorkouts();

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe(errorMessage);
    });
  });

  describe('getWorkoutById', () => {
    it('fetches workout by id successfully', async () => {
      const mockWorkout = createMockWorkout();
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockWorkout,
          error: null,
        }),
      } as any);

      const result = await workoutService.getWorkoutById('test-id');

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(result.data).toEqual(mockWorkout);
      expect(result.error).toBeNull();
    });

    it('handles workout not found', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'Workout not found' },
        }),
      } as any);

      const result = await workoutService.getWorkoutById('non-existent');

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe('Workout not found');
    });
  });

  describe('createWorkout', () => {
    it('creates workout successfully', async () => {
      const workoutData = {
        name: 'New Workout',
        description: 'A new workout',
        exercises: [],
        duration: 45,
        difficulty: 'intermediate',
        type: 'strength',
      };

      const createdWorkout = createMockWorkout(workoutData);

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: createdWorkout,
          error: null,
        }),
      } as any);

      const result = await workoutService.createWorkout(workoutData);

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(result.data).toEqual(createdWorkout);
      expect(result.error).toBeNull();
    });

    it('handles create workout error', async () => {
      const workoutData = {
        name: 'New Workout',
        description: 'A new workout',
        exercises: [],
      };

      const errorMessage = 'Failed to create workout';
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: errorMessage },
        }),
      } as any);

      const result = await workoutService.createWorkout(workoutData);

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe(errorMessage);
    });
  });

  describe('updateWorkout', () => {
    it('updates workout successfully', async () => {
      const updateData = { name: 'Updated Workout' };
      const updatedWorkout = createMockWorkout(updateData);

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: updatedWorkout,
          error: null,
        }),
      } as any);

      const result = await workoutService.updateWorkout('test-id', updateData);

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(result.data).toEqual(updatedWorkout);
      expect(result.error).toBeNull();
    });
  });

  describe('deleteWorkout', () => {
    it('deletes workout successfully', async () => {
      mockSupabase.from.mockReturnValue({
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      } as any);

      const result = await workoutService.deleteWorkout('test-id');

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(result.data).toBe(true);
      expect(result.error).toBeNull();
    });
  });

  describe('getWorkoutPrograms', () => {
    it('fetches workout programs successfully', async () => {
      const mockPrograms = [
        createMockWorkoutProgram(),
        createMockWorkoutProgram({ id: '2', name: 'Program 2' }),
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockPrograms,
          error: null,
        }),
      } as any);

      const result = await workoutService.getWorkoutPrograms();

      expect(mockSupabase.from).toHaveBeenCalledWith('workout_programs');
      expect(result.data).toEqual(mockPrograms);
      expect(result.error).toBeNull();
    });
  });

  describe('startWorkoutSession', () => {
    it('starts workout session successfully', async () => {
      const mockSession = {
        id: 'session-1',
        workoutId: 'workout-1',
        startedAt: new Date().toISOString(),
      };

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockSession,
          error: null,
        }),
      } as any);

      const result = await workoutService.startWorkoutSession('workout-1');

      expect(mockSupabase.from).toHaveBeenCalledWith('workout_sessions');
      expect(result.data).toEqual(mockSession);
      expect(result.error).toBeNull();
    });
  });

  describe('completeWorkoutSession', () => {
    it('completes workout session successfully', async () => {
      const sessionData = {
        sessionId: 'session-1',
        exercises: [
          {
            exerciseId: 'exercise-1',
            sets: [{ reps: 10, weight: 50, completed: true }],
          },
        ],
        notes: 'Great workout!',
        rating: 5,
      };

      const mockLog = createMockWorkoutLog();

      // Mock session update
      mockSupabase.from.mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      } as any);

      // Mock workout log creation
      mockSupabase.from.mockReturnValueOnce({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockLog,
          error: null,
        }),
      } as any);

      const result = await workoutService.completeWorkoutSession(sessionData);

      expect(result.data).toEqual(mockLog);
      expect(result.error).toBeNull();
    });
  });

  describe('getWorkoutLogs', () => {
    it('fetches workout logs successfully', async () => {
      const mockLogs = [
        createMockWorkoutLog(),
        createMockWorkoutLog({ id: '2' }),
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockLogs,
          error: null,
        }),
      } as any);

      const result = await workoutService.getWorkoutLogs();

      expect(mockSupabase.from).toHaveBeenCalledWith('workout_logs');
      expect(result.data).toEqual(mockLogs);
      expect(result.error).toBeNull();
    });
  });

  describe('getWorkoutsByType', () => {
    it('filters workouts by type successfully', async () => {
      const mockWorkouts = [
        createMockWorkout({ type: 'strength' }),
        createMockWorkout({ id: '2', type: 'strength' }),
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockWorkouts,
          error: null,
        }),
      } as any);

      const result = await workoutService.getWorkoutsByType('strength');

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(result.data).toEqual(mockWorkouts);
      expect(result.error).toBeNull();
    });
  });

  describe('searchWorkouts', () => {
    it('searches workouts successfully', async () => {
      const mockWorkouts = [
        createMockWorkout({ name: 'Push Day Workout' }),
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        ilike: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockWorkouts,
          error: null,
        }),
      } as any);

      const result = await workoutService.searchWorkouts('Push');

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(result.data).toEqual(mockWorkouts);
      expect(result.error).toBeNull();
    });
  });

  describe('getWorkoutStatistics', () => {
    it('fetches workout statistics successfully', async () => {
      const mockStats = {
        totalWorkouts: 50,
        totalDuration: 2500,
        averageRating: 4.2,
        streakDays: 7,
      };

      // Mock multiple database calls for statistics
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        count: jest.fn().mockResolvedValue({
          data: [{ count: mockStats.totalWorkouts }],
          error: null,
        }),
      } as any);

      const result = await workoutService.getWorkoutStatistics();

      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
    });
  });
});
