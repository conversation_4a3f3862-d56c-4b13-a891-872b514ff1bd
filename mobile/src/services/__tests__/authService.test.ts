import { authService } from '../authService';
import { supabase } from '@/lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createMockUser } from '@test/utils';

// Mock Supabase
jest.mock('@/lib/supabase');
const mockSupabase = supabase as jest.Mocked<typeof supabase>;

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage');
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('signUp', () => {
    it('creates a new user account successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const mockUser = createMockUser({
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
      });

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser, session: null },
        error: null,
      });

      const result = await authService.signUp(userData);

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
          },
        },
      });

      expect(result.data).toEqual(mockUser);
      expect(result.error).toBeNull();
    });

    it('handles sign up error', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const errorMessage = 'User already exists';
      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: errorMessage },
      });

      const result = await authService.signUp(userData);

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe(errorMessage);
    });
  });

  describe('signIn', () => {
    it('signs in user successfully', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockUser = createMockUser({ email: credentials.email });
      const mockSession = {
        access_token: 'mock-token',
        refresh_token: 'mock-refresh-token',
        user: mockUser,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      });

      const result = await authService.signIn(credentials);

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: credentials.email,
        password: credentials.password,
      });

      expect(result.data).toEqual({ user: mockUser, session: mockSession });
      expect(result.error).toBeNull();
    });

    it('handles invalid credentials', async () => {
      const credentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const errorMessage = 'Invalid login credentials';
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: errorMessage },
      });

      const result = await authService.signIn(credentials);

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe(errorMessage);
    });
  });

  describe('signOut', () => {
    it('signs out user successfully', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({ error: null });
      mockAsyncStorage.multiRemove.mockResolvedValue();

      const result = await authService.signOut();

      expect(mockSupabase.auth.signOut).toHaveBeenCalled();
      expect(mockAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'user_session',
        'push_token',
        'device_id',
      ]);
      expect(result.error).toBeNull();
    });

    it('handles sign out error', async () => {
      const errorMessage = 'Sign out failed';
      mockSupabase.auth.signOut.mockResolvedValue({ error: { message: errorMessage } });

      const result = await authService.signOut();

      expect(result.error?.message).toBe(errorMessage);
    });
  });

  describe('getCurrentUser', () => {
    it('returns current user when authenticated', async () => {
      const mockUser = createMockUser();
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const result = await authService.getCurrentUser();

      expect(mockSupabase.auth.getUser).toHaveBeenCalled();
      expect(result.data).toEqual(mockUser);
      expect(result.error).toBeNull();
    });

    it('returns null when not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const result = await authService.getCurrentUser();

      expect(result.data).toBeNull();
      expect(result.error).toBeNull();
    });
  });

  describe('resetPassword', () => {
    it('sends password reset email successfully', async () => {
      const email = '<EMAIL>';
      mockSupabase.auth.resetPasswordForEmail.mockResolvedValue({
        data: {},
        error: null,
      });

      const result = await authService.resetPassword(email);

      expect(mockSupabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(email);
      expect(result.error).toBeNull();
    });

    it('handles reset password error', async () => {
      const email = '<EMAIL>';
      const errorMessage = 'Email not found';
      mockSupabase.auth.resetPasswordForEmail.mockResolvedValue({
        data: {},
        error: { message: errorMessage },
      });

      const result = await authService.resetPassword(email);

      expect(result.error?.message).toBe(errorMessage);
    });
  });

  describe('updatePassword', () => {
    it('updates password successfully', async () => {
      const newPassword = 'newpassword123';
      mockSupabase.auth.updateUser.mockResolvedValue({
        data: { user: createMockUser() },
        error: null,
      });

      const result = await authService.updatePassword(newPassword);

      expect(mockSupabase.auth.updateUser).toHaveBeenCalledWith({
        password: newPassword,
      });
      expect(result.error).toBeNull();
    });

    it('handles update password error', async () => {
      const newPassword = 'newpassword123';
      const errorMessage = 'Password update failed';
      mockSupabase.auth.updateUser.mockResolvedValue({
        data: { user: null },
        error: { message: errorMessage },
      });

      const result = await authService.updatePassword(newPassword);

      expect(result.error?.message).toBe(errorMessage);
    });
  });

  describe('updateProfile', () => {
    it('updates user profile successfully', async () => {
      const profileData = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const updatedUser = createMockUser({
        firstName: profileData.firstName,
        lastName: profileData.lastName,
      });

      mockSupabase.auth.updateUser.mockResolvedValue({
        data: { user: updatedUser },
        error: null,
      });

      const result = await authService.updateProfile(profileData);

      expect(mockSupabase.auth.updateUser).toHaveBeenCalledWith({
        data: {
          first_name: profileData.firstName,
          last_name: profileData.lastName,
        },
      });
      expect(result.data).toEqual(updatedUser);
      expect(result.error).toBeNull();
    });
  });

  describe('signInWithGoogle', () => {
    it('initiates Google sign in successfully', async () => {
      mockSupabase.auth.signInWithOAuth.mockResolvedValue({
        data: { url: 'https://google.com/oauth', provider: 'google' },
        error: null,
      });

      const result = await authService.signInWithGoogle();

      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: expect.any(String),
        },
      });
      expect(result.error).toBeNull();
    });
  });

  describe('refreshSession', () => {
    it('refreshes session successfully', async () => {
      const mockSession = {
        access_token: 'new-token',
        refresh_token: 'new-refresh-token',
        user: createMockUser(),
      };

      mockSupabase.auth.refreshSession.mockResolvedValue({
        data: { session: mockSession, user: mockSession.user },
        error: null,
      });

      const result = await authService.refreshSession();

      expect(mockSupabase.auth.refreshSession).toHaveBeenCalled();
      expect(result.data).toEqual(mockSession);
      expect(result.error).toBeNull();
    });
  });

  describe('getSession', () => {
    it('returns current session', async () => {
      const mockSession = {
        access_token: 'token',
        refresh_token: 'refresh-token',
        user: createMockUser(),
      };

      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      const result = await authService.getSession();

      expect(mockSupabase.auth.getSession).toHaveBeenCalled();
      expect(result.data).toEqual(mockSession);
      expect(result.error).toBeNull();
    });
  });

  describe('onAuthStateChange', () => {
    it('sets up auth state change listener', () => {
      const mockCallback = jest.fn();
      const mockUnsubscribe = jest.fn();

      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: { subscription: { unsubscribe: mockUnsubscribe } },
      });

      const result = authService.onAuthStateChange(mockCallback);

      expect(mockSupabase.auth.onAuthStateChange).toHaveBeenCalledWith(mockCallback);
      expect(result.unsubscribe).toBe(mockUnsubscribe);
    });
  });
});
