import { notificationService } from '../notificationService';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock Expo Notifications
jest.mock('expo-notifications');
const mockNotifications = Notifications as jest.Mocked<typeof Notifications>;

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage');
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('NotificationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('requestPermissions', () => {
    it('requests notification permissions successfully', async () => {
      mockNotifications.getPermissionsAsync.mockResolvedValue({
        status: 'undetermined',
        canAskAgain: true,
        granted: false,
        ios: {},
        android: {},
      });

      mockNotifications.requestPermissionsAsync.mockResolvedValue({
        status: 'granted',
        canAskAgain: true,
        granted: true,
        ios: {},
        android: {},
      });

      const result = await notificationService.requestPermissions();

      expect(mockNotifications.getPermissionsAsync).toHaveBeenCalled();
      expect(mockNotifications.requestPermissionsAsync).toHaveBeenCalled();
      expect(result.granted).toBe(true);
    });

    it('returns existing permissions if already granted', async () => {
      mockNotifications.getPermissionsAsync.mockResolvedValue({
        status: 'granted',
        canAskAgain: true,
        granted: true,
        ios: {},
        android: {},
      });

      const result = await notificationService.requestPermissions();

      expect(mockNotifications.getPermissionsAsync).toHaveBeenCalled();
      expect(mockNotifications.requestPermissionsAsync).not.toHaveBeenCalled();
      expect(result.granted).toBe(true);
    });

    it('handles permission denied', async () => {
      mockNotifications.getPermissionsAsync.mockResolvedValue({
        status: 'undetermined',
        canAskAgain: true,
        granted: false,
        ios: {},
        android: {},
      });

      mockNotifications.requestPermissionsAsync.mockResolvedValue({
        status: 'denied',
        canAskAgain: false,
        granted: false,
        ios: {},
        android: {},
      });

      const result = await notificationService.requestPermissions();

      expect(result.granted).toBe(false);
    });
  });

  describe('getPushToken', () => {
    it('gets push token successfully', async () => {
      const mockToken = 'ExponentPushToken[test-token]';
      mockNotifications.getExpoPushTokenAsync.mockResolvedValue({
        data: mockToken,
        type: 'expo',
      });

      const result = await notificationService.getPushToken();

      expect(mockNotifications.getExpoPushTokenAsync).toHaveBeenCalled();
      expect(result).toBe(mockToken);
    });

    it('handles push token error', async () => {
      mockNotifications.getExpoPushTokenAsync.mockRejectedValue(
        new Error('Failed to get push token')
      );

      const result = await notificationService.getPushToken();

      expect(result).toBeNull();
    });
  });

  describe('scheduleNotification', () => {
    it('schedules notification successfully', async () => {
      const notificationData = {
        title: 'Workout Reminder',
        body: 'Time for your workout!',
        data: { workoutId: 'workout-1' },
        trigger: { seconds: 3600 },
      };

      const mockNotificationId = 'notification-1';
      mockNotifications.scheduleNotificationAsync.mockResolvedValue(mockNotificationId);

      const result = await notificationService.scheduleNotification(notificationData);

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: notificationData.title,
          body: notificationData.body,
          data: notificationData.data,
          sound: 'default',
        },
        trigger: notificationData.trigger,
      });

      expect(result).toBe(mockNotificationId);
    });

    it('handles schedule notification error', async () => {
      const notificationData = {
        title: 'Test',
        body: 'Test body',
        trigger: { seconds: 60 },
      };

      mockNotifications.scheduleNotificationAsync.mockRejectedValue(
        new Error('Failed to schedule notification')
      );

      const result = await notificationService.scheduleNotification(notificationData);

      expect(result).toBeNull();
    });
  });

  describe('cancelNotification', () => {
    it('cancels notification successfully', async () => {
      const notificationId = 'notification-1';
      mockNotifications.cancelScheduledNotificationAsync.mockResolvedValue();

      await notificationService.cancelNotification(notificationId);

      expect(mockNotifications.cancelScheduledNotificationAsync).toHaveBeenCalledWith(
        notificationId
      );
    });

    it('handles cancel notification error', async () => {
      const notificationId = 'notification-1';
      mockNotifications.cancelScheduledNotificationAsync.mockRejectedValue(
        new Error('Failed to cancel notification')
      );

      // Should not throw error
      await expect(notificationService.cancelNotification(notificationId)).resolves.toBeUndefined();
    });
  });

  describe('cancelAllNotifications', () => {
    it('cancels all notifications successfully', async () => {
      mockNotifications.cancelAllScheduledNotificationsAsync.mockResolvedValue();

      await notificationService.cancelAllNotifications();

      expect(mockNotifications.cancelAllScheduledNotificationsAsync).toHaveBeenCalled();
    });
  });

  describe('scheduleWorkoutReminder', () => {
    it('schedules workout reminder successfully', async () => {
      const workoutData = {
        id: 'workout-1',
        name: 'Push Day',
        scheduledTime: new Date(Date.now() + 3600000), // 1 hour from now
      };

      const mockNotificationId = 'notification-1';
      mockNotifications.scheduleNotificationAsync.mockResolvedValue(mockNotificationId);

      const result = await notificationService.scheduleWorkoutReminder(workoutData);

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: 'Workout Reminder',
          body: `Time for your ${workoutData.name} workout!`,
          data: { 
            type: 'workout_reminder',
            workoutId: workoutData.id,
          },
          sound: 'default',
        },
        trigger: workoutData.scheduledTime,
      });

      expect(result).toBe(mockNotificationId);
    });
  });

  describe('scheduleRestReminder', () => {
    it('schedules rest reminder successfully', async () => {
      const restTime = 60; // 60 seconds
      const mockNotificationId = 'notification-1';
      mockNotifications.scheduleNotificationAsync.mockResolvedValue(mockNotificationId);

      const result = await notificationService.scheduleRestReminder(restTime);

      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: 'Rest Complete',
          body: 'Time for your next set!',
          data: { type: 'rest_reminder' },
          sound: 'default',
        },
        trigger: { seconds: restTime },
      });

      expect(result).toBe(mockNotificationId);
    });
  });

  describe('setupNotificationHandler', () => {
    it('sets up notification handler correctly', () => {
      notificationService.setupNotificationHandler();

      expect(mockNotifications.setNotificationHandler).toHaveBeenCalledWith({
        handleNotification: expect.any(Function),
      });
    });
  });

  describe('addNotificationListener', () => {
    it('adds notification received listener', () => {
      const mockCallback = jest.fn();
      const mockSubscription = { remove: jest.fn() };
      mockNotifications.addNotificationReceivedListener.mockReturnValue(mockSubscription);

      const result = notificationService.addNotificationReceivedListener(mockCallback);

      expect(mockNotifications.addNotificationReceivedListener).toHaveBeenCalledWith(mockCallback);
      expect(result).toBe(mockSubscription);
    });

    it('adds notification response listener', () => {
      const mockCallback = jest.fn();
      const mockSubscription = { remove: jest.fn() };
      mockNotifications.addNotificationResponseReceivedListener.mockReturnValue(mockSubscription);

      const result = notificationService.addNotificationResponseReceivedListener(mockCallback);

      expect(mockNotifications.addNotificationResponseReceivedListener).toHaveBeenCalledWith(mockCallback);
      expect(result).toBe(mockSubscription);
    });
  });

  describe('getScheduledNotifications', () => {
    it('gets scheduled notifications successfully', async () => {
      const mockNotifications = [
        {
          identifier: 'notification-1',
          content: { title: 'Test', body: 'Test body' },
          trigger: { type: 'timeInterval', seconds: 60 },
        },
      ];

      mockNotifications.getAllScheduledNotificationsAsync.mockResolvedValue(mockNotifications);

      const result = await notificationService.getScheduledNotifications();

      expect(mockNotifications.getAllScheduledNotificationsAsync).toHaveBeenCalled();
      expect(result).toEqual(mockNotifications);
    });
  });

  describe('dismissNotification', () => {
    it('dismisses notification successfully', async () => {
      const notificationId = 'notification-1';
      mockNotifications.dismissNotificationAsync.mockResolvedValue();

      await notificationService.dismissNotification(notificationId);

      expect(mockNotifications.dismissNotificationAsync).toHaveBeenCalledWith(notificationId);
    });
  });

  describe('storePushToken', () => {
    it('stores push token in AsyncStorage', async () => {
      const token = 'test-token';
      mockAsyncStorage.setItem.mockResolvedValue();

      await notificationService.storePushToken(token);

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('push_token', token);
    });
  });

  describe('getStoredPushToken', () => {
    it('retrieves stored push token', async () => {
      const token = 'test-token';
      mockAsyncStorage.getItem.mockResolvedValue(token);

      const result = await notificationService.getStoredPushToken();

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('push_token');
      expect(result).toBe(token);
    });

    it('returns null when no token stored', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const result = await notificationService.getStoredPushToken();

      expect(result).toBeNull();
    });
  });
});
