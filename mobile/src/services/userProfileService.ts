import { supabase } from '@/lib/supabase';
import { uploadImage } from './storageService';

export interface UserProfile {
  id: string;
  user_id: string;
  username: string;
  display_name: string;
  bio: string;
  location: string;
  website: string;
  date_of_birth: string;
  gender: string;
  fitness_level: string;
  primary_goals: string[];
  preferred_workout_types: string[];
  available_equipment: string[];
  profile_visibility: string;
  show_workout_stats: boolean;
  show_progress_photos: boolean;
  show_achievements: boolean;
  allow_friend_requests: boolean;
  followers_count: number;
  following_count: number;
  workouts_shared_count: number;
  achievements_count: number;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface ProfileUpdateData {
  username?: string;
  display_name?: string;
  bio?: string;
  location?: string;
  website?: string;
  date_of_birth?: string;
  gender?: string;
  fitness_level?: string;
  primary_goals?: string[];
  preferred_workout_types?: string[];
  available_equipment?: string[];
  profile_visibility?: string;
  show_workout_stats?: boolean;
  show_progress_photos?: boolean;
  show_achievements?: boolean;
  allow_friend_requests?: boolean;
}

class UserProfileService {
  async getProfile(userId?: string): Promise<UserProfile> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;

    if (!targetUserId) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', targetUserId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Profile doesn't exist, create one
        return this.createProfile(targetUserId);
      }
      throw error;
    }

    return data;
  }

  async createProfile(userId: string): Promise<UserProfile> {
    const { data: authUser } = await supabase.auth.getUser();
    
    const profileData = {
      user_id: userId,
      username: null,
      display_name: authUser.user?.user_metadata?.full_name || 
                   authUser.user?.user_metadata?.first_name || 
                   'User',
      bio: '',
      location: '',
      website: '',
      fitness_level: 'beginner',
      primary_goals: [],
      preferred_workout_types: [],
      available_equipment: [],
      profile_visibility: 'public',
      show_workout_stats: true,
      show_progress_photos: false,
      show_achievements: true,
      allow_friend_requests: true,
    };

    const { data, error } = await supabase
      .from('user_profiles')
      .insert(profileData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  async updateProfile(updates: ProfileUpdateData): Promise<UserProfile> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if username is available (if being updated)
    if (updates.username) {
      const { data: existingUser } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('username', updates.username)
        .neq('user_id', user.id)
        .single();

      if (existingUser) {
        throw new Error('Username is already taken');
      }
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  async uploadAvatar(imageUri: string): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      // Upload image to storage
      const fileName = `avatar_${user.id}_${Date.now()}.jpg`;
      const avatarUrl = await uploadImage(imageUri, 'avatars', fileName);

      // Update profile with new avatar URL
      const { error } = await supabase
        .from('user_profiles')
        .update({ 
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      return avatarUrl;
    } catch (error) {
      console.error('Failed to upload avatar:', error);
      throw error;
    }
  }

  async searchProfiles(query: string, limit = 20): Promise<UserProfile[]> {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
      .eq('profile_visibility', 'public')
      .limit(limit);

    if (error) {
      throw error;
    }

    return data || [];
  }

  async getProfilesByIds(userIds: string[]): Promise<UserProfile[]> {
    if (userIds.length === 0) return [];

    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .in('user_id', userIds);

    if (error) {
      throw error;
    }

    return data || [];
  }

  async checkUsernameAvailability(username: string): Promise<boolean> {
    const { data } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('username', username)
      .single();

    return !data; // Available if no data found
  }

  async getProfileStats(userId: string): Promise<{
    totalWorkouts: number;
    totalWeight: number;
    streakDays: number;
    averageRating: number;
  }> {
    // Get workout stats
    const { data: workoutStats } = await supabase
      .from('workout_logs')
      .select('duration, rating')
      .eq('user_id', userId);

    // Get total weight lifted
    const { data: weightStats } = await supabase
      .from('workout_logs')
      .select('exercises')
      .eq('user_id', userId);

    let totalWeight = 0;
    let totalRating = 0;
    let ratingCount = 0;

    workoutStats?.forEach(workout => {
      if (workout.rating) {
        totalRating += workout.rating;
        ratingCount++;
      }
    });

    weightStats?.forEach(log => {
      if (log.exercises) {
        log.exercises.forEach((exercise: any) => {
          exercise.sets?.forEach((set: any) => {
            if (set.weight) {
              totalWeight += set.weight * (set.reps || 1);
            }
          });
        });
      }
    });

    // Calculate streak (simplified - would need more complex logic for actual streaks)
    const streakDays = await this.calculateWorkoutStreak(userId);

    return {
      totalWorkouts: workoutStats?.length || 0,
      totalWeight,
      streakDays,
      averageRating: ratingCount > 0 ? totalRating / ratingCount : 0,
    };
  }

  private async calculateWorkoutStreak(userId: string): Promise<number> {
    const { data } = await supabase
      .from('workout_logs')
      .select('completed_at')
      .eq('user_id', userId)
      .order('completed_at', { ascending: false })
      .limit(30);

    if (!data || data.length === 0) return 0;

    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    for (const log of data) {
      const logDate = new Date(log.completed_at);
      logDate.setHours(0, 0, 0, 0);

      const daysDiff = Math.floor((currentDate.getTime() - logDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff === streak) {
        streak++;
      } else if (daysDiff > streak) {
        break;
      }
    }

    return streak;
  }

  async deleteProfile(): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('user_profiles')
      .delete()
      .eq('user_id', user.id);

    if (error) {
      throw error;
    }
  }

  async getPrivacySettings(userId?: string): Promise<{
    profile_visibility: string;
    show_workout_stats: boolean;
    show_progress_photos: boolean;
    show_achievements: boolean;
    allow_friend_requests: boolean;
  }> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;

    if (!targetUserId) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .select('profile_visibility, show_workout_stats, show_progress_photos, show_achievements, allow_friend_requests')
      .eq('user_id', targetUserId)
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  async updatePrivacySettings(settings: {
    profile_visibility?: string;
    show_workout_stats?: boolean;
    show_progress_photos?: boolean;
    show_achievements?: boolean;
    allow_friend_requests?: boolean;
  }): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('user_profiles')
      .update({
        ...settings,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', user.id);

    if (error) {
      throw error;
    }
  }
}

export const userProfileService = new UserProfileService();
