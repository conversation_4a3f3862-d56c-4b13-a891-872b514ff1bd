import { supabase } from '@/lib/supabase';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  stripe_price_id: string;
  stripe_product_id: string;
  price_monthly?: number;
  price_yearly?: number;
  currency: string;
  billing_interval: 'month' | 'year';
  trial_period_days: number;
  features: Record<string, any>;
  limits: Record<string, any>;
  is_active: boolean;
  is_popular: boolean;
  sort_order: number;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id?: string;
  stripe_customer_id?: string;
  stripe_subscription_id?: string;
  stripe_payment_method_id?: string;
  status: string;
  current_period_start?: string;
  current_period_end?: string;
  trial_start?: string;
  trial_end?: string;
  canceled_at?: string;
  ended_at?: string;
  amount?: number;
  currency: string;
  billing_interval?: string;
  metadata?: Record<string, any>;
  plan?: SubscriptionPlan;
}

export interface PaymentMethod {
  id: string;
  user_id: string;
  stripe_payment_method_id: string;
  type: string;
  brand?: string;
  last4?: string;
  exp_month?: number;
  exp_year?: number;
  is_default: boolean;
  is_active: boolean;
}

export interface BillingHistory {
  id: string;
  user_id: string;
  subscription_id?: string;
  stripe_invoice_id?: string;
  invoice_number?: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  status: string;
  invoice_date: string;
  due_date?: string;
  paid_at?: string;
  description?: string;
  invoice_url?: string;
  pdf_url?: string;
  line_items: any[];
}

class StripeService {
  private baseUrl = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000';

  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) throw error;
    return data || [];
  }

  async getUserSubscription(): Promise<UserSubscription | null> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_subscriptions')
      .select(`
        *,
        plan:subscription_plans(*)
      `)
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data;
  }

  async createCustomer(email: string, name?: string): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/create-customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({ email, name }),
    });

    if (!response.ok) {
      throw new Error('Failed to create customer');
    }

    const { customer_id } = await response.json();
    return customer_id;
  }

  async createSubscription(
    priceId: string,
    paymentMethodId?: string,
    promoCode?: string
  ): Promise<{ subscription_id: string; client_secret?: string }> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/create-subscription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        price_id: priceId,
        payment_method_id: paymentMethodId,
        promo_code: promoCode,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create subscription');
    }

    return response.json();
  }

  async updateSubscription(
    subscriptionId: string,
    priceId: string
  ): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/update-subscription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        subscription_id: subscriptionId,
        price_id: priceId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update subscription');
    }
  }

  async cancelSubscription(subscriptionId: string, immediately = false): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/cancel-subscription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        subscription_id: subscriptionId,
        immediately,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to cancel subscription');
    }
  }

  async resumeSubscription(subscriptionId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/resume-subscription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        subscription_id: subscriptionId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to resume subscription');
    }
  }

  async getPaymentMethods(): Promise<PaymentMethod[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_payment_methods')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('is_default', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async addPaymentMethod(paymentMethodId: string): Promise<PaymentMethod> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/add-payment-method`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        payment_method_id: paymentMethodId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add payment method');
    }

    return response.json();
  }

  async removePaymentMethod(paymentMethodId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/remove-payment-method`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        payment_method_id: paymentMethodId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to remove payment method');
    }
  }

  async setDefaultPaymentMethod(paymentMethodId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/set-default-payment-method`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        payment_method_id: paymentMethodId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to set default payment method');
    }
  }

  async getBillingHistory(limit = 50): Promise<BillingHistory[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('billing_history')
      .select('*')
      .eq('user_id', user.id)
      .order('invoice_date', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async downloadInvoice(invoiceId: string): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/download-invoice`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        invoice_id: invoiceId,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to download invoice');
    }

    const { download_url } = await response.json();
    return download_url;
  }

  async validatePromoCode(code: string): Promise<{
    valid: boolean;
    discount_type?: string;
    discount_value?: number;
    currency?: string;
  }> {
    const { data, error } = await supabase
      .from('promotional_codes')
      .select('*')
      .eq('code', code.toUpperCase())
      .eq('is_active', true)
      .single();

    if (error || !data) {
      return { valid: false };
    }

    // Check validity dates
    const now = new Date();
    const validFrom = new Date(data.valid_from);
    const validUntil = data.valid_until ? new Date(data.valid_until) : null;

    if (now < validFrom || (validUntil && now > validUntil)) {
      return { valid: false };
    }

    // Check usage limits
    if (data.max_redemptions && data.times_redeemed >= data.max_redemptions) {
      return { valid: false };
    }

    return {
      valid: true,
      discount_type: data.discount_type,
      discount_value: data.discount_value,
      currency: data.currency,
    };
  }

  async hasPremiumAccess(): Promise<boolean> {
    const { data, error } = await supabase.rpc('has_premium_access');
    
    if (error) {
      console.error('Error checking premium access:', error);
      return false;
    }

    return data || false;
  }

  async getSubscriptionFeatures(): Promise<Record<string, any>> {
    const subscription = await this.getUserSubscription();
    
    if (!subscription || !subscription.plan) {
      return {}; // Free tier features
    }

    return subscription.plan.features || {};
  }

  async getSubscriptionLimits(): Promise<Record<string, any>> {
    const subscription = await this.getUserSubscription();
    
    if (!subscription || !subscription.plan) {
      return {
        // Free tier limits
        max_programs: 1,
        max_workouts_per_month: 10,
        max_progress_photos: 5,
        advanced_analytics: false,
        priority_support: false,
      };
    }

    return subscription.plan.limits || {};
  }

  async createPaymentIntent(amount: number, currency = 'usd'): Promise<{
    client_secret: string;
    payment_intent_id: string;
  }> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await fetch(`${this.baseUrl}/api/stripe/create-payment-intent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
      },
      body: JSON.stringify({
        amount,
        currency,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create payment intent');
    }

    return response.json();
  }
}

export const stripeService = new StripeService();
