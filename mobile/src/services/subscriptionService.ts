import { supabase } from '@/lib/supabase';
import { stripeService } from './stripeService';

export interface PlanInfo {
  plan_name: string;
  plan_tier: 'free' | 'basic' | 'pro' | 'elite';
  features: Record<string, boolean>;
  limits: Record<string, number>;
  status: string;
  trial_end?: string;
  current_period_end?: string;
}

export interface FeatureAccess {
  hasAccess: boolean;
  planRequired?: string;
  upgradeMessage?: string;
}

export interface UsageLimit {
  withinLimit: boolean;
  currentUsage: number;
  limit: number;
  limitName: string;
  upgradeMessage?: string;
}

class SubscriptionService {
  private planCache: PlanInfo | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  async getUserPlanInfo(forceRefresh = false): Promise<PlanInfo> {
    const now = Date.now();
    
    if (!forceRefresh && this.planCache && now < this.cacheExpiry) {
      return this.planCache;
    }

    const { data, error } = await supabase.rpc('get_user_plan_info');
    
    if (error) {
      console.error('Error getting plan info:', error);
      // Return free plan as fallback
      return {
        plan_name: 'Free',
        plan_tier: 'free',
        features: {},
        limits: {
          max_programs: 1,
          max_workouts_per_month: 10,
          max_progress_photos: 5,
        },
        status: 'free',
      };
    }

    this.planCache = data[0] || {
      plan_name: 'Free',
      plan_tier: 'free',
      features: {},
      limits: {},
      status: 'free',
    };
    
    this.cacheExpiry = now + this.CACHE_DURATION;
    return this.planCache;
  }

  async checkFeatureAccess(featureName: string): Promise<FeatureAccess> {
    try {
      const { data, error } = await supabase.rpc('check_feature_access', {
        p_feature_name: featureName,
      });

      if (error) {
        console.error('Error checking feature access:', error);
        return { hasAccess: false };
      }

      if (data) {
        return { hasAccess: true };
      }

      // Get required plan for this feature
      const requiredPlan = this.getRequiredPlanForFeature(featureName);
      const upgradeMessage = this.getUpgradeMessage(featureName, requiredPlan);

      return {
        hasAccess: false,
        planRequired: requiredPlan,
        upgradeMessage,
      };
    } catch (error) {
      console.error('Error checking feature access:', error);
      return { hasAccess: false };
    }
  }

  async checkUsageLimit(limitName: string, currentUsage: number): Promise<UsageLimit> {
    try {
      const { data, error } = await supabase.rpc('check_usage_limit', {
        p_limit_name: limitName,
        p_current_usage: currentUsage,
      });

      if (error) {
        console.error('Error checking usage limit:', error);
        return {
          withinLimit: false,
          currentUsage,
          limit: 0,
          limitName,
        };
      }

      const planInfo = await this.getUserPlanInfo();
      const limit = planInfo.limits[limitName] || 0;

      if (data) {
        return {
          withinLimit: true,
          currentUsage,
          limit,
          limitName,
        };
      }

      const requiredPlan = this.getRequiredPlanForLimit(limitName, currentUsage);
      const upgradeMessage = this.getUpgradeMessageForLimit(limitName, requiredPlan);

      return {
        withinLimit: false,
        currentUsage,
        limit,
        limitName,
        upgradeMessage,
      };
    } catch (error) {
      console.error('Error checking usage limit:', error);
      return {
        withinLimit: false,
        currentUsage,
        limit: 0,
        limitName,
      };
    }
  }

  async getCurrentUsage(limitName: string): Promise<number> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return 0;

    try {
      switch (limitName) {
        case 'max_programs':
          const { count: programCount } = await supabase
            .from('workout_programs')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', user.id);
          return programCount || 0;

        case 'max_workouts_per_month':
          const startOfMonth = new Date();
          startOfMonth.setDate(1);
          startOfMonth.setHours(0, 0, 0, 0);
          
          const { count: workoutCount } = await supabase
            .from('workout_logs')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', user.id)
            .gte('completed_at', startOfMonth.toISOString());
          return workoutCount || 0;

        case 'max_progress_photos':
          const { count: photoCount } = await supabase
            .from('progress_tracking')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', user.id)
            .not('photos', 'is', null);
          return photoCount || 0;

        default:
          return 0;
      }
    } catch (error) {
      console.error(`Error getting current usage for ${limitName}:`, error);
      return 0;
    }
  }

  async canCreateProgram(): Promise<FeatureAccess & UsageLimit> {
    const [featureAccess, currentUsage] = await Promise.all([
      this.checkFeatureAccess('custom_programs'),
      this.getCurrentUsage('max_programs'),
    ]);

    if (!featureAccess.hasAccess) {
      return {
        ...featureAccess,
        withinLimit: false,
        currentUsage,
        limit: 0,
        limitName: 'max_programs',
      };
    }

    const usageLimit = await this.checkUsageLimit('max_programs', currentUsage);
    
    return {
      ...featureAccess,
      ...usageLimit,
    };
  }

  async canLogWorkout(): Promise<UsageLimit> {
    const currentUsage = await this.getCurrentUsage('max_workouts_per_month');
    return this.checkUsageLimit('max_workouts_per_month', currentUsage);
  }

  async canUploadProgressPhoto(): Promise<UsageLimit> {
    const currentUsage = await this.getCurrentUsage('max_progress_photos');
    return this.checkUsageLimit('max_progress_photos', currentUsage);
  }

  async canAccessAdvancedAnalytics(): Promise<FeatureAccess> {
    return this.checkFeatureAccess('advanced_analytics');
  }

  async canAccessVideoWorkouts(): Promise<FeatureAccess> {
    return this.checkFeatureAccess('video_workouts');
  }

  async canAccessOfflineMode(): Promise<FeatureAccess> {
    return this.checkFeatureAccess('offline_access');
  }

  async canAccessAICoaching(): Promise<FeatureAccess> {
    return this.checkFeatureAccess('ai_coaching');
  }

  async canAccessNutritionPlans(): Promise<FeatureAccess> {
    return this.checkFeatureAccess('nutrition_plans');
  }

  async canAccessPrioritySupport(): Promise<FeatureAccess> {
    return this.checkFeatureAccess('priority_support');
  }

  private getRequiredPlanForFeature(featureName: string): string {
    const featurePlanMap: Record<string, string> = {
      'unlimited_workouts': 'Basic',
      'advanced_analytics': 'Pro',
      'priority_support': 'Pro',
      'custom_programs': 'Pro',
      'nutrition_plans': 'Pro',
      'video_workouts': 'Basic',
      'offline_access': 'Basic',
      'ai_coaching': 'Pro',
      'personal_coaching': 'Elite',
      'white_label': 'Elite',
    };

    return featurePlanMap[featureName] || 'Pro';
  }

  private getRequiredPlanForLimit(limitName: string, currentUsage: number): string {
    if (limitName === 'max_programs' && currentUsage >= 1) {
      return 'Basic';
    }
    if (limitName === 'max_workouts_per_month' && currentUsage >= 10) {
      return 'Basic';
    }
    if (limitName === 'max_progress_photos' && currentUsage >= 5) {
      return 'Basic';
    }
    return 'Pro';
  }

  private getUpgradeMessage(featureName: string, requiredPlan: string): string {
    const featureMessages: Record<string, string> = {
      'unlimited_workouts': `Upgrade to ${requiredPlan} for unlimited workouts`,
      'advanced_analytics': `Upgrade to ${requiredPlan} for advanced analytics and insights`,
      'priority_support': `Upgrade to ${requiredPlan} for priority customer support`,
      'custom_programs': `Upgrade to ${requiredPlan} to create custom workout programs`,
      'nutrition_plans': `Upgrade to ${requiredPlan} for personalized nutrition plans`,
      'video_workouts': `Upgrade to ${requiredPlan} for video workout demonstrations`,
      'offline_access': `Upgrade to ${requiredPlan} for offline workout access`,
      'ai_coaching': `Upgrade to ${requiredPlan} for AI-powered coaching insights`,
      'personal_coaching': `Upgrade to ${requiredPlan} for personal coaching features`,
      'white_label': `Upgrade to ${requiredPlan} for white-label customization`,
    };

    return featureMessages[featureName] || `Upgrade to ${requiredPlan} to access this feature`;
  }

  private getUpgradeMessageForLimit(limitName: string, requiredPlan: string): string {
    const limitMessages: Record<string, string> = {
      'max_programs': `Upgrade to ${requiredPlan} to create more workout programs`,
      'max_workouts_per_month': `Upgrade to ${requiredPlan} for unlimited monthly workouts`,
      'max_progress_photos': `Upgrade to ${requiredPlan} to upload more progress photos`,
    };

    return limitMessages[limitName] || `Upgrade to ${requiredPlan} to increase your limits`;
  }

  async getUpgradeRecommendation(): Promise<{
    recommendedPlan: string;
    reasons: string[];
    savings?: string;
  }> {
    const planInfo = await this.getUserPlanInfo();
    
    if (planInfo.plan_tier === 'elite') {
      return {
        recommendedPlan: 'Elite',
        reasons: ['You already have our highest tier plan!'],
      };
    }

    const reasons: string[] = [];
    let recommendedPlan = 'Basic';

    // Check current usage patterns
    const [programUsage, workoutUsage, photoUsage] = await Promise.all([
      this.getCurrentUsage('max_programs'),
      this.getCurrentUsage('max_workouts_per_month'),
      this.getCurrentUsage('max_progress_photos'),
    ]);

    if (planInfo.plan_tier === 'free') {
      if (programUsage >= 1) {
        reasons.push('Create more custom workout programs');
      }
      if (workoutUsage >= 8) {
        reasons.push('Unlimited monthly workouts');
      }
      if (photoUsage >= 3) {
        reasons.push('Upload more progress photos');
      }
      
      reasons.push('Access to video workout demonstrations');
      reasons.push('Offline workout access');
      
      if (reasons.length >= 3) {
        recommendedPlan = 'Pro';
        reasons.push('Advanced analytics and AI coaching');
        reasons.push('Priority customer support');
      }
    } else if (planInfo.plan_tier === 'basic') {
      recommendedPlan = 'Pro';
      reasons.push('Advanced analytics and insights');
      reasons.push('AI-powered coaching recommendations');
      reasons.push('Custom nutrition plans');
      reasons.push('Priority customer support');
    } else if (planInfo.plan_tier === 'pro') {
      recommendedPlan = 'Elite';
      reasons.push('Personal coaching features');
      reasons.push('White-label customization');
      reasons.push('Unlimited everything');
    }

    // Calculate potential savings for yearly plans
    const plans = await stripeService.getSubscriptionPlans();
    const monthlyPlan = plans.find(p => p.name === recommendedPlan && p.billing_interval === 'month');
    const yearlyPlan = plans.find(p => p.name === recommendedPlan && p.billing_interval === 'year');
    
    let savings: string | undefined;
    if (monthlyPlan?.price_monthly && yearlyPlan?.price_yearly) {
      const monthlyCost = monthlyPlan.price_monthly * 12;
      const yearlyCost = yearlyPlan.price_yearly;
      const savingsAmount = monthlyCost - yearlyCost;
      const savingsPercent = Math.round((savingsAmount / monthlyCost) * 100);
      savings = `Save $${savingsAmount} (${savingsPercent}%) with yearly billing`;
    }

    return {
      recommendedPlan,
      reasons,
      savings,
    };
  }

  clearCache(): void {
    this.planCache = null;
    this.cacheExpiry = 0;
  }

  async refreshPlanInfo(): Promise<PlanInfo> {
    return this.getUserPlanInfo(true);
  }
}

export const subscriptionService = new SubscriptionService();
