import { supabase } from '@/lib/supabase';

export interface PromoCode {
  id: string;
  code: string;
  stripe_coupon_id?: string;
  stripe_promotion_code_id?: string;
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number;
  currency: string;
  max_redemptions?: number;
  times_redeemed: number;
  valid_from: string;
  valid_until?: string;
  is_active: boolean;
  applicable_plans: string[];
  first_time_customers_only: boolean;
  minimum_amount?: number;
  description?: string;
  metadata?: Record<string, any>;
}

export interface PromoCodeUsage {
  id: string;
  promo_code_id: string;
  user_id: string;
  subscription_id?: string;
  discount_amount: number;
  currency: string;
  used_at: string;
}

export interface PromoCodeValidation {
  valid: boolean;
  code?: PromoCode;
  error?: string;
  discount_amount?: number;
  final_amount?: number;
}

class PromoCodeService {
  async validatePromoCode(
    code: string,
    planId?: string,
    amount?: number
  ): Promise<PromoCodeValidation> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      // Get promo code details
      const { data: promoCode, error } = await supabase
        .from('promotional_codes')
        .select('*')
        .eq('code', code.toUpperCase())
        .eq('is_active', true)
        .single();

      if (error || !promoCode) {
        return {
          valid: false,
          error: 'Invalid or expired promo code',
        };
      }

      // Check validity dates
      const now = new Date();
      const validFrom = new Date(promoCode.valid_from);
      const validUntil = promoCode.valid_until ? new Date(promoCode.valid_until) : null;

      if (now < validFrom) {
        return {
          valid: false,
          error: 'Promo code is not yet active',
        };
      }

      if (validUntil && now > validUntil) {
        return {
          valid: false,
          error: 'Promo code has expired',
        };
      }

      // Check usage limits
      if (promoCode.max_redemptions && promoCode.times_redeemed >= promoCode.max_redemptions) {
        return {
          valid: false,
          error: 'Promo code has reached its usage limit',
        };
      }

      // Check if user has already used this code
      if (user) {
        const { data: existingUsage } = await supabase
          .from('promo_code_usage')
          .select('id')
          .eq('promo_code_id', promoCode.id)
          .eq('user_id', user.id)
          .single();

        if (existingUsage) {
          return {
            valid: false,
            error: 'You have already used this promo code',
          };
        }
      }

      // Check first-time customer restriction
      if (promoCode.first_time_customers_only && user) {
        const { data: existingSubscription } = await supabase
          .from('user_subscriptions')
          .select('id')
          .eq('user_id', user.id)
          .single();

        if (existingSubscription) {
          return {
            valid: false,
            error: 'This promo code is only valid for new customers',
          };
        }
      }

      // Check plan restrictions
      if (planId && promoCode.applicable_plans.length > 0) {
        if (!promoCode.applicable_plans.includes(planId)) {
          return {
            valid: false,
            error: 'This promo code is not valid for the selected plan',
          };
        }
      }

      // Check minimum amount
      if (amount && promoCode.minimum_amount && amount < promoCode.minimum_amount) {
        return {
          valid: false,
          error: `Minimum order amount of ${promoCode.currency.toUpperCase()} ${promoCode.minimum_amount} required`,
        };
      }

      // Calculate discount
      let discountAmount = 0;
      let finalAmount = amount || 0;

      if (amount) {
        if (promoCode.discount_type === 'percentage') {
          discountAmount = (amount * promoCode.discount_value) / 100;
        } else {
          discountAmount = promoCode.discount_value;
        }
        
        finalAmount = Math.max(0, amount - discountAmount);
      }

      return {
        valid: true,
        code: promoCode,
        discount_amount: discountAmount,
        final_amount: finalAmount,
      };
    } catch (error) {
      console.error('Error validating promo code:', error);
      return {
        valid: false,
        error: 'Failed to validate promo code',
      };
    }
  }

  async applyPromoCode(
    promoCodeId: string,
    subscriptionId: string,
    discountAmount: number
  ): Promise<PromoCodeUsage> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Record promo code usage
    const { data: usage, error } = await supabase
      .from('promo_code_usage')
      .insert({
        promo_code_id: promoCodeId,
        user_id: user.id,
        subscription_id: subscriptionId,
        discount_amount: discountAmount,
        currency: 'USD', // Default currency
      })
      .select()
      .single();

    if (error) throw error;

    // Increment usage count
    await supabase.rpc('increment_promo_usage', {
      promo_code_id: promoCodeId,
    });

    return usage;
  }

  async getUserPromoUsage(): Promise<PromoCodeUsage[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('promo_code_usage')
      .select(`
        *,
        promo_code:promotional_codes(code, description)
      `)
      .eq('user_id', user.id)
      .order('used_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async getActivePromoCodes(): Promise<PromoCode[]> {
    const { data, error } = await supabase
      .from('promotional_codes')
      .select('*')
      .eq('is_active', true)
      .gte('valid_until', new Date().toISOString())
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async createPromoCode(promoData: {
    code: string;
    discount_type: 'percentage' | 'fixed_amount';
    discount_value: number;
    currency?: string;
    max_redemptions?: number;
    valid_from?: string;
    valid_until?: string;
    applicable_plans?: string[];
    first_time_customers_only?: boolean;
    minimum_amount?: number;
    description?: string;
  }): Promise<PromoCode> {
    const { data, error } = await supabase
      .from('promotional_codes')
      .insert({
        code: promoData.code.toUpperCase(),
        discount_type: promoData.discount_type,
        discount_value: promoData.discount_value,
        currency: promoData.currency || 'USD',
        max_redemptions: promoData.max_redemptions,
        valid_from: promoData.valid_from || new Date().toISOString(),
        valid_until: promoData.valid_until,
        applicable_plans: promoData.applicable_plans || [],
        first_time_customers_only: promoData.first_time_customers_only || false,
        minimum_amount: promoData.minimum_amount,
        description: promoData.description,
        is_active: true,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async updatePromoCode(
    promoCodeId: string,
    updates: Partial<PromoCode>
  ): Promise<PromoCode> {
    const { data, error } = await supabase
      .from('promotional_codes')
      .update(updates)
      .eq('id', promoCodeId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deactivatePromoCode(promoCodeId: string): Promise<void> {
    const { error } = await supabase
      .from('promotional_codes')
      .update({ is_active: false })
      .eq('id', promoCodeId);

    if (error) throw error;
  }

  async getPromoCodeStats(promoCodeId: string): Promise<{
    total_usage: number;
    total_discount_amount: number;
    unique_users: number;
    conversion_rate: number;
  }> {
    const { data, error } = await supabase.rpc('get_promo_code_stats', {
      promo_code_id: promoCodeId,
    });

    if (error) throw error;
    return data || {
      total_usage: 0,
      total_discount_amount: 0,
      unique_users: 0,
      conversion_rate: 0,
    };
  }

  async generatePromoCode(
    prefix: string = 'PROMO',
    length: number = 8
  ): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    
    for (let i = 0; i < length - prefix.length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    // Check if code already exists
    const { data: existing } = await supabase
      .from('promotional_codes')
      .select('id')
      .eq('code', result)
      .single();

    if (existing) {
      // Generate a new code if this one exists
      return this.generatePromoCode(prefix, length);
    }

    return result;
  }

  async getBulkPromoCodes(codes: string[]): Promise<PromoCode[]> {
    const { data, error } = await supabase
      .from('promotional_codes')
      .select('*')
      .in('code', codes.map(c => c.toUpperCase()))
      .eq('is_active', true);

    if (error) throw error;
    return data || [];
  }

  async getPromoCodeByStripeId(stripeCouponId: string): Promise<PromoCode | null> {
    const { data, error } = await supabase
      .from('promotional_codes')
      .select('*')
      .eq('stripe_coupon_id', stripeCouponId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data;
  }

  async syncWithStripe(promoCodeId: string, stripeData: {
    coupon_id: string;
    promotion_code_id?: string;
  }): Promise<void> {
    const { error } = await supabase
      .from('promotional_codes')
      .update({
        stripe_coupon_id: stripeData.coupon_id,
        stripe_promotion_code_id: stripeData.promotion_code_id,
      })
      .eq('id', promoCodeId);

    if (error) throw error;
  }
}

export const promoCodeService = new PromoCodeService();
