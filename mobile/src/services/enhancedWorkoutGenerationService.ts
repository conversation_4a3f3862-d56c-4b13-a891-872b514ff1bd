import { supabase } from '@/lib/supabase';

export interface UserProfile {
  user_id: string;
  age: number;
  experience_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  primary_goals: string[];
  available_equipment: string[];
  workout_frequency: number;
  session_duration: number;
  injury_history: string[];
  preferences: {
    exercise_types: string[];
    intensity_preference: 'low' | 'moderate' | 'high';
    variety_preference: 'low' | 'moderate' | 'high';
  };
}

export interface WorkoutContext {
  current_fatigue_level: number;
  recent_workout_history: any[];
  recovery_status: 'poor' | 'fair' | 'good' | 'excellent';
  available_time: number;
  equipment_available: string[];
  environmental_factors: {
    location: 'home' | 'gym' | 'outdoor';
    temperature: number;
    noise_level: 'quiet' | 'moderate' | 'loud';
  };
}

export interface ExerciseRecommendation {
  exercise_id: string;
  exercise_name: string;
  muscle_groups: string[];
  equipment_needed: string[];
  difficulty_level: number;
  recommendation_score: number;
  reasoning: string[];
  adaptations: {
    beginner: { sets: number; reps: string; weight: string };
    intermediate: { sets: number; reps: string; weight: string };
    advanced: { sets: number; reps: string; weight: string };
  };
}

export interface GeneratedWorkout {
  id: string;
  name: string;
  description: string;
  estimated_duration: number;
  difficulty_level: number;
  primary_focus: string[];
  exercises: {
    exercise_id: string;
    exercise_name: string;
    sets: number;
    reps: string;
    weight: string;
    rest_time: number;
    notes: string;
    progression_notes: string;
  }[];
  warm_up: {
    exercise_name: string;
    duration: number;
    instructions: string;
  }[];
  cool_down: {
    exercise_name: string;
    duration: number;
    instructions: string;
  }[];
  ai_insights: {
    personalization_factors: string[];
    adaptation_reasoning: string[];
    progression_plan: string;
    recovery_considerations: string[];
  };
}

class EnhancedWorkoutGenerationService {
  private readonly EXERCISE_SELECTION_WEIGHTS = {
    user_preference: 0.25,
    goal_alignment: 0.30,
    recovery_status: 0.20,
    progression_potential: 0.15,
    variety_factor: 0.10,
  };

  async generatePersonalizedWorkout(
    userProfile: UserProfile,
    workoutContext: WorkoutContext,
    specificGoals?: string[]
  ): Promise<GeneratedWorkout> {
    try {
      // Analyze user's current state and needs
      const userAnalysis = await this.analyzeUserState(userProfile, workoutContext);
      
      // Get exercise recommendations based on advanced algorithms
      const exerciseRecommendations = await this.getSmartExerciseRecommendations(
        userProfile,
        workoutContext,
        userAnalysis,
        specificGoals
      );

      // Generate workout structure with optimal sequencing
      const workoutStructure = this.optimizeWorkoutSequence(
        exerciseRecommendations,
        userProfile,
        workoutContext
      );

      // Apply personalization and adaptations
      const personalizedWorkout = this.applyPersonalizationLayer(
        workoutStructure,
        userProfile,
        userAnalysis
      );

      // Generate warm-up and cool-down
      const warmUp = this.generateSmartWarmUp(personalizedWorkout, userProfile);
      const coolDown = this.generateSmartCoolDown(personalizedWorkout, userProfile);

      // Create AI insights and explanations
      const aiInsights = this.generateAIInsights(
        personalizedWorkout,
        userProfile,
        workoutContext,
        userAnalysis
      );

      return {
        id: `workout_${Date.now()}`,
        name: this.generateWorkoutName(personalizedWorkout, userProfile),
        description: this.generateWorkoutDescription(personalizedWorkout, userProfile),
        estimated_duration: this.calculateEstimatedDuration(personalizedWorkout, warmUp, coolDown),
        difficulty_level: this.calculateDifficultyLevel(personalizedWorkout, userProfile),
        primary_focus: this.identifyPrimaryFocus(personalizedWorkout),
        exercises: personalizedWorkout,
        warm_up: warmUp,
        cool_down: coolDown,
        ai_insights: aiInsights,
      };
    } catch (error) {
      console.error('Error generating personalized workout:', error);
      throw error;
    }
  }

  private async analyzeUserState(userProfile: UserProfile, workoutContext: WorkoutContext) {
    // Advanced user state analysis
    const fatigueScore = this.calculateFatigueScore(workoutContext);
    const readinessScore = this.calculateReadinessScore(userProfile, workoutContext);
    const adaptationNeeds = this.identifyAdaptationNeeds(userProfile, workoutContext);
    const progressionOpportunities = await this.identifyProgressionOpportunities(userProfile);

    return {
      fatigue_score: fatigueScore,
      readiness_score: readinessScore,
      adaptation_needs: adaptationNeeds,
      progression_opportunities: progressionOpportunities,
      recommended_intensity: this.calculateOptimalIntensity(fatigueScore, readinessScore),
      volume_adjustment: this.calculateVolumeAdjustment(workoutContext, userProfile),
    };
  }

  private async getSmartExerciseRecommendations(
    userProfile: UserProfile,
    workoutContext: WorkoutContext,
    userAnalysis: any,
    specificGoals?: string[]
  ): Promise<ExerciseRecommendation[]> {
    // Get all available exercises
    const { data: exercises } = await supabase
      .from('exercises')
      .select('*')
      .in('equipment_needed', userProfile.available_equipment);

    if (!exercises) return [];

    // Score each exercise using advanced algorithms
    const scoredExercises = exercises.map(exercise => {
      const score = this.calculateExerciseScore(
        exercise,
        userProfile,
        workoutContext,
        userAnalysis,
        specificGoals
      );

      return {
        exercise_id: exercise.id,
        exercise_name: exercise.name,
        muscle_groups: exercise.muscle_groups,
        equipment_needed: exercise.equipment_needed,
        difficulty_level: exercise.difficulty_level,
        recommendation_score: score.total_score,
        reasoning: score.reasoning,
        adaptations: this.generateExerciseAdaptations(exercise, userProfile),
      };
    });

    // Select optimal exercises using advanced selection algorithm
    return this.selectOptimalExercises(scoredExercises, userProfile, workoutContext);
  }

  private calculateExerciseScore(
    exercise: any,
    userProfile: UserProfile,
    workoutContext: WorkoutContext,
    userAnalysis: any,
    specificGoals?: string[]
  ) {
    const scores = {
      user_preference: 0,
      goal_alignment: 0,
      recovery_status: 0,
      progression_potential: 0,
      variety_factor: 0,
    };

    const reasoning: string[] = [];

    // User preference scoring
    if (userProfile.preferences.exercise_types.includes(exercise.category)) {
      scores.user_preference = 0.8;
      reasoning.push('Matches user exercise preferences');
    }

    // Goal alignment scoring
    const goalAlignment = this.calculateGoalAlignment(exercise, userProfile.primary_goals, specificGoals);
    scores.goal_alignment = goalAlignment.score;
    if (goalAlignment.score > 0.7) {
      reasoning.push(`Excellent for ${goalAlignment.primary_goal}`);
    }

    // Recovery status consideration
    const recoveryScore = this.calculateRecoveryCompatibility(exercise, workoutContext.recovery_status);
    scores.recovery_status = recoveryScore;
    if (recoveryScore < 0.5) {
      reasoning.push('Adjusted for current recovery status');
    }

    // Progression potential
    const progressionScore = this.calculateProgressionPotential(exercise, userProfile, userAnalysis);
    scores.progression_potential = progressionScore;
    if (progressionScore > 0.8) {
      reasoning.push('High progression potential');
    }

    // Variety factor
    const varietyScore = this.calculateVarietyScore(exercise, workoutContext.recent_workout_history);
    scores.variety_factor = varietyScore;

    // Calculate weighted total score
    const totalScore = Object.entries(scores).reduce((total, [key, score]) => {
      return total + (score * this.EXERCISE_SELECTION_WEIGHTS[key as keyof typeof this.EXERCISE_SELECTION_WEIGHTS]);
    }, 0);

    return {
      total_score: totalScore,
      component_scores: scores,
      reasoning,
    };
  }

  private calculateGoalAlignment(exercise: any, primaryGoals: string[], specificGoals?: string[]) {
    const allGoals = [...primaryGoals, ...(specificGoals || [])];
    let maxAlignment = 0;
    let primaryGoal = '';

    for (const goal of allGoals) {
      const alignment = this.getExerciseGoalAlignment(exercise, goal);
      if (alignment > maxAlignment) {
        maxAlignment = alignment;
        primaryGoal = goal;
      }
    }

    return { score: maxAlignment, primary_goal: primaryGoal };
  }

  private getExerciseGoalAlignment(exercise: any, goal: string): number {
    const alignmentMap: Record<string, Record<string, number>> = {
      'strength': {
        'compound': 0.9,
        'isolation': 0.6,
        'bodyweight': 0.7,
      },
      'muscle_gain': {
        'compound': 0.8,
        'isolation': 0.9,
        'bodyweight': 0.6,
      },
      'weight_loss': {
        'compound': 0.9,
        'cardio': 0.9,
        'circuit': 0.8,
      },
      'endurance': {
        'cardio': 0.9,
        'circuit': 0.8,
        'bodyweight': 0.7,
      },
    };

    return alignmentMap[goal]?.[exercise.category] || 0.5;
  }

  private calculateRecoveryCompatibility(exercise: any, recoveryStatus: string): number {
    const recoveryMultipliers = {
      'poor': 0.3,
      'fair': 0.6,
      'good': 0.8,
      'excellent': 1.0,
    };

    const baseCompatibility = recoveryMultipliers[recoveryStatus] || 0.5;
    
    // Adjust based on exercise intensity
    if (exercise.intensity_level === 'high' && recoveryStatus === 'poor') {
      return baseCompatibility * 0.5;
    }

    return baseCompatibility;
  }

  private calculateProgressionPotential(exercise: any, userProfile: UserProfile, userAnalysis: any): number {
    // Check if exercise aligns with progression opportunities
    const hasProgressionOpportunity = userAnalysis.progression_opportunities.some(
      (opp: any) => opp.exercise_category === exercise.category
    );

    if (hasProgressionOpportunity) {
      return 0.9;
    }

    // Check experience level compatibility
    const experienceLevels = ['beginner', 'intermediate', 'advanced', 'expert'];
    const userLevelIndex = experienceLevels.indexOf(userProfile.experience_level);
    const exerciseLevelIndex = exercise.difficulty_level - 1;

    // Optimal if exercise is at user level or slightly above
    const levelDifference = Math.abs(userLevelIndex - exerciseLevelIndex);
    
    if (levelDifference === 0) return 0.8;
    if (levelDifference === 1) return 0.6;
    return 0.4;
  }

  private calculateVarietyScore(exercise: any, recentWorkouts: any[]): number {
    if (!recentWorkouts || recentWorkouts.length === 0) return 1.0;

    // Check how recently this exercise was performed
    const recentExercises = recentWorkouts.flatMap(w => w.exercises || []);
    const lastPerformed = recentExercises.findIndex(e => e.exercise_id === exercise.id);

    if (lastPerformed === -1) return 1.0; // Never performed recently
    if (lastPerformed > 5) return 0.8; // Performed more than 5 workouts ago
    if (lastPerformed > 2) return 0.6; // Performed 3-5 workouts ago
    return 0.3; // Performed very recently
  }

  private selectOptimalExercises(
    scoredExercises: ExerciseRecommendation[],
    userProfile: UserProfile,
    workoutContext: WorkoutContext
  ): ExerciseRecommendation[] {
    // Sort by score
    const sortedExercises = scoredExercises.sort((a, b) => b.recommendation_score - a.recommendation_score);

    // Apply constraints and selection logic
    const selectedExercises: ExerciseRecommendation[] = [];
    const muscleGroupCoverage = new Set<string>();
    const equipmentUsed = new Set<string>();

    // Target number of exercises based on available time and experience
    const targetExerciseCount = this.calculateTargetExerciseCount(userProfile, workoutContext);

    for (const exercise of sortedExercises) {
      if (selectedExercises.length >= targetExerciseCount) break;

      // Check muscle group balance
      const newMuscleGroups = exercise.muscle_groups.filter(mg => !muscleGroupCoverage.has(mg));
      
      // Prefer exercises that target new muscle groups
      if (newMuscleGroups.length > 0 || selectedExercises.length < 3) {
        selectedExercises.push(exercise);
        exercise.muscle_groups.forEach(mg => muscleGroupCoverage.add(mg));
        exercise.equipment_needed.forEach(eq => equipmentUsed.add(eq));
      }
    }

    return selectedExercises;
  }

  private optimizeWorkoutSequence(
    exercises: ExerciseRecommendation[],
    userProfile: UserProfile,
    workoutContext: WorkoutContext
  ) {
    // Apply optimal exercise sequencing principles
    const sequencedExercises = [...exercises];

    // Sort by optimal order: compound -> isolation, large muscle -> small muscle
    sequencedExercises.sort((a, b) => {
      // Compound exercises first
      const aCompound = a.muscle_groups.length > 2 ? 1 : 0;
      const bCompound = b.muscle_groups.length > 2 ? 1 : 0;
      if (aCompound !== bCompound) return bCompound - aCompound;

      // Higher difficulty exercises first (when fresh)
      return b.difficulty_level - a.difficulty_level;
    });

    // Convert to workout format with sets, reps, etc.
    return sequencedExercises.map(exercise => {
      const adaptation = this.selectAdaptation(exercise, userProfile);
      
      return {
        exercise_id: exercise.exercise_id,
        exercise_name: exercise.exercise_name,
        sets: adaptation.sets,
        reps: adaptation.reps,
        weight: adaptation.weight,
        rest_time: this.calculateOptimalRestTime(exercise, userProfile),
        notes: this.generateExerciseNotes(exercise, userProfile),
        progression_notes: this.generateProgressionNotes(exercise, userProfile),
      };
    });
  }

  private applyPersonalizationLayer(
    workoutStructure: any[],
    userProfile: UserProfile,
    userAnalysis: any
  ) {
    return workoutStructure.map(exercise => {
      // Apply volume adjustments
      const volumeAdjustment = userAnalysis.volume_adjustment;
      const adjustedSets = Math.max(1, Math.round(exercise.sets * volumeAdjustment));

      // Apply intensity adjustments
      const intensityAdjustment = userAnalysis.recommended_intensity;
      let adjustedWeight = exercise.weight;
      
      if (intensityAdjustment < 0.8) {
        adjustedWeight = exercise.weight.replace(/(\d+)/, (match: string) => {
          const weight = parseInt(match);
          return Math.max(1, Math.round(weight * intensityAdjustment)).toString();
        });
      }

      return {
        ...exercise,
        sets: adjustedSets,
        weight: adjustedWeight,
        rest_time: this.adjustRestTime(exercise.rest_time, userAnalysis),
      };
    });
  }

  private generateSmartWarmUp(workout: any[], userProfile: UserProfile) {
    const warmUpExercises = [
      {
        exercise_name: 'Dynamic Stretching',
        duration: 5,
        instructions: 'Perform dynamic stretches targeting major muscle groups',
      },
      {
        exercise_name: 'Light Cardio',
        duration: 5,
        instructions: 'Light cardio to increase heart rate and blood flow',
      },
    ];

    // Add specific warm-up based on workout focus
    const primaryMuscleGroups = new Set(workout.flatMap(ex => ex.muscle_groups || []));
    
    if (primaryMuscleGroups.has('legs')) {
      warmUpExercises.push({
        exercise_name: 'Leg Swings',
        duration: 2,
        instructions: 'Forward and lateral leg swings to prepare hip joints',
      });
    }

    if (primaryMuscleGroups.has('shoulders')) {
      warmUpExercises.push({
        exercise_name: 'Arm Circles',
        duration: 2,
        instructions: 'Small to large arm circles to prepare shoulder joints',
      });
    }

    return warmUpExercises;
  }

  private generateSmartCoolDown(workout: any[], userProfile: UserProfile) {
    return [
      {
        exercise_name: 'Light Walking',
        duration: 5,
        instructions: 'Gentle walking to gradually lower heart rate',
      },
      {
        exercise_name: 'Static Stretching',
        duration: 10,
        instructions: 'Hold stretches for 30 seconds each, focusing on worked muscle groups',
      },
      {
        exercise_name: 'Deep Breathing',
        duration: 3,
        instructions: 'Deep breathing exercises to promote recovery',
      },
    ];
  }

  private generateAIInsights(
    workout: any[],
    userProfile: UserProfile,
    workoutContext: WorkoutContext,
    userAnalysis: any
  ) {
    return {
      personalization_factors: [
        `Adjusted for ${userProfile.experience_level} experience level`,
        `Optimized for ${userProfile.primary_goals.join(', ')} goals`,
        `Tailored to ${workoutContext.available_time} minute session`,
      ],
      adaptation_reasoning: [
        `Volume adjusted by ${Math.round((userAnalysis.volume_adjustment - 1) * 100)}% based on recovery status`,
        `Intensity set to ${Math.round(userAnalysis.recommended_intensity * 100)}% based on fatigue analysis`,
      ],
      progression_plan: 'Focus on progressive overload by increasing weight by 2.5-5% when you can complete all sets with 2+ reps in reserve',
      recovery_considerations: [
        'Allow 48-72 hours between training the same muscle groups',
        'Monitor fatigue levels and adjust intensity accordingly',
        'Prioritize sleep and nutrition for optimal recovery',
      ],
    };
  }

  // Helper methods
  private calculateFatigueScore(workoutContext: WorkoutContext): number {
    return workoutContext.current_fatigue_level;
  }

  private calculateReadinessScore(userProfile: UserProfile, workoutContext: WorkoutContext): number {
    const recoveryMap = { 'poor': 0.3, 'fair': 0.6, 'good': 0.8, 'excellent': 1.0 };
    return recoveryMap[workoutContext.recovery_status] || 0.5;
  }

  private identifyAdaptationNeeds(userProfile: UserProfile, workoutContext: WorkoutContext): string[] {
    const needs = [];
    
    if (workoutContext.recovery_status === 'poor') {
      needs.push('reduced_intensity');
    }
    
    if (workoutContext.available_time < userProfile.session_duration) {
      needs.push('time_efficient');
    }
    
    return needs;
  }

  private async identifyProgressionOpportunities(userProfile: UserProfile) {
    // This would analyze user's workout history to identify progression opportunities
    return [
      { exercise_category: 'compound', opportunity: 'strength_increase' },
      { exercise_category: 'isolation', opportunity: 'volume_increase' },
    ];
  }

  private calculateOptimalIntensity(fatigueScore: number, readinessScore: number): number {
    return Math.max(0.5, Math.min(1.0, readinessScore - (fatigueScore * 0.3)));
  }

  private calculateVolumeAdjustment(workoutContext: WorkoutContext, userProfile: UserProfile): number {
    let adjustment = 1.0;
    
    if (workoutContext.recovery_status === 'poor') adjustment *= 0.7;
    if (workoutContext.recovery_status === 'fair') adjustment *= 0.85;
    if (workoutContext.available_time < userProfile.session_duration) {
      adjustment *= workoutContext.available_time / userProfile.session_duration;
    }
    
    return Math.max(0.5, adjustment);
  }

  private generateExerciseAdaptations(exercise: any, userProfile: UserProfile) {
    return {
      beginner: { sets: 2, reps: '8-12', weight: 'Light' },
      intermediate: { sets: 3, reps: '8-12', weight: 'Moderate' },
      advanced: { sets: 4, reps: '6-10', weight: 'Heavy' },
    };
  }

  private calculateTargetExerciseCount(userProfile: UserProfile, workoutContext: WorkoutContext): number {
    const baseCount = Math.floor(workoutContext.available_time / 8); // ~8 minutes per exercise
    return Math.max(3, Math.min(8, baseCount));
  }

  private selectAdaptation(exercise: ExerciseRecommendation, userProfile: UserProfile) {
    return exercise.adaptations[userProfile.experience_level] || exercise.adaptations.intermediate;
  }

  private calculateOptimalRestTime(exercise: ExerciseRecommendation, userProfile: UserProfile): number {
    const baseRestTime = exercise.difficulty_level * 30; // 30 seconds per difficulty level
    const experienceMultiplier = { 'beginner': 1.2, 'intermediate': 1.0, 'advanced': 0.8, 'expert': 0.7 };
    return Math.round(baseRestTime * (experienceMultiplier[userProfile.experience_level] || 1.0));
  }

  private generateExerciseNotes(exercise: ExerciseRecommendation, userProfile: UserProfile): string {
    return `Focus on proper form. ${exercise.reasoning.join('. ')}.`;
  }

  private generateProgressionNotes(exercise: ExerciseRecommendation, userProfile: UserProfile): string {
    return 'Increase weight when you can complete all sets with 2+ reps in reserve';
  }

  private adjustRestTime(baseRestTime: number, userAnalysis: any): number {
    if (userAnalysis.fatigue_score > 0.7) return Math.round(baseRestTime * 1.2);
    if (userAnalysis.readiness_score < 0.5) return Math.round(baseRestTime * 1.1);
    return baseRestTime;
  }

  private generateWorkoutName(workout: any[], userProfile: UserProfile): string {
    const primaryFocus = this.identifyPrimaryFocus(workout);
    return `${primaryFocus.join(' & ')} Focus`;
  }

  private generateWorkoutDescription(workout: any[], userProfile: UserProfile): string {
    const exerciseCount = workout.length;
    const primaryFocus = this.identifyPrimaryFocus(workout);
    return `${exerciseCount}-exercise ${primaryFocus.join(' and ')} workout tailored for ${userProfile.experience_level} level`;
  }

  private calculateEstimatedDuration(workout: any[], warmUp: any[], coolDown: any[]): number {
    const workoutTime = workout.reduce((total, ex) => total + (ex.sets * 2) + ex.rest_time, 0) / 60;
    const warmUpTime = warmUp.reduce((total, ex) => total + ex.duration, 0);
    const coolDownTime = coolDown.reduce((total, ex) => total + ex.duration, 0);
    return Math.round(workoutTime + warmUpTime + coolDownTime);
  }

  private calculateDifficultyLevel(workout: any[], userProfile: UserProfile): number {
    const avgDifficulty = workout.reduce((sum, ex) => sum + (ex.difficulty_level || 3), 0) / workout.length;
    return Math.round(avgDifficulty);
  }

  private identifyPrimaryFocus(workout: any[]): string[] {
    const muscleGroups = workout.flatMap(ex => ex.muscle_groups || []);
    const groupCounts = muscleGroups.reduce((counts, group) => {
      counts[group] = (counts[group] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return Object.entries(groupCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 2)
      .map(([group]) => group);
  }
}

export const enhancedWorkoutGenerationService = new EnhancedWorkoutGenerationService();
