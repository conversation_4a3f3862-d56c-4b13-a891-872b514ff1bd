import { supabase } from '@/lib/supabase';

export interface AdvancedAnalytics {
  performance: {
    strength_score: number;
    endurance_score: number;
    consistency_score: number;
    overall_score: number;
  };
  trends: {
    date: string;
    volume: number;
    intensity: number;
    frequency: number;
  }[];
  predictions: {
    next_month_volume: number;
    strength_gain_prediction: number;
    goal_completion_probability: number;
  };
  comparisons: {
    peer_percentile: number;
    improvement_rate: number;
    consistency_rank: number;
  };
  insights: {
    type: 'strength' | 'endurance' | 'consistency' | 'recovery';
    title: string;
    description: string;
    recommendation: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

export interface DetailedMetrics {
  strength_metrics: {
    one_rep_max: Record<string, number>;
    volume_progression: { date: string; volume: number }[];
    strength_balance: Record<string, number>;
    plateau_analysis: {
      exercise: string;
      plateau_duration: number;
      suggested_intervention: string;
    }[];
  };
  endurance_metrics: {
    cardiovascular_fitness: number;
    muscular_endurance: number;
    recovery_rate: number;
    endurance_trends: { date: string; score: number }[];
  };
  body_composition: {
    muscle_mass_trend: { date: string; mass: number }[];
    body_fat_trend: { date: string; percentage: number }[];
    measurements: Record<string, { date: string; value: number }[]>;
  };
  recovery_metrics: {
    sleep_quality: number;
    stress_levels: number;
    recovery_score: number;
    hrv_trend: { date: string; hrv: number }[];
  };
}

export interface ComparisonData {
  user_percentiles: {
    strength: number;
    endurance: number;
    consistency: number;
    overall: number;
  };
  peer_group: {
    age_group: string;
    experience_level: string;
    similar_goals: string[];
  };
  benchmarks: {
    metric: string;
    user_value: number;
    peer_average: number;
    top_10_percent: number;
  }[];
}

class AnalyticsService {
  async getAdvancedAnalytics(period: '1month' | '3months' | '6months' | '1year'): Promise<AdvancedAnalytics> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '3months':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      const [performance, trends, predictions, comparisons, insights] = await Promise.all([
        this.calculatePerformanceScores(user.id, startDate, endDate),
        this.getTrends(user.id, startDate, endDate),
        this.getPredictions(user.id),
        this.getComparisons(user.id),
        this.generateInsights(user.id, startDate, endDate),
      ]);

      return {
        performance,
        trends,
        predictions,
        comparisons,
        insights,
      };
    } catch (error) {
      console.error('Error getting advanced analytics:', error);
      throw error;
    }
  }

  private async calculatePerformanceScores(userId: string, startDate: Date, endDate: Date) {
    // Get workout data for the period
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select(`
        *,
        exercise_logs(*)
      `)
      .eq('user_id', userId)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString());

    if (!workouts || workouts.length === 0) {
      return {
        strength_score: 0,
        endurance_score: 0,
        consistency_score: 0,
        overall_score: 0,
      };
    }

    // Calculate strength score based on progressive overload
    const strengthScore = this.calculateStrengthScore(workouts);
    
    // Calculate endurance score based on workout duration and intensity
    const enduranceScore = this.calculateEnduranceScore(workouts);
    
    // Calculate consistency score based on workout frequency
    const consistencyScore = this.calculateConsistencyScore(workouts, startDate, endDate);
    
    // Calculate overall score as weighted average
    const overallScore = (strengthScore * 0.4 + enduranceScore * 0.3 + consistencyScore * 0.3);

    return {
      strength_score: Math.round(strengthScore),
      endurance_score: Math.round(enduranceScore),
      consistency_score: Math.round(consistencyScore),
      overall_score: Math.round(overallScore),
    };
  }

  private calculateStrengthScore(workouts: any[]): number {
    let totalVolumeIncrease = 0;
    let progressiveWorkouts = 0;

    // Group workouts by exercise to track progression
    const exerciseProgression = new Map<string, number[]>();

    workouts.forEach(workout => {
      workout.exercise_logs?.forEach((exercise: any) => {
        const exerciseName = exercise.exercise_name;
        const totalVolume = exercise.sets?.reduce((sum: number, set: any) => {
          return sum + (set.weight || 0) * (set.reps || 0);
        }, 0) || 0;

        if (!exerciseProgression.has(exerciseName)) {
          exerciseProgression.set(exerciseName, []);
        }
        exerciseProgression.get(exerciseName)!.push(totalVolume);
      });
    });

    // Calculate progression for each exercise
    exerciseProgression.forEach((volumes) => {
      if (volumes.length >= 2) {
        const firstHalf = volumes.slice(0, Math.floor(volumes.length / 2));
        const secondHalf = volumes.slice(Math.floor(volumes.length / 2));
        
        const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
        
        if (secondAvg > firstAvg) {
          totalVolumeIncrease += (secondAvg - firstAvg) / firstAvg;
          progressiveWorkouts++;
        }
      }
    });

    // Score based on percentage of exercises showing progression
    const progressionRate = progressiveWorkouts / exerciseProgression.size;
    return Math.min(100, progressionRate * 100 + (totalVolumeIncrease * 20));
  }

  private calculateEnduranceScore(workouts: any[]): number {
    const avgDuration = workouts.reduce((sum, w) => sum + (w.duration || 0), 0) / workouts.length;
    const avgRating = workouts.reduce((sum, w) => sum + (w.rating || 3), 0) / workouts.length;
    
    // Score based on workout duration and perceived exertion
    const durationScore = Math.min(100, (avgDuration / 60) * 20); // 60 min = 100 points
    const intensityScore = (avgRating / 5) * 100;
    
    return (durationScore + intensityScore) / 2;
  }

  private calculateConsistencyScore(workouts: any[], startDate: Date, endDate: Date): number {
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const workoutDays = workouts.length;
    
    // Target: 3-4 workouts per week
    const targetWorkouts = Math.floor(totalDays / 7) * 3.5;
    const consistencyRate = Math.min(1, workoutDays / targetWorkouts);
    
    return consistencyRate * 100;
  }

  private async getTrends(userId: string, startDate: Date, endDate: Date) {
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select(`
        completed_at,
        duration,
        exercise_logs(*)
      `)
      .eq('user_id', userId)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString())
      .order('completed_at');

    if (!workouts) return [];

    // Group workouts by week
    const weeklyData = new Map<string, { volume: number; intensity: number; frequency: number }>();

    workouts.forEach(workout => {
      const weekStart = new Date(workout.completed_at);
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());
      const weekKey = weekStart.toISOString().split('T')[0];

      if (!weeklyData.has(weekKey)) {
        weeklyData.set(weekKey, { volume: 0, intensity: 0, frequency: 0 });
      }

      const weekData = weeklyData.get(weekKey)!;
      
      // Calculate volume
      const workoutVolume = workout.exercise_logs?.reduce((sum: number, exercise: any) => {
        return sum + (exercise.sets?.reduce((setSum: number, set: any) => {
          return setSum + (set.weight || 0) * (set.reps || 0);
        }, 0) || 0);
      }, 0) || 0;

      weekData.volume += workoutVolume;
      weekData.intensity += workout.duration || 0;
      weekData.frequency += 1;
    });

    return Array.from(weeklyData.entries()).map(([date, data]) => ({
      date,
      volume: data.volume / 1000, // Convert to thousands
      intensity: data.intensity / data.frequency || 0, // Average duration
      frequency: data.frequency,
    }));
  }

  private async getPredictions(userId: string) {
    // Get recent workout data for predictions
    const { data: recentWorkouts } = await supabase
      .from('workout_logs')
      .select(`
        *,
        exercise_logs(*)
      `)
      .eq('user_id', userId)
      .order('completed_at', { ascending: false })
      .limit(20);

    if (!recentWorkouts || recentWorkouts.length < 5) {
      return {
        next_month_volume: 0,
        strength_gain_prediction: 0,
        goal_completion_probability: 0.5,
      };
    }

    // Simple linear regression for volume prediction
    const volumes = recentWorkouts.map(w => {
      return w.exercise_logs?.reduce((sum: number, exercise: any) => {
        return sum + (exercise.sets?.reduce((setSum: number, set: any) => {
          return setSum + (set.weight || 0) * (set.reps || 0);
        }, 0) || 0);
      }, 0) || 0;
    });

    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
    const trend = volumes.length > 1 ? (volumes[0] - volumes[volumes.length - 1]) / volumes.length : 0;

    return {
      next_month_volume: (avgVolume + trend * 4) / 1000, // 4 weeks projection
      strength_gain_prediction: Math.max(0, Math.min(20, trend / avgVolume * 100)),
      goal_completion_probability: Math.min(1, Math.max(0.1, 0.7 + trend / avgVolume)),
    };
  }

  private async getComparisons(userId: string) {
    // Get user profile for peer comparison
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('age, experience_level, primary_goals')
      .eq('user_id', userId)
      .single();

    // In a real implementation, this would compare against actual peer data
    // For now, return mock data based on user performance
    return {
      peer_percentile: Math.floor(Math.random() * 40) + 60, // 60-100th percentile
      improvement_rate: Math.floor(Math.random() * 30) + 15, // 15-45% improvement
      consistency_rank: Math.floor(Math.random() * 20) + 5, // Top 5-25
    };
  }

  private async generateInsights(userId: string, startDate: Date, endDate: Date) {
    const insights = [];

    // Get recent workout data for analysis
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select(`
        *,
        exercise_logs(*)
      `)
      .eq('user_id', userId)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString());

    if (!workouts || workouts.length === 0) {
      return [];
    }

    // Analyze workout frequency
    const avgWorkoutsPerWeek = workouts.length / (Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7)));
    
    if (avgWorkoutsPerWeek < 2) {
      insights.push({
        type: 'consistency' as const,
        title: 'Increase Workout Frequency',
        description: `You're averaging ${avgWorkoutsPerWeek.toFixed(1)} workouts per week. Consistency is key for progress.`,
        recommendation: 'Aim for at least 3 workouts per week. Start with shorter sessions if time is limited.',
        priority: 'high' as const,
      });
    }

    // Analyze workout duration
    const avgDuration = workouts.reduce((sum, w) => sum + (w.duration || 0), 0) / workouts.length;
    
    if (avgDuration > 90) {
      insights.push({
        type: 'recovery' as const,
        title: 'Consider Shorter Workouts',
        description: `Your average workout duration is ${Math.round(avgDuration)} minutes, which may lead to fatigue.`,
        recommendation: 'Try splitting longer workouts into focused sessions or reduce rest times between sets.',
        priority: 'medium' as const,
      });
    }

    // Analyze exercise variety
    const uniqueExercises = new Set();
    workouts.forEach(workout => {
      workout.exercise_logs?.forEach((exercise: any) => {
        uniqueExercises.add(exercise.exercise_name);
      });
    });

    if (uniqueExercises.size < 10) {
      insights.push({
        type: 'strength' as const,
        title: 'Add Exercise Variety',
        description: `You're using ${uniqueExercises.size} different exercises. More variety can prevent plateaus.`,
        recommendation: 'Incorporate new exercises targeting the same muscle groups to challenge your body differently.',
        priority: 'low' as const,
      });
    }

    return insights;
  }

  async getDetailedMetrics(userId: string): Promise<DetailedMetrics> {
    // This would be a comprehensive analysis of all user metrics
    // For now, return mock data structure
    return {
      strength_metrics: {
        one_rep_max: {
          'Bench Press': 185,
          'Squat': 225,
          'Deadlift': 275,
        },
        volume_progression: [],
        strength_balance: {
          'Push/Pull Ratio': 0.85,
          'Quad/Hamstring Ratio': 1.2,
        },
        plateau_analysis: [],
      },
      endurance_metrics: {
        cardiovascular_fitness: 75,
        muscular_endurance: 68,
        recovery_rate: 82,
        endurance_trends: [],
      },
      body_composition: {
        muscle_mass_trend: [],
        body_fat_trend: [],
        measurements: {},
      },
      recovery_metrics: {
        sleep_quality: 7.5,
        stress_levels: 3.2,
        recovery_score: 78,
        hrv_trend: [],
      },
    };
  }

  async getComparisonData(userId: string): Promise<ComparisonData> {
    // Get user profile for peer grouping
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('age, experience_level, primary_goals')
      .eq('user_id', userId)
      .single();

    return {
      user_percentiles: {
        strength: 75,
        endurance: 68,
        consistency: 82,
        overall: 75,
      },
      peer_group: {
        age_group: '25-35',
        experience_level: profile?.experience_level || 'intermediate',
        similar_goals: profile?.primary_goals || ['strength', 'muscle_gain'],
      },
      benchmarks: [
        {
          metric: 'Weekly Workout Frequency',
          user_value: 4.2,
          peer_average: 3.8,
          top_10_percent: 5.5,
        },
        {
          metric: 'Average Workout Duration (min)',
          user_value: 65,
          peer_average: 58,
          top_10_percent: 75,
        },
      ],
    };
  }

  async exportAnalyticsData(userId: string, format: 'csv' | 'json' = 'csv') {
    const analytics = await this.getAdvancedAnalytics(userId, '1year');
    const detailed = await this.getDetailedMetrics(userId);
    
    const exportData = {
      analytics,
      detailed,
      exported_at: new Date().toISOString(),
      user_id: userId,
    };

    if (format === 'csv') {
      return this.convertToCSV(exportData);
    }
    
    return JSON.stringify(exportData, null, 2);
  }

  private convertToCSV(data: any): string {
    // Convert analytics data to CSV format
    const lines = [];
    
    lines.push('Analytics Export');
    lines.push(`Exported at: ${data.exported_at}`);
    lines.push('');
    
    // Performance scores
    lines.push('Performance Scores');
    lines.push('Metric,Score');
    Object.entries(data.analytics.performance).forEach(([key, value]) => {
      lines.push(`${key},${value}`);
    });
    
    return lines.join('\n');
  }
}

export const analyticsService = new AnalyticsService();
