import { supabase } from '@/lib/supabase';
import { socialService } from './socialService';

export interface Challenge {
  id: string;
  creator_id: string;
  title: string;
  description: string;
  challenge_type: 'distance' | 'reps' | 'weight' | 'duration' | 'frequency';
  target_value: number;
  target_unit: string;
  start_date: string;
  end_date: string;
  visibility: 'public' | 'friends' | 'private';
  max_participants?: number;
  requires_approval: boolean;
  participants_count: number;
  completed_count: number;
  reward_type?: string;
  reward_data?: any;
  created_at: string;
  updated_at: string;
  creator_profile?: any;
  user_participation?: ChallengeParticipation;
}

export interface ChallengeParticipation {
  id: string;
  challenge_id: string;
  user_id: string;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  current_progress: number;
  completion_date?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateChallengeData {
  title: string;
  description: string;
  challenge_type: 'distance' | 'reps' | 'weight' | 'duration' | 'frequency';
  target_value: number;
  target_unit: string;
  start_date: string;
  end_date: string;
  visibility: 'public' | 'friends' | 'private';
  max_participants?: number;
  requires_approval?: boolean;
  reward_type?: string;
  reward_data?: any;
}

class ChallengeService {
  async createChallenge(data: CreateChallengeData): Promise<Challenge> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const challengeData = {
      creator_id: user.id,
      title: data.title,
      description: data.description,
      challenge_type: data.challenge_type,
      target_value: data.target_value,
      target_unit: data.target_unit,
      start_date: data.start_date,
      end_date: data.end_date,
      visibility: data.visibility,
      max_participants: data.max_participants,
      requires_approval: data.requires_approval || false,
      reward_type: data.reward_type,
      reward_data: data.reward_data,
    };

    const { data: challenge, error } = await supabase
      .from('fitness_challenges')
      .insert(challengeData)
      .select(`
        *,
        creator_profile:user_profiles(*)
      `)
      .single();

    if (error) throw error;

    // Create activity feed entry
    await socialService.createActivityFeedEntry(
      user.id,
      'challenge_created',
      `Created a new challenge: ${data.title}`,
      data.description,
      undefined,
      undefined,
      challenge.id
    );

    return challenge;
  }

  async getPublicChallenges(limit = 20): Promise<Challenge[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    let query = supabase
      .from('fitness_challenges')
      .select(`
        *,
        creator_profile:user_profiles!fitness_challenges_creator_id_fkey(*)
      `)
      .eq('visibility', 'public')
      .order('created_at', { ascending: false })
      .limit(limit);

    const { data: challenges, error } = await query;
    if (error) throw error;

    // Get user participation status if authenticated
    if (user && challenges) {
      const challengeIds = challenges.map(c => c.id);
      const { data: participations } = await supabase
        .from('challenge_participants')
        .select('*')
        .eq('user_id', user.id)
        .in('challenge_id', challengeIds);

      const participationMap = new Map(
        participations?.map(p => [p.challenge_id, p]) || []
      );

      return challenges.map(challenge => ({
        ...challenge,
        user_participation: participationMap.get(challenge.id),
      }));
    }

    return challenges || [];
  }

  async getUserJoinedChallenges(): Promise<Challenge[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('challenge_participants')
      .select(`
        *,
        challenge:fitness_challenges(
          *,
          creator_profile:user_profiles(*)
        )
      `)
      .eq('user_id', user.id)
      .in('status', ['active', 'completed'])
      .order('created_at', { ascending: false });

    if (error) throw error;

    return (data || []).map(participation => ({
      ...participation.challenge,
      user_participation: {
        id: participation.id,
        challenge_id: participation.challenge_id,
        user_id: participation.user_id,
        status: participation.status,
        current_progress: participation.current_progress,
        completion_date: participation.completion_date,
        created_at: participation.created_at,
        updated_at: participation.updated_at,
      },
    }));
  }

  async getUserCreatedChallenges(): Promise<Challenge[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('fitness_challenges')
      .select(`
        *,
        creator_profile:user_profiles(*)
      `)
      .eq('creator_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  async getChallengeById(challengeId: string): Promise<Challenge | null> {
    const { data: { user } } = await supabase.auth.getUser();
    
    const { data: challenge, error } = await supabase
      .from('fitness_challenges')
      .select(`
        *,
        creator_profile:user_profiles(*)
      `)
      .eq('id', challengeId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    // Get user participation if authenticated
    if (user) {
      const { data: participation } = await supabase
        .from('challenge_participants')
        .select('*')
        .eq('challenge_id', challengeId)
        .eq('user_id', user.id)
        .single();

      if (participation) {
        challenge.user_participation = participation;
      }
    }

    return challenge;
  }

  async joinChallenge(challengeId: string): Promise<ChallengeParticipation> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if already participating
    const { data: existingParticipation } = await supabase
      .from('challenge_participants')
      .select('*')
      .eq('challenge_id', challengeId)
      .eq('user_id', user.id)
      .single();

    if (existingParticipation) {
      throw new Error('Already participating in this challenge');
    }

    // Get challenge details
    const challenge = await this.getChallengeById(challengeId);
    if (!challenge) {
      throw new Error('Challenge not found');
    }

    // Check if challenge is full
    if (challenge.max_participants && challenge.participants_count >= challenge.max_participants) {
      throw new Error('Challenge is full');
    }

    // Check if challenge has started
    const now = new Date();
    const endDate = new Date(challenge.end_date);
    if (now > endDate) {
      throw new Error('Challenge has ended');
    }

    const participationData = {
      challenge_id: challengeId,
      user_id: user.id,
      status: challenge.requires_approval ? 'pending' : 'active',
      current_progress: 0,
    };

    const { data: participation, error } = await supabase
      .from('challenge_participants')
      .insert(participationData)
      .select()
      .single();

    if (error) throw error;

    // Create activity feed entry
    await socialService.createActivityFeedEntry(
      user.id,
      'challenge_joined',
      `Joined challenge: ${challenge.title}`,
      undefined,
      undefined,
      undefined,
      challengeId
    );

    return participation;
  }

  async leaveChallenge(challengeId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('challenge_participants')
      .delete()
      .eq('challenge_id', challengeId)
      .eq('user_id', user.id);

    if (error) throw error;
  }

  async updateProgress(challengeId: string, progress: number): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get current participation
    const { data: participation } = await supabase
      .from('challenge_participants')
      .select('*, challenge:fitness_challenges(*)')
      .eq('challenge_id', challengeId)
      .eq('user_id', user.id)
      .single();

    if (!participation) {
      throw new Error('Not participating in this challenge');
    }

    const newProgress = Math.max(participation.current_progress, progress);
    const isCompleted = newProgress >= participation.challenge.target_value;

    const updateData: any = {
      current_progress: newProgress,
      updated_at: new Date().toISOString(),
    };

    if (isCompleted && participation.status !== 'completed') {
      updateData.status = 'completed';
      updateData.completion_date = new Date().toISOString();
    }

    const { error } = await supabase
      .from('challenge_participants')
      .update(updateData)
      .eq('challenge_id', challengeId)
      .eq('user_id', user.id);

    if (error) throw error;

    // Create activity feed entry if completed
    if (isCompleted && participation.status !== 'completed') {
      await socialService.createActivityFeedEntry(
        user.id,
        'challenge_completed',
        `Completed challenge: ${participation.challenge.title}`,
        undefined,
        undefined,
        undefined,
        challengeId
      );

      // Award achievement
      await this.awardChallengeCompletion(challengeId, participation.challenge);
    }
  }

  async getChallengeParticipants(challengeId: string, limit = 50): Promise<any[]> {
    const { data, error } = await supabase
      .from('challenge_participants')
      .select(`
        *,
        user_profile:user_profiles(*)
      `)
      .eq('challenge_id', challengeId)
      .in('status', ['active', 'completed'])
      .order('current_progress', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getChallengeLeaderboard(challengeId: string, limit = 10): Promise<any[]> {
    const participants = await this.getChallengeParticipants(challengeId, limit);
    
    return participants.map((participant, index) => ({
      ...participant,
      rank: index + 1,
    }));
  }

  async searchChallenges(query: string, limit = 20): Promise<Challenge[]> {
    const { data, error } = await supabase
      .from('fitness_challenges')
      .select(`
        *,
        creator_profile:user_profiles(*)
      `)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('visibility', 'public')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async updateChallenge(
    challengeId: string,
    updates: Partial<CreateChallengeData>
  ): Promise<Challenge> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('fitness_challenges')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', challengeId)
      .eq('creator_id', user.id) // Ensure user owns the challenge
      .select(`
        *,
        creator_profile:user_profiles(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  async deleteChallenge(challengeId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('fitness_challenges')
      .delete()
      .eq('id', challengeId)
      .eq('creator_id', user.id);

    if (error) throw error;
  }

  private async awardChallengeCompletion(challengeId: string, challenge: any): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return;

    // Award achievement for completing challenge
    const achievementData = {
      user_id: user.id,
      achievement_type: 'challenge_completion',
      achievement_name: `Challenge Completed: ${challenge.title}`,
      description: `Completed the ${challenge.title} challenge`,
      icon_url: 'https://example.com/icons/challenge-trophy.png',
      criteria: {
        challenge_id: challengeId,
        challenge_type: challenge.challenge_type,
        target_value: challenge.target_value,
      },
      progress_data: {
        completed_at: new Date().toISOString(),
      },
    };

    await supabase
      .from('user_achievements')
      .insert(achievementData)
      .select()
      .single();
  }

  async getChallengesByType(type: string, limit = 20): Promise<Challenge[]> {
    const { data, error } = await supabase
      .from('fitness_challenges')
      .select(`
        *,
        creator_profile:user_profiles(*)
      `)
      .eq('challenge_type', type)
      .eq('visibility', 'public')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getUserChallengeStats(userId?: string): Promise<{
    total_joined: number;
    total_completed: number;
    total_created: number;
    completion_rate: number;
  }> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;

    if (!targetUserId) {
      throw new Error('User not authenticated');
    }

    // Get participation stats
    const { data: participations } = await supabase
      .from('challenge_participants')
      .select('status')
      .eq('user_id', targetUserId);

    // Get created challenges count
    const { count: createdCount } = await supabase
      .from('fitness_challenges')
      .select('*', { count: 'exact', head: true })
      .eq('creator_id', targetUserId);

    const totalJoined = participations?.length || 0;
    const totalCompleted = participations?.filter(p => p.status === 'completed').length || 0;
    const completionRate = totalJoined > 0 ? (totalCompleted / totalJoined) * 100 : 0;

    return {
      total_joined: totalJoined,
      total_completed: totalCompleted,
      total_created: createdCount || 0,
      completion_rate: completionRate,
    };
  }
}

export const challengeService = new ChallengeService();
