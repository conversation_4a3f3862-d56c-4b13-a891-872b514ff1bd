import { supabase } from '@/lib/supabase';

export interface UserConnection {
  id: string;
  follower_id: string;
  following_id: string;
  status: 'pending' | 'accepted' | 'blocked';
  created_at: string;
  updated_at: string;
  follower_profile?: any;
  following_profile?: any;
}

export interface SocialInteraction {
  id: string;
  user_id: string;
  target_type: string;
  target_id: string;
  interaction_type: 'like' | 'comment' | 'share' | 'report';
  content?: string;
  created_at: string;
  user_profile?: any;
}

export interface ActivityFeedItem {
  id: string;
  user_id: string;
  activity_type: string;
  title: string;
  description?: string;
  related_user_id?: string;
  related_workout_id?: string;
  related_challenge_id?: string;
  related_achievement_id?: string;
  activity_data?: any;
  created_at: string;
  user_profile?: any;
  related_user_profile?: any;
}

class SocialService {
  async followUser(userId: string): Promise<UserConnection> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    if (user.id === userId) {
      throw new Error('Cannot follow yourself');
    }

    // Check if connection already exists
    const { data: existingConnection } = await supabase
      .from('user_connections')
      .select('*')
      .eq('follower_id', user.id)
      .eq('following_id', userId)
      .single();

    if (existingConnection) {
      if (existingConnection.status === 'blocked') {
        throw new Error('Cannot follow this user');
      }
      if (existingConnection.status === 'accepted') {
        throw new Error('Already following this user');
      }
      // If pending, update to accepted
      const { data, error } = await supabase
        .from('user_connections')
        .update({ 
          status: 'accepted',
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingConnection.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    }

    // Create new connection
    const { data, error } = await supabase
      .from('user_connections')
      .insert({
        follower_id: user.id,
        following_id: userId,
        status: 'accepted', // Auto-accept for now, can be changed to 'pending' for approval flow
      })
      .select()
      .single();

    if (error) throw error;

    // Create activity feed entry
    await this.createActivityFeedEntry(
      user.id,
      'user_followed',
      'Started following a new user',
      null,
      userId
    );

    return data;
  }

  async unfollowUser(userId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('user_connections')
      .delete()
      .eq('follower_id', user.id)
      .eq('following_id', userId);

    if (error) throw error;
  }

  async getConnectionStatus(userId: string): Promise<{
    status: string | null;
    is_following: boolean;
    is_follower: boolean;
  }> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return { status: null, is_following: false, is_follower: false };
    }

    // Check if current user follows target user
    const { data: followingConnection } = await supabase
      .from('user_connections')
      .select('status')
      .eq('follower_id', user.id)
      .eq('following_id', userId)
      .single();

    // Check if target user follows current user
    const { data: followerConnection } = await supabase
      .from('user_connections')
      .select('status')
      .eq('follower_id', userId)
      .eq('following_id', user.id)
      .single();

    return {
      status: followingConnection?.status || null,
      is_following: followingConnection?.status === 'accepted',
      is_follower: followerConnection?.status === 'accepted',
    };
  }

  async getFollowers(userId: string, limit = 50): Promise<UserConnection[]> {
    const { data, error } = await supabase
      .from('user_connections')
      .select(`
        *,
        follower_profile:user_profiles!user_connections_follower_id_fkey(*)
      `)
      .eq('following_id', userId)
      .eq('status', 'accepted')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getFollowing(userId: string, limit = 50): Promise<UserConnection[]> {
    const { data, error } = await supabase
      .from('user_connections')
      .select(`
        *,
        following_profile:user_profiles!user_connections_following_id_fkey(*)
      `)
      .eq('follower_id', userId)
      .eq('status', 'accepted')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async likeContent(targetType: string, targetId: string): Promise<SocialInteraction> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if already liked
    const { data: existingLike } = await supabase
      .from('social_interactions')
      .select('id')
      .eq('user_id', user.id)
      .eq('target_type', targetType)
      .eq('target_id', targetId)
      .eq('interaction_type', 'like')
      .single();

    if (existingLike) {
      throw new Error('Already liked this content');
    }

    const { data, error } = await supabase
      .from('social_interactions')
      .insert({
        user_id: user.id,
        target_type: targetType,
        target_id: targetId,
        interaction_type: 'like',
      })
      .select()
      .single();

    if (error) throw error;

    // Update likes count on target content
    if (targetType === 'workout_share') {
      await supabase
        .from('workout_shares')
        .update({ likes_count: supabase.sql`likes_count + 1` })
        .eq('id', targetId);
    }

    return data;
  }

  async unlikeContent(targetType: string, targetId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('social_interactions')
      .delete()
      .eq('user_id', user.id)
      .eq('target_type', targetType)
      .eq('target_id', targetId)
      .eq('interaction_type', 'like');

    if (error) throw error;

    // Update likes count on target content
    if (targetType === 'workout_share') {
      await supabase
        .from('workout_shares')
        .update({ likes_count: supabase.sql`GREATEST(0, likes_count - 1)` })
        .eq('id', targetId);
    }
  }

  async commentOnContent(
    targetType: string, 
    targetId: string, 
    content: string
  ): Promise<SocialInteraction> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    if (!content.trim()) {
      throw new Error('Comment cannot be empty');
    }

    const { data, error } = await supabase
      .from('social_interactions')
      .insert({
        user_id: user.id,
        target_type: targetType,
        target_id: targetId,
        interaction_type: 'comment',
        content: content.trim(),
      })
      .select(`
        *,
        user_profile:user_profiles(*)
      `)
      .single();

    if (error) throw error;

    // Update comments count on target content
    if (targetType === 'workout_share') {
      await supabase
        .from('workout_shares')
        .update({ comments_count: supabase.sql`comments_count + 1` })
        .eq('id', targetId);
    }

    return data;
  }

  async getContentInteractions(
    targetType: string, 
    targetId: string, 
    interactionType?: string,
    limit = 50
  ): Promise<SocialInteraction[]> {
    let query = supabase
      .from('social_interactions')
      .select(`
        *,
        user_profile:user_profiles(*)
      `)
      .eq('target_type', targetType)
      .eq('target_id', targetId);

    if (interactionType) {
      query = query.eq('interaction_type', interactionType);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getActivityFeed(limit = 50): Promise<ActivityFeedItem[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get activity from users the current user follows
    const { data, error } = await supabase
      .from('activity_feed')
      .select(`
        *,
        user_profile:user_profiles!activity_feed_user_id_fkey(*),
        related_user_profile:user_profiles!activity_feed_related_user_id_fkey(*)
      `)
      .or(`user_id.eq.${user.id},user_id.in.(
        SELECT following_id FROM user_connections 
        WHERE follower_id = '${user.id}' AND status = 'accepted'
      )`)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async createActivityFeedEntry(
    userId: string,
    activityType: string,
    title: string,
    description?: string,
    relatedUserId?: string,
    relatedWorkoutId?: string,
    relatedChallengeId?: string,
    relatedAchievementId?: string,
    activityData?: any
  ): Promise<string> {
    const { data, error } = await supabase
      .rpc('create_activity_feed_entry', {
        p_user_id: userId,
        p_activity_type: activityType,
        p_title: title,
        p_description: description,
        p_related_user_id: relatedUserId,
        p_related_workout_id: relatedWorkoutId,
        p_related_challenge_id: relatedChallengeId,
        p_related_achievement_id: relatedAchievementId,
        p_activity_data: activityData,
      });

    if (error) throw error;
    return data;
  }

  async reportContent(
    targetType: string, 
    targetId: string, 
    reason: string
  ): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('social_interactions')
      .insert({
        user_id: user.id,
        target_type: targetType,
        target_id: targetId,
        interaction_type: 'report',
        content: reason,
      });

    if (error) throw error;
  }

  async blockUser(userId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Remove any existing connections
    await supabase
      .from('user_connections')
      .delete()
      .or(`follower_id.eq.${user.id},following_id.eq.${user.id}`)
      .or(`follower_id.eq.${userId},following_id.eq.${userId}`);

    // Create block entry
    const { error } = await supabase
      .from('user_connections')
      .insert({
        follower_id: user.id,
        following_id: userId,
        status: 'blocked',
      });

    if (error) throw error;
  }

  async unblockUser(userId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('user_connections')
      .delete()
      .eq('follower_id', user.id)
      .eq('following_id', userId)
      .eq('status', 'blocked');

    if (error) throw error;
  }

  async getBlockedUsers(): Promise<UserConnection[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_connections')
      .select(`
        *,
        following_profile:user_profiles!user_connections_following_id_fkey(*)
      `)
      .eq('follower_id', user.id)
      .eq('status', 'blocked')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
}

export const socialService = new SocialService();
