import { supabase } from '@/lib/supabase';

export interface TrendData {
  date: string;
  value: number;
  metric: string;
}

export interface TrendAnalysis {
  metric: string;
  current_value: number;
  trend_direction: 'increasing' | 'decreasing' | 'stable';
  trend_strength: number; // 0-1, where 1 is very strong trend
  rate_of_change: number; // per week/month
  r_squared: number; // correlation coefficient
  data_points: TrendData[];
  seasonal_patterns?: {
    pattern_type: 'weekly' | 'monthly';
    peak_periods: string[];
    low_periods: string[];
  };
}

export interface Forecast {
  metric: string;
  forecast_period: '1week' | '1month' | '3months' | '6months';
  predictions: {
    date: string;
    predicted_value: number;
    confidence_interval: {
      lower: number;
      upper: number;
    };
    confidence_level: number;
  }[];
  model_accuracy: number;
  factors_considered: string[];
}

export interface GoalPrediction {
  goal_id: string;
  goal_name: string;
  target_value: number;
  current_value: number;
  predicted_completion_date: string;
  completion_probability: number;
  days_to_completion: number;
  required_rate_of_change: number;
  recommendations: string[];
}

export interface SeasonalAnalysis {
  metric: string;
  seasonal_patterns: {
    day_of_week: Record<string, number>;
    month_of_year: Record<string, number>;
    time_of_day: Record<string, number>;
  };
  peak_performance_periods: {
    period: string;
    average_value: number;
    improvement_over_baseline: number;
  }[];
  recommendations: string[];
}

class TrendAnalysisService {
  async analyzeTrends(
    metrics: string[],
    period: '1month' | '3months' | '6months' | '1year' = '3months'
  ): Promise<TrendAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '3months':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      const analyses: TrendAnalysis[] = [];

      for (const metric of metrics) {
        const data = await this.getMetricData(userId, metric, startDate, endDate);
        if (data.length >= 3) {
          const analysis = this.performTrendAnalysis(metric, data);
          analyses.push(analysis);
        }
      }

      return analyses;
    } catch (error) {
      console.error('Error analyzing trends:', error);
      throw error;
    }
  }

  async generateForecasts(
    metrics: string[],
    forecastPeriod: '1week' | '1month' | '3months' | '6months' = '1month'
  ): Promise<Forecast[]> {
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }
    try {
      const forecasts: Forecast[] = [];

      for (const metric of metrics) {
        // Get historical data for forecasting
        const historicalData = await this.getMetricData(
          userId,
          metric,
          new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000), // 6 months back
          new Date()
        );

        if (historicalData.length >= 10) {
          const forecast = this.generateForecast(metric, historicalData, forecastPeriod);
          forecasts.push(forecast);
        }
      }

      return forecasts;
    } catch (error) {
      console.error('Error generating forecasts:', error);
      throw error;
    }
  }

  async predictGoalCompletion(): Promise<GoalPrediction[]> {
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }
    try {
      // Get user's active goals
      const { data: goals } = await supabase
        .from('user_goals')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (!goals) return [];

      const predictions: GoalPrediction[] = [];

      for (const goal of goals) {
        const prediction = await this.analyzeGoalProgress(userId, goal);
        if (prediction) {
          predictions.push(prediction);
        }
      }

      return predictions;
    } catch (error) {
      console.error('Error predicting goal completion:', error);
      throw error;
    }
  }

  async analyzeSeasonalPatterns(
    metric: string
  ): Promise<SeasonalAnalysis> {
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }
    try {
      // Get 1 year of data for seasonal analysis
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 1);
      
      const data = await this.getMetricData(userId, metric, startDate, new Date());
      
      return this.performSeasonalAnalysis(metric, data);
    } catch (error) {
      console.error('Error analyzing seasonal patterns:', error);
      throw error;
    }
  }

  private async getMetricData(
    userId: string,
    metric: string,
    startDate: Date,
    endDate: Date
  ): Promise<TrendData[]> {
    switch (metric) {
      case 'total_volume':
        return this.getTotalVolumeData(userId, startDate, endDate);
      case 'workout_frequency':
        return this.getWorkoutFrequencyData(userId, startDate, endDate);
      case 'average_weight':
        return this.getAverageWeightData(userId, startDate, endDate);
      case 'workout_duration':
        return this.getWorkoutDurationData(userId, startDate, endDate);
      case 'body_weight':
        return this.getBodyWeightData(userId, startDate, endDate);
      case 'strength_score':
        return this.getStrengthScoreData(userId, startDate, endDate);
      default:
        return [];
    }
  }

  private async getTotalVolumeData(userId: string, startDate: Date, endDate: Date): Promise<TrendData[]> {
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select(`
        completed_at,
        exercise_logs(
          sets
        )
      `)
      .eq('user_id', userId)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString())
      .order('completed_at');

    if (!workouts) return [];

    return workouts.map(workout => {
      const totalVolume = workout.exercise_logs?.reduce((sum: number, exercise: any) => {
        return sum + (exercise.sets?.reduce((setSum: number, set: any) => {
          return setSum + (set.weight || 0) * (set.reps || 0);
        }, 0) || 0);
      }, 0) || 0;

      return {
        date: workout.completed_at,
        value: totalVolume,
        metric: 'total_volume',
      };
    });
  }

  private async getWorkoutFrequencyData(userId: string, startDate: Date, endDate: Date): Promise<TrendData[]> {
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select('completed_at')
      .eq('user_id', userId)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString())
      .order('completed_at');

    if (!workouts) return [];

    // Group by week and count workouts
    const weeklyData = new Map<string, number>();
    
    workouts.forEach(workout => {
      const date = new Date(workout.completed_at);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekKey = weekStart.toISOString().split('T')[0];
      
      weeklyData.set(weekKey, (weeklyData.get(weekKey) || 0) + 1);
    });

    return Array.from(weeklyData.entries()).map(([date, count]) => ({
      date,
      value: count,
      metric: 'workout_frequency',
    }));
  }

  private async getAverageWeightData(userId: string, startDate: Date, endDate: Date): Promise<TrendData[]> {
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select(`
        completed_at,
        exercise_logs(
          sets
        )
      `)
      .eq('user_id', userId)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString())
      .order('completed_at');

    if (!workouts) return [];

    return workouts.map(workout => {
      let totalWeight = 0;
      let setCount = 0;

      workout.exercise_logs?.forEach((exercise: any) => {
        exercise.sets?.forEach((set: any) => {
          if (set.weight) {
            totalWeight += set.weight;
            setCount++;
          }
        });
      });

      const averageWeight = setCount > 0 ? totalWeight / setCount : 0;

      return {
        date: workout.completed_at,
        value: averageWeight,
        metric: 'average_weight',
      };
    });
  }

  private async getWorkoutDurationData(userId: string, startDate: Date, endDate: Date): Promise<TrendData[]> {
    const { data: workouts } = await supabase
      .from('workout_logs')
      .select('completed_at, duration')
      .eq('user_id', userId)
      .gte('completed_at', startDate.toISOString())
      .lte('completed_at', endDate.toISOString())
      .order('completed_at');

    if (!workouts) return [];

    return workouts.map(workout => ({
      date: workout.completed_at,
      value: workout.duration || 0,
      metric: 'workout_duration',
    }));
  }

  private async getBodyWeightData(userId: string, startDate: Date, endDate: Date): Promise<TrendData[]> {
    const { data: measurements } = await supabase
      .from('progress_tracking')
      .select('recorded_at, body_weight')
      .eq('user_id', userId)
      .not('body_weight', 'is', null)
      .gte('recorded_at', startDate.toISOString())
      .lte('recorded_at', endDate.toISOString())
      .order('recorded_at');

    if (!measurements) return [];

    return measurements.map(measurement => ({
      date: measurement.recorded_at,
      value: measurement.body_weight,
      metric: 'body_weight',
    }));
  }

  private async getStrengthScoreData(userId: string, startDate: Date, endDate: Date): Promise<TrendData[]> {
    // This would calculate a composite strength score over time
    // For now, return mock data based on volume progression
    const volumeData = await this.getTotalVolumeData(userId, startDate, endDate);
    
    return volumeData.map((point, index) => ({
      date: point.date,
      value: Math.min(100, 50 + (point.value / 1000) + (index * 2)), // Mock strength score
      metric: 'strength_score',
    }));
  }

  private performTrendAnalysis(metric: string, data: TrendData[]): TrendAnalysis {
    if (data.length < 2) {
      return {
        metric,
        current_value: data[0]?.value || 0,
        trend_direction: 'stable',
        trend_strength: 0,
        rate_of_change: 0,
        r_squared: 0,
        data_points: data,
      };
    }

    // Perform linear regression
    const n = data.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = data.map(d => d.value);

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Calculate R-squared
    const yMean = sumY / n;
    const ssRes = y.reduce((sum, yi, i) => {
      const predicted = slope * x[i] + intercept;
      return sum + Math.pow(yi - predicted, 2);
    }, 0);
    const ssTot = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
    const rSquared = ssTot > 0 ? 1 - (ssRes / ssTot) : 0;

    // Determine trend direction and strength
    const trendDirection = slope > 0.1 ? 'increasing' : slope < -0.1 ? 'decreasing' : 'stable';
    const trendStrength = Math.min(1, Math.abs(slope) / (yMean || 1));

    return {
      metric,
      current_value: data[data.length - 1].value,
      trend_direction: trendDirection,
      trend_strength: trendStrength,
      rate_of_change: slope,
      r_squared: Math.max(0, rSquared),
      data_points: data,
    };
  }

  private generateForecast(
    metric: string,
    historicalData: TrendData[],
    forecastPeriod: string
  ): Forecast {
    const analysis = this.performTrendAnalysis(metric, historicalData);
    
    // Determine number of periods to forecast
    const periodsToForecast = {
      '1week': 1,
      '1month': 4,
      '3months': 12,
      '6months': 24,
    }[forecastPeriod] || 4;

    const predictions = [];
    const lastValue = historicalData[historicalData.length - 1].value;
    const rateOfChange = analysis.rate_of_change;
    
    for (let i = 1; i <= periodsToForecast; i++) {
      const predictedValue = lastValue + (rateOfChange * i);
      
      // Calculate confidence interval (simplified)
      const standardError = Math.sqrt(1 - analysis.r_squared) * (lastValue * 0.1);
      const confidenceLevel = Math.max(0.5, analysis.r_squared);
      
      const futureDate = new Date(historicalData[historicalData.length - 1].date);
      futureDate.setDate(futureDate.getDate() + (i * 7)); // Weekly intervals

      predictions.push({
        date: futureDate.toISOString(),
        predicted_value: Math.max(0, predictedValue),
        confidence_interval: {
          lower: Math.max(0, predictedValue - standardError * 1.96),
          upper: predictedValue + standardError * 1.96,
        },
        confidence_level: confidenceLevel,
      });
    }

    return {
      metric,
      forecast_period: forecastPeriod as any,
      predictions,
      model_accuracy: analysis.r_squared,
      factors_considered: ['historical_trend', 'seasonal_patterns', 'user_behavior'],
    };
  }

  private async analyzeGoalProgress(userId: string, goal: any): Promise<GoalPrediction | null> {
    try {
      // Get relevant metric data for the goal
      const metricData = await this.getMetricData(
        userId,
        goal.metric_type,
        new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days back
        new Date()
      );

      if (metricData.length < 3) return null;

      const analysis = this.performTrendAnalysis(goal.metric_type, metricData);
      const currentValue = analysis.current_value;
      const targetValue = goal.target_value;
      const rateOfChange = analysis.rate_of_change;

      // Calculate days to completion
      const remainingProgress = targetValue - currentValue;
      const daysToCompletion = rateOfChange > 0 
        ? Math.ceil(remainingProgress / (rateOfChange / 7)) // Convert weekly rate to daily
        : -1;

      // Calculate completion probability
      const completionProbability = this.calculateCompletionProbability(
        currentValue,
        targetValue,
        rateOfChange,
        analysis.r_squared
      );

      // Generate recommendations
      const recommendations = this.generateGoalRecommendations(
        goal,
        currentValue,
        targetValue,
        rateOfChange,
        completionProbability
      );

      const predictedCompletionDate = daysToCompletion > 0 
        ? new Date(Date.now() + daysToCompletion * 24 * 60 * 60 * 1000).toISOString()
        : new Date(goal.target_date).toISOString();

      return {
        goal_id: goal.id,
        goal_name: goal.name,
        target_value: targetValue,
        current_value: currentValue,
        predicted_completion_date: predictedCompletionDate,
        completion_probability: completionProbability,
        days_to_completion: Math.max(0, daysToCompletion),
        required_rate_of_change: remainingProgress / (new Date(goal.target_date).getTime() - Date.now()) * (1000 * 60 * 60 * 24 * 7), // Weekly rate needed
        recommendations,
      };
    } catch (error) {
      console.error('Error analyzing goal progress:', error);
      return null;
    }
  }

  private calculateCompletionProbability(
    currentValue: number,
    targetValue: number,
    rateOfChange: number,
    confidence: number
  ): number {
    if (rateOfChange <= 0 && targetValue > currentValue) {
      return 0.1; // Very low probability if no progress
    }

    if (currentValue >= targetValue) {
      return 1.0; // Already achieved
    }

    // Base probability on rate of change and confidence
    const progressRatio = currentValue / targetValue;
    const trendStrength = Math.min(1, Math.abs(rateOfChange) / currentValue);
    
    return Math.min(0.95, Math.max(0.1, progressRatio * 0.5 + trendStrength * 0.3 + confidence * 0.2));
  }

  private generateGoalRecommendations(
    goal: any,
    currentValue: number,
    targetValue: number,
    rateOfChange: number,
    probability: number
  ): string[] {
    const recommendations = [];

    if (probability < 0.3) {
      recommendations.push('Consider adjusting your goal timeline or target value');
      recommendations.push('Increase workout frequency or intensity');
      recommendations.push('Review your current program with a coach');
    } else if (probability < 0.7) {
      recommendations.push('Stay consistent with your current approach');
      recommendations.push('Consider small increases in training volume');
      recommendations.push('Track progress more frequently');
    } else {
      recommendations.push('You\'re on track! Keep up the great work');
      recommendations.push('Consider setting a more challenging goal');
      recommendations.push('Focus on maintaining consistency');
    }

    return recommendations;
  }

  private performSeasonalAnalysis(metric: string, data: TrendData[]): SeasonalAnalysis {
    const dayOfWeekData: Record<string, number[]> = {};
    const monthOfYearData: Record<string, number[]> = {};
    const timeOfDayData: Record<string, number[]> = {};

    // Group data by different time periods
    data.forEach(point => {
      const date = new Date(point.date);
      
      // Day of week analysis
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
      if (!dayOfWeekData[dayOfWeek]) dayOfWeekData[dayOfWeek] = [];
      dayOfWeekData[dayOfWeek].push(point.value);

      // Month of year analysis
      const month = date.toLocaleDateString('en-US', { month: 'long' });
      if (!monthOfYearData[month]) monthOfYearData[month] = [];
      monthOfYearData[month].push(point.value);

      // Time of day analysis (simplified)
      const hour = date.getHours();
      const timeOfDay = hour < 12 ? 'Morning' : hour < 17 ? 'Afternoon' : 'Evening';
      if (!timeOfDayData[timeOfDay]) timeOfDayData[timeOfDay] = [];
      timeOfDayData[timeOfDay].push(point.value);
    });

    // Calculate averages
    const dayOfWeekAverages: Record<string, number> = {};
    Object.entries(dayOfWeekData).forEach(([day, values]) => {
      dayOfWeekAverages[day] = values.reduce((a, b) => a + b, 0) / values.length;
    });

    const monthOfYearAverages: Record<string, number> = {};
    Object.entries(monthOfYearData).forEach(([month, values]) => {
      monthOfYearAverages[month] = values.reduce((a, b) => a + b, 0) / values.length;
    });

    const timeOfDayAverages: Record<string, number> = {};
    Object.entries(timeOfDayData).forEach(([time, values]) => {
      timeOfDayAverages[time] = values.reduce((a, b) => a + b, 0) / values.length;
    });

    // Find peak performance periods
    const overallAverage = data.reduce((sum, point) => sum + point.value, 0) / data.length;
    const peakPeriods = [];

    Object.entries(dayOfWeekAverages).forEach(([period, average]) => {
      if (average > overallAverage * 1.1) {
        peakPeriods.push({
          period: `${period}s`,
          average_value: average,
          improvement_over_baseline: (average - overallAverage) / overallAverage,
        });
      }
    });

    return {
      metric,
      seasonal_patterns: {
        day_of_week: dayOfWeekAverages,
        month_of_year: monthOfYearAverages,
        time_of_day: timeOfDayAverages,
      },
      peak_performance_periods: peakPeriods,
      recommendations: this.generateSeasonalRecommendations(dayOfWeekAverages, timeOfDayAverages),
    };
  }

  private generateSeasonalRecommendations(
    dayOfWeek: Record<string, number>,
    timeOfDay: Record<string, number>
  ): string[] {
    const recommendations = [];

    // Find best day of week
    const bestDay = Object.entries(dayOfWeek).reduce((a, b) => a[1] > b[1] ? a : b)[0];
    recommendations.push(`Your best performance day is ${bestDay} - consider scheduling important workouts then`);

    // Find best time of day
    const bestTime = Object.entries(timeOfDay).reduce((a, b) => a[1] > b[1] ? a : b)[0];
    recommendations.push(`You perform best in the ${bestTime.toLowerCase()} - try to workout during this time when possible`);

    return recommendations;
  }
}

export const trendAnalysisService = new TrendAnalysisService();
