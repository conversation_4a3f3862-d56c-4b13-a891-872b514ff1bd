import { supabase } from '@/lib/supabase';
import { uploadImage } from './storageService';
import { socialService } from './socialService';

export interface WorkoutShare {
  id: string;
  user_id: string;
  workout_log_id?: string;
  workout_program_id?: string;
  title: string;
  description?: string;
  visibility: 'public' | 'friends' | 'private';
  likes_count: number;
  comments_count: number;
  shares_count: number;
  images?: string[];
  created_at: string;
  updated_at: string;
  user_profile?: any;
  workout_log?: any;
  workout_program?: any;
  is_liked?: boolean;
}

export interface CreateShareData {
  workoutLogId?: string;
  workoutProgramId?: string;
  title: string;
  description?: string;
  visibility: 'public' | 'friends' | 'private';
  images?: string[];
}

class WorkoutShareService {
  async createShare(data: CreateShareData): Promise<WorkoutShare> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    if (!data.workoutLogId && !data.workoutProgramId) {
      throw new Error('Either workout log or program must be specified');
    }

    try {
      // Upload images if provided
      let uploadedImages: string[] = [];
      if (data.images && data.images.length > 0) {
        uploadedImages = await Promise.all(
          data.images.map(async (imageUri, index) => {
            const fileName = `share_${user.id}_${Date.now()}_${index}.jpg`;
            return uploadImage(imageUri, 'workout-shares', fileName);
          })
        );
      }

      // Create the share
      const shareData = {
        user_id: user.id,
        workout_log_id: data.workoutLogId || null,
        workout_program_id: data.workoutProgramId || null,
        title: data.title,
        description: data.description || null,
        visibility: data.visibility,
        images: uploadedImages.length > 0 ? uploadedImages : null,
      };

      const { data: share, error } = await supabase
        .from('workout_shares')
        .insert(shareData)
        .select(`
          *,
          user_profile:user_profiles(*),
          workout_log:workout_logs(*),
          workout_program:workout_programs(*)
        `)
        .single();

      if (error) throw error;

      // Create activity feed entry
      await socialService.createActivityFeedEntry(
        user.id,
        'workout_shared',
        `Shared a workout: ${data.title}`,
        data.description,
        undefined,
        data.workoutLogId,
        undefined,
        undefined,
        { share_id: share.id }
      );

      return share;
    } catch (error) {
      console.error('Failed to create workout share:', error);
      throw error;
    }
  }

  async getShares(
    userId?: string,
    visibility?: string,
    limit = 20,
    offset = 0
  ): Promise<WorkoutShare[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    let query = supabase
      .from('workout_shares')
      .select(`
        *,
        user_profile:user_profiles(*),
        workout_log:workout_logs(*),
        workout_program:workout_programs(*)
      `);

    // Filter by user if specified
    if (userId) {
      query = query.eq('user_id', userId);
    }

    // Filter by visibility if specified
    if (visibility) {
      query = query.eq('visibility', visibility);
    } else {
      // Default visibility filtering based on current user
      if (user) {
        query = query.or(`visibility.eq.public,user_id.eq.${user.id}`);
      } else {
        query = query.eq('visibility', 'public');
      }
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    // Check if current user has liked each share
    if (user && data) {
      const shareIds = data.map(share => share.id);
      const { data: likes } = await supabase
        .from('social_interactions')
        .select('target_id')
        .eq('user_id', user.id)
        .eq('target_type', 'workout_share')
        .eq('interaction_type', 'like')
        .in('target_id', shareIds);

      const likedShareIds = new Set(likes?.map(like => like.target_id) || []);
      
      return data.map(share => ({
        ...share,
        is_liked: likedShareIds.has(share.id),
      }));
    }

    return data || [];
  }

  async getShareById(shareId: string): Promise<WorkoutShare | null> {
    const { data: { user } } = await supabase.auth.getUser();
    
    const { data, error } = await supabase
      .from('workout_shares')
      .select(`
        *,
        user_profile:user_profiles(*),
        workout_log:workout_logs(*),
        workout_program:workout_programs(*)
      `)
      .eq('id', shareId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    // Check if current user has liked this share
    if (user) {
      const { data: like } = await supabase
        .from('social_interactions')
        .select('id')
        .eq('user_id', user.id)
        .eq('target_type', 'workout_share')
        .eq('target_id', shareId)
        .eq('interaction_type', 'like')
        .single();

      data.is_liked = !!like;
    }

    return data;
  }

  async updateShare(
    shareId: string,
    updates: {
      title?: string;
      description?: string;
      visibility?: 'public' | 'friends' | 'private';
    }
  ): Promise<WorkoutShare> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('workout_shares')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', shareId)
      .eq('user_id', user.id) // Ensure user owns the share
      .select(`
        *,
        user_profile:user_profiles(*),
        workout_log:workout_logs(*),
        workout_program:workout_programs(*)
      `)
      .single();

    if (error) throw error;
    return data;
  }

  async deleteShare(shareId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get share details first to delete associated images
    const { data: share } = await supabase
      .from('workout_shares')
      .select('images')
      .eq('id', shareId)
      .eq('user_id', user.id)
      .single();

    if (share?.images) {
      // Delete images from storage
      const imagePaths = share.images.map(url => {
        const urlParts = url.split('/');
        return urlParts[urlParts.length - 1];
      });

      await supabase.storage
        .from('workout-shares')
        .remove(imagePaths);
    }

    // Delete the share
    const { error } = await supabase
      .from('workout_shares')
      .delete()
      .eq('id', shareId)
      .eq('user_id', user.id);

    if (error) throw error;
  }

  async likeShare(shareId: string): Promise<void> {
    await socialService.likeContent('workout_share', shareId);
  }

  async unlikeShare(shareId: string): Promise<void> {
    await socialService.unlikeContent('workout_share', shareId);
  }

  async commentOnShare(shareId: string, content: string): Promise<any> {
    return socialService.commentOnContent('workout_share', shareId, content);
  }

  async getShareComments(shareId: string, limit = 50): Promise<any[]> {
    return socialService.getContentInteractions('workout_share', shareId, 'comment', limit);
  }

  async getShareLikes(shareId: string, limit = 50): Promise<any[]> {
    return socialService.getContentInteractions('workout_share', shareId, 'like', limit);
  }

  async getFeedShares(limit = 20, offset = 0): Promise<WorkoutShare[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      // Return public shares for non-authenticated users
      return this.getShares(undefined, 'public', limit, offset);
    }

    // Get shares from users the current user follows + public shares
    const { data, error } = await supabase
      .from('workout_shares')
      .select(`
        *,
        user_profile:user_profiles(*),
        workout_log:workout_logs(*),
        workout_program:workout_programs(*)
      `)
      .or(`visibility.eq.public,user_id.eq.${user.id},user_id.in.(
        SELECT following_id FROM user_connections 
        WHERE follower_id = '${user.id}' AND status = 'accepted'
      )`)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    // Check likes for current user
    if (data) {
      const shareIds = data.map(share => share.id);
      const { data: likes } = await supabase
        .from('social_interactions')
        .select('target_id')
        .eq('user_id', user.id)
        .eq('target_type', 'workout_share')
        .eq('interaction_type', 'like')
        .in('target_id', shareIds);

      const likedShareIds = new Set(likes?.map(like => like.target_id) || []);
      
      return data.map(share => ({
        ...share,
        is_liked: likedShareIds.has(share.id),
      }));
    }

    return [];
  }

  async searchShares(query: string, limit = 20): Promise<WorkoutShare[]> {
    const { data, error } = await supabase
      .from('workout_shares')
      .select(`
        *,
        user_profile:user_profiles(*),
        workout_log:workout_logs(*),
        workout_program:workout_programs(*)
      `)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('visibility', 'public')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async reportShare(shareId: string, reason: string): Promise<void> {
    await socialService.reportContent('workout_share', shareId, reason);
  }

  async getShareStats(shareId: string): Promise<{
    likes_count: number;
    comments_count: number;
    shares_count: number;
  }> {
    const { data, error } = await supabase
      .from('workout_shares')
      .select('likes_count, comments_count, shares_count')
      .eq('id', shareId)
      .single();

    if (error) throw error;
    return data;
  }

  async getUserShareStats(userId: string): Promise<{
    total_shares: number;
    total_likes: number;
    total_comments: number;
    most_liked_share?: WorkoutShare;
  }> {
    // Get total shares
    const { count: totalShares } = await supabase
      .from('workout_shares')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Get total likes and comments
    const { data: shares } = await supabase
      .from('workout_shares')
      .select('likes_count, comments_count')
      .eq('user_id', userId);

    const totalLikes = shares?.reduce((sum, share) => sum + share.likes_count, 0) || 0;
    const totalComments = shares?.reduce((sum, share) => sum + share.comments_count, 0) || 0;

    // Get most liked share
    const { data: mostLikedShare } = await supabase
      .from('workout_shares')
      .select(`
        *,
        user_profile:user_profiles(*),
        workout_log:workout_logs(*),
        workout_program:workout_programs(*)
      `)
      .eq('user_id', userId)
      .order('likes_count', { ascending: false })
      .limit(1)
      .single();

    return {
      total_shares: totalShares || 0,
      total_likes: totalLikes,
      total_comments: totalComments,
      most_liked_share: mostLikedShare || undefined,
    };
  }
}

export const workoutShareService = new WorkoutShareService();
