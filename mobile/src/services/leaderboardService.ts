import { supabase } from '@/lib/supabase';

export interface Leaderboard {
  id: string;
  name: string;
  description: string;
  metric_type: string;
  time_period: string;
  category: string;
  is_active: boolean;
  max_entries: number;
  created_at: string;
  updated_at: string;
}

export interface LeaderboardEntry {
  id: string;
  leaderboard_id: string;
  user_id: string;
  rank: number;
  score: number;
  entry_data?: any;
  period_start: string;
  period_end: string;
  created_at: string;
  user_profile?: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

class LeaderboardService {
  async getLeaderboards(): Promise<Leaderboard[]> {
    const { data, error } = await supabase
      .from('leaderboards')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    return data || [];
  }

  async getLeaderboardEntries(
    leaderboardId: string,
    timePeriod: string,
    limit = 50
  ): Promise<LeaderboardEntry[]> {
    const { startDate, endDate } = this.getPeriodDates(timePeriod);

    const { data, error } = await supabase
      .from('leaderboard_entries')
      .select(`
        *,
        user_profile:user_profiles(username, display_name, avatar_url)
      `)
      .eq('leaderboard_id', leaderboardId)
      .gte('period_start', startDate)
      .lte('period_end', endDate)
      .order('rank')
      .limit(limit);

    if (error) throw error;
    return data || [];
  }

  async getUserRank(
    leaderboardId: string,
    timePeriod: string,
    userId: string
  ): Promise<LeaderboardEntry | null> {
    const { startDate, endDate } = this.getPeriodDates(timePeriod);

    const { data, error } = await supabase
      .from('leaderboard_entries')
      .select(`
        *,
        user_profile:user_profiles(username, display_name, avatar_url)
      `)
      .eq('leaderboard_id', leaderboardId)
      .eq('user_id', userId)
      .gte('period_start', startDate)
      .lte('period_end', endDate)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data;
  }

  async updateLeaderboards(): Promise<void> {
    // This function would typically be called by a scheduled job
    // For now, we'll implement the logic to calculate and update leaderboard entries

    const leaderboards = await this.getLeaderboards();

    for (const leaderboard of leaderboards) {
      await this.calculateLeaderboardEntries(leaderboard);
    }
  }

  private async calculateLeaderboardEntries(leaderboard: Leaderboard): Promise<void> {
    const { startDate, endDate } = this.getPeriodDates(leaderboard.time_period);

    // Clear existing entries for this period
    await supabase
      .from('leaderboard_entries')
      .delete()
      .eq('leaderboard_id', leaderboard.id)
      .gte('period_start', startDate)
      .lte('period_end', endDate);

    // Calculate new entries based on metric type
    let userScores: { user_id: string; score: number; entry_data?: any }[] = [];

    switch (leaderboard.metric_type) {
      case 'total_workouts':
        userScores = await this.calculateTotalWorkouts(startDate, endDate);
        break;
      case 'total_weight':
        userScores = await this.calculateTotalWeight(startDate, endDate);
        break;
      case 'streak_days':
        userScores = await this.calculateStreakDays(startDate, endDate);
        break;
      case 'total_duration':
        userScores = await this.calculateTotalDuration(startDate, endDate);
        break;
      case 'average_rating':
        userScores = await this.calculateAverageRating(startDate, endDate);
        break;
      default:
        console.warn(`Unknown metric type: ${leaderboard.metric_type}`);
        continue;
    }

    // Sort by score (descending) and assign ranks
    userScores.sort((a, b) => b.score - a.score);

    const entries = userScores.slice(0, leaderboard.max_entries).map((userScore, index) => ({
      leaderboard_id: leaderboard.id,
      user_id: userScore.user_id,
      rank: index + 1,
      score: userScore.score,
      entry_data: userScore.entry_data,
      period_start: startDate,
      period_end: endDate,
    }));

    if (entries.length > 0) {
      const { error } = await supabase
        .from('leaderboard_entries')
        .insert(entries);

      if (error) {
        console.error(`Failed to insert leaderboard entries for ${leaderboard.name}:`, error);
      }
    }
  }

  private async calculateTotalWorkouts(startDate: string, endDate: string): Promise<
    { user_id: string; score: number }[]
  > {
    const { data, error } = await supabase
      .from('workout_logs')
      .select('user_id')
      .gte('completed_at', startDate)
      .lte('completed_at', endDate);

    if (error) throw error;

    const userCounts = (data || []).reduce((acc, log) => {
      acc[log.user_id] = (acc[log.user_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(userCounts).map(([user_id, score]) => ({
      user_id,
      score,
    }));
  }

  private async calculateTotalWeight(startDate: string, endDate: string): Promise<
    { user_id: string; score: number }[]
  > {
    const { data, error } = await supabase
      .from('workout_logs')
      .select('user_id, exercises')
      .gte('completed_at', startDate)
      .lte('completed_at', endDate);

    if (error) throw error;

    const userWeights = (data || []).reduce((acc, log) => {
      let totalWeight = 0;
      
      if (log.exercises) {
        log.exercises.forEach((exercise: any) => {
          exercise.sets?.forEach((set: any) => {
            if (set.weight && set.reps) {
              totalWeight += set.weight * set.reps;
            }
          });
        });
      }

      acc[log.user_id] = (acc[log.user_id] || 0) + totalWeight;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(userWeights).map(([user_id, score]) => ({
      user_id,
      score,
    }));
  }

  private async calculateStreakDays(startDate: string, endDate: string): Promise<
    { user_id: string; score: number }[]
  > {
    // Get all users who have workouts in this period
    const { data: users, error: usersError } = await supabase
      .from('workout_logs')
      .select('user_id')
      .gte('completed_at', startDate)
      .lte('completed_at', endDate);

    if (usersError) throw usersError;

    const uniqueUsers = [...new Set((users || []).map(u => u.user_id))];
    const userStreaks: { user_id: string; score: number }[] = [];

    for (const userId of uniqueUsers) {
      const streak = await this.calculateUserStreak(userId, endDate);
      userStreaks.push({ user_id: userId, score: streak });
    }

    return userStreaks;
  }

  private async calculateUserStreak(userId: string, endDate: string): Promise<number> {
    const { data, error } = await supabase
      .from('workout_logs')
      .select('completed_at')
      .eq('user_id', userId)
      .lte('completed_at', endDate)
      .order('completed_at', { ascending: false })
      .limit(100);

    if (error) throw error;
    if (!data || data.length === 0) return 0;

    let streak = 0;
    let currentDate = new Date(endDate);
    currentDate.setHours(0, 0, 0, 0);

    const workoutDates = data.map(log => {
      const date = new Date(log.completed_at);
      date.setHours(0, 0, 0, 0);
      return date.getTime();
    });

    const uniqueWorkoutDates = [...new Set(workoutDates)].sort((a, b) => b - a);

    for (const workoutDate of uniqueWorkoutDates) {
      const daysDiff = Math.floor((currentDate.getTime() - workoutDate) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === streak) {
        streak++;
      } else if (daysDiff > streak) {
        break;
      }
    }

    return streak;
  }

  private async calculateTotalDuration(startDate: string, endDate: string): Promise<
    { user_id: string; score: number }[]
  > {
    const { data, error } = await supabase
      .from('workout_logs')
      .select('user_id, duration')
      .gte('completed_at', startDate)
      .lte('completed_at', endDate);

    if (error) throw error;

    const userDurations = (data || []).reduce((acc, log) => {
      if (log.duration) {
        acc[log.user_id] = (acc[log.user_id] || 0) + log.duration;
      }
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(userDurations).map(([user_id, score]) => ({
      user_id,
      score,
    }));
  }

  private async calculateAverageRating(startDate: string, endDate: string): Promise<
    { user_id: string; score: number }[]
  > {
    const { data, error } = await supabase
      .from('workout_logs')
      .select('user_id, rating')
      .gte('completed_at', startDate)
      .lte('completed_at', endDate)
      .not('rating', 'is', null);

    if (error) throw error;

    const userRatings = (data || []).reduce((acc, log) => {
      if (!acc[log.user_id]) {
        acc[log.user_id] = { total: 0, count: 0 };
      }
      acc[log.user_id].total += log.rating;
      acc[log.user_id].count += 1;
      return acc;
    }, {} as Record<string, { total: number; count: number }>);

    return Object.entries(userRatings).map(([user_id, data]) => ({
      user_id,
      score: data.total / data.count,
    }));
  }

  private getPeriodDates(timePeriod: string): { startDate: string; endDate: string } {
    const now = new Date();
    const endDate = now.toISOString();
    let startDate: string;

    switch (timePeriod) {
      case 'daily':
        const startOfDay = new Date(now);
        startOfDay.setHours(0, 0, 0, 0);
        startDate = startOfDay.toISOString();
        break;
      case 'weekly':
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        startDate = startOfWeek.toISOString();
        break;
      case 'monthly':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        startDate = startOfMonth.toISOString();
        break;
      case 'yearly':
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        startDate = startOfYear.toISOString();
        break;
      case 'all_time':
      default:
        startDate = '1970-01-01T00:00:00.000Z';
        break;
    }

    return { startDate, endDate };
  }

  async createCustomLeaderboard(data: {
    name: string;
    description: string;
    metric_type: string;
    time_period: string;
    category: string;
    max_entries?: number;
  }): Promise<Leaderboard> {
    const { data: leaderboard, error } = await supabase
      .from('leaderboards')
      .insert({
        ...data,
        max_entries: data.max_entries || 100,
        is_active: true,
      })
      .select()
      .single();

    if (error) throw error;
    return leaderboard;
  }

  async getLeaderboardsByCategory(category: string): Promise<Leaderboard[]> {
    const { data, error } = await supabase
      .from('leaderboards')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .order('name');

    if (error) throw error;
    return data || [];
  }

  async getUserLeaderboardHistory(
    userId: string,
    leaderboardId: string,
    limit = 10
  ): Promise<LeaderboardEntry[]> {
    const { data, error } = await supabase
      .from('leaderboard_entries')
      .select('*')
      .eq('user_id', userId)
      .eq('leaderboard_id', leaderboardId)
      .order('period_start', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }
}

export const leaderboardService = new LeaderboardService();
