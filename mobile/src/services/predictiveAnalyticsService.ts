import { supabase } from '@/lib/supabase';

export interface PredictionModel {
  id: string;
  name: string;
  type: 'progress' | 'behavior' | 'churn' | 'recommendation';
  accuracy: number;
  last_trained: string;
  features: string[];
  parameters: Record<string, any>;
}

export interface ProgressPrediction {
  metric: string;
  current_value: number;
  predicted_value: number;
  prediction_date: string;
  confidence: number;
  factors: {
    name: string;
    impact: number;
    description: string;
  }[];
}

export interface BehaviorPrediction {
  user_id: string;
  behavior_type: 'workout_frequency' | 'program_completion' | 'engagement';
  prediction: number;
  confidence: number;
  risk_factors: string[];
  recommendations: string[];
}

export interface ChurnPrediction {
  user_id: string;
  churn_probability: number;
  risk_level: 'low' | 'medium' | 'high';
  key_factors: {
    factor: string;
    weight: number;
    value: number;
  }[];
  intervention_recommendations: string[];
  predicted_churn_date?: string;
}

export interface RecommendationPrediction {
  user_id: string;
  recommendation_type: 'exercise' | 'program' | 'goal' | 'nutrition';
  recommendations: {
    item_id: string;
    item_name: string;
    score: number;
    reasoning: string;
  }[];
  confidence: number;
}

class PredictiveAnalyticsService {
  private baseUrl = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000';

  async getProgressPredictions(
    userId?: string,
    timeframe: '1week' | '1month' | '3months' | '6months' = '1month'
  ): Promise<ProgressPrediction[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        throw new Error('User not authenticated');
      }

      // Get user's workout history for prediction
      const { data: workoutHistory } = await supabase
        .from('workout_logs')
        .select(`
          *,
          exercise_logs(*)
        `)
        .eq('user_id', targetUserId)
        .order('completed_at', { ascending: false })
        .limit(50);

      if (!workoutHistory || workoutHistory.length < 5) {
        return []; // Need at least 5 workouts for predictions
      }

      // Calculate current metrics
      const currentMetrics = this.calculateCurrentMetrics(workoutHistory);
      
      // Generate predictions using simple linear regression
      const predictions: ProgressPrediction[] = [];

      for (const [metric, values] of Object.entries(currentMetrics)) {
        if (values.length >= 3) {
          const prediction = this.predictLinearTrend(values, timeframe);
          
          predictions.push({
            metric,
            current_value: values[values.length - 1],
            predicted_value: prediction.value,
            prediction_date: prediction.date,
            confidence: prediction.confidence,
            factors: this.getProgressFactors(metric, workoutHistory),
          });
        }
      }

      return predictions;
    } catch (error) {
      console.error('Error getting progress predictions:', error);
      throw error;
    }
  }

  async getBehaviorPredictions(userId?: string): Promise<BehaviorPrediction[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        throw new Error('User not authenticated');
      }

      // Get user behavior data
      const [workoutData, engagementData] = await Promise.all([
        this.getUserWorkoutBehavior(targetUserId),
        this.getUserEngagementBehavior(targetUserId),
      ]);

      const predictions: BehaviorPrediction[] = [];

      // Predict workout frequency
      const workoutFrequencyPrediction = this.predictWorkoutFrequency(workoutData);
      predictions.push(workoutFrequencyPrediction);

      // Predict program completion
      const programCompletionPrediction = this.predictProgramCompletion(workoutData);
      predictions.push(programCompletionPrediction);

      // Predict engagement
      const engagementPrediction = this.predictEngagement(engagementData);
      predictions.push(engagementPrediction);

      return predictions;
    } catch (error) {
      console.error('Error getting behavior predictions:', error);
      throw error;
    }
  }

  async getChurnPrediction(userId?: string): Promise<ChurnPrediction | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        throw new Error('User not authenticated');
      }

      // Get user data for churn prediction
      const userData = await this.getUserChurnFeatures(targetUserId);
      
      if (!userData) {
        return null;
      }

      // Calculate churn probability using weighted scoring
      const churnScore = this.calculateChurnScore(userData);
      
      const riskLevel = churnScore > 0.7 ? 'high' : churnScore > 0.4 ? 'medium' : 'low';
      
      return {
        user_id: targetUserId,
        churn_probability: churnScore,
        risk_level: riskLevel,
        key_factors: this.getChurnFactors(userData),
        intervention_recommendations: this.getChurnInterventions(churnScore, userData),
        predicted_churn_date: churnScore > 0.5 ? this.predictChurnDate(userData) : undefined,
      };
    } catch (error) {
      console.error('Error getting churn prediction:', error);
      throw error;
    }
  }

  async getRecommendations(
    userId?: string,
    type?: 'exercise' | 'program' | 'goal' | 'nutrition'
  ): Promise<RecommendationPrediction[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        throw new Error('User not authenticated');
      }

      const recommendations: RecommendationPrediction[] = [];

      if (!type || type === 'exercise') {
        const exerciseRecs = await this.getExerciseRecommendations(targetUserId);
        recommendations.push(exerciseRecs);
      }

      if (!type || type === 'program') {
        const programRecs = await this.getProgramRecommendations(targetUserId);
        recommendations.push(programRecs);
      }

      if (!type || type === 'goal') {
        const goalRecs = await this.getGoalRecommendations(targetUserId);
        recommendations.push(goalRecs);
      }

      return recommendations;
    } catch (error) {
      console.error('Error getting recommendations:', error);
      throw error;
    }
  }

  private calculateCurrentMetrics(workoutHistory: any[]): Record<string, number[]> {
    const metrics: Record<string, number[]> = {
      total_volume: [],
      average_weight: [],
      workout_duration: [],
      exercises_completed: [],
    };

    workoutHistory.forEach(workout => {
      // Calculate total volume (sets × reps × weight)
      let totalVolume = 0;
      let totalWeight = 0;
      let weightCount = 0;

      workout.exercise_logs?.forEach((exercise: any) => {
        exercise.sets?.forEach((set: any) => {
          if (set.weight && set.reps) {
            totalVolume += set.weight * set.reps;
            totalWeight += set.weight;
            weightCount++;
          }
        });
      });

      metrics.total_volume.push(totalVolume);
      metrics.average_weight.push(weightCount > 0 ? totalWeight / weightCount : 0);
      metrics.workout_duration.push(workout.duration || 0);
      metrics.exercises_completed.push(workout.exercise_logs?.length || 0);
    });

    return metrics;
  }

  private predictLinearTrend(values: number[], timeframe: string): {
    value: number;
    date: string;
    confidence: number;
  } {
    if (values.length < 2) {
      return {
        value: values[0] || 0,
        date: new Date().toISOString(),
        confidence: 0.1,
      };
    }

    // Simple linear regression
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Predict future value
    const timeMultiplier = {
      '1week': 1,
      '1month': 4,
      '3months': 12,
      '6months': 24,
    }[timeframe] || 4;

    const futureX = n + timeMultiplier;
    const predictedValue = slope * futureX + intercept;

    // Calculate confidence based on R-squared
    const yMean = sumY / n;
    const ssRes = y.reduce((sum, yi, i) => {
      const predicted = slope * x[i] + intercept;
      return sum + Math.pow(yi - predicted, 2);
    }, 0);
    const ssTot = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
    const rSquared = 1 - (ssRes / ssTot);
    const confidence = Math.max(0.1, Math.min(0.95, rSquared));

    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + timeMultiplier * 7);

    return {
      value: Math.max(0, predictedValue),
      date: futureDate.toISOString(),
      confidence,
    };
  }

  private getProgressFactors(metric: string, workoutHistory: any[]) {
    const factors = [];

    switch (metric) {
      case 'total_volume':
        factors.push(
          { name: 'Workout Frequency', impact: 0.4, description: 'More frequent workouts increase volume' },
          { name: 'Progressive Overload', impact: 0.3, description: 'Gradually increasing weights' },
          { name: 'Exercise Selection', impact: 0.2, description: 'Compound movements boost volume' },
          { name: 'Recovery', impact: 0.1, description: 'Adequate rest improves performance' }
        );
        break;
      case 'average_weight':
        factors.push(
          { name: 'Progressive Overload', impact: 0.5, description: 'Systematic weight increases' },
          { name: 'Nutrition', impact: 0.2, description: 'Proper nutrition supports strength gains' },
          { name: 'Sleep Quality', impact: 0.2, description: 'Recovery affects strength development' },
          { name: 'Training Consistency', impact: 0.1, description: 'Regular training builds strength' }
        );
        break;
      default:
        factors.push(
          { name: 'Consistency', impact: 0.4, description: 'Regular training improves all metrics' },
          { name: 'Progressive Overload', impact: 0.3, description: 'Gradual increases drive progress' },
          { name: 'Recovery', impact: 0.3, description: 'Rest and nutrition support improvement' }
        );
    }

    return factors;
  }

  private async getUserWorkoutBehavior(userId: string) {
    const { data } = await supabase
      .from('workout_logs')
      .select('completed_at, duration, rating')
      .eq('user_id', userId)
      .order('completed_at', { ascending: false })
      .limit(30);

    return data || [];
  }

  private async getUserEngagementBehavior(userId: string) {
    // This would include app usage, message interactions, etc.
    // For now, return mock data
    return {
      app_sessions_per_week: 5,
      avg_session_duration: 15,
      message_response_rate: 0.8,
      feature_usage: ['workouts', 'progress_tracking'],
    };
  }

  private predictWorkoutFrequency(workoutData: any[]): BehaviorPrediction {
    const recentWorkouts = workoutData.slice(0, 14); // Last 2 weeks
    const workoutsPerWeek = recentWorkouts.length / 2;
    
    // Predict next week's frequency based on trend
    const prediction = Math.max(0, Math.min(7, workoutsPerWeek * 1.1));
    
    return {
      user_id: '',
      behavior_type: 'workout_frequency',
      prediction,
      confidence: recentWorkouts.length >= 5 ? 0.8 : 0.5,
      risk_factors: workoutsPerWeek < 2 ? ['Low workout frequency'] : [],
      recommendations: workoutsPerWeek < 3 ? ['Schedule more regular workouts', 'Set workout reminders'] : [],
    };
  }

  private predictProgramCompletion(workoutData: any[]): BehaviorPrediction {
    const completionRate = workoutData.length > 0 ? 0.85 : 0.5; // Simplified
    
    return {
      user_id: '',
      behavior_type: 'program_completion',
      prediction: completionRate,
      confidence: 0.7,
      risk_factors: completionRate < 0.6 ? ['Low completion rate'] : [],
      recommendations: completionRate < 0.7 ? ['Break workouts into smaller sessions', 'Adjust program difficulty'] : [],
    };
  }

  private predictEngagement(engagementData: any): BehaviorPrediction {
    const engagementScore = (
      engagementData.app_sessions_per_week / 7 * 0.4 +
      engagementData.avg_session_duration / 30 * 0.3 +
      engagementData.message_response_rate * 0.3
    );
    
    return {
      user_id: '',
      behavior_type: 'engagement',
      prediction: Math.min(1, engagementScore),
      confidence: 0.75,
      risk_factors: engagementScore < 0.5 ? ['Low app engagement'] : [],
      recommendations: engagementScore < 0.6 ? ['Enable push notifications', 'Try new features'] : [],
    };
  }

  private async getUserChurnFeatures(userId: string) {
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    const { data: workouts } = await supabase
      .from('workout_logs')
      .select('completed_at')
      .eq('user_id', userId)
      .order('completed_at', { ascending: false })
      .limit(30);

    const { data: profile } = await supabase
      .from('user_profiles')
      .select('created_at')
      .eq('user_id', userId)
      .single();

    return {
      subscription,
      workouts: workouts || [],
      profile,
      days_since_signup: profile ? Math.floor((Date.now() - new Date(profile.created_at).getTime()) / (1000 * 60 * 60 * 24)) : 0,
    };
  }

  private calculateChurnScore(userData: any): number {
    let score = 0;

    // Days since last workout
    const daysSinceLastWorkout = userData.workouts.length > 0 
      ? Math.floor((Date.now() - new Date(userData.workouts[0].completed_at).getTime()) / (1000 * 60 * 60 * 24))
      : 30;
    
    score += Math.min(0.4, daysSinceLastWorkout / 30 * 0.4);

    // Workout frequency decline
    const recentWorkouts = userData.workouts.filter((w: any) => 
      new Date(w.completed_at) > new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
    ).length;
    
    if (recentWorkouts < 2) score += 0.3;
    else if (recentWorkouts < 4) score += 0.15;

    // Subscription status
    if (userData.subscription?.status === 'past_due') score += 0.2;
    if (userData.subscription?.status === 'canceled') score += 0.5;

    // Time since signup (early churn risk)
    if (userData.days_since_signup < 30 && recentWorkouts < 5) score += 0.2;

    return Math.min(1, score);
  }

  private getChurnFactors(userData: any) {
    const factors = [];
    
    const daysSinceLastWorkout = userData.workouts.length > 0 
      ? Math.floor((Date.now() - new Date(userData.workouts[0].completed_at).getTime()) / (1000 * 60 * 60 * 24))
      : 30;

    if (daysSinceLastWorkout > 7) {
      factors.push({ factor: 'Workout Inactivity', weight: 0.4, value: daysSinceLastWorkout });
    }

    const recentWorkouts = userData.workouts.filter((w: any) => 
      new Date(w.completed_at) > new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
    ).length;

    if (recentWorkouts < 3) {
      factors.push({ factor: 'Low Workout Frequency', weight: 0.3, value: recentWorkouts });
    }

    if (userData.subscription?.status === 'past_due') {
      factors.push({ factor: 'Payment Issues', weight: 0.2, value: 1 });
    }

    return factors;
  }

  private getChurnInterventions(churnScore: number, userData: any): string[] {
    const interventions = [];

    if (churnScore > 0.7) {
      interventions.push('Immediate personal outreach from coach');
      interventions.push('Offer program modification or break');
      interventions.push('Provide motivation and goal reassessment');
    } else if (churnScore > 0.4) {
      interventions.push('Send re-engagement email campaign');
      interventions.push('Offer workout variety or new challenges');
      interventions.push('Check in on goals and progress');
    } else {
      interventions.push('Continue regular engagement');
      interventions.push('Provide positive reinforcement');
    }

    return interventions;
  }

  private predictChurnDate(userData: any): string {
    // Simple prediction based on current trend
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 30); // Predict 30 days out
    return futureDate.toISOString();
  }

  private async getExerciseRecommendations(userId: string): Promise<RecommendationPrediction> {
    // This would use collaborative filtering in a real implementation
    const mockRecommendations = [
      { item_id: '1', item_name: 'Bulgarian Split Squats', score: 0.9, reasoning: 'Great for unilateral leg strength' },
      { item_id: '2', item_name: 'Face Pulls', score: 0.85, reasoning: 'Improves posterior deltoid strength' },
      { item_id: '3', item_name: 'Farmer\'s Walks', score: 0.8, reasoning: 'Excellent for grip and core strength' },
    ];

    return {
      user_id: userId,
      recommendation_type: 'exercise',
      recommendations: mockRecommendations,
      confidence: 0.75,
    };
  }

  private async getProgramRecommendations(userId: string): Promise<RecommendationPrediction> {
    const mockRecommendations = [
      { item_id: '1', item_name: 'Strength & Hypertrophy Program', score: 0.9, reasoning: 'Matches your current goals' },
      { item_id: '2', item_name: 'Athletic Performance Program', score: 0.8, reasoning: 'Good progression from current program' },
    ];

    return {
      user_id: userId,
      recommendation_type: 'program',
      recommendations: mockRecommendations,
      confidence: 0.8,
    };
  }

  private async getGoalRecommendations(userId: string): Promise<RecommendationPrediction> {
    const mockRecommendations = [
      { item_id: '1', item_name: 'Increase Bench Press by 20lbs', score: 0.85, reasoning: 'Achievable based on current progress' },
      { item_id: '2', item_name: 'Complete 5K Run', score: 0.7, reasoning: 'Good cardio goal to complement strength training' },
    ];

    return {
      user_id: userId,
      recommendation_type: 'goal',
      recommendations: mockRecommendations,
      confidence: 0.7,
    };
  }
}

export const predictiveAnalyticsService = new PredictiveAnalyticsService();
