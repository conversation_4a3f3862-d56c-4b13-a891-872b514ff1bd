import { supabase } from '@/lib/supabase';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

export interface AnalyticsData {
  revenue: {
    mrr: number;
    arr: number;
    total_revenue: number;
    growth_rate: number;
  };
  subscriptions: {
    active: number;
    trial: number;
    canceled: number;
    churn_rate: number;
    retention_rate: number;
  };
  plans: {
    name: string;
    count: number;
    revenue: number;
    percentage: number;
  }[];
  trends: {
    date: string;
    new_subscriptions: number;
    canceled_subscriptions: number;
    revenue: number;
  }[];
}

export interface ChurnAnalysis {
  overall_churn_rate: number;
  churn_by_plan: {
    plan_name: string;
    churn_rate: number;
    subscribers: number;
    churned: number;
  }[];
  churn_reasons: {
    reason: string;
    count: number;
    percentage: number;
  }[];
  at_risk_users: {
    user_id: string;
    risk_score: number;
    factors: string[];
  }[];
}

export interface RevenueMetrics {
  mrr: number;
  arr: number;
  arpu: number; // Average Revenue Per User
  ltv: number; // Lifetime Value
  cac: number; // Customer Acquisition Cost
  ltv_cac_ratio: number;
  payback_period: number; // months
}

class SubscriptionAnalyticsService {
  async getAnalytics(period: '7d' | '30d' | '90d' | '1y'): Promise<AnalyticsData> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      const [revenueData, subscriptionData, planData, trendsData] = await Promise.all([
        this.getRevenueMetrics(startDate, endDate),
        this.getSubscriptionMetrics(startDate, endDate),
        this.getPlanDistribution(),
        this.getTrends(startDate, endDate),
      ]);

      return {
        revenue: revenueData,
        subscriptions: subscriptionData,
        plans: planData,
        trends: trendsData,
      };
    } catch (error) {
      console.error('Error getting analytics:', error);
      throw error;
    }
  }

  private async getRevenueMetrics(startDate: Date, endDate: Date) {
    // Get current MRR and ARR
    const { data: currentRevenue } = await supabase
      .from('subscription_analytics')
      .select('monthly_recurring_revenue, annual_recurring_revenue')
      .order('date', { ascending: false })
      .limit(1)
      .single();

    // Get total revenue for period
    const { data: periodRevenue } = await supabase
      .from('billing_history')
      .select('amount_paid')
      .eq('status', 'paid')
      .gte('paid_at', startDate.toISOString())
      .lte('paid_at', endDate.toISOString());

    const totalRevenue = periodRevenue?.reduce((sum, item) => sum + (item.amount_paid || 0), 0) || 0;

    // Calculate growth rate (comparing to previous period)
    const previousStartDate = new Date(startDate);
    const previousEndDate = new Date(endDate);
    const periodLength = endDate.getTime() - startDate.getTime();
    previousStartDate.setTime(startDate.getTime() - periodLength);
    previousEndDate.setTime(endDate.getTime() - periodLength);

    const { data: previousRevenue } = await supabase
      .from('billing_history')
      .select('amount_paid')
      .eq('status', 'paid')
      .gte('paid_at', previousStartDate.toISOString())
      .lte('paid_at', previousEndDate.toISOString());

    const previousTotal = previousRevenue?.reduce((sum, item) => sum + (item.amount_paid || 0), 0) || 0;
    const growthRate = previousTotal > 0 ? (totalRevenue - previousTotal) / previousTotal : 0;

    return {
      mrr: currentRevenue?.monthly_recurring_revenue || 0,
      arr: currentRevenue?.annual_recurring_revenue || 0,
      total_revenue: totalRevenue,
      growth_rate: growthRate,
    };
  }

  private async getSubscriptionMetrics(startDate: Date, endDate: Date) {
    // Get current subscription counts
    const { data: subscriptions } = await supabase
      .from('user_subscriptions')
      .select('status, created_at, canceled_at');

    const active = subscriptions?.filter(s => s.status === 'active').length || 0;
    const trial = subscriptions?.filter(s => s.status === 'trialing').length || 0;
    const canceled = subscriptions?.filter(s => 
      s.canceled_at && 
      new Date(s.canceled_at) >= startDate && 
      new Date(s.canceled_at) <= endDate
    ).length || 0;

    // Calculate churn and retention rates
    const totalActive = active + trial;
    const churnRate = totalActive > 0 ? canceled / totalActive : 0;
    const retentionRate = 1 - churnRate;

    return {
      active,
      trial,
      canceled,
      churn_rate: churnRate,
      retention_rate: retentionRate,
    };
  }

  private async getPlanDistribution() {
    const { data: planData } = await supabase
      .from('user_subscriptions')
      .select(`
        plan_id,
        amount,
        subscription_plans!inner(name)
      `)
      .in('status', ['active', 'trialing']);

    const planMap = new Map<string, { count: number; revenue: number }>();
    let totalRevenue = 0;

    planData?.forEach(sub => {
      const planName = sub.subscription_plans.name;
      const revenue = sub.amount || 0;
      
      if (!planMap.has(planName)) {
        planMap.set(planName, { count: 0, revenue: 0 });
      }
      
      const plan = planMap.get(planName)!;
      plan.count += 1;
      plan.revenue += revenue;
      totalRevenue += revenue;
    });

    return Array.from(planMap.entries()).map(([name, data]) => ({
      name,
      count: data.count,
      revenue: data.revenue,
      percentage: totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0,
    }));
  }

  private async getTrends(startDate: Date, endDate: Date) {
    const { data: trendsData } = await supabase
      .from('subscription_analytics')
      .select('date, new_subscriptions, canceled_subscriptions, total_revenue')
      .gte('date', startDate.toISOString().split('T')[0])
      .lte('date', endDate.toISOString().split('T')[0])
      .order('date');

    return trendsData?.map(item => ({
      date: item.date,
      new_subscriptions: item.new_subscriptions || 0,
      canceled_subscriptions: item.canceled_subscriptions || 0,
      revenue: item.total_revenue || 0,
    })) || [];
  }

  async getChurnAnalysis(): Promise<ChurnAnalysis> {
    try {
      // Get overall churn rate
      const { data: churnData } = await supabase.rpc('calculate_churn_rate');
      
      // Get churn by plan
      const { data: planChurn } = await supabase.rpc('get_churn_by_plan');
      
      // Get churn reasons (would need to be collected from cancellation surveys)
      const churnReasons = [
        { reason: 'Too expensive', count: 15, percentage: 30 },
        { reason: 'Not using enough', count: 12, percentage: 24 },
        { reason: 'Found alternative', count: 10, percentage: 20 },
        { reason: 'Technical issues', count: 8, percentage: 16 },
        { reason: 'Other', count: 5, percentage: 10 },
      ];

      // Identify at-risk users (simplified logic)
      const { data: atRiskUsers } = await supabase.rpc('identify_at_risk_users');

      return {
        overall_churn_rate: churnData || 0,
        churn_by_plan: planChurn || [],
        churn_reasons: churnReasons,
        at_risk_users: atRiskUsers || [],
      };
    } catch (error) {
      console.error('Error getting churn analysis:', error);
      throw error;
    }
  }

  async getRevenueMetrics(): Promise<RevenueMetrics> {
    try {
      const { data: metrics } = await supabase.rpc('calculate_revenue_metrics');
      
      return {
        mrr: metrics?.mrr || 0,
        arr: metrics?.arr || 0,
        arpu: metrics?.arpu || 0,
        ltv: metrics?.ltv || 0,
        cac: metrics?.cac || 0,
        ltv_cac_ratio: metrics?.ltv_cac_ratio || 0,
        payback_period: metrics?.payback_period || 0,
      };
    } catch (error) {
      console.error('Error getting revenue metrics:', error);
      throw error;
    }
  }

  async getCohortAnalysis(cohortType: 'monthly' | 'weekly' = 'monthly') {
    try {
      const { data: cohortData } = await supabase.rpc('get_cohort_analysis', {
        cohort_type: cohortType,
      });

      return cohortData || [];
    } catch (error) {
      console.error('Error getting cohort analysis:', error);
      throw error;
    }
  }

  async getSubscriptionForecast(months: number = 12) {
    try {
      // Simple linear regression forecast based on historical data
      const { data: historicalData } = await supabase
        .from('subscription_analytics')
        .select('date, new_subscriptions, canceled_subscriptions, monthly_recurring_revenue')
        .order('date')
        .limit(90); // Last 90 days

      if (!historicalData || historicalData.length < 30) {
        throw new Error('Insufficient data for forecasting');
      }

      // Calculate trends
      const avgNewSubs = historicalData.reduce((sum, item) => sum + (item.new_subscriptions || 0), 0) / historicalData.length;
      const avgCanceled = historicalData.reduce((sum, item) => sum + (item.canceled_subscriptions || 0), 0) / historicalData.length;
      const avgMRR = historicalData.reduce((sum, item) => sum + (item.monthly_recurring_revenue || 0), 0) / historicalData.length;

      // Generate forecast
      const forecast = [];
      let currentMRR = avgMRR;
      
      for (let i = 1; i <= months; i++) {
        const netGrowth = avgNewSubs - avgCanceled;
        currentMRR += netGrowth * 20; // Assuming $20 average per new subscription
        
        forecast.push({
          month: i,
          projected_mrr: currentMRR,
          projected_new_subs: avgNewSubs,
          projected_churn: avgCanceled,
          confidence: Math.max(0.5, 1 - (i * 0.05)), // Decreasing confidence over time
        });
      }

      return forecast;
    } catch (error) {
      console.error('Error generating forecast:', error);
      throw error;
    }
  }

  async exportData(period: string, format: 'csv' | 'json' = 'csv') {
    try {
      const analyticsData = await this.getAnalytics(period as any);
      
      let content: string;
      let filename: string;
      let mimeType: string;

      if (format === 'csv') {
        content = this.convertToCSV(analyticsData);
        filename = `subscription_analytics_${period}_${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
      } else {
        content = JSON.stringify(analyticsData, null, 2);
        filename = `subscription_analytics_${period}_${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      }

      const fileUri = FileSystem.documentDirectory + filename;
      await FileSystem.writeAsStringAsync(fileUri, content);

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType,
          dialogTitle: 'Export Subscription Analytics',
        });
      }

      return fileUri;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  private convertToCSV(data: AnalyticsData): string {
    const lines: string[] = [];
    
    // Revenue metrics
    lines.push('Revenue Metrics');
    lines.push('Metric,Value');
    lines.push(`MRR,${data.revenue.mrr}`);
    lines.push(`ARR,${data.revenue.arr}`);
    lines.push(`Total Revenue,${data.revenue.total_revenue}`);
    lines.push(`Growth Rate,${data.revenue.growth_rate}`);
    lines.push('');

    // Subscription metrics
    lines.push('Subscription Metrics');
    lines.push('Metric,Value');
    lines.push(`Active Subscriptions,${data.subscriptions.active}`);
    lines.push(`Trial Subscriptions,${data.subscriptions.trial}`);
    lines.push(`Canceled Subscriptions,${data.subscriptions.canceled}`);
    lines.push(`Churn Rate,${data.subscriptions.churn_rate}`);
    lines.push(`Retention Rate,${data.subscriptions.retention_rate}`);
    lines.push('');

    // Plan distribution
    lines.push('Plan Distribution');
    lines.push('Plan,Count,Revenue,Percentage');
    data.plans.forEach(plan => {
      lines.push(`${plan.name},${plan.count},${plan.revenue},${plan.percentage}`);
    });
    lines.push('');

    // Trends
    lines.push('Trends');
    lines.push('Date,New Subscriptions,Canceled Subscriptions,Revenue');
    data.trends.forEach(trend => {
      lines.push(`${trend.date},${trend.new_subscriptions},${trend.canceled_subscriptions},${trend.revenue}`);
    });

    return lines.join('\n');
  }

  async getSubscriptionHealth(): Promise<{
    score: number;
    factors: { name: string; score: number; weight: number }[];
    recommendations: string[];
  }> {
    try {
      const analytics = await this.getAnalytics('30d');
      
      const factors = [
        {
          name: 'Churn Rate',
          score: Math.max(0, 100 - (analytics.subscriptions.churn_rate * 1000)), // Lower churn = higher score
          weight: 0.3,
        },
        {
          name: 'Growth Rate',
          score: Math.min(100, Math.max(0, analytics.revenue.growth_rate * 100 + 50)), // Positive growth = higher score
          weight: 0.25,
        },
        {
          name: 'Trial Conversion',
          score: analytics.subscriptions.trial > 0 ? 75 : 50, // Having trials is good
          weight: 0.2,
        },
        {
          name: 'Plan Diversity',
          score: Math.min(100, analytics.plans.length * 25), // More plans = better
          weight: 0.15,
        },
        {
          name: 'Revenue Stability',
          score: analytics.revenue.mrr > 0 ? 80 : 20, // Having MRR is good
          weight: 0.1,
        },
      ];

      const overallScore = factors.reduce((sum, factor) => sum + (factor.score * factor.weight), 0);

      const recommendations = [];
      if (analytics.subscriptions.churn_rate > 0.05) {
        recommendations.push('Focus on reducing churn rate through better onboarding and engagement');
      }
      if (analytics.revenue.growth_rate < 0) {
        recommendations.push('Implement growth strategies to increase new subscriptions');
      }
      if (analytics.plans.length < 3) {
        recommendations.push('Consider adding more subscription tiers to capture different customer segments');
      }

      return {
        score: Math.round(overallScore),
        factors,
        recommendations,
      };
    } catch (error) {
      console.error('Error calculating subscription health:', error);
      throw error;
    }
  }
}

export const subscriptionAnalyticsService = new SubscriptionAnalyticsService();
