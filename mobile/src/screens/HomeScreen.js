import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const ServiceCard = ({ title, description, icon, onPress, status = 'ready' }) => (
  <TouchableOpacity style={styles.card} onPress={onPress}>
    <View style={styles.cardHeader}>
      <Ionicons name={icon} size={24} color="#2563eb" />
      <View style={styles.statusBadge}>
        <Text style={[styles.statusText, { color: status === 'ready' ? '#10b981' : '#f59e0b' }]}>
          {status === 'ready' ? '✓ Ready' : '⚠ Demo'}
        </Text>
      </View>
    </View>
    <Text style={styles.cardTitle}>{title}</Text>
    <Text style={styles.cardDescription}>{description}</Text>
  </TouchableOpacity>
);

export default function HomeScreen({ navigation }) {
  const services = [
    {
      title: 'AI Workout Generation',
      description: 'Advanced AI-powered workout creation with personalized scoring algorithms',
      icon: 'fitness-outline',
      screen: 'Workout AI',
      status: 'ready'
    },
    {
      title: 'Predictive Analytics',
      description: 'Progress predictions, churn analysis, and behavior forecasting',
      icon: 'analytics-outline',
      screen: 'Analytics',
      status: 'ready'
    },
    {
      title: 'Social Platform',
      description: 'Complete social system with challenges, sharing, and community features',
      icon: 'people-outline',
      screen: 'Social',
      status: 'ready'
    },
    {
      title: 'Premium Subscriptions',
      description: 'Full Stripe integration with feature access control and billing',
      icon: 'diamond-outline',
      screen: 'Premium',
      status: 'ready'
    },
  ];

  const handleServicePress = (service) => {
    if (service.screen) {
      navigation.navigate(service.screen);
    } else {
      Alert.alert('Service Demo', `${service.title} service is ready for testing!`);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🏋️‍♂️ AI Fitness Coach</Text>
        <Text style={styles.subtitle}>Enterprise-Grade Services Demo</Text>
        <Text style={styles.description}>
          Explore the sophisticated services built for the mobile platform
        </Text>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>14</Text>
          <Text style={styles.statLabel}>Services</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>5</Text>
          <Text style={styles.statLabel}>AI Features</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>100%</Text>
          <Text style={styles.statLabel}>TypeScript</Text>
        </View>
      </View>

      <Text style={styles.sectionTitle}>Core Services</Text>
      
      {services.map((service, index) => (
        <ServiceCard
          key={index}
          title={service.title}
          description={service.description}
          icon={service.icon}
          status={service.status}
          onPress={() => handleServicePress(service)}
        />
      ))}

      <View style={styles.additionalServices}>
        <Text style={styles.sectionTitle}>Additional Services</Text>
        <Text style={styles.serviceList}>
          • Comparative Analytics Service{'\n'}
          • Trend Analysis Service{'\n'}
          • Leaderboard Service{'\n'}
          • Promo Code Service{'\n'}
          • Workout Share Service{'\n'}
          • User Profile Service{'\n'}
          • Subscription Analytics Service
        </Text>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          All services are fully implemented with comprehensive TypeScript interfaces and business logic.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2563eb',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 20,
    marginBottom: 30,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2563eb',
  },
  statLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginHorizontal: 20,
    marginBottom: 16,
    marginTop: 8,
  },
  card: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f3f4f6',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  additionalServices: {
    marginHorizontal: 20,
    marginTop: 20,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  serviceList: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 24,
  },
  footer: {
    margin: 20,
    padding: 16,
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2563eb',
  },
  footerText: {
    fontSize: 14,
    color: '#1e40af',
    fontStyle: 'italic',
  },
});