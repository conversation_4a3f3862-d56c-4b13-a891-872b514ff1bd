import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const PlanCard = ({ plan, isPopular, onSelect, isSelected }) => (
  <TouchableOpacity 
    style={[styles.planCard, isSelected && styles.selectedPlan, isPopular && styles.popularPlan]}
    onPress={() => onSelect(plan.id)}
  >
    {isPopular && (
      <View style={styles.popularBadge}>
        <Text style={styles.popularBadgeText}>MOST POPULAR</Text>
      </View>
    )}
    
    <Text style={styles.planName}>{plan.name}</Text>
    <View style={styles.priceContainer}>
      <Text style={styles.price}>${plan.price}</Text>
      <Text style={styles.priceUnit}>/{plan.interval}</Text>
    </View>
    
    <Text style={styles.planDescription}>{plan.description}</Text>
    
    <View style={styles.featuresContainer}>
      {plan.features.map((feature, index) => (
        <View key={index} style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={16} color="#10b981" />
          <Text style={styles.featureText}>{feature}</Text>
        </View>
      ))}
    </View>
    
    {plan.limits && (
      <View style={styles.limitsContainer}>
        <Text style={styles.limitsTitle}>Usage Limits:</Text>
        {Object.entries(plan.limits).map(([key, value]) => (
          <Text key={key} style={styles.limitText}>
            • {key.replace(/_/g, ' ')}: {value === -1 ? 'Unlimited' : value}
          </Text>
        ))}
      </View>
    )}
  </TouchableOpacity>
);

const FeatureComparisonRow = ({ feature, free, basic, pro, elite }) => (
  <View style={styles.comparisonRow}>
    <Text style={styles.featureName}>{feature}</Text>
    <View style={styles.comparisonValues}>
      <Text style={styles.comparisonValue}>{free}</Text>
      <Text style={styles.comparisonValue}>{basic}</Text>
      <Text style={styles.comparisonValue}>{pro}</Text>
      <Text style={styles.comparisonValue}>{elite}</Text>
    </View>
  </View>
);

const UsageCard = ({ title, current, limit, icon, color }) => (
  <View style={styles.usageCard}>
    <View style={styles.usageHeader}>
      <Ionicons name={icon} size={24} color={color} />
      <Text style={styles.usageTitle}>{title}</Text>
    </View>
    
    <View style={styles.usageProgress}>
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill, 
            { 
              width: `${Math.min(100, (current / limit) * 100)}%`,
              backgroundColor: color 
            }
          ]} 
        />
      </View>
      <Text style={styles.usageText}>
        {current} / {limit === -1 ? '∞' : limit}
      </Text>
    </View>
  </View>
);

export default function SubscriptionScreen() {
  const [selectedPlan, setSelectedPlan] = useState('pro');
  const [isYearly, setIsYearly] = useState(false);
  const [activeTab, setActiveTab] = useState('plans');

  const plans = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      interval: 'forever',
      description: 'Perfect for getting started with basic workouts',
      features: [
        'Basic workout generation',
        '1 custom program',
        '10 workouts per month',
        'Basic progress tracking'
      ],
      limits: {
        max_programs: 1,
        max_workouts_per_month: 10,
        max_progress_photos: 5
      }
    },
    {
      id: 'basic',
      name: 'Basic',
      price: isYearly ? 96 : 9.99,
      interval: isYearly ? 'year' : 'month',
      description: 'Great for regular fitness enthusiasts',
      features: [
        'Unlimited workouts',
        '5 custom programs',
        'Video demonstrations',
        'Offline access',
        'Basic analytics'
      ],
      limits: {
        max_programs: 5,
        max_workouts_per_month: -1,
        max_progress_photos: 25
      }
    },
    {
      id: 'pro',
      name: 'Pro',
      price: isYearly ? 192 : 19.99,
      interval: isYearly ? 'year' : 'month',
      description: 'Advanced features for serious athletes',
      features: [
        'Everything in Basic',
        'AI coaching insights',
        'Advanced analytics',
        'Predictive modeling',
        'Nutrition planning',
        'Priority support'
      ],
      limits: {
        max_programs: -1,
        max_workouts_per_month: -1,
        max_progress_photos: -1
      }
    },
    {
      id: 'elite',
      name: 'Elite',
      price: isYearly ? 480 : 49.99,
      interval: isYearly ? 'year' : 'month',
      description: 'Premium experience with personal coaching',
      features: [
        'Everything in Pro',
        'Personal coach access',
        'Custom meal planning',
        'White-label features',
        'API access',
        'Dedicated support'
      ],
      limits: {
        max_programs: -1,
        max_workouts_per_month: -1,
        max_progress_photos: -1
      }
    }
  ];

  const currentUsage = {
    programs: { current: 3, limit: 5 },
    workouts: { current: 18, limit: -1 },
    photos: { current: 12, limit: 25 }
  };

  const handleSelectPlan = (planId) => {
    setSelectedPlan(planId);
  };

  const handleSubscribe = () => {
    const plan = plans.find(p => p.id === selectedPlan);
    Alert.alert(
      'Subscription Service Ready',
      `Selected: ${plan.name} Plan\n\nStripe integration includes:\n• Payment processing\n• Subscription management\n• Billing history\n• Promo code support\n• Feature access control`,
      [{ text: 'OK' }]
    );
  };

  const handleManageBilling = () => {
    Alert.alert(
      'Billing Management',
      'Full billing management available:\n• View payment history\n• Update payment methods\n• Download invoices\n• Manage subscriptions',
      [{ text: 'OK' }]
    );
  };

  const renderPlansTab = () => (
    <ScrollView>
      <View style={styles.toggleContainer}>
        <Text style={[styles.toggleText, !isYearly && styles.activeToggleText]}>Monthly</Text>
        <Switch
          value={isYearly}
          onValueChange={setIsYearly}
          trackColor={{ false: '#e5e7eb', true: '#2563eb' }}
          thumbColor={isYearly ? '#ffffff' : '#ffffff'}
        />
        <Text style={[styles.toggleText, isYearly && styles.activeToggleText]}>
          Yearly (Save 20%)
        </Text>
      </View>

      <View style={styles.plansContainer}>
        {plans.map((plan, index) => (
          <PlanCard
            key={plan.id}
            plan={plan}
            isPopular={plan.id === 'pro'}
            onSelect={handleSelectPlan}
            isSelected={selectedPlan === plan.id}
          />
        ))}
      </View>

      <TouchableOpacity style={styles.subscribeButton} onPress={handleSubscribe}>
        <Text style={styles.subscribeButtonText}>
          {selectedPlan === 'free' ? 'Continue with Free' : `Subscribe to ${plans.find(p => p.id === selectedPlan)?.name}`}
        </Text>
      </TouchableOpacity>

      <View style={styles.guaranteeContainer}>
        <Ionicons name="shield-checkmark" size={24} color="#10b981" />
        <Text style={styles.guaranteeText}>
          30-day money-back guarantee • Cancel anytime • Secure payments
        </Text>
      </View>
    </ScrollView>
  );

  const renderUsageTab = () => (
    <ScrollView>
      <Text style={styles.sectionTitle}>Current Usage</Text>
      
      <UsageCard
        title="Custom Programs"
        current={currentUsage.programs.current}
        limit={currentUsage.programs.limit}
        icon="fitness-outline"
        color="#2563eb"
      />
      
      <UsageCard
        title="Monthly Workouts"
        current={currentUsage.workouts.current}
        limit={currentUsage.workouts.limit}
        icon="calendar-outline"
        color="#10b981"
      />
      
      <UsageCard
        title="Progress Photos"
        current={currentUsage.photos.current}
        limit={currentUsage.photos.limit}
        icon="camera-outline"
        color="#f59e0b"
      />

      <View style={styles.upgradePrompt}>
        <Text style={styles.upgradeTitle}>Need More?</Text>
        <Text style={styles.upgradeDescription}>
          Upgrade to Pro for unlimited access to all features
        </Text>
        <TouchableOpacity style={styles.upgradeButton} onPress={() => setActiveTab('plans')}>
          <Text style={styles.upgradeButtonText}>View Plans</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderFeaturesTab = () => (
    <ScrollView>
      <Text style={styles.sectionTitle}>Feature Comparison</Text>
      
      <View style={styles.comparisonTable}>
        <View style={styles.comparisonHeader}>
          <Text style={styles.comparisonHeaderText}>Feature</Text>
          <View style={styles.comparisonHeaderPlans}>
            <Text style={styles.planHeaderText}>Free</Text>
            <Text style={styles.planHeaderText}>Basic</Text>
            <Text style={styles.planHeaderText}>Pro</Text>
            <Text style={styles.planHeaderText}>Elite</Text>
          </View>
        </View>

        <FeatureComparisonRow
          feature="AI Workout Generation"
          free="Basic"
          basic="Advanced"
          pro="Expert"
          elite="Custom"
        />
        
        <FeatureComparisonRow
          feature="Custom Programs"
          free="1"
          basic="5"
          pro="Unlimited"
          elite="Unlimited"
        />
        
        <FeatureComparisonRow
          feature="Analytics"
          free="Basic"
          basic="Standard"
          pro="Advanced"
          elite="Premium"
        />
        
        <FeatureComparisonRow
          feature="Predictive Insights"
          free="✗"
          basic="✗"
          pro="✓"
          elite="✓"
        />
        
        <FeatureComparisonRow
          feature="Personal Coach"
          free="✗"
          basic="✗"
          pro="✗"
          elite="✓"
        />
      </View>

      <View style={styles.premiumFeatures}>
        <Text style={styles.premiumTitle}>🚀 Premium Features</Text>
        <Text style={styles.premiumDescription}>
          • Advanced AI algorithms with 5-factor scoring{'\n'}
          • Predictive analytics and churn prevention{'\n'}
          • Comparative benchmarking with peer groups{'\n'}
          • Social challenges and leaderboards{'\n'}
          • Comprehensive Stripe payment integration{'\n'}
          • Enterprise-grade subscription management
        </Text>
      </View>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'plans' && styles.activeTab]}
          onPress={() => setActiveTab('plans')}
        >
          <Text style={[styles.tabText, activeTab === 'plans' && styles.activeTabText]}>
            Plans
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'usage' && styles.activeTab]}
          onPress={() => setActiveTab('usage')}
        >
          <Text style={[styles.tabText, activeTab === 'usage' && styles.activeTabText]}>
            Usage
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'features' && styles.activeTab]}
          onPress={() => setActiveTab('features')}
        >
          <Text style={[styles.tabText, activeTab === 'features' && styles.activeTabText]}>
            Features
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {activeTab === 'plans' && renderPlansTab()}
        {activeTab === 'usage' && renderUsageTab()}
        {activeTab === 'features' && renderFeaturesTab()}
      </View>

      <TouchableOpacity style={styles.billingButton} onPress={handleManageBilling}>
        <Ionicons name="card-outline" size={20} color="#2563eb" />
        <Text style={styles.billingButtonText}>Manage Billing</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#2563eb',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  activeTabText: {
    color: '#2563eb',
  },
  content: {
    flex: 1,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  toggleText: {
    fontSize: 16,
    color: '#6b7280',
    marginHorizontal: 12,
  },
  activeToggleText: {
    color: '#2563eb',
    fontWeight: '600',
  },
  plansContainer: {
    paddingHorizontal: 20,
  },
  planCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedPlan: {
    borderColor: '#2563eb',
  },
  popularPlan: {
    borderColor: '#10b981',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    backgroundColor: '#10b981',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 12,
  },
  price: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2563eb',
  },
  priceUnit: {
    fontSize: 16,
    color: '#6b7280',
    marginLeft: 4,
  },
  planDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
  },
  featuresContainer: {
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#1f2937',
    marginLeft: 8,
  },
  limitsContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
  },
  limitsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  limitText: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  subscribeButton: {
    backgroundColor: '#2563eb',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  guaranteeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  guaranteeText: {
    fontSize: 12,
    color: '#6b7280',
    marginLeft: 8,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    margin: 20,
    marginBottom: 16,
  },
  usageCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  usageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  usageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginLeft: 12,
  },
  usageProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  usageText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  upgradePrompt: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  upgradeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  upgradeDescription: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 16,
  },
  upgradeButton: {
    backgroundColor: '#2563eb',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  upgradeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  comparisonTable: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  comparisonHeader: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  comparisonHeaderText: {
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  comparisonHeaderPlans: {
    flexDirection: 'row',
    flex: 2,
  },
  planHeaderText: {
    flex: 1,
    fontSize: 12,
    fontWeight: '600',
    color: '#2563eb',
    textAlign: 'center',
  },
  comparisonRow: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  featureName: {
    flex: 1,
    fontSize: 14,
    color: '#1f2937',
  },
  comparisonValues: {
    flexDirection: 'row',
    flex: 2,
  },
  comparisonValue: {
    flex: 1,
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  premiumFeatures: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 20,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  premiumTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  premiumDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  billingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#2563eb',
  },
  billingButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2563eb',
    marginLeft: 8,
  },
});