import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { render, createMockNavigation } from '@test/utils';
import { LoginScreen } from '../LoginScreen';
import { authService } from '@/services/authService';

// Mock the auth service
jest.mock('@/services/authService');
const mockAuthService = authService as jest.Mocked<typeof authService>;

describe('LoginScreen', () => {
  const mockNavigation = createMockNavigation();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form correctly', () => {
    const { getByPlaceholderText, getByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    expect(getByPlaceholderText('Email')).toBeTruthy();
    expect(getByPlaceholderText('Password')).toBeTruthy();
    expect(getByText('Sign In')).toBeTruthy();
    expect(getByText('Don\'t have an account? Sign Up')).toBeTruthy();
  });

  it('validates email format', async () => {
    const { getByPlaceholderText, getByText, findByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const emailInput = getByPlaceholderText('Email');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, 'invalid-email');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(findByText('Please enter a valid email address')).toBeTruthy();
    });
  });

  it('validates password requirement', async () => {
    const { getByPlaceholderText, getByText, findByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const emailInput = getByPlaceholderText('Email');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(findByText('Password is required')).toBeTruthy();
    });
  });

  it('submits form with valid credentials', async () => {
    mockAuthService.signIn.mockResolvedValue({
      data: { user: { id: '1', email: '<EMAIL>' } },
      error: null,
    });

    const { getByPlaceholderText, getByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const emailInput = getByPlaceholderText('Email');
    const passwordInput = getByPlaceholderText('Password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(mockAuthService.signIn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  it('shows error message on login failure', async () => {
    mockAuthService.signIn.mockResolvedValue({
      data: null,
      error: { message: 'Invalid credentials' },
    });

    const { getByPlaceholderText, getByText, findByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const emailInput = getByPlaceholderText('Email');
    const passwordInput = getByPlaceholderText('Password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'wrongpassword');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(findByText('Invalid credentials')).toBeTruthy();
    });
  });

  it('shows loading state during login', async () => {
    mockAuthService.signIn.mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({
        data: { user: { id: '1', email: '<EMAIL>' } },
        error: null,
      }), 1000))
    );

    const { getByPlaceholderText, getByText, getByTestId } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const emailInput = getByPlaceholderText('Email');
    const passwordInput = getByPlaceholderText('Password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);

    expect(getByTestId('button-loading')).toBeTruthy();
  });

  it('navigates to register screen when sign up link is pressed', () => {
    const { getByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const signUpLink = getByText('Don\'t have an account? Sign Up');
    fireEvent.press(signUpLink);

    expect(mockNavigation.navigate).toHaveBeenCalledWith('Register');
  });

  it('navigates to forgot password screen', () => {
    const { getByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const forgotPasswordLink = getByText('Forgot Password?');
    fireEvent.press(forgotPasswordLink);

    expect(mockNavigation.navigate).toHaveBeenCalledWith('ForgotPassword');
  });

  it('toggles password visibility', () => {
    const { getByPlaceholderText, getByTestId } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const passwordInput = getByPlaceholderText('Password');
    const toggleButton = getByTestId('password-toggle');

    expect(passwordInput.props.secureTextEntry).toBe(true);

    fireEvent.press(toggleButton);

    expect(passwordInput.props.secureTextEntry).toBe(false);
  });

  it('handles social login', async () => {
    mockAuthService.signInWithGoogle.mockResolvedValue({
      data: { user: { id: '1', email: '<EMAIL>' } },
      error: null,
    });

    const { getByText } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const googleButton = getByText('Continue with Google');
    fireEvent.press(googleButton);

    await waitFor(() => {
      expect(mockAuthService.signInWithGoogle).toHaveBeenCalled();
    });
  });

  it('remembers user preference', () => {
    const { getByTestId } = render(
      <LoginScreen navigation={mockNavigation} route={{ key: 'login', name: 'Login' }} />
    );

    const rememberMeCheckbox = getByTestId('remember-me-checkbox');
    fireEvent.press(rememberMeCheckbox);

    // Check that the checkbox state changed
    expect(rememberMeCheckbox.props.accessibilityState.checked).toBe(true);
  });
});
