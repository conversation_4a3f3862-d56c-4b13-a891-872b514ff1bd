import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const WorkoutShareCard = ({ share, onLike, onComment }) => (
  <View style={styles.shareCard}>
    <View style={styles.shareHeader}>
      <View style={styles.userInfo}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>{share.user.name.charAt(0)}</Text>
        </View>
        <View>
          <Text style={styles.userName}>{share.user.name}</Text>
          <Text style={styles.shareTime}>{share.timeAgo}</Text>
        </View>
      </View>
      <TouchableOpacity>
        <Ionicons name="ellipsis-horizontal" size={20} color="#6b7280" />
      </TouchableOpacity>
    </View>
    
    <Text style={styles.shareTitle}>{share.title}</Text>
    <Text style={styles.shareDescription}>{share.description}</Text>
    
    <View style={styles.workoutSummary}>
      <View style={styles.summaryItem}>
        <Text style={styles.summaryValue}>{share.workout.duration}min</Text>
        <Text style={styles.summaryLabel}>Duration</Text>
      </View>
      <View style={styles.summaryItem}>
        <Text style={styles.summaryValue}>{share.workout.exercises}</Text>
        <Text style={styles.summaryLabel}>Exercises</Text>
      </View>
      <View style={styles.summaryItem}>
        <Text style={styles.summaryValue}>{share.workout.volume}lbs</Text>
        <Text style={styles.summaryLabel}>Total Volume</Text>
      </View>
    </View>
    
    <View style={styles.shareActions}>
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => onLike(share.id)}
      >
        <Ionicons 
          name={share.isLiked ? "heart" : "heart-outline"} 
          size={20} 
          color={share.isLiked ? "#ef4444" : "#6b7280"} 
        />
        <Text style={styles.actionText}>{share.likes}</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => onComment(share.id)}
      >
        <Ionicons name="chatbubble-outline" size={20} color="#6b7280" />
        <Text style={styles.actionText}>{share.comments}</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton}>
        <Ionicons name="share-outline" size={20} color="#6b7280" />
        <Text style={styles.actionText}>Share</Text>
      </TouchableOpacity>
    </View>
  </View>
);

const ChallengeCard = ({ challenge, onJoin }) => (
  <View style={styles.challengeCard}>
    <View style={styles.challengeHeader}>
      <Text style={styles.challengeTitle}>{challenge.title}</Text>
      <View style={[styles.challengeBadge, { backgroundColor: challenge.color }]}>
        <Text style={styles.challengeBadgeText}>{challenge.type}</Text>
      </View>
    </View>
    
    <Text style={styles.challengeDescription}>{challenge.description}</Text>
    
    <View style={styles.challengeStats}>
      <View style={styles.challengeStat}>
        <Text style={styles.challengeStatValue}>{challenge.participants}</Text>
        <Text style={styles.challengeStatLabel}>Participants</Text>
      </View>
      <View style={styles.challengeStat}>
        <Text style={styles.challengeStatValue}>{challenge.daysLeft}</Text>
        <Text style={styles.challengeStatLabel}>Days Left</Text>
      </View>
      <View style={styles.challengeStat}>
        <Text style={styles.challengeStatValue}>{challenge.target}</Text>
        <Text style={styles.challengeStatLabel}>Target</Text>
      </View>
    </View>
    
    <TouchableOpacity 
      style={[styles.joinButton, challenge.joined && styles.joinedButton]}
      onPress={() => onJoin(challenge.id)}
    >
      <Text style={[styles.joinButtonText, challenge.joined && styles.joinedButtonText]}>
        {challenge.joined ? 'Joined' : 'Join Challenge'}
      </Text>
    </TouchableOpacity>
  </View>
);

const LeaderboardItem = ({ item, rank }) => (
  <View style={styles.leaderboardItem}>
    <View style={styles.rankContainer}>
      <Text style={styles.rankNumber}>{rank}</Text>
      {rank <= 3 && (
        <Ionicons 
          name="trophy" 
          size={16} 
          color={rank === 1 ? "#fbbf24" : rank === 2 ? "#9ca3af" : "#cd7c2f"} 
        />
      )}
    </View>
    
    <View style={styles.leaderboardAvatar}>
      <Text style={styles.leaderboardAvatarText}>{item.name.charAt(0)}</Text>
    </View>
    
    <View style={styles.leaderboardInfo}>
      <Text style={styles.leaderboardName}>{item.name}</Text>
      <Text style={styles.leaderboardScore}>{item.score} {item.unit}</Text>
    </View>
    
    <TouchableOpacity style={styles.followButton}>
      <Text style={styles.followButtonText}>Follow</Text>
    </TouchableOpacity>
  </View>
);

export default function SocialScreen() {
  const [activeTab, setActiveTab] = useState('feed');
  const [commentText, setCommentText] = useState('');

  const mockShares = [
    {
      id: 1,
      user: { name: 'Sarah Johnson', avatar: 'SJ' },
      title: 'Crushed my PR today! 💪',
      description: 'Finally hit that 200lb deadlift I\'ve been working towards. The AI workout program really helped me progress systematically!',
      timeAgo: '2 hours ago',
      workout: { duration: 65, exercises: 6, volume: 8500 },
      likes: 24,
      comments: 8,
      isLiked: false
    },
    {
      id: 2,
      user: { name: 'Mike Chen', avatar: 'MC' },
      title: 'Morning cardio session ✅',
      description: 'Started the day with a solid HIIT workout. The predictive analytics suggested I focus on endurance this week.',
      timeAgo: '4 hours ago',
      workout: { duration: 45, exercises: 8, volume: 0 },
      likes: 15,
      comments: 3,
      isLiked: true
    }
  ];

  const mockChallenges = [
    {
      id: 1,
      title: '30-Day Consistency Challenge',
      description: 'Complete at least 3 workouts per week for 30 days',
      type: 'Frequency',
      color: '#10b981',
      participants: 1247,
      daysLeft: 18,
      target: '12 workouts',
      joined: false
    },
    {
      id: 2,
      title: 'Volume Beast Challenge',
      description: 'Lift a total of 50,000 lbs this month',
      type: 'Volume',
      color: '#2563eb',
      participants: 892,
      daysLeft: 12,
      target: '50K lbs',
      joined: true
    }
  ];

  const mockLeaderboard = [
    { name: 'Alex Rodriguez', score: 52340, unit: 'lbs' },
    { name: 'Emma Wilson', score: 48920, unit: 'lbs' },
    { name: 'David Kim', score: 45680, unit: 'lbs' },
    { name: 'Lisa Thompson', score: 43210, unit: 'lbs' },
    { name: 'You', score: 38750, unit: 'lbs' }
  ];

  const handleLike = (shareId) => {
    Alert.alert('Like Action', `Liked share ${shareId} - Social service integration ready!`);
  };

  const handleComment = (shareId) => {
    Alert.alert('Comment Action', `Comment on share ${shareId} - Social service integration ready!`);
  };

  const handleJoinChallenge = (challengeId) => {
    Alert.alert('Join Challenge', `Joining challenge ${challengeId} - Challenge service integration ready!`);
  };

  const renderFeedTab = () => (
    <ScrollView>
      <Text style={styles.sectionTitle}>Workout Feed</Text>
      {mockShares.map((share) => (
        <WorkoutShareCard
          key={share.id}
          share={share}
          onLike={handleLike}
          onComment={handleComment}
        />
      ))}
      
      <View style={styles.createPostCard}>
        <Text style={styles.createPostTitle}>Share Your Workout</Text>
        <TextInput
          style={styles.createPostInput}
          placeholder="How was your workout today?"
          multiline
          numberOfLines={3}
        />
        <TouchableOpacity style={styles.createPostButton}>
          <Ionicons name="camera-outline" size={20} color="#2563eb" />
          <Text style={styles.createPostButtonText}>Add Photo & Share</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderChallengesTab = () => (
    <ScrollView>
      <Text style={styles.sectionTitle}>Active Challenges</Text>
      {mockChallenges.map((challenge) => (
        <ChallengeCard
          key={challenge.id}
          challenge={challenge}
          onJoin={handleJoinChallenge}
        />
      ))}
      
      <TouchableOpacity style={styles.createChallengeButton}>
        <Ionicons name="add-circle-outline" size={24} color="#ffffff" />
        <Text style={styles.createChallengeText}>Create New Challenge</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const renderLeaderboardTab = () => (
    <ScrollView>
      <Text style={styles.sectionTitle}>This Week's Volume Leaders</Text>
      <View style={styles.leaderboardContainer}>
        {mockLeaderboard.map((item, index) => (
          <LeaderboardItem
            key={index}
            item={item}
            rank={index + 1}
          />
        ))}
      </View>
      
      <View style={styles.leaderboardInfo}>
        <Text style={styles.leaderboardInfoTitle}>🏆 Multiple Leaderboards Available</Text>
        <Text style={styles.leaderboardInfoText}>
          • Total Volume (Weekly/Monthly){'\n'}
          • Workout Streaks{'\n'}
          • Total Workouts{'\n'}
          • Average Rating{'\n'}
          • Custom Challenges
        </Text>
      </View>
    </ScrollView>
  );

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'feed' && styles.activeTab]}
          onPress={() => setActiveTab('feed')}
        >
          <Ionicons 
            name={activeTab === 'feed' ? 'home' : 'home-outline'} 
            size={20} 
            color={activeTab === 'feed' ? '#2563eb' : '#6b7280'} 
          />
          <Text style={[styles.tabText, activeTab === 'feed' && styles.activeTabText]}>
            Feed
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'challenges' && styles.activeTab]}
          onPress={() => setActiveTab('challenges')}
        >
          <Ionicons 
            name={activeTab === 'challenges' ? 'trophy' : 'trophy-outline'} 
            size={20} 
            color={activeTab === 'challenges' ? '#2563eb' : '#6b7280'} 
          />
          <Text style={[styles.tabText, activeTab === 'challenges' && styles.activeTabText]}>
            Challenges
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'leaderboard' && styles.activeTab]}
          onPress={() => setActiveTab('leaderboard')}
        >
          <Ionicons 
            name={activeTab === 'leaderboard' ? 'podium' : 'podium-outline'} 
            size={20} 
            color={activeTab === 'leaderboard' ? '#2563eb' : '#6b7280'} 
          />
          <Text style={[styles.tabText, activeTab === 'leaderboard' && styles.activeTabText]}>
            Leaderboard
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {activeTab === 'feed' && renderFeedTab()}
        {activeTab === 'challenges' && renderChallengesTab()}
        {activeTab === 'leaderboard' && renderLeaderboardTab()}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: '#eff6ff',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
    marginLeft: 6,
  },
  activeTabText: {
    color: '#2563eb',
  },
  content: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    margin: 20,
    marginBottom: 16,
  },
  shareCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  shareHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2563eb',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  shareTime: {
    fontSize: 12,
    color: '#6b7280',
  },
  shareTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  shareDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  workoutSummary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 16,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2563eb',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  shareActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 6,
  },
  createPostCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  createPostTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  createPostInput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    marginBottom: 12,
    textAlignVertical: 'top',
  },
  createPostButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#eff6ff',
    paddingVertical: 12,
    borderRadius: 8,
  },
  createPostButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2563eb',
    marginLeft: 8,
  },
  challengeCard: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  challengeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  challengeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    flex: 1,
  },
  challengeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  challengeBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  challengeDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
  },
  challengeStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  challengeStat: {
    alignItems: 'center',
  },
  challengeStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  challengeStatLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  joinButton: {
    backgroundColor: '#2563eb',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  joinedButton: {
    backgroundColor: '#10b981',
  },
  joinButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  joinedButtonText: {
    color: '#ffffff',
  },
  createChallengeButton: {
    backgroundColor: '#2563eb',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  createChallengeText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    marginLeft: 8,
  },
  leaderboardContainer: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  rankContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 40,
    marginRight: 12,
  },
  rankNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginRight: 4,
  },
  leaderboardAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6b7280',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  leaderboardAvatarText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  leaderboardInfo: {
    flex: 1,
  },
  leaderboardName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  leaderboardScore: {
    fontSize: 12,
    color: '#6b7280',
  },
  followButton: {
    backgroundColor: '#eff6ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  followButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2563eb',
  },
  leaderboardInfo: {
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 20,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  leaderboardInfoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  leaderboardInfoText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
});