import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const AnalyticsCard = ({ title, value, subtitle, icon, color = '#2563eb' }) => (
  <View style={styles.analyticsCard}>
    <View style={styles.cardHeader}>
      <Ionicons name={icon} size={24} color={color} />
      <Text style={[styles.cardValue, { color }]}>{value}</Text>
    </View>
    <Text style={styles.cardTitle}>{title}</Text>
    <Text style={styles.cardSubtitle}>{subtitle}</Text>
  </View>
);

const PredictionCard = ({ title, prediction, confidence, factors }) => (
  <View style={styles.predictionCard}>
    <Text style={styles.predictionTitle}>{title}</Text>
    <View style={styles.predictionHeader}>
      <Text style={styles.predictionValue}>{prediction}</Text>
      <View style={styles.confidenceBadge}>
        <Text style={styles.confidenceText}>{Math.round(confidence * 100)}% confidence</Text>
      </View>
    </View>
    <Text style={styles.factorsTitle}>Key Factors:</Text>
    {factors.map((factor, index) => (
      <Text key={index} style={styles.factorText}>• {factor.name} ({Math.round(factor.impact * 100)}%)</Text>
    ))}
  </View>
);

export default function AnalyticsScreen() {
  const [activeTab, setActiveTab] = useState('performance');
  const [isLoading, setIsLoading] = useState(false);

  const performanceData = {
    strength_score: 78,
    endurance_score: 65,
    consistency_score: 82,
    overall_score: 75
  };

  const predictions = [
    {
      title: 'Total Volume (Next Month)',
      prediction: '18,500 lbs',
      confidence: 0.85,
      factors: [
        { name: 'Workout Frequency', impact: 0.4 },
        { name: 'Progressive Overload', impact: 0.3 },
        { name: 'Recovery Status', impact: 0.2 },
        { name: 'Exercise Selection', impact: 0.1 }
      ]
    },
    {
      title: 'Strength Gain Prediction',
      prediction: '+12% improvement',
      confidence: 0.78,
      factors: [
        { name: 'Progressive Overload', impact: 0.5 },
        { name: 'Training Consistency', impact: 0.3 },
        { name: 'Recovery Quality', impact: 0.2 }
      ]
    }
  ];

  const benchmarks = [
    {
      metric: 'Total Volume',
      userValue: 15000,
      peerAverage: 12500,
      percentile: 75,
      status: 'above'
    },
    {
      metric: 'Workout Frequency',
      userValue: 4.2,
      peerAverage: 3.8,
      percentile: 68,
      status: 'above'
    },
    {
      metric: 'Consistency Score',
      userValue: 82,
      peerAverage: 68,
      percentile: 85,
      status: 'above'
    }
  ];

  const churnPrediction = {
    probability: 0.15,
    riskLevel: 'low',
    factors: [
      { factor: 'Workout Frequency', weight: 0.4, value: 4.2 },
      { factor: 'Engagement Score', weight: 0.3, value: 0.85 },
      { factor: 'Subscription Status', weight: 0.3, value: 1.0 }
    ],
    interventions: [
      'Continue regular engagement',
      'Provide positive reinforcement',
      'Introduce new challenges'
    ]
  };

  const handleRunAnalysis = async (type) => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  const renderPerformanceTab = () => (
    <View>
      <Text style={styles.sectionTitle}>Performance Scores</Text>
      <View style={styles.analyticsGrid}>
        <AnalyticsCard
          title="Strength Score"
          value={`${performanceData.strength_score}/100`}
          subtitle="Progressive overload tracking"
          icon="barbell-outline"
          color="#10b981"
        />
        <AnalyticsCard
          title="Endurance Score"
          value={`${performanceData.endurance_score}/100`}
          subtitle="Cardiovascular fitness"
          icon="heart-outline"
          color="#f59e0b"
        />
        <AnalyticsCard
          title="Consistency Score"
          value={`${performanceData.consistency_score}/100`}
          subtitle="Workout frequency"
          icon="calendar-outline"
          color="#8b5cf6"
        />
        <AnalyticsCard
          title="Overall Score"
          value={`${performanceData.overall_score}/100`}
          subtitle="Weighted average"
          icon="trophy-outline"
          color="#2563eb"
        />
      </View>

      <Text style={styles.sectionTitle}>AI Insights</Text>
      <View style={styles.insightCard}>
        <Text style={styles.insightTitle}>🎯 Strengths</Text>
        <Text style={styles.insightText}>• Excellent workout consistency (82/100)</Text>
        <Text style={styles.insightText}>• Strong progressive overload adherence</Text>
        
        <Text style={styles.insightTitle}>📈 Improvement Areas</Text>
        <Text style={styles.insightText}>• Focus on cardiovascular endurance</Text>
        <Text style={styles.insightText}>• Consider adding variety to prevent plateaus</Text>
      </View>
    </View>
  );

  const renderPredictiveTab = () => (
    <View>
      <Text style={styles.sectionTitle}>Progress Predictions</Text>
      {predictions.map((prediction, index) => (
        <PredictionCard
          key={index}
          title={prediction.title}
          prediction={prediction.prediction}
          confidence={prediction.confidence}
          factors={prediction.factors}
        />
      ))}

      <Text style={styles.sectionTitle}>Churn Risk Analysis</Text>
      <View style={styles.churnCard}>
        <View style={styles.churnHeader}>
          <Text style={styles.churnTitle}>Retention Probability</Text>
          <View style={[styles.riskBadge, { backgroundColor: '#dcfce7' }]}>
            <Text style={[styles.riskText, { color: '#16a34a' }]}>
              {churnPrediction.riskLevel.toUpperCase()} RISK
            </Text>
          </View>
        </View>
        <Text style={styles.churnProbability}>
          {Math.round((1 - churnPrediction.probability) * 100)}% likely to continue
        </Text>
        
        <Text style={styles.factorsTitle}>Risk Factors:</Text>
        {churnPrediction.factors.map((factor, index) => (
          <View key={index} style={styles.factorRow}>
            <Text style={styles.factorName}>{factor.factor}</Text>
            <Text style={styles.factorValue}>{factor.value}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderComparativeTab = () => (
    <View>
      <Text style={styles.sectionTitle}>Peer Benchmarking</Text>
      <View style={styles.peerGroup}>
        <Text style={styles.peerGroupTitle}>Your Peer Group</Text>
        <Text style={styles.peerGroupDesc}>Intermediate • Age 25-35 • Strength Goals</Text>
        <Text style={styles.peerGroupSize}>Compared against 1,247 similar users</Text>
      </View>

      {benchmarks.map((benchmark, index) => (
        <View key={index} style={styles.benchmarkCard}>
          <View style={styles.benchmarkHeader}>
            <Text style={styles.benchmarkMetric}>{benchmark.metric}</Text>
            <Text style={styles.percentileText}>{benchmark.percentile}th percentile</Text>
          </View>
          
          <View style={styles.benchmarkValues}>
            <View style={styles.valueItem}>
              <Text style={styles.valueLabel}>You</Text>
              <Text style={styles.valueNumber}>{benchmark.userValue}</Text>
            </View>
            <View style={styles.valueItem}>
              <Text style={styles.valueLabel}>Peer Average</Text>
              <Text style={styles.valueNumber}>{benchmark.peerAverage}</Text>
            </View>
          </View>
          
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${benchmark.percentile}%`, backgroundColor: '#10b981' }
              ]} 
            />
          </View>
        </View>
      ))}

      <TouchableOpacity 
        style={styles.analysisButton}
        onPress={() => handleRunAnalysis('comparative')}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#ffffff" />
        ) : (
          <Ionicons name="analytics" size={20} color="#ffffff" />
        )}
        <Text style={styles.analysisButtonText}>
          {isLoading ? 'Analyzing...' : 'Run Deep Analysis'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'performance' && styles.activeTab]}
          onPress={() => setActiveTab('performance')}
        >
          <Text style={[styles.tabText, activeTab === 'performance' && styles.activeTabText]}>
            Performance
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'predictive' && styles.activeTab]}
          onPress={() => setActiveTab('predictive')}
        >
          <Text style={[styles.tabText, activeTab === 'predictive' && styles.activeTabText]}>
            Predictive
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'comparative' && styles.activeTab]}
          onPress={() => setActiveTab('comparative')}
        >
          <Text style={[styles.tabText, activeTab === 'comparative' && styles.activeTabText]}>
            Comparative
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {activeTab === 'performance' && renderPerformanceTab()}
        {activeTab === 'predictive' && renderPredictiveTab()}
        {activeTab === 'comparative' && renderComparativeTab()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#2563eb',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  activeTabText: {
    color: '#2563eb',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    marginTop: 8,
  },
  analyticsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  analyticsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: '48%',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 12,
    color: '#6b7280',
  },
  insightCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
    marginTop: 8,
  },
  insightText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  predictionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  predictionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  predictionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  predictionValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2563eb',
  },
  confidenceBadge: {
    backgroundColor: '#eff6ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  confidenceText: {
    fontSize: 12,
    color: '#2563eb',
    fontWeight: '600',
  },
  factorsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  factorText: {
    fontSize: 13,
    color: '#6b7280',
    marginBottom: 4,
  },
  churnCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  churnHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  churnTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  riskBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  riskText: {
    fontSize: 12,
    fontWeight: '600',
  },
  churnProbability: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#16a34a',
    marginBottom: 16,
  },
  factorRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  factorName: {
    fontSize: 14,
    color: '#6b7280',
  },
  factorValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
  },
  peerGroup: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  peerGroupTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  peerGroupDesc: {
    fontSize: 14,
    color: '#2563eb',
    marginBottom: 4,
  },
  peerGroupSize: {
    fontSize: 12,
    color: '#6b7280',
  },
  benchmarkCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  benchmarkHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  benchmarkMetric: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  percentileText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10b981',
  },
  benchmarkValues: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  valueItem: {
    alignItems: 'center',
  },
  valueLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  valueNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e5e7eb',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  analysisButton: {
    backgroundColor: '#2563eb',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 20,
  },
  analysisButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});