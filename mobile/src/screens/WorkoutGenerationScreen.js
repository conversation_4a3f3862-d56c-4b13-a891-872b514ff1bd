import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const FeatureCard = ({ title, description, icon, complexity }) => (
  <View style={styles.featureCard}>
    <View style={styles.featureHeader}>
      <Ionicons name={icon} size={20} color="#2563eb" />
      <View style={styles.complexityBadge}>
        <Text style={styles.complexityText}>
          {'⭐'.repeat(complexity)}
        </Text>
      </View>
    </View>
    <Text style={styles.featureTitle}>{title}</Text>
    <Text style={styles.featureDescription}>{description}</Text>
  </View>
);

const MockUserProfile = {
  age: 28,
  experience_level: 'intermediate',
  primary_goals: ['strength', 'muscle_gain'],
  available_equipment: ['barbell', 'dumbbells', 'bench'],
  workout_frequency: 4,
  session_duration: 60,
  injury_history: [],
  preferences: {
    exercise_types: ['compound', 'isolation'],
    intensity_preference: 'moderate',
    variety_preference: 'moderate',
  }
};

const MockWorkoutContext = {
  current_fatigue_level: 0.3,
  recent_workout_history: [],
  recovery_status: 'good',
  available_time: 60,
  equipment_available: ['barbell', 'dumbbells', 'bench'],
  environmental_factors: {
    location: 'gym',
    temperature: 22,
    noise_level: 'moderate',
  }
};

export default function WorkoutGenerationScreen() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedWorkout, setGeneratedWorkout] = useState(null);

  const features = [
    {
      title: 'Weighted Scoring Algorithm',
      description: '5-factor exercise scoring: User preference (25%), Goal alignment (30%), Recovery status (20%), Progression potential (15%), Variety factor (10%)',
      icon: 'calculator-outline',
      complexity: 5
    },
    {
      title: 'Smart Exercise Selection',
      description: 'Advanced algorithms analyze exercise compatibility with user goals, equipment, and recovery status',
      icon: 'fitness-outline',
      complexity: 4
    },
    {
      title: 'Optimal Sequencing',
      description: 'Compound exercises first, then isolation. Large muscle groups before small. Difficulty-based ordering.',
      icon: 'list-outline',
      complexity: 4
    },
    {
      title: 'Personalization Layers',
      description: 'Volume and intensity adjustments based on fatigue analysis, recovery status, and user analysis',
      icon: 'person-outline',
      complexity: 5
    },
    {
      title: 'AI Insights Generation',
      description: 'Automated reasoning for exercise selection, progression plans, and recovery considerations',
      icon: 'bulb-outline',
      complexity: 4
    },
    {
      title: 'Adaptive Warm-up/Cool-down',
      description: 'Dynamic warm-up and cool-down generation based on workout focus and user needs',
      icon: 'thermometer-outline',
      complexity: 3
    }
  ];

  const handleGenerateWorkout = async () => {
    setIsGenerating(true);
    
    // Simulate the complex AI generation process
    setTimeout(() => {
      const mockWorkout = {
        id: `workout_${Date.now()}`,
        name: 'Strength & Hypertrophy Focus',
        description: '5-exercise intermediate level workout tailored for strength and muscle gain goals',
        estimated_duration: 65,
        difficulty_level: 3,
        primary_focus: ['chest', 'shoulders'],
        exercises: [
          {
            exercise_name: 'Barbell Bench Press',
            sets: 4,
            reps: '6-8',
            weight: '80% 1RM',
            rest_time: 180,
            notes: 'Focus on proper form. Excellent for strength and muscle gain goals.',
            progression_notes: 'Increase weight when you can complete all sets with 2+ reps in reserve'
          },
          {
            exercise_name: 'Incline Dumbbell Press',
            sets: 3,
            reps: '8-10',
            weight: 'Moderate',
            rest_time: 120,
            notes: 'Great for upper chest development. Matches your equipment preferences.',
            progression_notes: 'Increase weight when you can complete all sets with 2+ reps in reserve'
          },
          {
            exercise_name: 'Dumbbell Shoulder Press',
            sets: 3,
            reps: '10-12',
            weight: 'Moderate',
            rest_time: 90,
            notes: 'Excellent shoulder builder. High progression potential.',
            progression_notes: 'Focus on controlled movement and full range of motion'
          }
        ],
        warm_up: [
          {
            exercise_name: 'Dynamic Stretching',
            duration: 5,
            instructions: 'Perform dynamic stretches targeting major muscle groups'
          },
          {
            exercise_name: 'Arm Circles',
            duration: 2,
            instructions: 'Small to large arm circles to prepare shoulder joints'
          }
        ],
        cool_down: [
          {
            exercise_name: 'Light Walking',
            duration: 5,
            instructions: 'Gentle walking to gradually lower heart rate'
          },
          {
            exercise_name: 'Static Stretching',
            duration: 10,
            instructions: 'Hold stretches for 30 seconds each, focusing on worked muscle groups'
          }
        ],
        ai_insights: {
          personalization_factors: [
            'Adjusted for intermediate experience level',
            'Optimized for strength, muscle_gain goals',
            'Tailored to 60 minute session'
          ],
          adaptation_reasoning: [
            'Volume adjusted by 0% based on recovery status',
            'Intensity set to 80% based on fatigue analysis'
          ],
          progression_plan: 'Focus on progressive overload by increasing weight by 2.5-5% when you can complete all sets with 2+ reps in reserve',
          recovery_considerations: [
            'Allow 48-72 hours between training the same muscle groups',
            'Monitor fatigue levels and adjust intensity accordingly',
            'Prioritize sleep and nutrition for optimal recovery'
          ]
        }
      };
      
      setGeneratedWorkout(mockWorkout);
      setIsGenerating(false);
    }, 3000);
  };

  const renderWorkout = () => {
    if (!generatedWorkout) return null;

    return (
      <View style={styles.workoutContainer}>
        <Text style={styles.workoutTitle}>{generatedWorkout.name}</Text>
        <Text style={styles.workoutDescription}>{generatedWorkout.description}</Text>
        
        <View style={styles.workoutStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{generatedWorkout.estimated_duration}min</Text>
            <Text style={styles.statLabel}>Duration</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{generatedWorkout.exercises.length}</Text>
            <Text style={styles.statLabel}>Exercises</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{generatedWorkout.difficulty_level}/5</Text>
            <Text style={styles.statLabel}>Difficulty</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Exercises</Text>
        {generatedWorkout.exercises.map((exercise, index) => (
          <View key={index} style={styles.exerciseCard}>
            <Text style={styles.exerciseName}>{exercise.exercise_name}</Text>
            <Text style={styles.exerciseDetails}>
              {exercise.sets} sets × {exercise.reps} reps @ {exercise.weight}
            </Text>
            <Text style={styles.exerciseNotes}>{exercise.notes}</Text>
          </View>
        ))}

        <Text style={styles.sectionTitle}>AI Insights</Text>
        <View style={styles.insightsCard}>
          <Text style={styles.insightTitle}>Personalization Factors:</Text>
          {generatedWorkout.ai_insights.personalization_factors.map((factor, index) => (
            <Text key={index} style={styles.insightText}>• {factor}</Text>
          ))}
          
          <Text style={styles.insightTitle}>Progression Plan:</Text>
          <Text style={styles.insightText}>{generatedWorkout.ai_insights.progression_plan}</Text>
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>AI Workout Generation</Text>
        <Text style={styles.subtitle}>Enterprise-Grade Personalization Engine</Text>
      </View>

      <Text style={styles.sectionTitle}>Advanced Features</Text>
      
      <View style={styles.featuresGrid}>
        {features.map((feature, index) => (
          <FeatureCard
            key={index}
            title={feature.title}
            description={feature.description}
            icon={feature.icon}
            complexity={feature.complexity}
          />
        ))}
      </View>

      <View style={styles.demoSection}>
        <Text style={styles.sectionTitle}>Live Demo</Text>
        <Text style={styles.demoDescription}>
          Generate a personalized workout using the advanced AI algorithms
        </Text>
        
        <TouchableOpacity 
          style={[styles.generateButton, isGenerating && styles.generateButtonDisabled]}
          onPress={handleGenerateWorkout}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <ActivityIndicator color="#ffffff" />
          ) : (
            <Ionicons name="flash" size={20} color="#ffffff" />
          )}
          <Text style={styles.generateButtonText}>
            {isGenerating ? 'Generating...' : 'Generate AI Workout'}
          </Text>
        </TouchableOpacity>

        {isGenerating && (
          <View style={styles.processingSteps}>
            <Text style={styles.processingText}>🧠 Analyzing user profile...</Text>
            <Text style={styles.processingText}>⚖️ Calculating exercise scores...</Text>
            <Text style={styles.processingText}>🎯 Optimizing workout sequence...</Text>
            <Text style={styles.processingText}>🔧 Applying personalization layers...</Text>
          </View>
        )}

        {renderWorkout()}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginHorizontal: 20,
    marginBottom: 16,
    marginTop: 20,
  },
  featuresGrid: {
    paddingHorizontal: 20,
  },
  featureCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featureHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  complexityBadge: {
    backgroundColor: '#fef3c7',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  complexityText: {
    fontSize: 12,
    color: '#d97706',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  demoSection: {
    marginTop: 20,
  },
  demoDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginHorizontal: 20,
    marginBottom: 20,
    textAlign: 'center',
  },
  generateButton: {
    backgroundColor: '#2563eb',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  generateButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  generateButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  processingSteps: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  processingText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  workoutContainer: {
    marginHorizontal: 20,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  workoutTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  workoutDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
  },
  workoutStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    paddingVertical: 16,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2563eb',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  exerciseCard: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  exerciseName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  exerciseDetails: {
    fontSize: 14,
    color: '#2563eb',
    fontWeight: '600',
    marginBottom: 4,
  },
  exerciseNotes: {
    fontSize: 12,
    color: '#6b7280',
  },
  insightsCard: {
    backgroundColor: '#eff6ff',
    borderRadius: 8,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#2563eb',
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 8,
    marginTop: 8,
  },
  insightText: {
    fontSize: 13,
    color: '#1e40af',
    marginBottom: 4,
  },
});