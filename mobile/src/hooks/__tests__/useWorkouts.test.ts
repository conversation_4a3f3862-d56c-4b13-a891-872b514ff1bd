import { renderHook, act, waitFor } from '@testing-library/react-native';
import { useWorkouts } from '../useWorkouts';
import { workoutService } from '@/services/workoutService';
import { createMockWorkout, createMockWorkoutProgram } from '@test/utils';

// Mock the workout service
jest.mock('@/services/workoutService');
const mockWorkoutService = workoutService as jest.Mocked<typeof workoutService>;

describe('useWorkouts Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('initializes with correct default state', () => {
    const { result } = renderHook(() => useWorkouts());

    expect(result.current.workouts).toEqual([]);
    expect(result.current.programs).toEqual([]);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('fetches workouts successfully', async () => {
    const mockWorkouts = [createMockWorkout(), createMockWorkout({ id: '2', name: 'Workout 2' })];
    mockWorkoutService.getWorkouts.mockResolvedValue({
      data: mockWorkouts,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    act(() => {
      result.current.fetchWorkouts();
    });

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.workouts).toEqual(mockWorkouts);
      expect(result.current.error).toBe(null);
    });

    expect(mockWorkoutService.getWorkouts).toHaveBeenCalledTimes(1);
  });

  it('handles fetch workouts error', async () => {
    const errorMessage = 'Failed to fetch workouts';
    mockWorkoutService.getWorkouts.mockResolvedValue({
      data: null,
      error: { message: errorMessage },
    });

    const { result } = renderHook(() => useWorkouts());

    act(() => {
      result.current.fetchWorkouts();
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.workouts).toEqual([]);
      expect(result.current.error).toBe(errorMessage);
    });
  });

  it('fetches workout programs successfully', async () => {
    const mockPrograms = [
      createMockWorkoutProgram(),
      createMockWorkoutProgram({ id: '2', name: 'Program 2' }),
    ];
    mockWorkoutService.getWorkoutPrograms.mockResolvedValue({
      data: mockPrograms,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    act(() => {
      result.current.fetchPrograms();
    });

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.programs).toEqual(mockPrograms);
      expect(result.current.error).toBe(null);
    });

    expect(mockWorkoutService.getWorkoutPrograms).toHaveBeenCalledTimes(1);
  });

  it('creates a new workout', async () => {
    const newWorkout = createMockWorkout({ name: 'New Workout' });
    mockWorkoutService.createWorkout.mockResolvedValue({
      data: newWorkout,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    let createdWorkout;
    await act(async () => {
      createdWorkout = await result.current.createWorkout({
        name: 'New Workout',
        description: 'A new workout',
        exercises: [],
      });
    });

    expect(createdWorkout).toEqual(newWorkout);
    expect(mockWorkoutService.createWorkout).toHaveBeenCalledWith({
      name: 'New Workout',
      description: 'A new workout',
      exercises: [],
    });
  });

  it('updates an existing workout', async () => {
    const updatedWorkout = createMockWorkout({ name: 'Updated Workout' });
    mockWorkoutService.updateWorkout.mockResolvedValue({
      data: updatedWorkout,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    let updated;
    await act(async () => {
      updated = await result.current.updateWorkout('test-id', {
        name: 'Updated Workout',
      });
    });

    expect(updated).toEqual(updatedWorkout);
    expect(mockWorkoutService.updateWorkout).toHaveBeenCalledWith('test-id', {
      name: 'Updated Workout',
    });
  });

  it('deletes a workout', async () => {
    mockWorkoutService.deleteWorkout.mockResolvedValue({
      data: true,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    let deleted;
    await act(async () => {
      deleted = await result.current.deleteWorkout('test-id');
    });

    expect(deleted).toBe(true);
    expect(mockWorkoutService.deleteWorkout).toHaveBeenCalledWith('test-id');
  });

  it('starts a workout session', async () => {
    const mockSession = { id: 'session-1', workoutId: 'workout-1', startedAt: new Date().toISOString() };
    mockWorkoutService.startWorkoutSession.mockResolvedValue({
      data: mockSession,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    let session;
    await act(async () => {
      session = await result.current.startWorkout('workout-1');
    });

    expect(session).toEqual(mockSession);
    expect(mockWorkoutService.startWorkoutSession).toHaveBeenCalledWith('workout-1');
  });

  it('completes a workout session', async () => {
    const mockLog = createMockWorkoutLog();
    mockWorkoutService.completeWorkoutSession.mockResolvedValue({
      data: mockLog,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    const sessionData = {
      sessionId: 'session-1',
      exercises: [
        {
          exerciseId: 'exercise-1',
          sets: [{ reps: 10, weight: 50, completed: true }],
        },
      ],
      notes: 'Great workout!',
      rating: 5,
    };

    let log;
    await act(async () => {
      log = await result.current.completeWorkout(sessionData);
    });

    expect(log).toEqual(mockLog);
    expect(mockWorkoutService.completeWorkoutSession).toHaveBeenCalledWith(sessionData);
  });

  it('filters workouts by type', () => {
    const mockWorkouts = [
      createMockWorkout({ type: 'strength' }),
      createMockWorkout({ id: '2', type: 'cardio' }),
      createMockWorkout({ id: '3', type: 'strength' }),
    ];

    const { result } = renderHook(() => useWorkouts());

    // Manually set workouts for testing
    act(() => {
      result.current.workouts = mockWorkouts;
    });

    const strengthWorkouts = result.current.getWorkoutsByType('strength');
    expect(strengthWorkouts).toHaveLength(2);
    expect(strengthWorkouts.every(w => w.type === 'strength')).toBe(true);
  });

  it('searches workouts by name', () => {
    const mockWorkouts = [
      createMockWorkout({ name: 'Push Day' }),
      createMockWorkout({ id: '2', name: 'Pull Day' }),
      createMockWorkout({ id: '3', name: 'Leg Day' }),
    ];

    const { result } = renderHook(() => useWorkouts());

    // Manually set workouts for testing
    act(() => {
      result.current.workouts = mockWorkouts;
    });

    const searchResults = result.current.searchWorkouts('Push');
    expect(searchResults).toHaveLength(1);
    expect(searchResults[0].name).toBe('Push Day');
  });

  it('gets workout by id', () => {
    const mockWorkouts = [
      createMockWorkout({ id: 'workout-1' }),
      createMockWorkout({ id: 'workout-2' }),
    ];

    const { result } = renderHook(() => useWorkouts());

    // Manually set workouts for testing
    act(() => {
      result.current.workouts = mockWorkouts;
    });

    const workout = result.current.getWorkoutById('workout-1');
    expect(workout).toEqual(mockWorkouts[0]);

    const nonExistentWorkout = result.current.getWorkoutById('non-existent');
    expect(nonExistentWorkout).toBeUndefined();
  });

  it('clears error state', () => {
    const { result } = renderHook(() => useWorkouts());

    // Set an error
    act(() => {
      result.current.error = 'Some error';
    });

    expect(result.current.error).toBe('Some error');

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBe(null);
  });

  it('refreshes data', async () => {
    const mockWorkouts = [createMockWorkout()];
    const mockPrograms = [createMockWorkoutProgram()];

    mockWorkoutService.getWorkouts.mockResolvedValue({
      data: mockWorkouts,
      error: null,
    });
    mockWorkoutService.getWorkoutPrograms.mockResolvedValue({
      data: mockPrograms,
      error: null,
    });

    const { result } = renderHook(() => useWorkouts());

    await act(async () => {
      await result.current.refresh();
    });

    expect(mockWorkoutService.getWorkouts).toHaveBeenCalled();
    expect(mockWorkoutService.getWorkoutPrograms).toHaveBeenCalled();
    expect(result.current.workouts).toEqual(mockWorkouts);
    expect(result.current.programs).toEqual(mockPrograms);
  });
});
