import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useTheme } from '@/contexts/ThemeContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { subscriptionAnalyticsService } from '@/services/subscriptionAnalyticsService';
import { styles } from './SubscriptionAnalytics.styles';

interface AnalyticsData {
  revenue: {
    mrr: number;
    arr: number;
    total_revenue: number;
    growth_rate: number;
  };
  subscriptions: {
    active: number;
    trial: number;
    canceled: number;
    churn_rate: number;
    retention_rate: number;
  };
  plans: {
    name: string;
    count: number;
    revenue: number;
    percentage: number;
  }[];
  trends: {
    date: string;
    new_subscriptions: number;
    canceled_subscriptions: number;
    revenue: number;
  }[];
}

const screenWidth = Dimensions.get('window').width;

export function SubscriptionAnalytics() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [loading, setLoading] = useState(true);

  const periods = [
    { key: '7d', label: '7 Days' },
    { key: '30d', label: '30 Days' },
    { key: '90d', label: '90 Days' },
    { key: '1y', label: '1 Year' },
  ];

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriod]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const data = await subscriptionAnalyticsService.getAnalytics(selectedPeriod);
      setAnalyticsData(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getChartConfig = () => ({
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(${theme.colors.primary.replace('#', '')}, ${opacity})`,
    labelColor: (opacity = 1) => theme.colors.textSecondary,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
  });

  const renderMetricCard = (
    title: string,
    value: string | number,
    subtitle?: string,
    icon?: string,
    trend?: number
  ) => (
    <View style={[styles.metricCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.metricHeader}>
        {icon && (
          <Ionicons name={icon as any} size={20} color={theme.colors.primary} />
        )}
        <Text style={[styles.metricTitle, { color: theme.colors.textSecondary }]}>
          {title}
        </Text>
      </View>
      <Text style={[styles.metricValue, { color: theme.colors.text }]}>
        {typeof value === 'number' ? formatCurrency(value) : value}
      </Text>
      {subtitle && (
        <Text style={[styles.metricSubtitle, { color: theme.colors.textSecondary }]}>
          {subtitle}
        </Text>
      )}
      {trend !== undefined && (
        <View style={styles.trendContainer}>
          <Ionicons
            name={trend >= 0 ? 'trending-up' : 'trending-down'}
            size={16}
            color={trend >= 0 ? theme.colors.success : theme.colors.error}
          />
          <Text
            style={[
              styles.trendText,
              { color: trend >= 0 ? theme.colors.success : theme.colors.error }
            ]}
          >
            {formatPercentage(Math.abs(trend))}
          </Text>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  if (!analyticsData) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.errorText, { color: theme.colors.textSecondary }]}>
          Failed to load analytics data
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Subscription Analytics
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Period Selector */}
      <View style={styles.periodSelector}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.key}
            style={[
              styles.periodButton,
              {
                backgroundColor: selectedPeriod === period.key
                  ? theme.colors.primary
                  : theme.colors.surface,
              }
            ]}
            onPress={() => setSelectedPeriod(period.key as any)}
          >
            <Text
              style={[
                styles.periodButtonText,
                {
                  color: selectedPeriod === period.key
                    ? theme.colors.white
                    : theme.colors.text,
                }
              ]}
            >
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Revenue Metrics */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Revenue Metrics
        </Text>
        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Monthly Recurring Revenue',
            analyticsData.revenue.mrr,
            'MRR',
            'trending-up',
            analyticsData.revenue.growth_rate
          )}
          {renderMetricCard(
            'Annual Recurring Revenue',
            analyticsData.revenue.arr,
            'ARR',
            'calendar'
          )}
          {renderMetricCard(
            'Total Revenue',
            analyticsData.revenue.total_revenue,
            `Last ${selectedPeriod}`,
            'cash'
          )}
        </View>
      </View>

      {/* Subscription Metrics */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Subscription Metrics
        </Text>
        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Active Subscriptions',
            analyticsData.subscriptions.active.toString(),
            'Currently active',
            'people'
          )}
          {renderMetricCard(
            'Trial Subscriptions',
            analyticsData.subscriptions.trial.toString(),
            'In trial period',
            'time'
          )}
          {renderMetricCard(
            'Churn Rate',
            formatPercentage(analyticsData.subscriptions.churn_rate),
            'Monthly churn',
            'trending-down'
          )}
          {renderMetricCard(
            'Retention Rate',
            formatPercentage(analyticsData.subscriptions.retention_rate),
            'Monthly retention',
            'shield-checkmark'
          )}
        </View>
      </View>

      {/* Revenue Trend Chart */}
      <View style={[styles.chartSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
          Revenue Trend
        </Text>
        {analyticsData.trends.length > 0 && (
          <LineChart
            data={{
              labels: analyticsData.trends.map(t => 
                new Date(t.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
              ),
              datasets: [{
                data: analyticsData.trends.map(t => t.revenue),
              }],
            }}
            width={screenWidth - 40}
            height={220}
            chartConfig={getChartConfig()}
            bezier
            style={styles.chart}
          />
        )}
      </View>

      {/* Subscription Trend Chart */}
      <View style={[styles.chartSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
          Subscription Activity
        </Text>
        {analyticsData.trends.length > 0 && (
          <BarChart
            data={{
              labels: analyticsData.trends.map(t => 
                new Date(t.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
              ),
              datasets: [
                {
                  data: analyticsData.trends.map(t => t.new_subscriptions),
                  color: (opacity = 1) => theme.colors.success + opacity.toString(16).padStart(2, '0'),
                },
                {
                  data: analyticsData.trends.map(t => t.canceled_subscriptions),
                  color: (opacity = 1) => theme.colors.error + opacity.toString(16).padStart(2, '0'),
                },
              ],
            }}
            width={screenWidth - 40}
            height={220}
            chartConfig={getChartConfig()}
            style={styles.chart}
          />
        )}
        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: theme.colors.success }]} />
            <Text style={[styles.legendText, { color: theme.colors.textSecondary }]}>
              New Subscriptions
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: theme.colors.error }]} />
            <Text style={[styles.legendText, { color: theme.colors.textSecondary }]}>
              Cancellations
            </Text>
          </View>
        </View>
      </View>

      {/* Plan Distribution */}
      <View style={[styles.chartSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
          Plan Distribution
        </Text>
        {analyticsData.plans.length > 0 && (
          <PieChart
            data={analyticsData.plans.map((plan, index) => ({
              name: plan.name,
              population: plan.count,
              color: [
                theme.colors.primary,
                theme.colors.success,
                theme.colors.warning,
                theme.colors.info,
                theme.colors.error,
              ][index % 5],
              legendFontColor: theme.colors.textSecondary,
              legendFontSize: 12,
            }))}
            width={screenWidth - 40}
            height={220}
            chartConfig={getChartConfig()}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
            style={styles.chart}
          />
        )}
      </View>

      {/* Plan Performance Table */}
      <View style={[styles.tableSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Plan Performance
        </Text>
        <View style={styles.table}>
          <View style={styles.tableHeader}>
            <Text style={[styles.tableHeaderText, { color: theme.colors.textSecondary }]}>
              Plan
            </Text>
            <Text style={[styles.tableHeaderText, { color: theme.colors.textSecondary }]}>
              Subscribers
            </Text>
            <Text style={[styles.tableHeaderText, { color: theme.colors.textSecondary }]}>
              Revenue
            </Text>
            <Text style={[styles.tableHeaderText, { color: theme.colors.textSecondary }]}>
              Share
            </Text>
          </View>
          {analyticsData.plans.map((plan, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.tableCellText, { color: theme.colors.text }]}>
                {plan.name}
              </Text>
              <Text style={[styles.tableCellText, { color: theme.colors.text }]}>
                {plan.count}
              </Text>
              <Text style={[styles.tableCellText, { color: theme.colors.text }]}>
                {formatCurrency(plan.revenue)}
              </Text>
              <Text style={[styles.tableCellText, { color: theme.colors.text }]}>
                {formatPercentage(plan.percentage / 100)}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* Key Insights */}
      <View style={[styles.insightsSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Key Insights
        </Text>
        <View style={styles.insightsList}>
          <View style={styles.insightItem}>
            <Ionicons name="trending-up" size={20} color={theme.colors.success} />
            <Text style={[styles.insightText, { color: theme.colors.text }]}>
              MRR growth rate is {formatPercentage(analyticsData.revenue.growth_rate)}
            </Text>
          </View>
          <View style={styles.insightItem}>
            <Ionicons name="people" size={20} color={theme.colors.primary} />
            <Text style={[styles.insightText, { color: theme.colors.text }]}>
              {analyticsData.subscriptions.active} active subscribers
            </Text>
          </View>
          <View style={styles.insightItem}>
            <Ionicons 
              name={analyticsData.subscriptions.churn_rate < 0.05 ? 'checkmark-circle' : 'warning'} 
              size={20} 
              color={analyticsData.subscriptions.churn_rate < 0.05 ? theme.colors.success : theme.colors.warning} 
            />
            <Text style={[styles.insightText, { color: theme.colors.text }]}>
              Churn rate is {formatPercentage(analyticsData.subscriptions.churn_rate)} 
              {analyticsData.subscriptions.churn_rate < 0.05 ? ' (Excellent)' : ' (Needs attention)'}
            </Text>
          </View>
        </View>
      </View>

      {/* Export Options */}
      <View style={styles.exportSection}>
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => subscriptionAnalyticsService.exportData(selectedPeriod)}
        >
          <Ionicons name="download" size={20} color={theme.colors.white} />
          <Text style={[styles.exportButtonText, { color: theme.colors.white }]}>
            Export Data
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
