import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { stripeService, UserSubscription, PaymentMethod, BillingHistory } from '@/services/stripeService';
import { subscriptionService } from '@/services/subscriptionService';
import { styles } from './ManageSubscription.styles';

export function ManageSubscription() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [billingHistory, setBillingHistory] = useState<BillingHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [subscriptionData, paymentMethodsData, billingData] = await Promise.all([
        stripeService.getUserSubscription(),
        stripeService.getPaymentMethods(),
        stripeService.getBillingHistory(10),
      ]);
      
      setSubscription(subscriptionData);
      setPaymentMethods(paymentMethodsData);
      setBillingHistory(billingData);
    } catch (error) {
      console.error('Failed to load subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleCancelSubscription = () => {
    if (!subscription?.stripe_subscription_id) return;

    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              setActionLoading('cancel');
              await stripeService.cancelSubscription(subscription.stripe_subscription_id!);
              Alert.alert('Success', 'Your subscription has been cancelled');
              loadData();
            } catch (error) {
              console.error('Failed to cancel subscription:', error);
              Alert.alert('Error', 'Failed to cancel subscription');
            } finally {
              setActionLoading(null);
            }
          },
        },
      ]
    );
  };

  const handleResumeSubscription = async () => {
    if (!subscription?.stripe_subscription_id) return;

    try {
      setActionLoading('resume');
      await stripeService.resumeSubscription(subscription.stripe_subscription_id);
      Alert.alert('Success', 'Your subscription has been resumed');
      loadData();
    } catch (error) {
      console.error('Failed to resume subscription:', error);
      Alert.alert('Error', 'Failed to resume subscription');
    } finally {
      setActionLoading(null);
    }
  };

  const handleChangePlan = () => {
    navigation.navigate('SubscriptionPlans');
  };

  const handleManagePaymentMethods = () => {
    navigation.navigate('PaymentMethods');
  };

  const handleViewInvoice = async (invoiceId: string) => {
    try {
      const downloadUrl = await stripeService.downloadInvoice(invoiceId);
      // Open invoice URL (would need to implement PDF viewer or web view)
      Alert.alert('Invoice', 'Invoice download functionality would be implemented here');
    } catch (error) {
      console.error('Failed to download invoice:', error);
      Alert.alert('Error', 'Failed to download invoice');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return theme.colors.success;
      case 'trialing': return theme.colors.info;
      case 'canceled': return theme.colors.error;
      case 'past_due': return theme.colors.warning;
      default: return theme.colors.textSecondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return 'checkmark-circle';
      case 'trialing': return 'time';
      case 'canceled': return 'close-circle';
      case 'past_due': return 'warning';
      default: return 'information-circle';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  if (!subscription) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Subscription
          </Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.noSubscriptionContainer}>
          <Ionicons name="card-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={[styles.noSubscriptionTitle, { color: theme.colors.text }]}>
            No Active Subscription
          </Text>
          <Text style={[styles.noSubscriptionMessage, { color: theme.colors.textSecondary }]}>
            You don't have an active subscription. Upgrade to unlock premium features.
          </Text>
          <Button
            title="View Plans"
            variant="primary"
            onPress={() => navigation.navigate('SubscriptionPlans')}
            style={styles.upgradeButton}
          />
        </View>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Manage Subscription
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Current Subscription */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Current Plan
        </Text>
        
        <View style={styles.subscriptionCard}>
          <View style={styles.subscriptionHeader}>
            <View style={styles.planInfo}>
              <Text style={[styles.planName, { color: theme.colors.text }]}>
                {subscription.plan?.name || 'Premium Plan'}
              </Text>
              <View style={styles.statusContainer}>
                <Ionicons
                  name={getStatusIcon(subscription.status) as any}
                  size={16}
                  color={getStatusColor(subscription.status)}
                />
                <Text style={[styles.statusText, { color: getStatusColor(subscription.status) }]}>
                  {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                </Text>
              </View>
            </View>
            
            <View style={styles.priceInfo}>
              <Text style={[styles.price, { color: theme.colors.text }]}>
                {formatAmount(subscription.amount || 0, subscription.currency)}
              </Text>
              <Text style={[styles.billingInterval, { color: theme.colors.textSecondary }]}>
                /{subscription.billing_interval}
              </Text>
            </View>
          </View>

          {/* Subscription Details */}
          <View style={styles.subscriptionDetails}>
            {subscription.trial_end && new Date(subscription.trial_end) > new Date() && (
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>
                  Trial ends:
                </Text>
                <Text style={[styles.detailValue, { color: theme.colors.text }]}>
                  {formatDate(subscription.trial_end)}
                </Text>
              </View>
            )}
            
            {subscription.current_period_end && (
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>
                  {subscription.status === 'canceled' ? 'Access ends:' : 'Next billing:'}
                </Text>
                <Text style={[styles.detailValue, { color: theme.colors.text }]}>
                  {formatDate(subscription.current_period_end)}
                </Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              title="Change Plan"
              variant="secondary"
              onPress={handleChangePlan}
              style={styles.actionButton}
            />
            
            {subscription.status === 'active' ? (
              <Button
                title="Cancel"
                variant="outline"
                onPress={handleCancelSubscription}
                loading={actionLoading === 'cancel'}
                style={styles.actionButton}
              />
            ) : subscription.status === 'canceled' ? (
              <Button
                title="Resume"
                variant="primary"
                onPress={handleResumeSubscription}
                loading={actionLoading === 'resume'}
                style={styles.actionButton}
              />
            ) : null}
          </View>
        </View>
      </View>

      {/* Payment Methods */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Payment Methods
          </Text>
          <TouchableOpacity onPress={handleManagePaymentMethods}>
            <Text style={[styles.manageLink, { color: theme.colors.primary }]}>
              Manage
            </Text>
          </TouchableOpacity>
        </View>

        {paymentMethods.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
              No payment methods added
            </Text>
          </View>
        ) : (
          paymentMethods.slice(0, 2).map((method) => (
            <View key={method.id} style={styles.paymentMethodCard}>
              <View style={styles.paymentMethodInfo}>
                <Ionicons
                  name={method.type === 'card' ? 'card' : 'wallet'}
                  size={20}
                  color={theme.colors.primary}
                />
                <View style={styles.paymentMethodDetails}>
                  <Text style={[styles.paymentMethodType, { color: theme.colors.text }]}>
                    {method.brand?.toUpperCase()} •••• {method.last4}
                  </Text>
                  {method.exp_month && method.exp_year && (
                    <Text style={[styles.paymentMethodExpiry, { color: theme.colors.textSecondary }]}>
                      Expires {method.exp_month}/{method.exp_year}
                    </Text>
                  )}
                </View>
              </View>
              {method.is_default && (
                <View style={[styles.defaultBadge, { backgroundColor: theme.colors.success + '20' }]}>
                  <Text style={[styles.defaultText, { color: theme.colors.success }]}>
                    Default
                  </Text>
                </View>
              )}
            </View>
          ))
        )}
      </View>

      {/* Billing History */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Billing History
        </Text>

        {billingHistory.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
              No billing history available
            </Text>
          </View>
        ) : (
          billingHistory.map((invoice) => (
            <TouchableOpacity
              key={invoice.id}
              style={styles.invoiceCard}
              onPress={() => handleViewInvoice(invoice.stripe_invoice_id!)}
            >
              <View style={styles.invoiceInfo}>
                <View style={styles.invoiceHeader}>
                  <Text style={[styles.invoiceDate, { color: theme.colors.text }]}>
                    {formatDate(invoice.invoice_date)}
                  </Text>
                  <Text style={[styles.invoiceAmount, { color: theme.colors.text }]}>
                    {formatAmount(invoice.amount, invoice.currency)}
                  </Text>
                </View>
                
                <View style={styles.invoiceDetails}>
                  <Text style={[styles.invoiceDescription, { color: theme.colors.textSecondary }]}>
                    {invoice.description || 'Subscription payment'}
                  </Text>
                  <View style={styles.invoiceStatus}>
                    <Ionicons
                      name={invoice.status === 'paid' ? 'checkmark-circle' : 'time'}
                      size={14}
                      color={invoice.status === 'paid' ? theme.colors.success : theme.colors.warning}
                    />
                    <Text
                      style={[
                        styles.invoiceStatusText,
                        {
                          color: invoice.status === 'paid' ? theme.colors.success : theme.colors.warning,
                        }
                      ]}
                    >
                      {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                    </Text>
                  </View>
                </View>
              </View>
              
              <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          ))
        )}
      </View>

      {/* Support */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Need Help?
        </Text>
        
        <TouchableOpacity
          style={styles.supportOption}
          onPress={() => navigation.navigate('Support')}
        >
          <Ionicons name="help-circle-outline" size={20} color={theme.colors.primary} />
          <Text style={[styles.supportText, { color: theme.colors.text }]}>
            Contact Support
          </Text>
          <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.supportOption}
          onPress={() => navigation.navigate('FAQ')}
        >
          <Ionicons name="document-text-outline" size={20} color={theme.colors.primary} />
          <Text style={[styles.supportText, { color: theme.colors.text }]}>
            FAQ & Billing Info
          </Text>
          <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
