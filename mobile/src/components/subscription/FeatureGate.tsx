import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { subscriptionService, FeatureAccess, UsageLimit } from '@/services/subscriptionService';
import { styles } from './FeatureGate.styles';

interface FeatureGateProps {
  feature?: string;
  limit?: string;
  currentUsage?: number;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  onUpgradePress?: () => void;
}

export function FeatureGate({
  feature,
  limit,
  currentUsage,
  children,
  fallback,
  showUpgradePrompt = true,
  onUpgradePress,
}: FeatureGateProps) {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [accessInfo, setAccessInfo] = useState<FeatureAccess | UsageLimit | null>(null);
  const [loading, setLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  useEffect(() => {
    checkAccess();
  }, [feature, limit, currentUsage]);

  const checkAccess = async () => {
    try {
      setLoading(true);
      
      if (feature) {
        const featureAccess = await subscriptionService.checkFeatureAccess(feature);
        setHasAccess(featureAccess.hasAccess);
        setAccessInfo(featureAccess);
      } else if (limit && currentUsage !== undefined) {
        const usageLimit = await subscriptionService.checkUsageLimit(limit, currentUsage);
        setHasAccess(usageLimit.withinLimit);
        setAccessInfo(usageLimit);
      } else {
        setHasAccess(true);
      }
    } catch (error) {
      console.error('Error checking access:', error);
      setHasAccess(false);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradePress = () => {
    if (onUpgradePress) {
      onUpgradePress();
    } else if (showUpgradePrompt) {
      setShowUpgradeModal(true);
    } else {
      navigation.navigate('SubscriptionPlans');
    }
  };

  const handleUpgradeConfirm = () => {
    setShowUpgradeModal(false);
    navigation.navigate('SubscriptionPlans');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner size="small" />
      </View>
    );
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  // Show fallback if provided and no upgrade prompt
  if (fallback && !showUpgradePrompt) {
    return <>{fallback}</>;
  }

  // Show upgrade prompt
  return (
    <>
      <View style={[styles.gateContainer, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.gateContent}>
          <View style={styles.iconContainer}>
            <Ionicons name="lock-closed" size={24} color={theme.colors.primary} />
          </View>
          
          <Text style={[styles.gateTitle, { color: theme.colors.text }]}>
            Premium Feature
          </Text>
          
          <Text style={[styles.gateMessage, { color: theme.colors.textSecondary }]}>
            {accessInfo?.upgradeMessage || 'Upgrade to access this feature'}
          </Text>
          
          {limit && 'currentUsage' in accessInfo! && 'limit' in accessInfo! && (
            <View style={styles.usageInfo}>
              <Text style={[styles.usageText, { color: theme.colors.textSecondary }]}>
                Current usage: {accessInfo.currentUsage} / {accessInfo.limit === -1 ? '∞' : accessInfo.limit}
              </Text>
            </View>
          )}
          
          <Button
            title="Upgrade Now"
            variant="primary"
            onPress={handleUpgradePress}
            style={styles.upgradeButton}
          />
        </View>
      </View>

      {/* Upgrade Modal */}
      <Modal
        visible={showUpgradeModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowUpgradeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <LinearGradient
              colors={[theme.colors.primary, theme.colors.primaryDark]}
              style={styles.modalHeader}
            >
              <Ionicons name="star" size={32} color={theme.colors.white} />
              <Text style={[styles.modalTitle, { color: theme.colors.white }]}>
                Upgrade Required
              </Text>
            </LinearGradient>
            
            <View style={styles.modalBody}>
              <Text style={[styles.modalMessage, { color: theme.colors.text }]}>
                {accessInfo?.upgradeMessage || 'This feature requires a premium subscription.'}
              </Text>
              
              <Text style={[styles.modalSubMessage, { color: theme.colors.textSecondary }]}>
                Unlock this feature and many more with our premium plans.
              </Text>
              
              {feature && (
                <View style={styles.featureList}>
                  <View style={styles.featureItem}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                    <Text style={[styles.featureText, { color: theme.colors.text }]}>
                      {getFeatureDescription(feature)}
                    </Text>
                  </View>
                </View>
              )}
            </View>
            
            <View style={styles.modalActions}>
              <Button
                title="Maybe Later"
                variant="secondary"
                onPress={() => setShowUpgradeModal(false)}
                style={styles.modalButton}
              />
              <Button
                title="View Plans"
                variant="primary"
                onPress={handleUpgradeConfirm}
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

// Hook for checking feature access
export function useFeatureAccess(feature: string) {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkFeatureAccess();
  }, [feature]);

  const checkFeatureAccess = async () => {
    try {
      setLoading(true);
      const access = await subscriptionService.checkFeatureAccess(feature);
      setHasAccess(access.hasAccess);
    } catch (error) {
      console.error('Error checking feature access:', error);
      setHasAccess(false);
    } finally {
      setLoading(false);
    }
  };

  return { hasAccess, loading, refresh: checkFeatureAccess };
}

// Hook for checking usage limits
export function useUsageLimit(limit: string, currentUsage: number) {
  const [withinLimit, setWithinLimit] = useState<boolean | null>(null);
  const [limitInfo, setLimitInfo] = useState<UsageLimit | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkUsageLimit();
  }, [limit, currentUsage]);

  const checkUsageLimit = async () => {
    try {
      setLoading(true);
      const usage = await subscriptionService.checkUsageLimit(limit, currentUsage);
      setWithinLimit(usage.withinLimit);
      setLimitInfo(usage);
    } catch (error) {
      console.error('Error checking usage limit:', error);
      setWithinLimit(false);
    } finally {
      setLoading(false);
    }
  };

  return { withinLimit, limitInfo, loading, refresh: checkUsageLimit };
}

// Helper function to get feature descriptions
function getFeatureDescription(feature: string): string {
  const descriptions: Record<string, string> = {
    'unlimited_workouts': 'Unlimited workout sessions per month',
    'advanced_analytics': 'Detailed analytics and progress insights',
    'priority_support': 'Priority customer support',
    'custom_programs': 'Create custom workout programs',
    'nutrition_plans': 'Personalized nutrition planning',
    'video_workouts': 'Video workout demonstrations',
    'offline_access': 'Download workouts for offline use',
    'ai_coaching': 'AI-powered coaching recommendations',
    'personal_coaching': 'Personal coaching features',
    'white_label': 'White-label customization options',
  };

  return descriptions[feature] || 'Premium feature access';
}

// Component for showing upgrade prompts in lists
export function UpgradePromptCard({ 
  title = "Unlock Premium Features",
  message = "Upgrade to access advanced features and unlimited usage",
  onPress,
}: {
  title?: string;
  message?: string;
  onPress?: () => void;
}) {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      navigation.navigate('SubscriptionPlans');
    }
  };

  return (
    <TouchableOpacity
      style={[styles.upgradeCard, { backgroundColor: theme.colors.primary + '10' }]}
      onPress={handlePress}
    >
      <LinearGradient
        colors={[theme.colors.primary + '20', theme.colors.primary + '10']}
        style={styles.upgradeCardGradient}
      >
        <View style={styles.upgradeCardContent}>
          <Ionicons name="star-outline" size={24} color={theme.colors.primary} />
          <View style={styles.upgradeCardText}>
            <Text style={[styles.upgradeCardTitle, { color: theme.colors.primary }]}>
              {title}
            </Text>
            <Text style={[styles.upgradeCardMessage, { color: theme.colors.text }]}>
              {message}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.primary} />
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
}
