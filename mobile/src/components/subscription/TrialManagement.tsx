import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { stripeService } from '@/services/stripeService';
import { subscriptionService } from '@/services/subscriptionService';
import { styles } from './TrialManagement.styles';

interface TrialInfo {
  isInTrial: boolean;
  trialEndDate?: string;
  trialDaysRemaining?: number;
  hasUsedTrial: boolean;
  eligibleForTrial: boolean;
}

export function TrialManagement() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [trialInfo, setTrialInfo] = useState<TrialInfo | null>(null);
  const [promoCode, setPromoCode] = useState('');
  const [loading, setLoading] = useState(true);
  const [validatingPromo, setValidatingPromo] = useState(false);
  const [startingTrial, setStartingTrial] = useState(false);

  useEffect(() => {
    loadTrialInfo();
  }, []);

  const loadTrialInfo = async () => {
    try {
      setLoading(true);
      const subscription = await stripeService.getUserSubscription();
      const planInfo = await subscriptionService.getUserPlanInfo();
      
      const now = new Date();
      const trialEnd = subscription?.trial_end ? new Date(subscription.trial_end) : null;
      const isInTrial = subscription?.status === 'trialing' && trialEnd && trialEnd > now;
      const trialDaysRemaining = trialEnd ? Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : 0;
      
      setTrialInfo({
        isInTrial: isInTrial || false,
        trialEndDate: subscription?.trial_end,
        trialDaysRemaining: Math.max(0, trialDaysRemaining),
        hasUsedTrial: !!subscription && subscription.status !== 'trialing',
        eligibleForTrial: !subscription || planInfo.plan_tier === 'free',
      });
    } catch (error) {
      console.error('Failed to load trial info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartTrial = async (planId?: string) => {
    try {
      setStartingTrial(true);
      
      // Get available plans
      const plans = await stripeService.getSubscriptionPlans();
      const trialPlan = planId ? plans.find(p => p.id === planId) : plans.find(p => p.name === 'Pro' && p.billing_interval === 'month');
      
      if (!trialPlan) {
        Alert.alert('Error', 'Trial plan not found');
        return;
      }

      // Start trial subscription
      await stripeService.createSubscription(trialPlan.stripe_price_id);
      
      Alert.alert(
        'Trial Started!',
        `Your ${trialPlan.trial_period_days}-day free trial has started. Enjoy all premium features!`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
      
      loadTrialInfo();
    } catch (error) {
      console.error('Failed to start trial:', error);
      Alert.alert('Error', 'Failed to start trial. Please try again.');
    } finally {
      setStartingTrial(false);
    }
  };

  const handleValidatePromoCode = async () => {
    if (!promoCode.trim()) {
      Alert.alert('Error', 'Please enter a promo code');
      return;
    }

    try {
      setValidatingPromo(true);
      const validation = await stripeService.validatePromoCode(promoCode.trim());
      
      if (validation.valid) {
        Alert.alert(
          'Valid Promo Code!',
          `This code gives you ${validation.discount_value}${validation.discount_type === 'percentage' ? '%' : ` ${validation.currency?.toUpperCase()}`} off your subscription.`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Apply & Subscribe',
              onPress: () => {
                navigation.navigate('SubscriptionPlans', {
                  promoCode: promoCode.trim(),
                });
              },
            },
          ]
        );
      } else {
        Alert.alert('Invalid Code', 'This promo code is not valid or has expired.');
      }
    } catch (error) {
      console.error('Failed to validate promo code:', error);
      Alert.alert('Error', 'Failed to validate promo code. Please try again.');
    } finally {
      setValidatingPromo(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Free Trial & Promos
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Trial Status */}
      {trialInfo?.isInTrial ? (
        <LinearGradient
          colors={[theme.colors.success + '20', theme.colors.success + '10']}
          style={styles.trialActiveCard}
        >
          <View style={styles.trialActiveContent}>
            <Ionicons name="checkmark-circle" size={32} color={theme.colors.success} />
            <View style={styles.trialActiveInfo}>
              <Text style={[styles.trialActiveTitle, { color: theme.colors.text }]}>
                Free Trial Active
              </Text>
              <Text style={[styles.trialActiveMessage, { color: theme.colors.textSecondary }]}>
                {trialInfo.trialDaysRemaining} days remaining
              </Text>
              {trialInfo.trialEndDate && (
                <Text style={[styles.trialEndDate, { color: theme.colors.textSecondary }]}>
                  Ends on {formatDate(trialInfo.trialEndDate)}
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.trialActiveActions}>
            <Button
              title="Upgrade Now"
              variant="primary"
              size="small"
              onPress={() => navigation.navigate('SubscriptionPlans')}
              style={styles.upgradeButton}
            />
          </View>
        </LinearGradient>
      ) : trialInfo?.eligibleForTrial ? (
        <LinearGradient
          colors={[theme.colors.primary + '20', theme.colors.primary + '10']}
          style={styles.trialOfferCard}
        >
          <View style={styles.trialOfferContent}>
            <Ionicons name="gift" size={32} color={theme.colors.primary} />
            <View style={styles.trialOfferInfo}>
              <Text style={[styles.trialOfferTitle, { color: theme.colors.text }]}>
                Start Your Free Trial
              </Text>
              <Text style={[styles.trialOfferMessage, { color: theme.colors.textSecondary }]}>
                Get 14 days of premium features absolutely free
              </Text>
            </View>
          </View>
          
          <View style={styles.trialOfferFeatures}>
            <View style={styles.trialFeature}>
              <Ionicons name="checkmark" size={16} color={theme.colors.success} />
              <Text style={[styles.trialFeatureText, { color: theme.colors.text }]}>
                Unlimited workout programs
              </Text>
            </View>
            <View style={styles.trialFeature}>
              <Ionicons name="checkmark" size={16} color={theme.colors.success} />
              <Text style={[styles.trialFeatureText, { color: theme.colors.text }]}>
                Advanced analytics & insights
              </Text>
            </View>
            <View style={styles.trialFeature}>
              <Ionicons name="checkmark" size={16} color={theme.colors.success} />
              <Text style={[styles.trialFeatureText, { color: theme.colors.text }]}>
                AI-powered coaching
              </Text>
            </View>
            <View style={styles.trialFeature}>
              <Ionicons name="checkmark" size={16} color={theme.colors.success} />
              <Text style={[styles.trialFeatureText, { color: theme.colors.text }]}>
                Priority support
              </Text>
            </View>
          </View>
          
          <Button
            title="Start Free Trial"
            variant="primary"
            onPress={() => handleStartTrial()}
            loading={startingTrial}
            style={styles.startTrialButton}
          />
          
          <Text style={[styles.trialDisclaimer, { color: theme.colors.textSecondary }]}>
            No credit card required • Cancel anytime
          </Text>
        </LinearGradient>
      ) : (
        <View style={[styles.trialUsedCard, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="information-circle" size={24} color={theme.colors.info} />
          <View style={styles.trialUsedInfo}>
            <Text style={[styles.trialUsedTitle, { color: theme.colors.text }]}>
              Trial Already Used
            </Text>
            <Text style={[styles.trialUsedMessage, { color: theme.colors.textSecondary }]}>
              You've already used your free trial. Check out our current plans and promotions below.
            </Text>
          </View>
        </View>
      )}

      {/* Promo Code Section */}
      <View style={[styles.promoSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Have a Promo Code?
        </Text>
        <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}>
          Enter your promotional code to get special discounts on your subscription.
        </Text>
        
        <View style={styles.promoInputContainer}>
          <TextInput
            style={[
              styles.promoInput,
              {
                backgroundColor: theme.colors.background,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              }
            ]}
            value={promoCode}
            onChangeText={setPromoCode}
            placeholder="Enter promo code"
            placeholderTextColor={theme.colors.textSecondary}
            autoCapitalize="characters"
            autoCorrect={false}
          />
          <Button
            title="Validate"
            variant="primary"
            size="small"
            onPress={handleValidatePromoCode}
            loading={validatingPromo}
            disabled={!promoCode.trim() || validatingPromo}
            style={styles.validateButton}
          />
        </View>
      </View>

      {/* Current Promotions */}
      <View style={[styles.promotionsSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Current Promotions
        </Text>
        
        <View style={styles.promotionsList}>
          <View style={[styles.promotionCard, { backgroundColor: theme.colors.background }]}>
            <View style={styles.promotionHeader}>
              <Ionicons name="calendar" size={20} color={theme.colors.warning} />
              <Text style={[styles.promotionTitle, { color: theme.colors.text }]}>
                Annual Plan Discount
              </Text>
            </View>
            <Text style={[styles.promotionDescription, { color: theme.colors.textSecondary }]}>
              Save 20% when you choose an annual subscription plan
            </Text>
            <View style={[styles.promotionBadge, { backgroundColor: theme.colors.warning + '20' }]}>
              <Text style={[styles.promotionBadgeText, { color: theme.colors.warning }]}>
                20% OFF
              </Text>
            </View>
          </View>
          
          <View style={[styles.promotionCard, { backgroundColor: theme.colors.background }]}>
            <View style={styles.promotionHeader}>
              <Ionicons name="people" size={20} color={theme.colors.info} />
              <Text style={[styles.promotionTitle, { color: theme.colors.text }]}>
                Refer a Friend
              </Text>
            </View>
            <Text style={[styles.promotionDescription, { color: theme.colors.textSecondary }]}>
              Get 1 month free for each friend who subscribes
            </Text>
            <TouchableOpacity
              style={styles.promotionAction}
              onPress={() => navigation.navigate('ReferralProgram')}
            >
              <Text style={[styles.promotionActionText, { color: theme.colors.primary }]}>
                Learn More
              </Text>
              <Ionicons name="chevron-forward" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={[styles.promotionCard, { backgroundColor: theme.colors.background }]}>
            <View style={styles.promotionHeader}>
              <Ionicons name="school" size={20} color={theme.colors.success} />
              <Text style={[styles.promotionTitle, { color: theme.colors.text }]}>
                Student Discount
              </Text>
            </View>
            <Text style={[styles.promotionDescription, { color: theme.colors.textSecondary }]}>
              50% off for verified students with valid .edu email
            </Text>
            <TouchableOpacity
              style={styles.promotionAction}
              onPress={() => navigation.navigate('StudentDiscount')}
            >
              <Text style={[styles.promotionActionText, { color: theme.colors.primary }]}>
                Verify Student Status
              </Text>
              <Ionicons name="chevron-forward" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Trial Benefits */}
      <View style={[styles.benefitsSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Why Try Premium?
        </Text>
        
        <View style={styles.benefitsList}>
          <View style={styles.benefitItem}>
            <Ionicons name="fitness" size={24} color={theme.colors.primary} />
            <View style={styles.benefitContent}>
              <Text style={[styles.benefitTitle, { color: theme.colors.text }]}>
                Unlimited Workouts
              </Text>
              <Text style={[styles.benefitDescription, { color: theme.colors.textSecondary }]}>
                Access to unlimited workout programs and custom routines
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitItem}>
            <Ionicons name="analytics" size={24} color={theme.colors.primary} />
            <View style={styles.benefitContent}>
              <Text style={[styles.benefitTitle, { color: theme.colors.text }]}>
                Advanced Analytics
              </Text>
              <Text style={[styles.benefitDescription, { color: theme.colors.textSecondary }]}>
                Detailed progress tracking and performance insights
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitItem}>
            <Ionicons name="brain" size={24} color={theme.colors.primary} />
            <View style={styles.benefitContent}>
              <Text style={[styles.benefitTitle, { color: theme.colors.text }]}>
                AI Coaching
              </Text>
              <Text style={[styles.benefitDescription, { color: theme.colors.textSecondary }]}>
                Personalized AI coaching and form analysis
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitItem}>
            <Ionicons name="headset" size={24} color={theme.colors.primary} />
            <View style={styles.benefitContent}>
              <Text style={[styles.benefitTitle, { color: theme.colors.text }]}>
                Priority Support
              </Text>
              <Text style={[styles.benefitDescription, { color: theme.colors.textSecondary }]}>
                24/7 priority customer support from fitness experts
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* CTA Section */}
      <View style={styles.ctaSection}>
        <Button
          title="View All Plans"
          variant="primary"
          onPress={() => navigation.navigate('SubscriptionPlans')}
          style={styles.ctaButton}
        />
        
        <TouchableOpacity
          style={styles.supportLink}
          onPress={() => navigation.navigate('Support')}
        >
          <Text style={[styles.supportLinkText, { color: theme.colors.primary }]}>
            Need help? Contact Support
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
