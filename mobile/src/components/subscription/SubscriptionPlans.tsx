import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { stripeService, SubscriptionPlan } from '@/services/stripeService';
import { styles } from './SubscriptionPlans.styles';

export function SubscriptionPlans() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState<string | null>(null);
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>('month');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [plansData, subscriptionData] = await Promise.all([
        stripeService.getSubscriptionPlans(),
        stripeService.getUserSubscription(),
      ]);
      
      setPlans(plansData);
      setCurrentSubscription(subscriptionData);
    } catch (error) {
      console.error('Failed to load subscription data:', error);
      Alert.alert('Error', 'Failed to load subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (plan: SubscriptionPlan) => {
    if (!user) {
      Alert.alert('Sign In Required', 'Please sign in to subscribe to a plan');
      return;
    }

    try {
      setSubscribing(plan.id);
      
      // Navigate to payment flow
      navigation.navigate('PaymentFlow', {
        planId: plan.id,
        priceId: plan.stripe_price_id,
        planName: plan.name,
        amount: billingInterval === 'month' ? plan.price_monthly : plan.price_yearly,
        billingInterval,
        onSuccess: () => {
          loadData();
          Alert.alert('Success', 'Subscription activated successfully!');
        },
      });
    } catch (error) {
      console.error('Failed to start subscription:', error);
      Alert.alert('Error', 'Failed to start subscription process');
    } finally {
      setSubscribing(null);
    }
  };

  const handleManageSubscription = () => {
    navigation.navigate('ManageSubscription');
  };

  const getFeatureIcon = (feature: string) => {
    const iconMap: Record<string, string> = {
      'unlimited_workouts': 'fitness-outline',
      'advanced_analytics': 'analytics-outline',
      'priority_support': 'headset-outline',
      'custom_programs': 'create-outline',
      'progress_tracking': 'trending-up-outline',
      'nutrition_plans': 'nutrition-outline',
      'video_workouts': 'play-circle-outline',
      'offline_access': 'download-outline',
    };
    return iconMap[feature] || 'checkmark-circle-outline';
  };

  const formatPrice = (price: number | undefined, interval: string) => {
    if (!price) return 'Free';
    return `$${price}/${interval === 'month' ? 'mo' : 'yr'}`;
  };

  const getDiscountPercentage = (monthlyPrice?: number, yearlyPrice?: number) => {
    if (!monthlyPrice || !yearlyPrice) return 0;
    const yearlyMonthly = yearlyPrice / 12;
    return Math.round(((monthlyPrice - yearlyMonthly) / monthlyPrice) * 100);
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isCurrentPlan = currentSubscription?.plan_id === plan.id;
    const price = billingInterval === 'month' ? plan.price_monthly : plan.price_yearly;
    const features = Object.entries(plan.features || {}).filter(([_, enabled]) => enabled);
    
    return (
      <View
        key={plan.id}
        style={[
          styles.planCard,
          {
            backgroundColor: theme.colors.surface,
            borderColor: plan.is_popular ? theme.colors.primary : theme.colors.border,
            borderWidth: plan.is_popular ? 2 : 1,
          }
        ]}
      >
        {/* Popular Badge */}
        {plan.is_popular && (
          <View style={[styles.popularBadge, { backgroundColor: theme.colors.primary }]}>
            <Text style={[styles.popularText, { color: theme.colors.white }]}>
              Most Popular
            </Text>
          </View>
        )}

        {/* Plan Header */}
        <View style={styles.planHeader}>
          <Text style={[styles.planName, { color: theme.colors.text }]}>
            {plan.name}
          </Text>
          <Text style={[styles.planDescription, { color: theme.colors.textSecondary }]}>
            {plan.description}
          </Text>
        </View>

        {/* Pricing */}
        <View style={styles.pricingContainer}>
          <View style={styles.priceRow}>
            <Text style={[styles.price, { color: theme.colors.text }]}>
              {formatPrice(price, billingInterval)}
            </Text>
            {billingInterval === 'year' && plan.price_monthly && plan.price_yearly && (
              <View style={[styles.discountBadge, { backgroundColor: theme.colors.success + '20' }]}>
                <Text style={[styles.discountText, { color: theme.colors.success }]}>
                  Save {getDiscountPercentage(plan.price_monthly, plan.price_yearly)}%
                </Text>
              </View>
            )}
          </View>
          
          {plan.trial_period_days > 0 && (
            <Text style={[styles.trialText, { color: theme.colors.primary }]}>
              {plan.trial_period_days} days free trial
            </Text>
          )}
        </View>

        {/* Features */}
        <View style={styles.featuresContainer}>
          {features.map(([feature, _], index) => (
            <View key={index} style={styles.featureRow}>
              <Ionicons
                name={getFeatureIcon(feature) as any}
                size={16}
                color={theme.colors.success}
              />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                {feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
            </View>
          ))}
        </View>

        {/* Action Button */}
        <View style={styles.actionContainer}>
          {isCurrentPlan ? (
            <Button
              title="Manage Subscription"
              variant="secondary"
              onPress={handleManageSubscription}
              style={styles.actionButton}
            />
          ) : (
            <Button
              title={price ? 'Subscribe' : 'Current Plan'}
              variant={plan.is_popular ? 'primary' : 'secondary'}
              onPress={() => handleSubscribe(plan)}
              loading={subscribing === plan.id}
              disabled={subscribing !== null || !price}
              style={styles.actionButton}
            />
          )}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.white }]}>
            Choose Your Plan
          </Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={[styles.headerSubtitle, { color: theme.colors.white }]}>
          Unlock premium features and take your fitness to the next level
        </Text>
      </LinearGradient>

      {/* Current Subscription Status */}
      {currentSubscription && (
        <View style={[styles.currentSubscription, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.subscriptionInfo}>
            <Ionicons name="checkmark-circle" size={24} color={theme.colors.success} />
            <View style={styles.subscriptionDetails}>
              <Text style={[styles.subscriptionTitle, { color: theme.colors.text }]}>
                Current Plan: {currentSubscription.plan?.name || 'Premium'}
              </Text>
              <Text style={[styles.subscriptionStatus, { color: theme.colors.textSecondary }]}>
                Status: {currentSubscription.status}
                {currentSubscription.current_period_end && (
                  ` • Renews ${new Date(currentSubscription.current_period_end).toLocaleDateString()}`
                )}
              </Text>
            </View>
          </View>
          <TouchableOpacity onPress={handleManageSubscription}>
            <Ionicons name="settings-outline" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Billing Toggle */}
      <View style={styles.billingToggle}>
        <Text style={[styles.billingLabel, { color: theme.colors.text }]}>
          Billing Frequency
        </Text>
        <View style={[styles.toggleContainer, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity
            style={[
              styles.toggleOption,
              {
                backgroundColor: billingInterval === 'month'
                  ? theme.colors.primary
                  : 'transparent',
              }
            ]}
            onPress={() => setBillingInterval('month')}
          >
            <Text
              style={[
                styles.toggleText,
                {
                  color: billingInterval === 'month'
                    ? theme.colors.white
                    : theme.colors.text,
                }
              ]}
            >
              Monthly
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.toggleOption,
              {
                backgroundColor: billingInterval === 'year'
                  ? theme.colors.primary
                  : 'transparent',
              }
            ]}
            onPress={() => setBillingInterval('year')}
          >
            <Text
              style={[
                styles.toggleText,
                {
                  color: billingInterval === 'year'
                    ? theme.colors.white
                    : theme.colors.text,
                }
              ]}
            >
              Yearly
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Plans */}
      <View style={styles.plansContainer}>
        {plans
          .filter(plan => plan.billing_interval === billingInterval)
          .sort((a, b) => a.sort_order - b.sort_order)
          .map(renderPlanCard)}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          All plans include a {plans[0]?.trial_period_days || 7} day free trial.
          Cancel anytime. No hidden fees.
        </Text>
        
        <TouchableOpacity
          style={styles.supportLink}
          onPress={() => navigation.navigate('Support')}
        >
          <Text style={[styles.supportText, { color: theme.colors.primary }]}>
            Need help choosing? Contact Support
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
