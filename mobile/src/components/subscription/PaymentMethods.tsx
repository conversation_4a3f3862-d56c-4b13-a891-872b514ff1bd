import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { stripeService, PaymentMethod } from '@/services/stripeService';
import { styles } from './PaymentMethods.styles';

export function PaymentMethods() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  const loadPaymentMethods = async () => {
    try {
      setLoading(true);
      const methods = await stripeService.getPaymentMethods();
      setPaymentMethods(methods);
    } catch (error) {
      console.error('Failed to load payment methods:', error);
      Alert.alert('Error', 'Failed to load payment methods');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPaymentMethods();
    setRefreshing(false);
  };

  const handleAddPaymentMethod = () => {
    navigation.navigate('AddPaymentMethod', {
      onSuccess: () => {
        loadPaymentMethods();
      },
    });
  };

  const handleSetDefault = async (paymentMethodId: string) => {
    try {
      setActionLoading(`default_${paymentMethodId}`);
      await stripeService.setDefaultPaymentMethod(paymentMethodId);
      Alert.alert('Success', 'Default payment method updated');
      loadPaymentMethods();
    } catch (error) {
      console.error('Failed to set default payment method:', error);
      Alert.alert('Error', 'Failed to update default payment method');
    } finally {
      setActionLoading(null);
    }
  };

  const handleRemovePaymentMethod = (paymentMethod: PaymentMethod) => {
    Alert.alert(
      'Remove Payment Method',
      `Are you sure you want to remove this ${paymentMethod.brand?.toUpperCase()} card ending in ${paymentMethod.last4}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              setActionLoading(`remove_${paymentMethod.id}`);
              await stripeService.removePaymentMethod(paymentMethod.stripe_payment_method_id);
              Alert.alert('Success', 'Payment method removed');
              loadPaymentMethods();
            } catch (error) {
              console.error('Failed to remove payment method:', error);
              Alert.alert('Error', 'Failed to remove payment method');
            } finally {
              setActionLoading(null);
            }
          },
        },
      ]
    );
  };

  const getCardIcon = (brand?: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa': return 'card';
      case 'mastercard': return 'card';
      case 'amex': return 'card';
      case 'discover': return 'card';
      default: return 'card-outline';
    }
  };

  const getCardColor = (brand?: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa': return '#1A1F71';
      case 'mastercard': return '#EB001B';
      case 'amex': return '#006FCF';
      case 'discover': return '#FF6000';
      default: return theme.colors.textSecondary;
    }
  };

  const renderPaymentMethod = (method: PaymentMethod) => (
    <View
      key={method.id}
      style={[
        styles.paymentMethodCard,
        {
          backgroundColor: theme.colors.surface,
          borderColor: method.is_default ? theme.colors.primary : theme.colors.border,
        }
      ]}
    >
      {/* Card Header */}
      <View style={styles.cardHeader}>
        <View style={styles.cardInfo}>
          <Ionicons
            name={getCardIcon(method.brand) as any}
            size={24}
            color={getCardColor(method.brand)}
          />
          <View style={styles.cardDetails}>
            <Text style={[styles.cardBrand, { color: theme.colors.text }]}>
              {method.brand?.toUpperCase()} •••• {method.last4}
            </Text>
            {method.exp_month && method.exp_year && (
              <Text style={[styles.cardExpiry, { color: theme.colors.textSecondary }]}>
                Expires {method.exp_month.toString().padStart(2, '0')}/{method.exp_year}
              </Text>
            )}
          </View>
        </View>

        {method.is_default && (
          <View style={[styles.defaultBadge, { backgroundColor: theme.colors.primary + '20' }]}>
            <Text style={[styles.defaultText, { color: theme.colors.primary }]}>
              Default
            </Text>
          </View>
        )}
      </View>

      {/* Card Actions */}
      <View style={styles.cardActions}>
        {!method.is_default && (
          <Button
            title="Set as Default"
            variant="secondary"
            size="small"
            onPress={() => handleSetDefault(method.stripe_payment_method_id)}
            loading={actionLoading === `default_${method.id}`}
            style={styles.actionButton}
          />
        )}
        
        <Button
          title="Remove"
          variant="outline"
          size="small"
          onPress={() => handleRemovePaymentMethod(method)}
          loading={actionLoading === `remove_${method.id}`}
          style={styles.actionButton}
        />
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Payment Methods
        </Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddPaymentMethod}
        >
          <Ionicons name="add" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Payment Methods List */}
        {paymentMethods.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="card-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
              No Payment Methods
            </Text>
            <Text style={[styles.emptyStateMessage, { color: theme.colors.textSecondary }]}>
              Add a payment method to manage your subscription and make purchases.
            </Text>
            <Button
              title="Add Payment Method"
              variant="primary"
              onPress={handleAddPaymentMethod}
              style={styles.addPaymentButton}
            />
          </View>
        ) : (
          <View style={styles.paymentMethodsList}>
            {paymentMethods.map(renderPaymentMethod)}
            
            {/* Add New Card Button */}
            <TouchableOpacity
              style={[styles.addNewCard, { backgroundColor: theme.colors.surface }]}
              onPress={handleAddPaymentMethod}
            >
              <Ionicons name="add-circle-outline" size={24} color={theme.colors.primary} />
              <Text style={[styles.addNewCardText, { color: theme.colors.primary }]}>
                Add New Payment Method
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Security Info */}
        <View style={[styles.securityInfo, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.securityHeader}>
            <Ionicons name="shield-checkmark" size={20} color={theme.colors.success} />
            <Text style={[styles.securityTitle, { color: theme.colors.text }]}>
              Secure & Encrypted
            </Text>
          </View>
          <Text style={[styles.securityMessage, { color: theme.colors.textSecondary }]}>
            Your payment information is encrypted and securely stored. We never store your full card details on our servers.
          </Text>
          
          <View style={styles.securityFeatures}>
            <View style={styles.securityFeature}>
              <Ionicons name="lock-closed" size={16} color={theme.colors.success} />
              <Text style={[styles.securityFeatureText, { color: theme.colors.textSecondary }]}>
                256-bit SSL encryption
              </Text>
            </View>
            <View style={styles.securityFeature}>
              <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
              <Text style={[styles.securityFeatureText, { color: theme.colors.textSecondary }]}>
                PCI DSS compliant
              </Text>
            </View>
            <View style={styles.securityFeature}>
              <Ionicons name="shield" size={16} color={theme.colors.success} />
              <Text style={[styles.securityFeatureText, { color: theme.colors.textSecondary }]}>
                Fraud protection
              </Text>
            </View>
          </View>
        </View>

        {/* Help Section */}
        <View style={[styles.helpSection, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.helpTitle, { color: theme.colors.text }]}>
            Need Help?
          </Text>
          
          <TouchableOpacity
            style={styles.helpOption}
            onPress={() => navigation.navigate('Support')}
          >
            <Ionicons name="help-circle-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.helpOptionText, { color: theme.colors.text }]}>
              Contact Support
            </Text>
            <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.helpOption}
            onPress={() => navigation.navigate('BillingFAQ')}
          >
            <Ionicons name="document-text-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.helpOptionText, { color: theme.colors.text }]}>
              Billing FAQ
            </Text>
            <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
