import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Video } from 'expo-av';
import { useTheme } from '@/contexts/ThemeContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { PremiumFeatureScreen } from './PremiumWorkoutFeatures';
import { styles } from './VideoWorkouts.styles';

interface VideoWorkout {
  id: string;
  title: string;
  description: string;
  duration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  instructor: string;
  thumbnail_url: string;
  video_url: string;
  equipment: string[];
  muscle_groups: string[];
  calories_burned: number;
  rating: number;
  views: number;
}

const mockVideoWorkouts: VideoWorkout[] = [
  {
    id: '1',
    title: 'Full Body HIIT Blast',
    description: 'High-intensity interval training for maximum calorie burn',
    duration: 30,
    difficulty: 'intermediate',
    category: 'HIIT',
    instructor: '<PERSON>',
    thumbnail_url: 'https://example.com/thumbnail1.jpg',
    video_url: 'https://example.com/video1.mp4',
    equipment: ['None'],
    muscle_groups: ['Full Body'],
    calories_burned: 350,
    rating: 4.8,
    views: 12500,
  },
  {
    id: '2',
    title: 'Yoga Flow for Flexibility',
    description: 'Gentle yoga flow to improve flexibility and reduce stress',
    duration: 45,
    difficulty: 'beginner',
    category: 'Yoga',
    instructor: 'Michael Chen',
    thumbnail_url: 'https://example.com/thumbnail2.jpg',
    video_url: 'https://example.com/video2.mp4',
    equipment: ['Yoga Mat'],
    muscle_groups: ['Full Body'],
    calories_burned: 180,
    rating: 4.9,
    views: 8900,
  },
  {
    id: '3',
    title: 'Strength Training Fundamentals',
    description: 'Learn proper form for essential strength exercises',
    duration: 60,
    difficulty: 'beginner',
    category: 'Strength',
    instructor: 'David Rodriguez',
    thumbnail_url: 'https://example.com/thumbnail3.jpg',
    video_url: 'https://example.com/video3.mp4',
    equipment: ['Dumbbells', 'Bench'],
    muscle_groups: ['Upper Body', 'Lower Body'],
    calories_burned: 280,
    rating: 4.7,
    views: 15200,
  },
];

const categories = ['All', 'HIIT', 'Strength', 'Yoga', 'Cardio', 'Pilates', 'Dance'];
const difficulties = ['All', 'Beginner', 'Intermediate', 'Advanced'];

export function VideoWorkouts() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [videos, setVideos] = useState<VideoWorkout[]>(mockVideoWorkouts);
  const [filteredVideos, setFilteredVideos] = useState<VideoWorkout[]>(mockVideoWorkouts);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedDifficulty, setSelectedDifficulty] = useState('All');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    filterVideos();
  }, [searchQuery, selectedCategory, selectedDifficulty, videos]);

  const filterVideos = () => {
    let filtered = videos;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(video =>
        video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.instructor.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(video => video.category === selectedCategory);
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'All') {
      filtered = filtered.filter(video => 
        video.difficulty === selectedDifficulty.toLowerCase()
      );
    }

    setFilteredVideos(filtered);
  };

  const handleVideoPress = (video: VideoWorkout) => {
    navigation.navigate('VideoPlayer', { video });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return theme.colors.success;
      case 'intermediate': return theme.colors.warning;
      case 'advanced': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const renderVideoCard = (video: VideoWorkout) => (
    <TouchableOpacity
      key={video.id}
      style={[styles.videoCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleVideoPress(video)}
    >
      {/* Video Thumbnail */}
      <View style={styles.thumbnailContainer}>
        <Image
          source={{ uri: video.thumbnail_url }}
          style={styles.thumbnail}
          defaultSource={require('@/assets/images/video-placeholder.png')}
        />
        <View style={styles.playOverlay}>
          <Ionicons name="play-circle" size={48} color={theme.colors.white} />
        </View>
        <View style={[styles.durationBadge, { backgroundColor: theme.colors.black + '80' }]}>
          <Text style={[styles.durationText, { color: theme.colors.white }]}>
            {formatDuration(video.duration)}
          </Text>
        </View>
      </View>

      {/* Video Info */}
      <View style={styles.videoInfo}>
        <Text style={[styles.videoTitle, { color: theme.colors.text }]}>
          {video.title}
        </Text>
        <Text style={[styles.videoDescription, { color: theme.colors.textSecondary }]}>
          {video.description}
        </Text>
        
        <View style={styles.videoMeta}>
          <Text style={[styles.instructor, { color: theme.colors.textSecondary }]}>
            by {video.instructor}
          </Text>
          <View style={styles.metaDivider} />
          <View style={styles.rating}>
            <Ionicons name="star" size={14} color={theme.colors.warning} />
            <Text style={[styles.ratingText, { color: theme.colors.textSecondary }]}>
              {video.rating}
            </Text>
          </View>
        </View>

        <View style={styles.videoTags}>
          <View style={[styles.difficultyTag, { backgroundColor: getDifficultyColor(video.difficulty) + '20' }]}>
            <Text style={[styles.difficultyText, { color: getDifficultyColor(video.difficulty) }]}>
              {video.difficulty.charAt(0).toUpperCase() + video.difficulty.slice(1)}
            </Text>
          </View>
          <View style={[styles.categoryTag, { backgroundColor: theme.colors.primary + '20' }]}>
            <Text style={[styles.categoryText, { color: theme.colors.primary }]}>
              {video.category}
            </Text>
          </View>
          <View style={styles.caloriesTag}>
            <Ionicons name="flame" size={14} color={theme.colors.error} />
            <Text style={[styles.caloriesText, { color: theme.colors.textSecondary }]}>
              {video.calories_burned} cal
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <PremiumFeatureScreen featureKey="video_workouts" title="Video Workouts">
      <ScrollView style={styles.container}>
        {/* Search Bar */}
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search workouts, instructors..."
            placeholderTextColor={theme.colors.textSecondary}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Category Filter */}
        <View style={styles.filterSection}>
          <Text style={[styles.filterTitle, { color: theme.colors.text }]}>
            Category
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterOptions}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.filterOption,
                    {
                      backgroundColor: selectedCategory === category
                        ? theme.colors.primary
                        : theme.colors.surface,
                      borderColor: theme.colors.border,
                    }
                  ]}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text
                    style={[
                      styles.filterOptionText,
                      {
                        color: selectedCategory === category
                          ? theme.colors.white
                          : theme.colors.text,
                      }
                    ]}
                  >
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Difficulty Filter */}
        <View style={styles.filterSection}>
          <Text style={[styles.filterTitle, { color: theme.colors.text }]}>
            Difficulty
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.filterOptions}>
              {difficulties.map((difficulty) => (
                <TouchableOpacity
                  key={difficulty}
                  style={[
                    styles.filterOption,
                    {
                      backgroundColor: selectedDifficulty === difficulty
                        ? theme.colors.primary
                        : theme.colors.surface,
                      borderColor: theme.colors.border,
                    }
                  ]}
                  onPress={() => setSelectedDifficulty(difficulty)}
                >
                  <Text
                    style={[
                      styles.filterOptionText,
                      {
                        color: selectedDifficulty === difficulty
                          ? theme.colors.white
                          : theme.colors.text,
                      }
                    ]}
                  >
                    {difficulty}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Results Count */}
        <View style={styles.resultsHeader}>
          <Text style={[styles.resultsCount, { color: theme.colors.textSecondary }]}>
            {filteredVideos.length} workout{filteredVideos.length !== 1 ? 's' : ''} found
          </Text>
        </View>

        {/* Video List */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <LoadingSpinner size="large" />
          </View>
        ) : filteredVideos.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="videocam-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
              No Videos Found
            </Text>
            <Text style={[styles.emptyStateMessage, { color: theme.colors.textSecondary }]}>
              Try adjusting your search or filter criteria
            </Text>
          </View>
        ) : (
          <View style={styles.videosList}>
            {filteredVideos.map(renderVideoCard)}
          </View>
        )}
      </ScrollView>
    </PremiumFeatureScreen>
  );
}
