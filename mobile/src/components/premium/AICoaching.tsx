import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { PremiumFeatureScreen } from './PremiumWorkoutFeatures';
import { styles } from './AICoaching.styles';

interface AIInsight {
  id: string;
  type: 'recommendation' | 'warning' | 'achievement' | 'tip';
  title: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  category: string;
  created_at: string;
  action?: {
    label: string;
    type: string;
    data?: any;
  };
}

interface CoachingSession {
  id: string;
  title: string;
  description: string;
  duration: number;
  type: 'form_analysis' | 'program_review' | 'goal_setting' | 'nutrition_advice';
  status: 'available' | 'in_progress' | 'completed';
  completed_at?: string;
}

const mockInsights: AIInsight[] = [
  {
    id: '1',
    type: 'recommendation',
    title: 'Increase Progressive Overload',
    message: 'Your bench press has plateaued for 2 weeks. Consider increasing weight by 2.5lbs or adding an extra rep.',
    priority: 'high',
    category: 'Strength Training',
    created_at: '2024-01-15T10:00:00Z',
    action: {
      label: 'Adjust Program',
      type: 'program_modification',
      data: { exercise: 'bench_press', adjustment: 'increase_weight' },
    },
  },
  {
    id: '2',
    type: 'warning',
    title: 'Recovery Concern',
    message: 'Your workout intensity has been high for 5 consecutive days. Consider taking a rest day to prevent overtraining.',
    priority: 'high',
    category: 'Recovery',
    created_at: '2024-01-14T15:30:00Z',
    action: {
      label: 'Schedule Rest Day',
      type: 'schedule_rest',
    },
  },
  {
    id: '3',
    type: 'achievement',
    title: 'Consistency Milestone',
    message: 'Congratulations! You\'ve completed 4 weeks of consistent training. Your dedication is paying off!',
    priority: 'medium',
    category: 'Motivation',
    created_at: '2024-01-13T09:00:00Z',
  },
  {
    id: '4',
    type: 'tip',
    title: 'Form Improvement',
    message: 'Focus on controlled eccentric movement in your squats. This will improve strength and reduce injury risk.',
    priority: 'medium',
    category: 'Technique',
    created_at: '2024-01-12T14:20:00Z',
    action: {
      label: 'Watch Tutorial',
      type: 'video_tutorial',
      data: { exercise: 'squat', focus: 'eccentric_control' },
    },
  },
];

const mockSessions: CoachingSession[] = [
  {
    id: '1',
    title: 'Form Analysis Session',
    description: 'AI-powered analysis of your exercise form with personalized feedback',
    duration: 15,
    type: 'form_analysis',
    status: 'available',
  },
  {
    id: '2',
    title: 'Program Review',
    description: 'Comprehensive review of your current program with optimization suggestions',
    duration: 20,
    type: 'program_review',
    status: 'available',
  },
  {
    id: '3',
    title: 'Goal Setting Workshop',
    description: 'Set SMART fitness goals with AI-guided planning and milestone tracking',
    duration: 25,
    type: 'goal_setting',
    status: 'completed',
    completed_at: '2024-01-10T16:00:00Z',
  },
];

export function AICoaching() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [insights, setInsights] = useState<AIInsight[]>(mockInsights);
  const [sessions, setSessions] = useState<CoachingSession[]>(mockSessions);
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [askingQuestion, setAskingQuestion] = useState(false);

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'recommendation': return 'bulb-outline';
      case 'warning': return 'warning-outline';
      case 'achievement': return 'trophy-outline';
      case 'tip': return 'information-circle-outline';
      default: return 'chatbubble-outline';
    }
  };

  const getInsightColor = (type: string, priority: string) => {
    if (priority === 'high') return theme.colors.error;
    
    switch (type) {
      case 'recommendation': return theme.colors.primary;
      case 'warning': return theme.colors.warning;
      case 'achievement': return theme.colors.success;
      case 'tip': return theme.colors.info;
      default: return theme.colors.textSecondary;
    }
  };

  const getSessionIcon = (type: string) => {
    switch (type) {
      case 'form_analysis': return 'eye-outline';
      case 'program_review': return 'document-text-outline';
      case 'goal_setting': return 'flag-outline';
      case 'nutrition_advice': return 'nutrition-outline';
      default: return 'chatbubble-outline';
    }
  };

  const handleInsightAction = (insight: AIInsight) => {
    if (!insight.action) return;

    switch (insight.action.type) {
      case 'program_modification':
        Alert.alert('Program Adjustment', 'This would navigate to program modification screen');
        break;
      case 'schedule_rest':
        Alert.alert('Rest Day', 'This would schedule a rest day in your program');
        break;
      case 'video_tutorial':
        Alert.alert('Tutorial', 'This would open the relevant video tutorial');
        break;
      default:
        Alert.alert('Action', `${insight.action.label} action would be performed`);
    }
  };

  const handleStartSession = (session: CoachingSession) => {
    if (session.status !== 'available') return;

    Alert.alert(
      'Start AI Coaching Session',
      `Start "${session.title}"? This will take approximately ${session.duration} minutes.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start',
          onPress: () => {
            // Navigate to specific coaching session
            navigation.navigate('CoachingSession', { 
              sessionId: session.id,
              type: session.type,
            });
          },
        },
      ]
    );
  };

  const handleAskQuestion = async () => {
    if (!question.trim()) return;

    try {
      setAskingQuestion(true);
      
      // Simulate AI response
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'AI Coach Response',
        'This is where the AI would provide a personalized response to your question. The response would be based on your workout history, goals, and current program.',
        [
          { text: 'Ask Another', onPress: () => setQuestion('') },
          { text: 'Done' },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to get AI response. Please try again.');
    } finally {
      setAskingQuestion(false);
    }
  };

  const renderInsight = (insight: AIInsight) => (
    <View
      key={insight.id}
      style={[
        styles.insightCard,
        {
          backgroundColor: theme.colors.surface,
          borderLeftColor: getInsightColor(insight.type, insight.priority),
        }
      ]}
    >
      <View style={styles.insightHeader}>
        <View style={styles.insightIcon}>
          <Ionicons
            name={getInsightIcon(insight.type) as any}
            size={20}
            color={getInsightColor(insight.type, insight.priority)}
          />
        </View>
        <View style={styles.insightInfo}>
          <Text style={[styles.insightTitle, { color: theme.colors.text }]}>
            {insight.title}
          </Text>
          <Text style={[styles.insightCategory, { color: theme.colors.textSecondary }]}>
            {insight.category}
          </Text>
        </View>
        <View style={[styles.priorityBadge, { backgroundColor: getInsightColor(insight.type, insight.priority) + '20' }]}>
          <Text style={[styles.priorityText, { color: getInsightColor(insight.type, insight.priority) }]}>
            {insight.priority}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.insightMessage, { color: theme.colors.textSecondary }]}>
        {insight.message}
      </Text>
      
      {insight.action && (
        <TouchableOpacity
          style={[styles.insightAction, { backgroundColor: theme.colors.primary + '10' }]}
          onPress={() => handleInsightAction(insight)}
        >
          <Text style={[styles.insightActionText, { color: theme.colors.primary }]}>
            {insight.action.label}
          </Text>
          <Ionicons name="chevron-forward" size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderSession = (session: CoachingSession) => (
    <TouchableOpacity
      key={session.id}
      style={[
        styles.sessionCard,
        {
          backgroundColor: theme.colors.surface,
          opacity: session.status === 'available' ? 1 : 0.7,
        }
      ]}
      onPress={() => handleStartSession(session)}
      disabled={session.status !== 'available'}
    >
      <View style={styles.sessionHeader}>
        <View style={styles.sessionIcon}>
          <Ionicons
            name={getSessionIcon(session.type) as any}
            size={24}
            color={session.status === 'completed' ? theme.colors.success : theme.colors.primary}
          />
        </View>
        <View style={styles.sessionInfo}>
          <Text style={[styles.sessionTitle, { color: theme.colors.text }]}>
            {session.title}
          </Text>
          <Text style={[styles.sessionDescription, { color: theme.colors.textSecondary }]}>
            {session.description}
          </Text>
        </View>
        <View style={styles.sessionMeta}>
          <Text style={[styles.sessionDuration, { color: theme.colors.textSecondary }]}>
            {session.duration}m
          </Text>
          {session.status === 'completed' && (
            <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <PremiumFeatureScreen featureKey="ai_coaching" title="AI Coaching">
      <ScrollView style={styles.container}>
        {/* AI Coach Header */}
        <LinearGradient
          colors={[theme.colors.primary + '20', theme.colors.primary + '10']}
          style={styles.coachHeader}
        >
          <View style={styles.coachInfo}>
            <View style={styles.coachAvatar}>
              <Ionicons name="brain" size={32} color={theme.colors.primary} />
            </View>
            <View style={styles.coachDetails}>
              <Text style={[styles.coachName, { color: theme.colors.text }]}>
                AI Fitness Coach
              </Text>
              <Text style={[styles.coachDescription, { color: theme.colors.textSecondary }]}>
                Your personal AI coach analyzing your progress 24/7
              </Text>
            </View>
          </View>
        </LinearGradient>

        {/* Ask AI Section */}
        <View style={[styles.askSection, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Ask Your AI Coach
          </Text>
          <View style={styles.questionContainer}>
            <TextInput
              style={[
                styles.questionInput,
                {
                  backgroundColor: theme.colors.background,
                  color: theme.colors.text,
                  borderColor: theme.colors.border,
                }
              ]}
              value={question}
              onChangeText={setQuestion}
              placeholder="Ask about your workout, form, nutrition, or goals..."
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={3}
            />
            <Button
              title="Ask AI"
              variant="primary"
              onPress={handleAskQuestion}
              loading={askingQuestion}
              disabled={!question.trim() || askingQuestion}
              style={styles.askButton}
            />
          </View>
        </View>

        {/* AI Insights */}
        <View style={styles.insightsSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            AI Insights & Recommendations
          </Text>
          {insights.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="bulb-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
                No insights available yet
              </Text>
              <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
                Complete more workouts to get personalized AI insights
              </Text>
            </View>
          ) : (
            <View style={styles.insightsList}>
              {insights.map(renderInsight)}
            </View>
          )}
        </View>

        {/* Coaching Sessions */}
        <View style={styles.sessionsSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            AI Coaching Sessions
          </Text>
          <View style={styles.sessionsList}>
            {sessions.map(renderSession)}
          </View>
        </View>

        {/* AI Features */}
        <View style={[styles.featuresSection, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            AI Coach Features
          </Text>
          
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Ionicons name="analytics" size={20} color={theme.colors.primary} />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                Progress Analysis & Predictions
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="eye" size={20} color={theme.colors.primary} />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                Form Analysis & Corrections
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="refresh" size={20} color={theme.colors.primary} />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                Adaptive Program Adjustments
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="shield-checkmark" size={20} color={theme.colors.primary} />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                Injury Prevention Alerts
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="nutrition" size={20} color={theme.colors.primary} />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                Nutrition Recommendations
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="moon" size={20} color={theme.colors.primary} />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                Recovery Optimization
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </PremiumFeatureScreen>
  );
}
