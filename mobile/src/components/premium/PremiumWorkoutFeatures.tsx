import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { FeatureGate, useFeatureAccess } from '@/components/subscription/FeatureGate';
import { subscriptionService } from '@/services/subscriptionService';
import { styles } from './PremiumWorkoutFeatures.styles';

interface PremiumFeature {
  id: string;
  title: string;
  description: string;
  icon: string;
  featureKey: string;
  requiredPlan: string;
  comingSoon?: boolean;
}

const premiumFeatures: PremiumFeature[] = [
  {
    id: 'ai_coaching',
    title: 'AI Personal Coaching',
    description: 'Get personalized coaching insights and real-time form feedback powered by AI',
    icon: 'brain-outline',
    featureKey: 'ai_coaching',
    requiredPlan: 'Pro',
  },
  {
    id: 'video_workouts',
    title: 'Video Workout Library',
    description: 'Access thousands of professional workout videos with step-by-step instructions',
    icon: 'play-circle-outline',
    featureKey: 'video_workouts',
    requiredPlan: 'Basic',
  },
  {
    id: 'nutrition_plans',
    title: 'Personalized Nutrition Plans',
    description: 'Custom meal plans and nutrition tracking tailored to your fitness goals',
    icon: 'nutrition-outline',
    featureKey: 'nutrition_plans',
    requiredPlan: 'Pro',
  },
  {
    id: 'advanced_analytics',
    title: 'Advanced Analytics',
    description: 'Detailed performance insights, trend analysis, and predictive recommendations',
    icon: 'analytics-outline',
    featureKey: 'advanced_analytics',
    requiredPlan: 'Pro',
  },
  {
    id: 'offline_access',
    title: 'Offline Workout Access',
    description: 'Download workouts and access them anywhere, even without internet connection',
    icon: 'download-outline',
    featureKey: 'offline_access',
    requiredPlan: 'Basic',
  },
  {
    id: 'priority_support',
    title: 'Priority Support',
    description: '24/7 priority customer support with dedicated fitness experts',
    icon: 'headset-outline',
    featureKey: 'priority_support',
    requiredPlan: 'Pro',
  },
  {
    id: 'custom_programs',
    title: 'Custom Program Builder',
    description: 'Create unlimited custom workout programs with advanced exercise library',
    icon: 'create-outline',
    featureKey: 'custom_programs',
    requiredPlan: 'Pro',
  },
  {
    id: 'personal_coaching',
    title: 'Personal Coaching Sessions',
    description: 'One-on-one virtual coaching sessions with certified trainers',
    icon: 'person-outline',
    featureKey: 'personal_coaching',
    requiredPlan: 'Elite',
  },
];

export function PremiumWorkoutFeatures() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [planInfo, setPlanInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPlanInfo();
  }, []);

  const loadPlanInfo = async () => {
    try {
      setLoading(true);
      const info = await subscriptionService.getUserPlanInfo();
      setPlanInfo(info);
    } catch (error) {
      console.error('Failed to load plan info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = () => {
    navigation.navigate('SubscriptionPlans');
  };

  const handleFeaturePress = (feature: PremiumFeature) => {
    if (feature.comingSoon) {
      Alert.alert('Coming Soon', 'This feature is coming soon! Stay tuned for updates.');
      return;
    }

    // Navigate to specific feature screen
    switch (feature.id) {
      case 'ai_coaching':
        navigation.navigate('AICoaching');
        break;
      case 'video_workouts':
        navigation.navigate('VideoWorkouts');
        break;
      case 'nutrition_plans':
        navigation.navigate('NutritionPlans');
        break;
      case 'advanced_analytics':
        navigation.navigate('AdvancedAnalytics');
        break;
      case 'offline_access':
        navigation.navigate('OfflineWorkouts');
        break;
      case 'priority_support':
        navigation.navigate('PrioritySupport');
        break;
      case 'custom_programs':
        navigation.navigate('CustomProgramBuilder');
        break;
      case 'personal_coaching':
        navigation.navigate('PersonalCoaching');
        break;
      default:
        Alert.alert('Feature', `${feature.title} feature would open here`);
    }
  };

  const getPlanTierColor = (requiredPlan: string) => {
    switch (requiredPlan) {
      case 'Basic': return theme.colors.info;
      case 'Pro': return theme.colors.primary;
      case 'Elite': return theme.colors.warning;
      default: return theme.colors.textSecondary;
    }
  };

  const hasAccessToPlan = (requiredPlan: string) => {
    if (!planInfo) return false;
    
    const planHierarchy = { 'Free': 0, 'Basic': 1, 'Pro': 2, 'Elite': 3 };
    const currentTier = planHierarchy[planInfo.plan_name] || 0;
    const requiredTier = planHierarchy[requiredPlan] || 0;
    
    return currentTier >= requiredTier;
  };

  const renderFeatureCard = (feature: PremiumFeature) => {
    const hasAccess = hasAccessToPlan(feature.requiredPlan);
    
    return (
      <TouchableOpacity
        key={feature.id}
        style={[
          styles.featureCard,
          {
            backgroundColor: theme.colors.surface,
            borderColor: hasAccess ? theme.colors.success : theme.colors.border,
            opacity: feature.comingSoon ? 0.7 : 1,
          }
        ]}
        onPress={() => handleFeaturePress(feature)}
        disabled={feature.comingSoon}
      >
        {/* Feature Header */}
        <View style={styles.featureHeader}>
          <View style={styles.featureIcon}>
            <Ionicons
              name={feature.icon as any}
              size={24}
              color={hasAccess ? theme.colors.success : theme.colors.primary}
            />
          </View>
          
          <View style={styles.featureInfo}>
            <Text style={[styles.featureTitle, { color: theme.colors.text }]}>
              {feature.title}
            </Text>
            <Text style={[styles.featureDescription, { color: theme.colors.textSecondary }]}>
              {feature.description}
            </Text>
          </View>
        </View>

        {/* Feature Status */}
        <View style={styles.featureStatus}>
          {feature.comingSoon ? (
            <View style={[styles.comingSoonBadge, { backgroundColor: theme.colors.warning + '20' }]}>
              <Text style={[styles.comingSoonText, { color: theme.colors.warning }]}>
                Coming Soon
              </Text>
            </View>
          ) : hasAccess ? (
            <View style={[styles.accessBadge, { backgroundColor: theme.colors.success + '20' }]}>
              <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
              <Text style={[styles.accessText, { color: theme.colors.success }]}>
                Available
              </Text>
            </View>
          ) : (
            <View style={styles.upgradeInfo}>
              <View style={[styles.planBadge, { backgroundColor: getPlanTierColor(feature.requiredPlan) + '20' }]}>
                <Text style={[styles.planText, { color: getPlanTierColor(feature.requiredPlan) }]}>
                  {feature.requiredPlan}
                </Text>
              </View>
              <Ionicons name="lock-closed" size={16} color={theme.colors.textSecondary} />
            </View>
          )}
        </View>

        {/* Access Indicator */}
        {!feature.comingSoon && (
          <View style={styles.accessIndicator}>
            <Ionicons
              name={hasAccess ? 'chevron-forward' : 'lock-closed'}
              size={16}
              color={hasAccess ? theme.colors.textSecondary : theme.colors.primary}
            />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.white} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.white }]}>
            Premium Features
          </Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={[styles.headerSubtitle, { color: theme.colors.white }]}>
          Unlock advanced features to supercharge your fitness journey
        </Text>
      </LinearGradient>

      {/* Current Plan Status */}
      <View style={[styles.planStatus, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.planStatusContent}>
          <View style={styles.planInfo}>
            <Text style={[styles.currentPlanLabel, { color: theme.colors.textSecondary }]}>
              Current Plan
            </Text>
            <Text style={[styles.currentPlanName, { color: theme.colors.text }]}>
              {planInfo?.plan_name || 'Free'}
            </Text>
          </View>
          
          {planInfo?.plan_tier !== 'elite' && (
            <Button
              title="Upgrade"
              variant="primary"
              size="small"
              onPress={handleUpgrade}
              style={styles.upgradeButton}
            />
          )}
        </View>
        
        {planInfo?.trial_end && new Date(planInfo.trial_end) > new Date() && (
          <View style={styles.trialInfo}>
            <Ionicons name="time" size={16} color={theme.colors.info} />
            <Text style={[styles.trialText, { color: theme.colors.info }]}>
              Trial ends {new Date(planInfo.trial_end).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>

      {/* Features Grid */}
      <View style={styles.featuresContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Premium Features
        </Text>
        
        <View style={styles.featuresGrid}>
          {premiumFeatures.map(renderFeatureCard)}
        </View>
      </View>

      {/* Upgrade Prompt */}
      {planInfo?.plan_tier !== 'elite' && (
        <View style={[styles.upgradePrompt, { backgroundColor: theme.colors.surface }]}>
          <LinearGradient
            colors={[theme.colors.primary + '20', theme.colors.primary + '10']}
            style={styles.upgradePromptGradient}
          >
            <View style={styles.upgradePromptContent}>
              <Ionicons name="star" size={32} color={theme.colors.primary} />
              <View style={styles.upgradePromptText}>
                <Text style={[styles.upgradePromptTitle, { color: theme.colors.text }]}>
                  Unlock All Features
                </Text>
                <Text style={[styles.upgradePromptMessage, { color: theme.colors.textSecondary }]}>
                  Get access to all premium features and take your fitness to the next level
                </Text>
              </View>
            </View>
            
            <Button
              title="View Plans"
              variant="primary"
              onPress={handleUpgrade}
              style={styles.upgradePromptButton}
            />
          </LinearGradient>
        </View>
      )}

      {/* Feature Categories */}
      <View style={styles.categoriesContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Feature Categories
        </Text>
        
        <View style={styles.categories}>
          <TouchableOpacity
            style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('WorkoutFeatures')}
          >
            <Ionicons name="fitness" size={24} color={theme.colors.primary} />
            <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
              Workout Features
            </Text>
            <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary }]}>
              Advanced workout tools and AI coaching
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('AnalyticsFeatures')}
          >
            <Ionicons name="analytics" size={24} color={theme.colors.primary} />
            <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
              Analytics & Insights
            </Text>
            <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary }]}>
              Detailed progress tracking and insights
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('SocialFeatures')}
          >
            <Ionicons name="people" size={24} color={theme.colors.primary} />
            <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
              Social & Community
            </Text>
            <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary }]}>
              Connect with others and share progress
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('SupportFeatures')}
          >
            <Ionicons name="headset" size={24} color={theme.colors.primary} />
            <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
              Support & Coaching
            </Text>
            <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary }]}>
              Priority support and personal coaching
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

// Component for individual premium feature screens
export function PremiumFeatureScreen({ 
  featureKey, 
  title, 
  children 
}: { 
  featureKey: string; 
  title: string; 
  children: React.ReactNode; 
}) {
  const navigation = useNavigation();
  const { theme } = useTheme();

  return (
    <FeatureGate feature={featureKey}>
      <View style={[styles.featureScreen, { backgroundColor: theme.colors.background }]}>
        <View style={styles.featureScreenHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.featureScreenTitle, { color: theme.colors.text }]}>
            {title}
          </Text>
          <View style={styles.placeholder} />
        </View>
        {children}
      </View>
    </FeatureGate>
  );
}
