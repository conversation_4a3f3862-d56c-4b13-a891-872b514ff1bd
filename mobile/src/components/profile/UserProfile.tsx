import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ProfileStats } from './ProfileStats';
import { ProfileWorkouts } from './ProfileWorkouts';
import { ProfileAchievements } from './ProfileAchievements';
import { userProfileService } from '@/services/userProfileService';
import { socialService } from '@/services/socialService';
import { styles } from './UserProfile.styles';

interface UserProfile {
  id: string;
  user_id: string;
  username: string;
  display_name: string;
  bio: string;
  location: string;
  fitness_level: string;
  primary_goals: string[];
  profile_visibility: string;
  followers_count: number;
  following_count: number;
  workouts_shared_count: number;
  achievements_count: number;
  avatar_url?: string;
  is_following?: boolean;
  connection_status?: string;
}

interface UserProfileProps {
  userId?: string;
}

export function UserProfile({ userId }: UserProfileProps) {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('workouts');
  const [connectionLoading, setConnectionLoading] = useState(false);

  const targetUserId = userId || route.params?.userId;
  const isOwnProfile = !targetUserId || targetUserId === user?.id;

  useEffect(() => {
    loadProfile();
  }, [targetUserId]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const profileData = await userProfileService.getProfile(targetUserId || user?.id);
      
      if (!isOwnProfile) {
        // Check connection status
        const connectionStatus = await socialService.getConnectionStatus(targetUserId);
        profileData.connection_status = connectionStatus.status;
        profileData.is_following = connectionStatus.is_following;
      }
      
      setProfile(profileData);
    } catch (error) {
      console.error('Failed to load profile:', error);
      Alert.alert('Error', 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadProfile();
    setRefreshing(false);
  };

  const handleFollow = async () => {
    if (!profile) return;
    
    try {
      setConnectionLoading(true);
      
      if (profile.is_following) {
        await socialService.unfollowUser(profile.user_id);
        setProfile(prev => prev ? {
          ...prev,
          is_following: false,
          followers_count: prev.followers_count - 1,
        } : null);
      } else {
        await socialService.followUser(profile.user_id);
        setProfile(prev => prev ? {
          ...prev,
          is_following: true,
          followers_count: prev.followers_count + 1,
        } : null);
      }
    } catch (error) {
      console.error('Failed to update follow status:', error);
      Alert.alert('Error', 'Failed to update follow status');
    } finally {
      setConnectionLoading(false);
    }
  };

  const handleMessage = () => {
    navigation.navigate('Chat', { userId: profile?.user_id });
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };

  const handleSettings = () => {
    navigation.navigate('ProfileSettings');
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={[styles.errorText, { color: theme.colors.text }]}>
          Profile not found
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          {/* Avatar */}
          <View style={styles.avatarContainer}>
            <Image
              source={{
                uri: profile.avatar_url || 'https://via.placeholder.com/120',
              }}
              style={styles.avatar}
            />
            {isOwnProfile && (
              <TouchableOpacity
                style={styles.editAvatarButton}
                onPress={() => navigation.navigate('EditAvatar')}
              >
                <Ionicons name="camera" size={16} color={theme.colors.white} />
              </TouchableOpacity>
            )}
          </View>

          {/* Profile Info */}
          <View style={styles.profileInfo}>
            <Text style={[styles.displayName, { color: theme.colors.white }]}>
              {profile.display_name || profile.username}
            </Text>
            {profile.username && (
              <Text style={[styles.username, { color: theme.colors.white }]}>
                @{profile.username}
              </Text>
            )}
            {profile.location && (
              <View style={styles.locationContainer}>
                <Ionicons name="location-outline" size={14} color={theme.colors.white} />
                <Text style={[styles.location, { color: theme.colors.white }]}>
                  {profile.location}
                </Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {isOwnProfile ? (
              <>
                <Button
                  title="Edit Profile"
                  variant="secondary"
                  size="small"
                  onPress={handleEditProfile}
                  style={styles.actionButton}
                />
                <TouchableOpacity
                  style={styles.settingsButton}
                  onPress={handleSettings}
                >
                  <Ionicons name="settings-outline" size={20} color={theme.colors.white} />
                </TouchableOpacity>
              </>
            ) : (
              <>
                <Button
                  title={profile.is_following ? 'Following' : 'Follow'}
                  variant={profile.is_following ? 'secondary' : 'primary'}
                  size="small"
                  onPress={handleFollow}
                  loading={connectionLoading}
                  style={styles.actionButton}
                />
                <TouchableOpacity
                  style={styles.messageButton}
                  onPress={handleMessage}
                >
                  <Ionicons name="chatbubble-outline" size={20} color={theme.colors.white} />
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </LinearGradient>

      {/* Bio */}
      {profile.bio && (
        <View style={styles.bioContainer}>
          <Text style={[styles.bio, { color: theme.colors.text }]}>
            {profile.bio}
          </Text>
        </View>
      )}

      {/* Stats */}
      <ProfileStats
        followersCount={profile.followers_count}
        followingCount={profile.following_count}
        workoutsSharedCount={profile.workouts_shared_count}
        achievementsCount={profile.achievements_count}
        fitnessLevel={profile.fitness_level}
        primaryGoals={profile.primary_goals}
        onFollowersPress={() => navigation.navigate('Followers', { userId: profile.user_id })}
        onFollowingPress={() => navigation.navigate('Following', { userId: profile.user_id })}
      />

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'workouts' && styles.activeTab,
          ]}
          onPress={() => setActiveTab('workouts')}
        >
          <Text
            style={[
              styles.tabText,
              { color: theme.colors.text },
              activeTab === 'workouts' && { color: theme.colors.primary },
            ]}
          >
            Workouts
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'achievements' && styles.activeTab,
          ]}
          onPress={() => setActiveTab('achievements')}
        >
          <Text
            style={[
              styles.tabText,
              { color: theme.colors.text },
              activeTab === 'achievements' && { color: theme.colors.primary },
            ]}
          >
            Achievements
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {activeTab === 'workouts' && (
          <ProfileWorkouts
            userId={profile.user_id}
            isOwnProfile={isOwnProfile}
            canViewWorkouts={profile.profile_visibility === 'public' || isOwnProfile}
          />
        )}
        {activeTab === 'achievements' && (
          <ProfileAchievements
            userId={profile.user_id}
            isOwnProfile={isOwnProfile}
            canViewAchievements={profile.profile_visibility === 'public' || isOwnProfile}
          />
        )}
      </View>
    </ScrollView>
  );
}
