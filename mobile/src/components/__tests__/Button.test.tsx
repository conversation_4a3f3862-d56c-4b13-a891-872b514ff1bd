import React from 'react';
import { fireEvent } from '@testing-library/react-native';
import { render } from '@test/utils';
import { Button } from '../Button';

describe('Button Component', () => {
  it('renders correctly with title', () => {
    const { getByText } = render(<Button title="Test Button" onPress={() => {}} />);
    
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(<Button title="Test Button" onPress={mockOnPress} />);
    
    fireEvent.press(getByText('Test Button'));
    
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} disabled />
    );
    
    const button = getByText('Test Button');
    fireEvent.press(button);
    
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('shows loading state', () => {
    const { getByTestId } = render(
      <Button title="Test Button" onPress={() => {}} loading />
    );
    
    expect(getByTestId('button-loading')).toBeTruthy();
  });

  it('applies correct variant styles', () => {
    const { getByTestId } = render(
      <Button title="Test Button" onPress={() => {}} variant="secondary" />
    );
    
    const button = getByTestId('button');
    expect(button.props.style).toMatchObject(
      expect.objectContaining({
        backgroundColor: expect.any(String),
      })
    );
  });

  it('applies correct size styles', () => {
    const { getByTestId } = render(
      <Button title="Test Button" onPress={() => {}} size="large" />
    );
    
    const button = getByTestId('button');
    expect(button.props.style).toMatchObject(
      expect.objectContaining({
        paddingVertical: expect.any(Number),
        paddingHorizontal: expect.any(Number),
      })
    );
  });

  it('renders with icon', () => {
    const { getByTestId } = render(
      <Button 
        title="Test Button" 
        onPress={() => {}} 
        icon="plus"
      />
    );
    
    expect(getByTestId('button-icon')).toBeTruthy();
  });

  it('handles long press', () => {
    const mockOnLongPress = jest.fn();
    const { getByText } = render(
      <Button 
        title="Test Button" 
        onPress={() => {}} 
        onLongPress={mockOnLongPress}
      />
    );
    
    fireEvent(getByText('Test Button'), 'longPress');
    
    expect(mockOnLongPress).toHaveBeenCalledTimes(1);
  });

  it('has correct accessibility properties', () => {
    const { getByText } = render(
      <Button 
        title="Test Button" 
        onPress={() => {}} 
        accessibilityLabel="Custom accessibility label"
      />
    );
    
    const button = getByText('Test Button');
    expect(button.props.accessibilityLabel).toBe('Custom accessibility label');
    expect(button.props.accessibilityRole).toBe('button');
  });

  it('renders full width when specified', () => {
    const { getByTestId } = render(
      <Button title="Test Button" onPress={() => {}} fullWidth />
    );
    
    const button = getByTestId('button');
    expect(button.props.style).toMatchObject(
      expect.objectContaining({
        width: '100%',
      })
    );
  });
});
