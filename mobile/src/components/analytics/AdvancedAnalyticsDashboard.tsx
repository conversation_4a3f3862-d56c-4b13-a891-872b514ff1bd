import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { FeatureGate } from '@/components/subscription/FeatureGate';
import { analyticsService } from '@/services/analyticsService';
import { predictiveAnalyticsService } from '@/services/predictiveAnalyticsService';
import { styles } from './AdvancedAnalyticsDashboard.styles';

interface AnalyticsData {
  performance: {
    strength_score: number;
    endurance_score: number;
    consistency_score: number;
    overall_score: number;
  };
  trends: {
    date: string;
    volume: number;
    intensity: number;
    frequency: number;
  }[];
  predictions: {
    next_month_volume: number;
    strength_gain_prediction: number;
    goal_completion_probability: number;
  };
  comparisons: {
    peer_percentile: number;
    improvement_rate: number;
    consistency_rank: number;
  };
  insights: {
    type: 'strength' | 'endurance' | 'consistency' | 'recovery';
    title: string;
    description: string;
    recommendation: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

const screenWidth = Dimensions.get('window').width;

export function AdvancedAnalyticsDashboard() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'1month' | '3months' | '6months' | '1year'>('3months');
  const [selectedMetric, setSelectedMetric] = useState<'volume' | 'strength' | 'endurance'>('volume');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const periods = [
    { key: '1month', label: '1M' },
    { key: '3months', label: '3M' },
    { key: '6months', label: '6M' },
    { key: '1year', label: '1Y' },
  ];

  const metrics = [
    { key: 'volume', label: 'Volume', icon: 'barbell-outline' },
    { key: 'strength', label: 'Strength', icon: 'fitness-outline' },
    { key: 'endurance', label: 'Endurance', icon: 'heart-outline' },
  ];

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriod, selectedMetric]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const [analytics, predictions] = await Promise.all([
        analyticsService.getAdvancedAnalytics(selectedPeriod),
        predictiveAnalyticsService.getProgressPredictions(undefined, selectedPeriod === '1month' ? '1month' : '3months'),
      ]);
      
      setAnalyticsData(analytics);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAnalytics();
    setRefreshing(false);
  };

  const getChartConfig = () => ({
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 1,
    color: (opacity = 1) => `${theme.colors.primary}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
    labelColor: (opacity = 1) => theme.colors.textSecondary,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
  });

  const getScoreColor = (score: number) => {
    if (score >= 80) return theme.colors.success;
    if (score >= 60) return theme.colors.warning;
    return theme.colors.error;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.info;
      default: return theme.colors.textSecondary;
    }
  };

  const renderPerformanceScores = () => {
    if (!analyticsData) return null;

    const scores = [
      { label: 'Strength', value: analyticsData.performance.strength_score, icon: 'fitness' },
      { label: 'Endurance', value: analyticsData.performance.endurance_score, icon: 'heart' },
      { label: 'Consistency', value: analyticsData.performance.consistency_score, icon: 'checkmark-circle' },
      { label: 'Overall', value: analyticsData.performance.overall_score, icon: 'trophy' },
    ];

    return (
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Performance Scores
        </Text>
        <View style={styles.scoresGrid}>
          {scores.map((score, index) => (
            <View key={index} style={styles.scoreCard}>
              <View style={styles.scoreHeader}>
                <Ionicons name={score.icon as any} size={20} color={getScoreColor(score.value)} />
                <Text style={[styles.scoreLabel, { color: theme.colors.textSecondary }]}>
                  {score.label}
                </Text>
              </View>
              <Text style={[styles.scoreValue, { color: getScoreColor(score.value) }]}>
                {score.value}
              </Text>
              <View style={styles.scoreBar}>
                <View
                  style={[
                    styles.scoreProgress,
                    {
                      backgroundColor: getScoreColor(score.value),
                      width: `${score.value}%`,
                    }
                  ]}
                />
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderTrendsChart = () => {
    if (!analyticsData || analyticsData.trends.length === 0) return null;

    const chartData = {
      labels: analyticsData.trends.map(t => 
        new Date(t.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      ),
      datasets: [{
        data: analyticsData.trends.map(t => {
          switch (selectedMetric) {
            case 'volume': return t.volume;
            case 'strength': return t.intensity;
            case 'endurance': return t.frequency;
            default: return t.volume;
          }
        }),
        color: (opacity = 1) => theme.colors.primary,
        strokeWidth: 3,
      }],
    };

    return (
      <View style={[styles.chartSection, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.chartHeader}>
          <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
            {selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)} Trends
          </Text>
          <View style={styles.metricSelector}>
            {metrics.map((metric) => (
              <TouchableOpacity
                key={metric.key}
                style={[
                  styles.metricButton,
                  {
                    backgroundColor: selectedMetric === metric.key
                      ? theme.colors.primary
                      : 'transparent',
                  }
                ]}
                onPress={() => setSelectedMetric(metric.key as any)}
              >
                <Ionicons
                  name={metric.icon as any}
                  size={16}
                  color={selectedMetric === metric.key ? theme.colors.white : theme.colors.textSecondary}
                />
                <Text
                  style={[
                    styles.metricButtonText,
                    {
                      color: selectedMetric === metric.key
                        ? theme.colors.white
                        : theme.colors.textSecondary,
                    }
                  ]}
                >
                  {metric.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <LineChart
          data={chartData}
          width={screenWidth - 40}
          height={220}
          chartConfig={getChartConfig()}
          bezier
          style={styles.chart}
        />
      </View>
    );
  };

  const renderPredictions = () => {
    if (!analyticsData) return null;

    const predictions = [
      {
        title: 'Next Month Volume',
        value: `${analyticsData.predictions.next_month_volume.toFixed(0)}k`,
        change: '+12%',
        positive: true,
      },
      {
        title: 'Strength Gain',
        value: `${analyticsData.predictions.strength_gain_prediction.toFixed(1)}%`,
        change: '+5%',
        positive: true,
      },
      {
        title: 'Goal Completion',
        value: `${(analyticsData.predictions.goal_completion_probability * 100).toFixed(0)}%`,
        change: 'On track',
        positive: true,
      },
    ];

    return (
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          AI Predictions
        </Text>
        <View style={styles.predictionsGrid}>
          {predictions.map((prediction, index) => (
            <View key={index} style={styles.predictionCard}>
              <Text style={[styles.predictionTitle, { color: theme.colors.textSecondary }]}>
                {prediction.title}
              </Text>
              <Text style={[styles.predictionValue, { color: theme.colors.text }]}>
                {prediction.value}
              </Text>
              <View style={styles.predictionChange}>
                <Ionicons
                  name={prediction.positive ? 'trending-up' : 'trending-down'}
                  size={14}
                  color={prediction.positive ? theme.colors.success : theme.colors.error}
                />
                <Text
                  style={[
                    styles.predictionChangeText,
                    {
                      color: prediction.positive ? theme.colors.success : theme.colors.error,
                    }
                  ]}
                >
                  {prediction.change}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderComparisons = () => {
    if (!analyticsData) return null;

    const comparisons = [
      {
        title: 'Peer Percentile',
        value: analyticsData.comparisons.peer_percentile,
        suffix: 'th',
        description: 'Better than peers',
      },
      {
        title: 'Improvement Rate',
        value: analyticsData.comparisons.improvement_rate,
        suffix: '%',
        description: 'Above average',
      },
      {
        title: 'Consistency Rank',
        value: analyticsData.comparisons.consistency_rank,
        suffix: '',
        description: 'Top performer',
      },
    ];

    return (
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Peer Comparisons
        </Text>
        <View style={styles.comparisonsGrid}>
          {comparisons.map((comparison, index) => (
            <View key={index} style={styles.comparisonCard}>
              <Text style={[styles.comparisonValue, { color: theme.colors.primary }]}>
                {comparison.value}{comparison.suffix}
              </Text>
              <Text style={[styles.comparisonTitle, { color: theme.colors.text }]}>
                {comparison.title}
              </Text>
              <Text style={[styles.comparisonDescription, { color: theme.colors.textSecondary }]}>
                {comparison.description}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderInsights = () => {
    if (!analyticsData || analyticsData.insights.length === 0) return null;

    return (
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          AI Insights & Recommendations
        </Text>
        <View style={styles.insightsList}>
          {analyticsData.insights.map((insight, index) => (
            <View key={index} style={styles.insightCard}>
              <View style={styles.insightHeader}>
                <View style={styles.insightIcon}>
                  <Ionicons
                    name={insight.type === 'strength' ? 'fitness' : 
                          insight.type === 'endurance' ? 'heart' :
                          insight.type === 'consistency' ? 'checkmark-circle' : 'refresh'}
                    size={20}
                    color={getPriorityColor(insight.priority)}
                  />
                </View>
                <View style={styles.insightContent}>
                  <Text style={[styles.insightTitle, { color: theme.colors.text }]}>
                    {insight.title}
                  </Text>
                  <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(insight.priority) + '20' }]}>
                    <Text style={[styles.priorityText, { color: getPriorityColor(insight.priority) }]}>
                      {insight.priority.toUpperCase()}
                    </Text>
                  </View>
                </View>
              </View>
              <Text style={[styles.insightDescription, { color: theme.colors.textSecondary }]}>
                {insight.description}
              </Text>
              <View style={[styles.recommendationBox, { backgroundColor: theme.colors.primary + '10' }]}>
                <Ionicons name="bulb" size={16} color={theme.colors.primary} />
                <Text style={[styles.recommendationText, { color: theme.colors.text }]}>
                  {insight.recommendation}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return (
    <FeatureGate feature="advanced_analytics">
      <ScrollView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header */}
        <LinearGradient
          colors={[theme.colors.primary, theme.colors.primaryDark]}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color={theme.colors.white} />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, { color: theme.colors.white }]}>
              Advanced Analytics
            </Text>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => navigation.navigate('AnalyticsSettings')}
            >
              <Ionicons name="settings-outline" size={24} color={theme.colors.white} />
            </TouchableOpacity>
          </View>
          <Text style={[styles.headerSubtitle, { color: theme.colors.white }]}>
            AI-powered insights into your fitness journey
          </Text>
        </LinearGradient>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          {periods.map((period) => (
            <TouchableOpacity
              key={period.key}
              style={[
                styles.periodButton,
                {
                  backgroundColor: selectedPeriod === period.key
                    ? theme.colors.primary
                    : theme.colors.surface,
                }
              ]}
              onPress={() => setSelectedPeriod(period.key as any)}
            >
              <Text
                style={[
                  styles.periodButtonText,
                  {
                    color: selectedPeriod === period.key
                      ? theme.colors.white
                      : theme.colors.text,
                  }
                ]}
              >
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Performance Scores */}
        {renderPerformanceScores()}

        {/* Trends Chart */}
        {renderTrendsChart()}

        {/* Predictions */}
        {renderPredictions()}

        {/* Comparisons */}
        {renderComparisons()}

        {/* Insights */}
        {renderInsights()}

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => navigation.navigate('DetailedAnalytics')}
          >
            <Ionicons name="analytics" size={20} color={theme.colors.white} />
            <Text style={[styles.actionButtonText, { color: theme.colors.white }]}>
              Detailed Analysis
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={() => navigation.navigate('ExportData')}
          >
            <Ionicons name="download" size={20} color={theme.colors.white} />
            <Text style={[styles.actionButtonText, { color: theme.colors.white }]}>
              Export Data
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </FeatureGate>
  );
}
