import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Image,
  Alert,
  TextInput,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { socialService } from '@/services/socialService';
import { userProfileService } from '@/services/userProfileService';
import { styles } from './FriendsAndFollowers.styles';

interface UserConnection {
  id: string;
  user_id: string;
  username?: string;
  display_name: string;
  avatar_url?: string;
  bio?: string;
  followers_count: number;
  following_count: number;
  is_following?: boolean;
  connection_status?: string;
}

export function FriendsAndFollowers() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const { userId, initialTab = 'followers' } = route.params as {
    userId?: string;
    initialTab?: 'followers' | 'following' | 'discover';
  };

  const [activeTab, setActiveTab] = useState(initialTab);
  const [connections, setConnections] = useState<UserConnection[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserConnection[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);

  const targetUserId = userId || user?.id;
  const isOwnProfile = !userId || userId === user?.id;

  const tabs = [
    { key: 'followers', label: 'Followers', icon: 'people-outline' },
    { key: 'following', label: 'Following', icon: 'person-outline' },
    ...(isOwnProfile ? [{ key: 'discover', label: 'Discover', icon: 'search-outline' }] : []),
  ];

  useEffect(() => {
    loadConnections();
  }, [activeTab, targetUserId]);

  useEffect(() => {
    if (activeTab === 'discover' && searchQuery.length > 2) {
      searchUsers();
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, activeTab]);

  const loadConnections = async () => {
    if (!targetUserId) return;

    try {
      setLoading(true);
      let data: any[] = [];

      if (activeTab === 'followers') {
        const followersData = await socialService.getFollowers(targetUserId);
        data = followersData.map(conn => ({
          id: conn.id,
          user_id: conn.follower_id,
          username: conn.follower_profile?.username,
          display_name: conn.follower_profile?.display_name || conn.follower_profile?.username,
          avatar_url: conn.follower_profile?.avatar_url,
          bio: conn.follower_profile?.bio,
          followers_count: conn.follower_profile?.followers_count || 0,
          following_count: conn.follower_profile?.following_count || 0,
        }));
      } else if (activeTab === 'following') {
        const followingData = await socialService.getFollowing(targetUserId);
        data = followingData.map(conn => ({
          id: conn.id,
          user_id: conn.following_id,
          username: conn.following_profile?.username,
          display_name: conn.following_profile?.display_name || conn.following_profile?.username,
          avatar_url: conn.following_profile?.avatar_url,
          bio: conn.following_profile?.bio,
          followers_count: conn.following_profile?.followers_count || 0,
          following_count: conn.following_profile?.following_count || 0,
        }));
      }

      // Get connection status for current user
      if (user && !isOwnProfile) {
        const connectionsWithStatus = await Promise.all(
          data.map(async (connection) => {
            const status = await socialService.getConnectionStatus(connection.user_id);
            return {
              ...connection,
              is_following: status.is_following,
              connection_status: status.status,
            };
          })
        );
        setConnections(connectionsWithStatus);
      } else {
        setConnections(data);
      }
    } catch (error) {
      console.error('Failed to load connections:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchUsers = async () => {
    try {
      setSearchLoading(true);
      const results = await userProfileService.searchProfiles(searchQuery);
      
      // Get connection status for each result
      const resultsWithStatus = await Promise.all(
        results.map(async (profile) => {
          const status = await socialService.getConnectionStatus(profile.user_id);
          return {
            id: profile.id,
            user_id: profile.user_id,
            username: profile.username,
            display_name: profile.display_name || profile.username,
            avatar_url: profile.avatar_url,
            bio: profile.bio,
            followers_count: profile.followers_count || 0,
            following_count: profile.following_count || 0,
            is_following: status.is_following,
            connection_status: status.status,
          };
        })
      );

      setSearchResults(resultsWithStatus);
    } catch (error) {
      console.error('Failed to search users:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadConnections();
    setRefreshing(false);
  };

  const handleFollow = async (userId: string) => {
    try {
      await socialService.followUser(userId);
      
      // Update local state
      const updateConnection = (conn: UserConnection) => {
        if (conn.user_id === userId) {
          return {
            ...conn,
            is_following: true,
            followers_count: conn.followers_count + 1,
          };
        }
        return conn;
      };

      setConnections(prev => prev.map(updateConnection));
      setSearchResults(prev => prev.map(updateConnection));
    } catch (error) {
      console.error('Failed to follow user:', error);
      Alert.alert('Error', 'Failed to follow user. Please try again.');
    }
  };

  const handleUnfollow = async (userId: string) => {
    try {
      await socialService.unfollowUser(userId);
      
      // Update local state
      const updateConnection = (conn: UserConnection) => {
        if (conn.user_id === userId) {
          return {
            ...conn,
            is_following: false,
            followers_count: Math.max(0, conn.followers_count - 1),
          };
        }
        return conn;
      };

      setConnections(prev => prev.map(updateConnection));
      setSearchResults(prev => prev.map(updateConnection));
    } catch (error) {
      console.error('Failed to unfollow user:', error);
      Alert.alert('Error', 'Failed to unfollow user. Please try again.');
    }
  };

  const handleUserPress = (userId: string) => {
    navigation.navigate('UserProfile', { userId });
  };

  const renderUserCard = (connection: UserConnection) => (
    <TouchableOpacity
      key={connection.id}
      style={[styles.userCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleUserPress(connection.user_id)}
    >
      <Image
        source={{
          uri: connection.avatar_url || 'https://via.placeholder.com/50',
        }}
        style={styles.avatar}
      />
      
      <View style={styles.userInfo}>
        <Text style={[styles.displayName, { color: theme.colors.text }]}>
          {connection.display_name}
        </Text>
        {connection.username && (
          <Text style={[styles.username, { color: theme.colors.textSecondary }]}>
            @{connection.username}
          </Text>
        )}
        {connection.bio && (
          <Text 
            style={[styles.bio, { color: theme.colors.textSecondary }]}
            numberOfLines={2}
          >
            {connection.bio}
          </Text>
        )}
        
        <View style={styles.stats}>
          <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
            {connection.followers_count} followers • {connection.following_count} following
          </Text>
        </View>
      </View>

      {/* Action Button */}
      {connection.user_id !== user?.id && (
        <View style={styles.actionContainer}>
          {connection.is_following ? (
            <Button
              title="Following"
              variant="secondary"
              size="small"
              onPress={() => handleUnfollow(connection.user_id)}
              style={styles.actionButton}
            />
          ) : (
            <Button
              title="Follow"
              variant="primary"
              size="small"
              onPress={() => handleFollow(connection.user_id)}
              style={styles.actionButton}
            />
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
        </View>
      );
    }

    const dataToRender = activeTab === 'discover' ? searchResults : connections;

    if (dataToRender.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons 
            name={activeTab === 'discover' ? 'search-outline' : 'people-outline'} 
            size={48} 
            color={theme.colors.textSecondary} 
          />
          <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
            {activeTab === 'followers' && 'No followers yet'}
            {activeTab === 'following' && 'Not following anyone yet'}
            {activeTab === 'discover' && (searchQuery.length > 2 ? 'No users found' : 'Search for users to connect')}
          </Text>
          <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
            {activeTab === 'followers' && 'Share your workouts to attract followers'}
            {activeTab === 'following' && 'Discover and follow other fitness enthusiasts'}
            {activeTab === 'discover' && 'Try searching with different keywords'}
          </Text>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.usersList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {dataToRender.map(renderUserCard)}
      </ScrollView>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          {isOwnProfile ? 'My Connections' : 'Connections'}
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Search Bar (for discover tab) */}
      {activeTab === 'discover' && (
        <View style={styles.searchContainer}>
          <View style={[styles.searchBar, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search users..."
              placeholderTextColor={theme.colors.textSecondary}
            />
            {searchLoading && <LoadingSpinner size="small" />}
          </View>
        </View>
      )}

      {/* Tabs */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tab,
              {
                backgroundColor: activeTab === tab.key
                  ? theme.colors.primary
                  : 'transparent',
              }
            ]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <Ionicons
              name={tab.icon as any}
              size={18}
              color={activeTab === tab.key ? theme.colors.white : theme.colors.textSecondary}
            />
            <Text
              style={[
                styles.tabText,
                {
                  color: activeTab === tab.key
                    ? theme.colors.white
                    : theme.colors.textSecondary,
                }
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      {renderContent()}
    </View>
  );
}
