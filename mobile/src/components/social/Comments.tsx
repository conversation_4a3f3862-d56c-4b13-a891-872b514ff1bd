import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { socialService } from '@/services/socialService';
import { workoutShareService } from '@/services/workoutShareService';
import { formatDistanceToNow } from 'date-fns';
import { styles } from './Comments.styles';

interface Comment {
  id: string;
  user_id: string;
  content: string;
  created_at: string;
  user_profile?: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

export function Comments() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const { itemId, itemType, onCommentAdded } = route.params as {
    itemId: string;
    itemType: string;
    onCommentAdded?: () => void;
  };

  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadComments();
  }, []);

  const loadComments = async () => {
    try {
      setLoading(true);
      let commentsData: Comment[] = [];

      if (itemType === 'workout_share') {
        commentsData = await workoutShareService.getShareComments(itemId);
      } else {
        commentsData = await socialService.getContentInteractions(itemType, itemId, 'comment');
      }

      setComments(commentsData);
    } catch (error) {
      console.error('Failed to load comments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    try {
      setSubmitting(true);
      
      let comment: Comment;
      if (itemType === 'workout_share') {
        comment = await workoutShareService.commentOnShare(itemId, newComment.trim());
      } else {
        comment = await socialService.commentOnContent(itemType, itemId, newComment.trim());
      }

      setComments(prev => [comment, ...prev]);
      setNewComment('');
      onCommentAdded?.();
    } catch (error) {
      console.error('Failed to submit comment:', error);
      Alert.alert('Error', 'Failed to post comment. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUserPress = (userId: string) => {
    navigation.navigate('UserProfile', { userId });
  };

  const renderComment = (comment: Comment) => (
    <View key={comment.id} style={styles.commentItem}>
      <TouchableOpacity onPress={() => handleUserPress(comment.user_id)}>
        <Image
          source={{
            uri: comment.user_profile?.avatar_url || 'https://via.placeholder.com/32',
          }}
          style={styles.commentAvatar}
        />
      </TouchableOpacity>
      
      <View style={styles.commentContent}>
        <View style={styles.commentBubble}>
          <TouchableOpacity onPress={() => handleUserPress(comment.user_id)}>
            <Text style={[styles.commentAuthor, { color: theme.colors.primary }]}>
              {comment.user_profile?.display_name || comment.user_profile?.username}
            </Text>
          </TouchableOpacity>
          <Text style={[styles.commentText, { color: theme.colors.text }]}>
            {comment.content}
          </Text>
        </View>
        <Text style={[styles.commentTimestamp, { color: theme.colors.textSecondary }]}>
          {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
        </Text>
      </View>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Comments
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Comments List */}
      <ScrollView style={styles.commentsList}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <LoadingSpinner size="large" />
          </View>
        ) : comments.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="chatbubble-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
              No comments yet
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
              Be the first to leave a comment!
            </Text>
          </View>
        ) : (
          <View style={styles.commentsContainer}>
            {comments.map(renderComment)}
          </View>
        )}
      </ScrollView>

      {/* Comment Input */}
      <View style={[styles.commentInput, { backgroundColor: theme.colors.surface }]}>
        <Image
          source={{
            uri: user?.user_metadata?.avatar_url || 'https://via.placeholder.com/32',
          }}
          style={styles.inputAvatar}
        />
        <TextInput
          style={[
            styles.textInput,
            { 
              backgroundColor: theme.colors.background,
              color: theme.colors.text,
              borderColor: theme.colors.border,
            }
          ]}
          value={newComment}
          onChangeText={setNewComment}
          placeholder="Write a comment..."
          placeholderTextColor={theme.colors.textSecondary}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            {
              backgroundColor: newComment.trim() && !submitting
                ? theme.colors.primary
                : theme.colors.border,
            }
          ]}
          onPress={handleSubmitComment}
          disabled={!newComment.trim() || submitting}
        >
          {submitting ? (
            <LoadingSpinner size="small" color={theme.colors.white} />
          ) : (
            <Ionicons 
              name="send" 
              size={20} 
              color={newComment.trim() ? theme.colors.white : theme.colors.textSecondary} 
            />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}
