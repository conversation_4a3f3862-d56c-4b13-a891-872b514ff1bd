import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { leaderboardService } from '@/services/leaderboardService';
import { styles } from './Leaderboards.styles';

interface LeaderboardEntry {
  id: string;
  user_id: string;
  rank: number;
  score: number;
  entry_data?: any;
  user_profile?: {
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

interface Leaderboard {
  id: string;
  name: string;
  description: string;
  metric_type: string;
  time_period: string;
  category: string;
}

export function Leaderboards() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [leaderboards, setLeaderboards] = useState<Leaderboard[]>([]);
  const [selectedLeaderboard, setSelectedLeaderboard] = useState<Leaderboard | null>(null);
  const [entries, setEntries] = useState<LeaderboardEntry[]>([]);
  const [userRank, setUserRank] = useState<LeaderboardEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('weekly');

  const timePeriods = [
    { value: 'weekly', label: 'This Week' },
    { value: 'monthly', label: 'This Month' },
    { value: 'yearly', label: 'This Year' },
    { value: 'all_time', label: 'All Time' },
  ];

  useEffect(() => {
    loadLeaderboards();
  }, []);

  useEffect(() => {
    if (selectedLeaderboard) {
      loadLeaderboardEntries();
    }
  }, [selectedLeaderboard, selectedPeriod]);

  const loadLeaderboards = async () => {
    try {
      setLoading(true);
      const data = await leaderboardService.getLeaderboards();
      setLeaderboards(data);
      
      if (data.length > 0) {
        // Select first leaderboard by default
        const defaultLeaderboard = data.find(lb => lb.time_period === selectedPeriod) || data[0];
        setSelectedLeaderboard(defaultLeaderboard);
      }
    } catch (error) {
      console.error('Failed to load leaderboards:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadLeaderboardEntries = async () => {
    if (!selectedLeaderboard) return;

    try {
      const [entriesData, userRankData] = await Promise.all([
        leaderboardService.getLeaderboardEntries(selectedLeaderboard.id, selectedPeriod),
        user ? leaderboardService.getUserRank(selectedLeaderboard.id, selectedPeriod, user.id) : null,
      ]);

      setEntries(entriesData);
      setUserRank(userRankData);
    } catch (error) {
      console.error('Failed to load leaderboard entries:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadLeaderboards();
    setRefreshing(false);
  };

  const handleLeaderboardChange = (leaderboard: Leaderboard) => {
    setSelectedLeaderboard(leaderboard);
  };

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
  };

  const formatScore = (score: number, metricType: string): string => {
    switch (metricType) {
      case 'total_weight':
        return `${score.toLocaleString()} lbs`;
      case 'total_workouts':
        return `${score} workouts`;
      case 'streak_days':
        return `${score} days`;
      case 'total_duration':
        return `${Math.round(score / 60)} hours`;
      default:
        return score.toString();
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `#${rank}`;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return '#FFD700'; // Gold
      case 2:
        return '#C0C0C0'; // Silver
      case 3:
        return '#CD7F32'; // Bronze
      default:
        return theme.colors.textSecondary;
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Leaderboards
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
          Compete with the community
        </Text>
      </View>

      {/* Leaderboard Categories */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
      >
        {leaderboards.map((leaderboard) => (
          <TouchableOpacity
            key={leaderboard.id}
            style={[
              styles.categoryButton,
              {
                backgroundColor: selectedLeaderboard?.id === leaderboard.id
                  ? theme.colors.primary
                  : theme.colors.surface,
                borderColor: selectedLeaderboard?.id === leaderboard.id
                  ? theme.colors.primary
                  : theme.colors.border,
              }
            ]}
            onPress={() => handleLeaderboardChange(leaderboard)}
          >
            <Text
              style={[
                styles.categoryButtonText,
                {
                  color: selectedLeaderboard?.id === leaderboard.id
                    ? theme.colors.white
                    : theme.colors.text,
                }
              ]}
            >
              {leaderboard.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Time Period Selector */}
      <View style={styles.periodContainer}>
        {timePeriods.map((period) => (
          <TouchableOpacity
            key={period.value}
            style={[
              styles.periodButton,
              {
                backgroundColor: selectedPeriod === period.value
                  ? theme.colors.primary
                  : 'transparent',
              }
            ]}
            onPress={() => handlePeriodChange(period.value)}
          >
            <Text
              style={[
                styles.periodButtonText,
                {
                  color: selectedPeriod === period.value
                    ? theme.colors.white
                    : theme.colors.text,
                }
              ]}
            >
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* User's Rank (if not in top 10) */}
      {userRank && userRank.rank > 10 && (
        <View style={styles.userRankContainer}>
          <LinearGradient
            colors={[theme.colors.primary + '20', theme.colors.primary + '10']}
            style={styles.userRankCard}
          >
            <View style={styles.userRankContent}>
              <Text style={[styles.userRankLabel, { color: theme.colors.primary }]}>
                Your Rank
              </Text>
              <View style={styles.userRankInfo}>
                <Text style={[styles.userRankNumber, { color: theme.colors.primary }]}>
                  #{userRank.rank}
                </Text>
                <Text style={[styles.userRankScore, { color: theme.colors.text }]}>
                  {formatScore(userRank.score, selectedLeaderboard?.metric_type || '')}
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>
      )}

      {/* Leaderboard Entries */}
      <View style={styles.entriesContainer}>
        {entries.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="trophy-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
              No entries yet for this period
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
              Complete some workouts to appear on the leaderboard!
            </Text>
          </View>
        ) : (
          entries.map((entry, index) => (
            <TouchableOpacity
              key={entry.id}
              style={[
                styles.entryCard,
                {
                  backgroundColor: entry.user_id === user?.id
                    ? theme.colors.primary + '10'
                    : theme.colors.surface,
                  borderColor: entry.user_id === user?.id
                    ? theme.colors.primary
                    : theme.colors.border,
                }
              ]}
              onPress={() => navigation.navigate('UserProfile', { userId: entry.user_id })}
            >
              {/* Rank */}
              <View style={styles.rankContainer}>
                <Text
                  style={[
                    styles.rankText,
                    { color: getRankColor(entry.rank) }
                  ]}
                >
                  {getRankIcon(entry.rank)}
                </Text>
              </View>

              {/* User Info */}
              <View style={styles.userInfo}>
                <Image
                  source={{
                    uri: entry.user_profile?.avatar_url || 'https://via.placeholder.com/40',
                  }}
                  style={styles.avatar}
                />
                <View style={styles.userDetails}>
                  <Text style={[styles.displayName, { color: theme.colors.text }]}>
                    {entry.user_profile?.display_name || entry.user_profile?.username}
                  </Text>
                  {entry.user_profile?.username && (
                    <Text style={[styles.username, { color: theme.colors.textSecondary }]}>
                      @{entry.user_profile.username}
                    </Text>
                  )}
                </View>
              </View>

              {/* Score */}
              <View style={styles.scoreContainer}>
                <Text style={[styles.scoreText, { color: theme.colors.text }]}>
                  {formatScore(entry.score, selectedLeaderboard?.metric_type || '')}
                </Text>
              </View>

              {/* Current User Indicator */}
              {entry.user_id === user?.id && (
                <View style={styles.currentUserIndicator}>
                  <Ionicons name="person" size={16} color={theme.colors.primary} />
                </View>
              )}
            </TouchableOpacity>
          ))
        )}
      </View>

      {/* Leaderboard Info */}
      {selectedLeaderboard && (
        <View style={styles.infoContainer}>
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            About {selectedLeaderboard.name}
          </Text>
          <Text style={[styles.infoDescription, { color: theme.colors.textSecondary }]}>
            {selectedLeaderboard.description}
          </Text>
          <Text style={[styles.infoNote, { color: theme.colors.textSecondary }]}>
            Rankings update every hour. Complete workouts to improve your position!
          </Text>
        </View>
      )}
    </ScrollView>
  );
}
