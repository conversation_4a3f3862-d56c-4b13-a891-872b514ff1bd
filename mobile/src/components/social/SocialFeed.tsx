import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Image,
  Alert,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { socialService } from '@/services/socialService';
import { workoutShareService } from '@/services/workoutShareService';
import { FeedItem } from './FeedItem';
import { styles } from './SocialFeed.styles';

interface FeedItem {
  id: string;
  type: 'activity' | 'workout_share';
  data: any;
  created_at: string;
}

export function SocialFeed() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [feedItems, setFeedItems] = useState<FeedItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  useFocusEffect(
    useCallback(() => {
      loadFeed();
    }, [])
  );

  const loadFeed = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // Load both activity feed and workout shares
      const [activityFeed, workoutShares] = await Promise.all([
        socialService.getActivityFeed(25),
        workoutShareService.getFeedShares(25),
      ]);

      // Combine and sort by date
      const combinedFeed: FeedItem[] = [
        ...activityFeed.map(item => ({
          id: `activity_${item.id}`,
          type: 'activity' as const,
          data: item,
          created_at: item.created_at,
        })),
        ...workoutShares.map(item => ({
          id: `share_${item.id}`,
          type: 'workout_share' as const,
          data: item,
          created_at: item.created_at,
        })),
      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      setFeedItems(combinedFeed);
      setHasMore(combinedFeed.length >= 50);
    } catch (error) {
      console.error('Failed to load feed:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadMoreFeed = async () => {
    if (loadingMore || !hasMore) return;

    try {
      setLoadingMore(true);
      
      // Load more items with offset
      const offset = feedItems.length;
      const [activityFeed, workoutShares] = await Promise.all([
        socialService.getActivityFeed(25),
        workoutShareService.getFeedShares(25, offset),
      ]);

      const newItems: FeedItem[] = [
        ...activityFeed.map(item => ({
          id: `activity_${item.id}`,
          type: 'activity' as const,
          data: item,
          created_at: item.created_at,
        })),
        ...workoutShares.map(item => ({
          id: `share_${item.id}`,
          type: 'workout_share' as const,
          data: item,
          created_at: item.created_at,
        })),
      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      if (newItems.length === 0) {
        setHasMore(false);
      } else {
        setFeedItems(prev => [...prev, ...newItems]);
      }
    } catch (error) {
      console.error('Failed to load more feed items:', error);
    } finally {
      setLoadingMore(false);
    }
  };

  const handleRefresh = () => {
    loadFeed(true);
  };

  const handleLike = async (itemId: string, itemType: string) => {
    try {
      if (itemType === 'workout_share') {
        await workoutShareService.likeShare(itemId);
      } else {
        await socialService.likeContent(itemType, itemId);
      }
      
      // Update local state
      setFeedItems(prev => prev.map(item => {
        if (item.data.id === itemId) {
          return {
            ...item,
            data: {
              ...item.data,
              is_liked: true,
              likes_count: (item.data.likes_count || 0) + 1,
            },
          };
        }
        return item;
      }));
    } catch (error) {
      console.error('Failed to like item:', error);
      Alert.alert('Error', 'Failed to like item. Please try again.');
    }
  };

  const handleUnlike = async (itemId: string, itemType: string) => {
    try {
      if (itemType === 'workout_share') {
        await workoutShareService.unlikeShare(itemId);
      } else {
        await socialService.unlikeContent(itemType, itemId);
      }
      
      // Update local state
      setFeedItems(prev => prev.map(item => {
        if (item.data.id === itemId) {
          return {
            ...item,
            data: {
              ...item.data,
              is_liked: false,
              likes_count: Math.max(0, (item.data.likes_count || 0) - 1),
            },
          };
        }
        return item;
      }));
    } catch (error) {
      console.error('Failed to unlike item:', error);
      Alert.alert('Error', 'Failed to unlike item. Please try again.');
    }
  };

  const handleComment = (itemId: string, itemType: string) => {
    navigation.navigate('Comments', { 
      itemId, 
      itemType,
      onCommentAdded: () => {
        // Refresh feed to show new comment count
        loadFeed();
      },
    });
  };

  const handleShare = (itemId: string, itemType: string) => {
    // Implement share functionality
    Alert.alert('Share', 'Share functionality coming soon!');
  };

  const handleUserPress = (userId: string) => {
    navigation.navigate('UserProfile', { userId });
  };

  const renderFeedItem = (item: FeedItem) => {
    return (
      <FeedItem
        key={item.id}
        item={item}
        onLike={handleLike}
        onUnlike={handleUnlike}
        onComment={handleComment}
        onShare={handleShare}
        onUserPress={handleUserPress}
      />
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="heart-outline" size={48} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
        Your feed is empty
      </Text>
      <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
        Follow other users and share your workouts to see activity here
      </Text>
      <TouchableOpacity
        style={[styles.exploreButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => navigation.navigate('Discover')}
      >
        <Text style={[styles.exploreButtonText, { color: theme.colors.white }]}>
          Discover Users
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <LoadingSpinner size="large" />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Social Feed
        </Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('Search')}
          >
            <Ionicons name="search-outline" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.navigate('CreatePost')}
          >
            <Ionicons name="add-outline" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Feed */}
      <ScrollView
        style={styles.feed}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;
          
          if (layoutMeasurement.height + contentOffset.y >= contentSize.height - paddingToBottom) {
            loadMoreFeed();
          }
        }}
        scrollEventThrottle={400}
      >
        {feedItems.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            {feedItems.map(renderFeedItem)}
            
            {/* Load More Indicator */}
            {loadingMore && (
              <View style={styles.loadMoreContainer}>
                <LoadingSpinner size="small" />
                <Text style={[styles.loadMoreText, { color: theme.colors.textSecondary }]}>
                  Loading more...
                </Text>
              </View>
            )}
            
            {/* End of Feed */}
            {!hasMore && feedItems.length > 0 && (
              <View style={styles.endOfFeed}>
                <Text style={[styles.endOfFeedText, { color: theme.colors.textSecondary }]}>
                  You're all caught up! 🎉
                </Text>
              </View>
            )}
          </>
        )}
      </ScrollView>
    </View>
  );
}
