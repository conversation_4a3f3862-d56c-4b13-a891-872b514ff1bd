import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { challengeService } from '@/services/challengeService';
import { styles } from './CreateChallenge.styles';

interface ChallengeFormData {
  title: string;
  description: string;
  challenge_type: 'distance' | 'reps' | 'weight' | 'duration' | 'frequency';
  target_value: string;
  target_unit: string;
  start_date: Date;
  end_date: Date;
  visibility: 'public' | 'friends' | 'private';
  max_participants: string;
  requires_approval: boolean;
  reward_type: string;
  reward_name: string;
}

export function CreateChallenge() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  
  const [formData, setFormData] = useState<ChallengeFormData>({
    title: '',
    description: '',
    challenge_type: 'reps',
    target_value: '',
    target_unit: '',
    start_date: new Date(),
    end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
    visibility: 'public',
    max_participants: '',
    requires_approval: false,
    reward_type: '',
    reward_name: '',
  });

  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [loading, setLoading] = useState(false);

  const challengeTypes = [
    { value: 'reps', label: 'Repetitions', icon: 'fitness-outline', units: ['reps'] },
    { value: 'weight', label: 'Weight Lifted', icon: 'barbell-outline', units: ['lbs', 'kg'] },
    { value: 'distance', label: 'Distance', icon: 'walk-outline', units: ['miles', 'km', 'meters'] },
    { value: 'duration', label: 'Time Duration', icon: 'time-outline', units: ['minutes', 'hours'] },
    { value: 'frequency', label: 'Frequency', icon: 'calendar-outline', units: ['times', 'days'] },
  ];

  const visibilityOptions = [
    { value: 'public', label: 'Public', icon: 'globe-outline', description: 'Anyone can join' },
    { value: 'friends', label: 'Friends Only', icon: 'people-outline', description: 'Only friends can join' },
    { value: 'private', label: 'Private', icon: 'lock-closed-outline', description: 'Invite only' },
  ];

  const rewardTypes = [
    { value: '', label: 'No Reward' },
    { value: 'badge', label: 'Digital Badge' },
    { value: 'points', label: 'Points' },
    { value: 'custom', label: 'Custom Reward' },
  ];

  const handleInputChange = (field: keyof ChallengeFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleChallengeTypeChange = (type: string) => {
    const selectedType = challengeTypes.find(t => t.value === type);
    setFormData(prev => ({
      ...prev,
      challenge_type: type as any,
      target_unit: selectedType?.units[0] || '',
    }));
  };

  const handleDateChange = (event: any, selectedDate?: Date, type: 'start' | 'end' = 'start') => {
    if (Platform.OS === 'android') {
      setShowStartDatePicker(false);
      setShowEndDatePicker(false);
    }

    if (selectedDate) {
      if (type === 'start') {
        setFormData(prev => ({ ...prev, start_date: selectedDate }));
        // Ensure end date is after start date
        if (selectedDate >= formData.end_date) {
          const newEndDate = new Date(selectedDate.getTime() + 7 * 24 * 60 * 60 * 1000);
          setFormData(prev => ({ ...prev, end_date: newEndDate }));
        }
      } else {
        setFormData(prev => ({ ...prev, end_date: selectedDate }));
      }
    }
  };

  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter a challenge title');
      return false;
    }

    if (!formData.description.trim()) {
      Alert.alert('Error', 'Please enter a challenge description');
      return false;
    }

    if (!formData.target_value || isNaN(Number(formData.target_value)) || Number(formData.target_value) <= 0) {
      Alert.alert('Error', 'Please enter a valid target value');
      return false;
    }

    if (formData.start_date >= formData.end_date) {
      Alert.alert('Error', 'End date must be after start date');
      return false;
    }

    if (formData.max_participants && (isNaN(Number(formData.max_participants)) || Number(formData.max_participants) <= 0)) {
      Alert.alert('Error', 'Please enter a valid maximum number of participants');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const challengeData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        challenge_type: formData.challenge_type,
        target_value: Number(formData.target_value),
        target_unit: formData.target_unit,
        start_date: formData.start_date.toISOString(),
        end_date: formData.end_date.toISOString(),
        visibility: formData.visibility,
        max_participants: formData.max_participants ? Number(formData.max_participants) : undefined,
        requires_approval: formData.requires_approval,
        reward_type: formData.reward_type || undefined,
        reward_data: formData.reward_type ? { name: formData.reward_name } : undefined,
      };

      await challengeService.createChallenge(challengeData);
      
      Alert.alert('Success', 'Challenge created successfully!', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    } catch (error) {
      console.error('Failed to create challenge:', error);
      Alert.alert('Error', 'Failed to create challenge. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const selectedChallengeType = challengeTypes.find(t => t.value === formData.challenge_type);

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Create Challenge
          </Text>
          <View style={styles.placeholder} />
        </View>

        {/* Title */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Challenge Title *
          </Text>
          <TextInput
            style={[
              styles.textInput,
              { 
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              }
            ]}
            value={formData.title}
            onChangeText={(value) => handleInputChange('title', value)}
            placeholder="Enter a catchy title for your challenge"
            placeholderTextColor={theme.colors.textSecondary}
            maxLength={100}
          />
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Description *
          </Text>
          <TextInput
            style={[
              styles.textAreaInput,
              { 
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              }
            ]}
            value={formData.description}
            onChangeText={(value) => handleInputChange('description', value)}
            placeholder="Describe your challenge and motivate participants"
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            numberOfLines={4}
            maxLength={500}
          />
        </View>

        {/* Challenge Type */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Challenge Type *
          </Text>
          <View style={styles.optionsGrid}>
            {challengeTypes.map((type) => (
              <TouchableOpacity
                key={type.value}
                style={[
                  styles.optionCard,
                  {
                    backgroundColor: formData.challenge_type === type.value
                      ? theme.colors.primary + '20'
                      : theme.colors.surface,
                    borderColor: formData.challenge_type === type.value
                      ? theme.colors.primary
                      : theme.colors.border,
                  }
                ]}
                onPress={() => handleChallengeTypeChange(type.value)}
              >
                <Ionicons
                  name={type.icon as any}
                  size={24}
                  color={formData.challenge_type === type.value
                    ? theme.colors.primary
                    : theme.colors.textSecondary
                  }
                />
                <Text
                  style={[
                    styles.optionLabel,
                    {
                      color: formData.challenge_type === type.value
                        ? theme.colors.primary
                        : theme.colors.text,
                    }
                  ]}
                >
                  {type.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Target Value */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Target Value *
          </Text>
          <View style={styles.targetContainer}>
            <TextInput
              style={[
                styles.targetInput,
                { 
                  backgroundColor: theme.colors.surface,
                  color: theme.colors.text,
                  borderColor: theme.colors.border,
                }
              ]}
              value={formData.target_value}
              onChangeText={(value) => handleInputChange('target_value', value)}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
            <View style={styles.unitSelector}>
              {selectedChallengeType?.units.map((unit) => (
                <TouchableOpacity
                  key={unit}
                  style={[
                    styles.unitOption,
                    {
                      backgroundColor: formData.target_unit === unit
                        ? theme.colors.primary
                        : theme.colors.surface,
                      borderColor: theme.colors.border,
                    }
                  ]}
                  onPress={() => handleInputChange('target_unit', unit)}
                >
                  <Text
                    style={[
                      styles.unitText,
                      {
                        color: formData.target_unit === unit
                          ? theme.colors.white
                          : theme.colors.text,
                      }
                    ]}
                  >
                    {unit}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Dates */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Duration *
          </Text>
          <View style={styles.dateContainer}>
            <TouchableOpacity
              style={[
                styles.dateButton,
                { 
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => setShowStartDatePicker(true)}
            >
              <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={[styles.dateText, { color: theme.colors.text }]}>
                Start: {formData.start_date.toLocaleDateString()}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.dateButton,
                { 
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => setShowEndDatePicker(true)}
            >
              <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={[styles.dateText, { color: theme.colors.text }]}>
                End: {formData.end_date.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Visibility */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Who can join?
          </Text>
          <View style={styles.visibilityOptions}>
            {visibilityOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.visibilityOption,
                  {
                    backgroundColor: formData.visibility === option.value
                      ? theme.colors.primary + '20'
                      : theme.colors.surface,
                    borderColor: formData.visibility === option.value
                      ? theme.colors.primary
                      : theme.colors.border,
                  }
                ]}
                onPress={() => handleInputChange('visibility', option.value)}
              >
                <View style={styles.visibilityContent}>
                  <Ionicons
                    name={option.icon as any}
                    size={20}
                    color={formData.visibility === option.value
                      ? theme.colors.primary
                      : theme.colors.textSecondary
                    }
                  />
                  <View style={styles.visibilityText}>
                    <Text
                      style={[
                        styles.visibilityLabel,
                        {
                          color: formData.visibility === option.value
                            ? theme.colors.primary
                            : theme.colors.text,
                        }
                      ]}
                    >
                      {option.label}
                    </Text>
                    <Text style={[styles.visibilityDescription, { color: theme.colors.textSecondary }]}>
                      {option.description}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Optional Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Optional Settings
          </Text>
          
          {/* Max Participants */}
          <View style={styles.optionalSetting}>
            <Text style={[styles.settingLabel, { color: theme.colors.text }]}>
              Maximum Participants
            </Text>
            <TextInput
              style={[
                styles.settingInput,
                { 
                  backgroundColor: theme.colors.surface,
                  color: theme.colors.text,
                  borderColor: theme.colors.border,
                }
              ]}
              value={formData.max_participants}
              onChangeText={(value) => handleInputChange('max_participants', value)}
              placeholder="Unlimited"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
          </View>

          {/* Requires Approval */}
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => handleInputChange('requires_approval', !formData.requires_approval)}
          >
            <View
              style={[
                styles.checkbox,
                {
                  backgroundColor: formData.requires_approval
                    ? theme.colors.primary
                    : 'transparent',
                  borderColor: theme.colors.border,
                }
              ]}
            >
              {formData.requires_approval && (
                <Ionicons name="checkmark" size={16} color={theme.colors.white} />
              )}
            </View>
            <Text style={[styles.checkboxLabel, { color: theme.colors.text }]}>
              Require approval to join
            </Text>
          </TouchableOpacity>
        </View>

        {/* Reward */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Reward (Optional)
          </Text>
          <View style={styles.rewardContainer}>
            {rewardTypes.map((reward) => (
              <TouchableOpacity
                key={reward.value}
                style={[
                  styles.rewardOption,
                  {
                    backgroundColor: formData.reward_type === reward.value
                      ? theme.colors.primary + '20'
                      : theme.colors.surface,
                    borderColor: formData.reward_type === reward.value
                      ? theme.colors.primary
                      : theme.colors.border,
                  }
                ]}
                onPress={() => handleInputChange('reward_type', reward.value)}
              >
                <Text
                  style={[
                    styles.rewardLabel,
                    {
                      color: formData.reward_type === reward.value
                        ? theme.colors.primary
                        : theme.colors.text,
                    }
                  ]}
                >
                  {reward.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {formData.reward_type && formData.reward_type !== '' && (
            <TextInput
              style={[
                styles.textInput,
                { 
                  backgroundColor: theme.colors.surface,
                  color: theme.colors.text,
                  borderColor: theme.colors.border,
                  marginTop: 12,
                }
              ]}
              value={formData.reward_name}
              onChangeText={(value) => handleInputChange('reward_name', value)}
              placeholder="Enter reward name or description"
              placeholderTextColor={theme.colors.textSecondary}
            />
          )}
        </View>

        {/* Create Button */}
        <View style={styles.createButtonContainer}>
          <Button
            title="Create Challenge"
            onPress={handleSubmit}
            loading={loading}
            disabled={loading}
            style={styles.createButton}
          />
        </View>
      </View>

      {/* Date Pickers */}
      {showStartDatePicker && (
        <DateTimePicker
          value={formData.start_date}
          mode="date"
          display="default"
          onChange={(event, date) => handleDateChange(event, date, 'start')}
          minimumDate={new Date()}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={formData.end_date}
          mode="date"
          display="default"
          onChange={(event, date) => handleDateChange(event, date, 'end')}
          minimumDate={formData.start_date}
        />
      )}
    </ScrollView>
  );
}
