import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ProgressBar } from '@/components/ui/ProgressBar';
import { challengeService } from '@/services/challengeService';
import { styles } from './FitnessChallenges.styles';

interface Challenge {
  id: string;
  creator_id: string;
  title: string;
  description: string;
  challenge_type: string;
  target_value: number;
  target_unit: string;
  start_date: string;
  end_date: string;
  visibility: string;
  max_participants?: number;
  participants_count: number;
  completed_count: number;
  reward_type?: string;
  reward_data?: any;
  creator_profile?: any;
  user_participation?: {
    status: string;
    current_progress: number;
    completion_date?: string;
  };
}

export function FitnessChallenges() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [activeTab, setActiveTab] = useState<'discover' | 'joined' | 'created'>('discover');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const tabs = [
    { key: 'discover', label: 'Discover', icon: 'search-outline' },
    { key: 'joined', label: 'Joined', icon: 'person-outline' },
    { key: 'created', label: 'Created', icon: 'add-circle-outline' },
  ];

  useEffect(() => {
    loadChallenges();
  }, [activeTab]);

  const loadChallenges = async () => {
    try {
      setLoading(true);
      let data: Challenge[] = [];

      switch (activeTab) {
        case 'discover':
          data = await challengeService.getPublicChallenges();
          break;
        case 'joined':
          data = await challengeService.getUserJoinedChallenges();
          break;
        case 'created':
          data = await challengeService.getUserCreatedChallenges();
          break;
      }

      setChallenges(data);
    } catch (error) {
      console.error('Failed to load challenges:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadChallenges();
    setRefreshing(false);
  };

  const handleJoinChallenge = async (challengeId: string) => {
    try {
      await challengeService.joinChallenge(challengeId);
      Alert.alert('Success', 'You have joined the challenge!');
      loadChallenges();
    } catch (error) {
      console.error('Failed to join challenge:', error);
      Alert.alert('Error', 'Failed to join challenge. Please try again.');
    }
  };

  const handleLeaveChallenge = async (challengeId: string) => {
    Alert.alert(
      'Leave Challenge',
      'Are you sure you want to leave this challenge?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            try {
              await challengeService.leaveChallenge(challengeId);
              loadChallenges();
            } catch (error) {
              console.error('Failed to leave challenge:', error);
              Alert.alert('Error', 'Failed to leave challenge. Please try again.');
            }
          },
        },
      ]
    );
  };

  const getChallengeTypeIcon = (type: string) => {
    switch (type) {
      case 'distance': return 'walk-outline';
      case 'reps': return 'fitness-outline';
      case 'weight': return 'barbell-outline';
      case 'duration': return 'time-outline';
      case 'frequency': return 'calendar-outline';
      default: return 'trophy-outline';
    }
  };

  const getChallengeStatusColor = (challenge: Challenge) => {
    const now = new Date();
    const startDate = new Date(challenge.start_date);
    const endDate = new Date(challenge.end_date);

    if (now < startDate) return theme.colors.warning; // Upcoming
    if (now > endDate) return theme.colors.textSecondary; // Ended
    return theme.colors.success; // Active
  };

  const getChallengeStatus = (challenge: Challenge) => {
    const now = new Date();
    const startDate = new Date(challenge.start_date);
    const endDate = new Date(challenge.end_date);

    if (now < startDate) return 'Upcoming';
    if (now > endDate) return 'Ended';
    return 'Active';
  };

  const formatTargetValue = (value: number, unit: string, type: string) => {
    switch (type) {
      case 'distance':
        return `${value} ${unit}`;
      case 'reps':
        return `${value} reps`;
      case 'weight':
        return `${value} ${unit}`;
      case 'duration':
        return `${value} minutes`;
      case 'frequency':
        return `${value} times`;
      default:
        return `${value} ${unit}`;
    }
  };

  const getProgressPercentage = (challenge: Challenge) => {
    if (!challenge.user_participation) return 0;
    return Math.min((challenge.user_participation.current_progress / challenge.target_value) * 100, 100);
  };

  const renderChallengeCard = (challenge: Challenge) => (
    <TouchableOpacity
      key={challenge.id}
      style={[styles.challengeCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => navigation.navigate('ChallengeDetails', { challengeId: challenge.id })}
    >
      {/* Header */}
      <View style={styles.challengeHeader}>
        <View style={styles.challengeTypeContainer}>
          <Ionicons
            name={getChallengeTypeIcon(challenge.challenge_type) as any}
            size={20}
            color={theme.colors.primary}
          />
          <Text style={[styles.challengeType, { color: theme.colors.primary }]}>
            {challenge.challenge_type.toUpperCase()}
          </Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getChallengeStatusColor(challenge) + '20' }
          ]}
        >
          <Text
            style={[
              styles.statusText,
              { color: getChallengeStatusColor(challenge) }
            ]}
          >
            {getChallengeStatus(challenge)}
          </Text>
        </View>
      </View>

      {/* Title and Description */}
      <Text style={[styles.challengeTitle, { color: theme.colors.text }]}>
        {challenge.title}
      </Text>
      <Text style={[styles.challengeDescription, { color: theme.colors.textSecondary }]}>
        {challenge.description}
      </Text>

      {/* Target and Progress */}
      <View style={styles.targetContainer}>
        <Text style={[styles.targetLabel, { color: theme.colors.textSecondary }]}>
          Target: {formatTargetValue(challenge.target_value, challenge.target_unit, challenge.challenge_type)}
        </Text>
        {challenge.user_participation && (
          <View style={styles.progressContainer}>
            <ProgressBar
              progress={getProgressPercentage(challenge)}
              color={theme.colors.primary}
              backgroundColor={theme.colors.border}
              height={6}
            />
            <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
              {challenge.user_participation.current_progress} / {challenge.target_value}
            </Text>
          </View>
        )}
      </View>

      {/* Stats */}
      <View style={styles.challengeStats}>
        <View style={styles.statItem}>
          <Ionicons name="people-outline" size={16} color={theme.colors.textSecondary} />
          <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
            {challenge.participants_count} participants
          </Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="checkmark-circle-outline" size={16} color={theme.colors.success} />
          <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
            {challenge.completed_count} completed
          </Text>
        </View>
      </View>

      {/* Creator */}
      <View style={styles.creatorContainer}>
        <Image
          source={{
            uri: challenge.creator_profile?.avatar_url || 'https://via.placeholder.com/24',
          }}
          style={styles.creatorAvatar}
        />
        <Text style={[styles.creatorName, { color: theme.colors.textSecondary }]}>
          by {challenge.creator_profile?.display_name || challenge.creator_profile?.username}
        </Text>
      </View>

      {/* Action Button */}
      <View style={styles.actionContainer}>
        {challenge.user_participation ? (
          <View style={styles.participationActions}>
            {challenge.user_participation.status === 'completed' ? (
              <View style={styles.completedBadge}>
                <Ionicons name="trophy" size={16} color={theme.colors.warning} />
                <Text style={[styles.completedText, { color: theme.colors.warning }]}>
                  Completed!
                </Text>
              </View>
            ) : (
              <Button
                title="Leave Challenge"
                variant="secondary"
                size="small"
                onPress={() => handleLeaveChallenge(challenge.id)}
              />
            )}
          </View>
        ) : (
          <Button
            title="Join Challenge"
            variant="primary"
            size="small"
            onPress={() => handleJoinChallenge(challenge.id)}
            disabled={getChallengeStatus(challenge) === 'Ended'}
          />
        )}
      </View>

      {/* Reward */}
      {challenge.reward_type && (
        <View style={styles.rewardContainer}>
          <Ionicons name="gift-outline" size={16} color={theme.colors.warning} />
          <Text style={[styles.rewardText, { color: theme.colors.warning }]}>
            Reward: {challenge.reward_data?.name || challenge.reward_type}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Fitness Challenges
        </Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => navigation.navigate('CreateChallenge')}
        >
          <Ionicons name="add" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tab,
              {
                backgroundColor: activeTab === tab.key
                  ? theme.colors.primary
                  : 'transparent',
              }
            ]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <Ionicons
              name={tab.icon as any}
              size={18}
              color={activeTab === tab.key ? theme.colors.white : theme.colors.textSecondary}
            />
            <Text
              style={[
                styles.tabText,
                {
                  color: activeTab === tab.key
                    ? theme.colors.white
                    : theme.colors.textSecondary,
                }
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <LoadingSpinner size="large" />
          </View>
        ) : challenges.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="trophy-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
              {activeTab === 'discover' && 'No challenges available'}
              {activeTab === 'joined' && 'You haven\'t joined any challenges yet'}
              {activeTab === 'created' && 'You haven\'t created any challenges yet'}
            </Text>
            <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
              {activeTab === 'discover' && 'Check back later for new challenges'}
              {activeTab === 'joined' && 'Discover and join challenges to compete with others'}
              {activeTab === 'created' && 'Create your first challenge to motivate the community'}
            </Text>
            {activeTab !== 'discover' && (
              <Button
                title={activeTab === 'joined' ? 'Discover Challenges' : 'Create Challenge'}
                variant="primary"
                onPress={() => {
                  if (activeTab === 'joined') {
                    setActiveTab('discover');
                  } else {
                    navigation.navigate('CreateChallenge');
                  }
                }}
                style={styles.emptyStateButton}
              />
            )}
          </View>
        ) : (
          <View style={styles.challengesList}>
            {challenges.map(renderChallengeCard)}
          </View>
        )}
      </ScrollView>
    </View>
  );
}
