import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { formatDistanceToNow } from 'date-fns';
import { styles } from './FeedItem.styles';

interface FeedItemProps {
  item: {
    id: string;
    type: 'activity' | 'workout_share';
    data: any;
    created_at: string;
  };
  onLike: (itemId: string, itemType: string) => void;
  onUnlike: (itemId: string, itemType: string) => void;
  onComment: (itemId: string, itemType: string) => void;
  onShare: (itemId: string, itemType: string) => void;
  onUserPress: (userId: string) => void;
}

export function FeedItem({ 
  item, 
  onLike, 
  onUnlike, 
  onComment, 
  onShare, 
  onUserPress 
}: FeedItemProps) {
  const { theme } = useTheme();

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'workout_completed': return 'fitness-outline';
      case 'workout_shared': return 'share-outline';
      case 'challenge_joined': return 'trophy-outline';
      case 'challenge_completed': return 'medal-outline';
      case 'achievement_earned': return 'star-outline';
      case 'user_followed': return 'person-add-outline';
      case 'program_started': return 'play-outline';
      default: return 'information-circle-outline';
    }
  };

  const getActivityColor = (activityType: string) => {
    switch (activityType) {
      case 'workout_completed': return theme.colors.success;
      case 'workout_shared': return theme.colors.primary;
      case 'challenge_joined': return theme.colors.warning;
      case 'challenge_completed': return theme.colors.warning;
      case 'achievement_earned': return theme.colors.warning;
      case 'user_followed': return theme.colors.info;
      case 'program_started': return theme.colors.primary;
      default: return theme.colors.textSecondary;
    }
  };

  const renderActivityFeedItem = (data: any) => (
    <View style={[styles.feedCard, { backgroundColor: theme.colors.surface }]}>
      {/* User Header */}
      <View style={styles.userHeader}>
        <TouchableOpacity
          style={styles.userInfo}
          onPress={() => onUserPress(data.user_id)}
        >
          <Image
            source={{
              uri: data.user_profile?.avatar_url || 'https://via.placeholder.com/40',
            }}
            style={styles.avatar}
          />
          <View style={styles.userDetails}>
            <Text style={[styles.userName, { color: theme.colors.text }]}>
              {data.user_profile?.display_name || data.user_profile?.username}
            </Text>
            <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
              {formatDistanceToNow(new Date(data.created_at), { addSuffix: true })}
            </Text>
          </View>
        </TouchableOpacity>
        
        <View style={styles.activityIcon}>
          <Ionicons
            name={getActivityIcon(data.activity_type) as any}
            size={20}
            color={getActivityColor(data.activity_type)}
          />
        </View>
      </View>

      {/* Activity Content */}
      <View style={styles.activityContent}>
        <Text style={[styles.activityTitle, { color: theme.colors.text }]}>
          {data.title}
        </Text>
        {data.description && (
          <Text style={[styles.activityDescription, { color: theme.colors.textSecondary }]}>
            {data.description}
          </Text>
        )}
        
        {/* Activity Data */}
        {data.activity_data && (
          <View style={styles.activityData}>
            {data.activity_data.workout_duration && (
              <View style={styles.dataItem}>
                <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
                <Text style={[styles.dataText, { color: theme.colors.textSecondary }]}>
                  {Math.round(data.activity_data.workout_duration / 60)} min
                </Text>
              </View>
            )}
            {data.activity_data.exercises_count && (
              <View style={styles.dataItem}>
                <Ionicons name="fitness-outline" size={16} color={theme.colors.textSecondary} />
                <Text style={[styles.dataText, { color: theme.colors.textSecondary }]}>
                  {data.activity_data.exercises_count} exercises
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      {/* Actions */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => data.is_liked 
            ? onUnlike(data.id, 'activity') 
            : onLike(data.id, 'activity')
          }
        >
          <Ionicons
            name={data.is_liked ? 'heart' : 'heart-outline'}
            size={20}
            color={data.is_liked ? theme.colors.error : theme.colors.textSecondary}
          />
          <Text style={[styles.actionText, { color: theme.colors.textSecondary }]}>
            {data.likes_count || 0}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onComment(data.id, 'activity')}
        >
          <Ionicons name="chatbubble-outline" size={20} color={theme.colors.textSecondary} />
          <Text style={[styles.actionText, { color: theme.colors.textSecondary }]}>
            {data.comments_count || 0}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onShare(data.id, 'activity')}
        >
          <Ionicons name="share-outline" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderWorkoutShareItem = (data: any) => (
    <View style={[styles.feedCard, { backgroundColor: theme.colors.surface }]}>
      {/* User Header */}
      <View style={styles.userHeader}>
        <TouchableOpacity
          style={styles.userInfo}
          onPress={() => onUserPress(data.user_id)}
        >
          <Image
            source={{
              uri: data.user_profile?.avatar_url || 'https://via.placeholder.com/40',
            }}
            style={styles.avatar}
          />
          <View style={styles.userDetails}>
            <Text style={[styles.userName, { color: theme.colors.text }]}>
              {data.user_profile?.display_name || data.user_profile?.username}
            </Text>
            <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
              {formatDistanceToNow(new Date(data.created_at), { addSuffix: true })}
            </Text>
          </View>
        </TouchableOpacity>
        
        <View style={styles.shareIcon}>
          <Ionicons name="share-outline" size={20} color={theme.colors.primary} />
        </View>
      </View>

      {/* Share Content */}
      <View style={styles.shareContent}>
        <Text style={[styles.shareTitle, { color: theme.colors.text }]}>
          {data.title}
        </Text>
        {data.description && (
          <Text style={[styles.shareDescription, { color: theme.colors.textSecondary }]}>
            {data.description}
          </Text>
        )}

        {/* Images */}
        {data.images && data.images.length > 0 && (
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.imagesContainer}
          >
            {data.images.map((imageUrl: string, index: number) => (
              <Image
                key={index}
                source={{ uri: imageUrl }}
                style={styles.shareImage}
              />
            ))}
          </ScrollView>
        )}

        {/* Workout Details */}
        {data.workout_log && (
          <View style={styles.workoutDetails}>
            <View style={styles.workoutStat}>
              <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
              <Text style={[styles.workoutStatText, { color: theme.colors.textSecondary }]}>
                {Math.round((data.workout_log.duration || 0) / 60)} min
              </Text>
            </View>
            {data.workout_log.exercises && (
              <View style={styles.workoutStat}>
                <Ionicons name="fitness-outline" size={16} color={theme.colors.textSecondary} />
                <Text style={[styles.workoutStatText, { color: theme.colors.textSecondary }]}>
                  {data.workout_log.exercises.length} exercises
                </Text>
              </View>
            )}
            {data.workout_log.rating && (
              <View style={styles.workoutStat}>
                <Ionicons name="star" size={16} color={theme.colors.warning} />
                <Text style={[styles.workoutStatText, { color: theme.colors.textSecondary }]}>
                  {data.workout_log.rating}/5
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      {/* Actions */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => data.is_liked 
            ? onUnlike(data.id, 'workout_share') 
            : onLike(data.id, 'workout_share')
          }
        >
          <Ionicons
            name={data.is_liked ? 'heart' : 'heart-outline'}
            size={20}
            color={data.is_liked ? theme.colors.error : theme.colors.textSecondary}
          />
          <Text style={[styles.actionText, { color: theme.colors.textSecondary }]}>
            {data.likes_count || 0}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onComment(data.id, 'workout_share')}
        >
          <Ionicons name="chatbubble-outline" size={20} color={theme.colors.textSecondary} />
          <Text style={[styles.actionText, { color: theme.colors.textSecondary }]}>
            {data.comments_count || 0}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onShare(data.id, 'workout_share')}
        >
          <Ionicons name="share-outline" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {item.type === 'activity' 
        ? renderActivityFeedItem(item.data)
        : renderWorkoutShareItem(item.data)
      }
    </View>
  );
}
