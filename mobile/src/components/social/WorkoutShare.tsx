import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { workoutShareService } from '@/services/workoutShareService';
import { styles } from './WorkoutShare.styles';

interface WorkoutShareProps {
  workoutLogId?: string;
  workoutProgramId?: string;
  onShareComplete?: () => void;
}

export function WorkoutShare({ 
  workoutLogId, 
  workoutProgramId, 
  onShareComplete 
}: WorkoutShareProps) {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [visibility, setVisibility] = useState<'public' | 'friends' | 'private'>('public');
  const [loading, setLoading] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  const visibilityOptions = [
    { value: 'public', label: 'Public', icon: 'globe-outline', description: 'Everyone can see' },
    { value: 'friends', label: 'Friends', icon: 'people-outline', description: 'Only friends can see' },
    { value: 'private', label: 'Private', icon: 'lock-closed-outline', description: 'Only you can see' },
  ];

  const handleShare = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a title for your share');
      return;
    }

    try {
      setLoading(true);
      
      await workoutShareService.createShare({
        workoutLogId,
        workoutProgramId,
        title: title.trim(),
        description: description.trim(),
        visibility,
        images: selectedImages,
      });

      Alert.alert('Success', 'Workout shared successfully!');
      onShareComplete?.();
      navigation.goBack();
    } catch (error) {
      console.error('Failed to share workout:', error);
      Alert.alert('Error', 'Failed to share workout. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddImage = () => {
    // Navigate to image picker or camera
    navigation.navigate('ImagePicker', {
      onImageSelected: (imageUri: string) => {
        setSelectedImages(prev => [...prev, imageUri]);
      },
    });
  };

  const handleRemoveImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Share Workout
          </Text>
          <View style={styles.placeholder} />
        </View>

        {/* Title Input */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Title *
          </Text>
          <TextInput
            style={[
              styles.titleInput,
              { 
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              }
            ]}
            value={title}
            onChangeText={setTitle}
            placeholder="Give your workout a catchy title..."
            placeholderTextColor={theme.colors.textSecondary}
            maxLength={100}
          />
          <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
            {title.length}/100
          </Text>
        </View>

        {/* Description Input */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Description
          </Text>
          <TextInput
            style={[
              styles.descriptionInput,
              { 
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.border,
              }
            ]}
            value={description}
            onChangeText={setDescription}
            placeholder="Tell others about your workout experience..."
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            numberOfLines={4}
            maxLength={500}
          />
          <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
            {description.length}/500
          </Text>
        </View>

        {/* Images */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Photos
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.imagesContainer}>
              {selectedImages.map((imageUri, index) => (
                <View key={index} style={styles.imageContainer}>
                  <Image source={{ uri: imageUri }} style={styles.selectedImage} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => handleRemoveImage(index)}
                  >
                    <Ionicons name="close-circle" size={20} color={theme.colors.error} />
                  </TouchableOpacity>
                </View>
              ))}
              {selectedImages.length < 5 && (
                <TouchableOpacity
                  style={[
                    styles.addImageButton,
                    { 
                      backgroundColor: theme.colors.surface,
                      borderColor: theme.colors.border,
                    }
                  ]}
                  onPress={handleAddImage}
                >
                  <Ionicons name="camera" size={24} color={theme.colors.textSecondary} />
                  <Text style={[styles.addImageText, { color: theme.colors.textSecondary }]}>
                    Add Photo
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </ScrollView>
        </View>

        {/* Visibility Settings */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Who can see this?
          </Text>
          <View style={styles.visibilityOptions}>
            {visibilityOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.visibilityOption,
                  { 
                    backgroundColor: theme.colors.surface,
                    borderColor: visibility === option.value 
                      ? theme.colors.primary 
                      : theme.colors.border,
                  }
                ]}
                onPress={() => setVisibility(option.value as any)}
              >
                <View style={styles.visibilityOptionContent}>
                  <View style={styles.visibilityOptionHeader}>
                    <Ionicons 
                      name={option.icon as any} 
                      size={20} 
                      color={visibility === option.value 
                        ? theme.colors.primary 
                        : theme.colors.textSecondary
                      } 
                    />
                    <Text 
                      style={[
                        styles.visibilityOptionLabel,
                        { 
                          color: visibility === option.value 
                            ? theme.colors.primary 
                            : theme.colors.text 
                        }
                      ]}
                    >
                      {option.label}
                    </Text>
                  </View>
                  <Text 
                    style={[
                      styles.visibilityOptionDescription,
                      { color: theme.colors.textSecondary }
                    ]}
                  >
                    {option.description}
                  </Text>
                </View>
                {visibility === option.value && (
                  <Ionicons 
                    name="checkmark-circle" 
                    size={20} 
                    color={theme.colors.primary} 
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Share Button */}
        <View style={styles.shareButtonContainer}>
          <Button
            title="Share Workout"
            onPress={handleShare}
            loading={loading}
            disabled={!title.trim() || loading}
            style={styles.shareButton}
          />
        </View>

        {/* Privacy Notice */}
        <View style={styles.privacyNotice}>
          <Ionicons name="information-circle-outline" size={16} color={theme.colors.textSecondary} />
          <Text style={[styles.privacyText, { color: theme.colors.textSecondary }]}>
            By sharing, you agree to our Community Guidelines. 
            You can change visibility settings anytime.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}
