import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Image,
  TextInput,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { userProfileService } from '@/services/userProfileService';
import { socialService } from '@/services/socialService';
import { styles } from './UserDiscovery.styles';

interface DiscoveredUser {
  id: string;
  user_id: string;
  username?: string;
  display_name: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  fitness_level?: string;
  primary_goals?: string[];
  followers_count: number;
  following_count: number;
  workouts_shared_count: number;
  is_following?: boolean;
  connection_status?: string;
}

export function UserDiscovery() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<DiscoveredUser[]>([]);
  const [suggestedUsers, setSuggestedUsers] = useState<DiscoveredUser[]>([]);
  const [activeTab, setActiveTab] = useState<'suggested' | 'search'>('suggested');
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadSuggestedUsers();
  }, []);

  useEffect(() => {
    if (searchQuery.length > 2) {
      searchUsers();
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  const loadSuggestedUsers = async () => {
    try {
      setLoading(true);
      
      // Get random public profiles (in a real app, this would be more sophisticated)
      const profiles = await userProfileService.searchProfiles('', 20);
      
      // Filter out current user and get connection status
      const filteredProfiles = profiles.filter(p => p.user_id !== user?.id);
      
      const usersWithStatus = await Promise.all(
        filteredProfiles.map(async (profile) => {
          const status = await socialService.getConnectionStatus(profile.user_id);
          return {
            id: profile.id,
            user_id: profile.user_id,
            username: profile.username,
            display_name: profile.display_name || profile.username,
            avatar_url: profile.avatar_url,
            bio: profile.bio,
            location: profile.location,
            fitness_level: profile.fitness_level,
            primary_goals: profile.primary_goals,
            followers_count: profile.followers_count || 0,
            following_count: profile.following_count || 0,
            workouts_shared_count: profile.workouts_shared_count || 0,
            is_following: status.is_following,
            connection_status: status.status,
          };
        })
      );

      // Sort by activity (followers + workouts shared)
      const sortedUsers = usersWithStatus.sort((a, b) => {
        const scoreA = a.followers_count + a.workouts_shared_count;
        const scoreB = b.followers_count + b.workouts_shared_count;
        return scoreB - scoreA;
      });

      setSuggestedUsers(sortedUsers);
    } catch (error) {
      console.error('Failed to load suggested users:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchUsers = async () => {
    try {
      setSearchLoading(true);
      const profiles = await userProfileService.searchProfiles(searchQuery);
      
      // Filter out current user and get connection status
      const filteredProfiles = profiles.filter(p => p.user_id !== user?.id);
      
      const usersWithStatus = await Promise.all(
        filteredProfiles.map(async (profile) => {
          const status = await socialService.getConnectionStatus(profile.user_id);
          return {
            id: profile.id,
            user_id: profile.user_id,
            username: profile.username,
            display_name: profile.display_name || profile.username,
            avatar_url: profile.avatar_url,
            bio: profile.bio,
            location: profile.location,
            fitness_level: profile.fitness_level,
            primary_goals: profile.primary_goals,
            followers_count: profile.followers_count || 0,
            following_count: profile.following_count || 0,
            workouts_shared_count: profile.workouts_shared_count || 0,
            is_following: status.is_following,
            connection_status: status.status,
          };
        })
      );

      setSearchResults(usersWithStatus);
    } catch (error) {
      console.error('Failed to search users:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSuggestedUsers();
    setRefreshing(false);
  };

  const handleFollow = async (userId: string) => {
    try {
      await socialService.followUser(userId);
      
      // Update local state
      const updateUser = (user: DiscoveredUser) => {
        if (user.user_id === userId) {
          return {
            ...user,
            is_following: true,
            followers_count: user.followers_count + 1,
          };
        }
        return user;
      };

      setSuggestedUsers(prev => prev.map(updateUser));
      setSearchResults(prev => prev.map(updateUser));
    } catch (error) {
      console.error('Failed to follow user:', error);
      Alert.alert('Error', 'Failed to follow user. Please try again.');
    }
  };

  const handleUnfollow = async (userId: string) => {
    try {
      await socialService.unfollowUser(userId);
      
      // Update local state
      const updateUser = (user: DiscoveredUser) => {
        if (user.user_id === userId) {
          return {
            ...user,
            is_following: false,
            followers_count: Math.max(0, user.followers_count - 1),
          };
        }
        return user;
      };

      setSuggestedUsers(prev => prev.map(updateUser));
      setSearchResults(prev => prev.map(updateUser));
    } catch (error) {
      console.error('Failed to unfollow user:', error);
      Alert.alert('Error', 'Failed to unfollow user. Please try again.');
    }
  };

  const handleUserPress = (userId: string) => {
    navigation.navigate('UserProfile', { userId });
  };

  const getFitnessLevelColor = (level?: string) => {
    switch (level) {
      case 'beginner': return theme.colors.success;
      case 'intermediate': return theme.colors.warning;
      case 'advanced': return theme.colors.error;
      default: return theme.colors.textSecondary;
    }
  };

  const renderUserCard = (discoveredUser: DiscoveredUser) => (
    <TouchableOpacity
      key={discoveredUser.id}
      style={[styles.userCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleUserPress(discoveredUser.user_id)}
    >
      <View style={styles.userHeader}>
        <Image
          source={{
            uri: discoveredUser.avatar_url || 'https://via.placeholder.com/60',
          }}
          style={styles.avatar}
        />
        
        <View style={styles.userInfo}>
          <Text style={[styles.displayName, { color: theme.colors.text }]}>
            {discoveredUser.display_name}
          </Text>
          {discoveredUser.username && (
            <Text style={[styles.username, { color: theme.colors.textSecondary }]}>
              @{discoveredUser.username}
            </Text>
          )}
          
          <View style={styles.userMeta}>
            {discoveredUser.location && (
              <View style={styles.metaItem}>
                <Ionicons name="location-outline" size={14} color={theme.colors.textSecondary} />
                <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                  {discoveredUser.location}
                </Text>
              </View>
            )}
            {discoveredUser.fitness_level && (
              <View style={styles.metaItem}>
                <View 
                  style={[
                    styles.levelBadge, 
                    { backgroundColor: getFitnessLevelColor(discoveredUser.fitness_level) + '20' }
                  ]}
                >
                  <Text 
                    style={[
                      styles.levelText, 
                      { color: getFitnessLevelColor(discoveredUser.fitness_level) }
                    ]}
                  >
                    {discoveredUser.fitness_level}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Follow Button */}
        <View style={styles.actionContainer}>
          {discoveredUser.is_following ? (
            <Button
              title="Following"
              variant="secondary"
              size="small"
              onPress={() => handleUnfollow(discoveredUser.user_id)}
              style={styles.followButton}
            />
          ) : (
            <Button
              title="Follow"
              variant="primary"
              size="small"
              onPress={() => handleFollow(discoveredUser.user_id)}
              style={styles.followButton}
            />
          )}
        </View>
      </View>

      {/* Bio */}
      {discoveredUser.bio && (
        <Text 
          style={[styles.bio, { color: theme.colors.textSecondary }]}
          numberOfLines={2}
        >
          {discoveredUser.bio}
        </Text>
      )}

      {/* Goals */}
      {discoveredUser.primary_goals && discoveredUser.primary_goals.length > 0 && (
        <View style={styles.goalsContainer}>
          <Text style={[styles.goalsLabel, { color: theme.colors.textSecondary }]}>
            Goals:
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.goalsList}>
              {discoveredUser.primary_goals.slice(0, 3).map((goal, index) => (
                <View 
                  key={index}
                  style={[styles.goalBadge, { backgroundColor: theme.colors.primary + '20' }]}
                >
                  <Text style={[styles.goalText, { color: theme.colors.primary }]}>
                    {goal}
                  </Text>
                </View>
              ))}
            </View>
          </ScrollView>
        </View>
      )}

      {/* Stats */}
      <View style={styles.stats}>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.text }]}>
            {discoveredUser.followers_count}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            Followers
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.text }]}>
            {discoveredUser.following_count}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            Following
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.text }]}>
            {discoveredUser.workouts_shared_count}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            Shared
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderContent = () => {
    const users = activeTab === 'suggested' ? suggestedUsers : searchResults;
    const isLoading = activeTab === 'suggested' ? loading : searchLoading;

    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
        </View>
      );
    }

    if (users.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons 
            name={activeTab === 'suggested' ? 'people-outline' : 'search-outline'} 
            size={48} 
            color={theme.colors.textSecondary} 
          />
          <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
            {activeTab === 'suggested' 
              ? 'No suggested users' 
              : searchQuery.length > 2 
                ? 'No users found' 
                : 'Search for users'
            }
          </Text>
          <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
            {activeTab === 'suggested' 
              ? 'Check back later for new suggestions' 
              : 'Try searching with different keywords'
            }
          </Text>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.usersList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {users.map(renderUserCard)}
      </ScrollView>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Discover People
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search users..."
            placeholderTextColor={theme.colors.textSecondary}
            onFocus={() => setActiveTab('search')}
          />
          {searchLoading && <LoadingSpinner size="small" />}
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            {
              backgroundColor: activeTab === 'suggested'
                ? theme.colors.primary
                : 'transparent',
            }
          ]}
          onPress={() => setActiveTab('suggested')}
        >
          <Ionicons
            name="people-outline"
            size={18}
            color={activeTab === 'suggested' ? theme.colors.white : theme.colors.textSecondary}
          />
          <Text
            style={[
              styles.tabText,
              {
                color: activeTab === 'suggested'
                  ? theme.colors.white
                  : theme.colors.textSecondary,
              }
            ]}
          >
            Suggested
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            {
              backgroundColor: activeTab === 'search'
                ? theme.colors.primary
                : 'transparent',
            }
          ]}
          onPress={() => setActiveTab('search')}
        >
          <Ionicons
            name="search-outline"
            size={18}
            color={activeTab === 'search' ? theme.colors.white : theme.colors.textSecondary}
          />
          <Text
            style={[
              styles.tabText,
              {
                color: activeTab === 'search'
                  ? theme.colors.white
                  : theme.colors.textSecondary,
              }
            ]}
          >
            Search
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {renderContent()}
    </View>
  );
}
