import React from 'react';
import { render, RenderOptions } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@/store';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock navigation container for testing
const MockNavigationContainer = ({ children }: { children: React.ReactNode }) => (
  <NavigationContainer>{children}</NavigationContainer>
);

// All providers wrapper
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider>
          <AuthProvider>
            <MockNavigationContainer>
              {children}
            </MockNavigationContainer>
          </AuthProvider>
        </ThemeProvider>
      </PersistGate>
    </Provider>
  );
};

// Custom render function
const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react-native';
export { customRender as render };

// Test data factories
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  avatar: null,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockWorkout = (overrides = {}) => ({
  id: 'test-workout-id',
  name: 'Test Workout',
  description: 'A test workout',
  duration: 45,
  difficulty: 'intermediate',
  type: 'strength',
  exercises: [
    {
      id: 'exercise-1',
      name: 'Push-ups',
      sets: 3,
      reps: 10,
      weight: null,
      restTime: 60,
    },
    {
      id: 'exercise-2',
      name: 'Squats',
      sets: 3,
      reps: 15,
      weight: null,
      restTime: 60,
    },
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockWorkoutProgram = (overrides = {}) => ({
  id: 'test-program-id',
  name: 'Test Program',
  description: 'A test workout program',
  duration: 12,
  difficulty: 'intermediate',
  type: 'strength',
  workouts: [createMockWorkout()],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockExercise = (overrides = {}) => ({
  id: 'test-exercise-id',
  name: 'Test Exercise',
  description: 'A test exercise',
  instructions: ['Step 1', 'Step 2', 'Step 3'],
  muscleGroups: ['chest', 'triceps'],
  equipment: ['bodyweight'],
  difficulty: 'beginner',
  imageUrl: 'https://example.com/exercise.jpg',
  videoUrl: 'https://example.com/exercise.mp4',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockWorkoutLog = (overrides = {}) => ({
  id: 'test-log-id',
  workoutId: 'test-workout-id',
  userId: 'test-user-id',
  startedAt: new Date().toISOString(),
  completedAt: new Date().toISOString(),
  duration: 45,
  exercises: [
    {
      exerciseId: 'exercise-1',
      sets: [
        { reps: 10, weight: null, completed: true },
        { reps: 10, weight: null, completed: true },
        { reps: 8, weight: null, completed: true },
      ],
    },
  ],
  notes: 'Great workout!',
  rating: 4,
  createdAt: new Date().toISOString(),
  ...overrides,
});

export const createMockMessage = (overrides = {}) => ({
  id: 'test-message-id',
  conversationId: 'test-conversation-id',
  senderId: 'test-user-id',
  content: 'Test message',
  type: 'text',
  timestamp: new Date().toISOString(),
  isRead: false,
  ...overrides,
});

export const createMockConversation = (overrides = {}) => ({
  id: 'test-conversation-id',
  participants: ['test-user-id', 'test-coach-id'],
  lastMessage: createMockMessage(),
  lastMessageAt: new Date().toISOString(),
  unreadCount: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockProgressEntry = (overrides = {}) => ({
  id: 'test-progress-id',
  userId: 'test-user-id',
  type: 'weight',
  value: 70.5,
  unit: 'kg',
  date: new Date().toISOString(),
  notes: 'Feeling strong!',
  createdAt: new Date().toISOString(),
  ...overrides,
});

// Mock API responses
export const mockApiResponse = <T>(data: T, delay = 0) => {
  return new Promise<{ data: T; error: null }>((resolve) => {
    setTimeout(() => {
      resolve({ data, error: null });
    }, delay);
  });
};

export const mockApiError = (message: string, delay = 0) => {
  return new Promise<{ data: null; error: { message: string } }>((resolve) => {
    setTimeout(() => {
      resolve({ data: null, error: { message } });
    }, delay);
  });
};

// Test helpers
export const waitForLoadingToFinish = () => {
  return new Promise((resolve) => setTimeout(resolve, 0));
};

export const createMockNavigation = () => ({
  navigate: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
  setParams: jest.fn(),
  dispatch: jest.fn(),
  setOptions: jest.fn(),
  isFocused: jest.fn(() => true),
  canGoBack: jest.fn(() => true),
  getId: jest.fn(() => 'test-screen'),
  getParent: jest.fn(),
  getState: jest.fn(() => ({})),
});

export const createMockRoute = (params = {}) => ({
  key: 'test-route',
  name: 'TestScreen',
  params,
});

// Async testing helpers
export const flushPromises = () => new Promise(setImmediate);

export const act = async (callback: () => void | Promise<void>) => {
  const { act: rtlAct } = await import('@testing-library/react-native');
  await rtlAct(callback);
};

// Mock store helpers
export const createMockStore = (initialState = {}) => {
  const mockStore = {
    getState: jest.fn(() => initialState),
    dispatch: jest.fn(),
    subscribe: jest.fn(),
  };
  return mockStore;
};

// Date helpers for testing
export const mockDate = (date: string | Date) => {
  const mockDate = new Date(date);
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
  return mockDate;
};

export const restoreDate = () => {
  (global.Date as jest.Mock).mockRestore();
};

// Network state helpers
export const mockNetworkState = (isConnected: boolean) => {
  const NetInfo = require('@react-native-netinfo/netinfo');
  NetInfo.fetch.mockResolvedValue({
    isConnected,
    isInternetReachable: isConnected,
  });
};

// Gesture helpers for testing swipes, etc.
export const createMockGestureEvent = (translationX = 0, translationY = 0) => ({
  nativeEvent: {
    translationX,
    translationY,
    velocityX: 0,
    velocityY: 0,
    state: 4, // END state
  },
});

// Animation helpers
export const mockAnimatedValue = (value = 0) => ({
  setValue: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  stopAnimation: jest.fn(),
  resetAnimation: jest.fn(),
  interpolate: jest.fn(() => mockAnimatedValue()),
  animate: jest.fn(),
  _value: value,
});

// Timer helpers
export const advanceTimersByTime = (time: number) => {
  jest.advanceTimersByTime(time);
};

export const runAllTimers = () => {
  jest.runAllTimers();
};
