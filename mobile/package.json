{"name": "fitnessapp-mobile-demo", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "expo": "~53.0.0", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-dom": "19.0.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "typescript": "~5.8.3"}, "private": true}