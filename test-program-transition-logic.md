# Test Program Transition Logic

## Overview
The program transition system provides seamless handoffs between workout program cycles, ensuring users always have access to their current program while new programs are prepared and reviewed.

## Key Components

### 1. **Program Transition Manager Edge Function**
- Handles batch processing of scheduled transitions
- Supports manual transition triggers
- Comprehensive error handling and logging

### 2. **Transition Helper Functions** (`_shared/transition-helpers.ts`)
- `executeImmediateTransition()` - Immediate program handoff
- `scheduleTransition()` - Schedule future transitions
- `hasPendingTransitions()` - Check for pending transitions
- `getUserTransitionHistory()` - Get transition history

## Transition Types

### **Immediate Transition**
Used when a program should be activated immediately:
```typescript
await executeImmediateTransition(supabaseClient, {
  userId: "user-123",
  fromProgramId: "current-program-id", // optional
  toProgramId: "new-program-id",
  transitionType: "manual", // or "automatic", "coach_initiated"
  forceTransition: false // optional
})
```

### **Scheduled Transition**
Used to schedule transitions for future dates:
```typescript
await scheduleTransition(supabaseClient, {
  userId: "user-123",
  toProgramId: "new-program-id",
  transitionType: "automatic",
  transitionDate: "2025-08-01" // optional, defaults to 28 days from cycle start
})
```

## Database Operations

### **Tables Involved:**
- `workout_programs` - Update cycle_status and activation dates
- `program_transitions` - Record all transitions for audit trail
- `scheduled_generation_jobs` - Schedule transition jobs
- `operation_logs` - Log all transition operations

### **Status Flow:**
1. **Current Program**: `cycle_status = 'active'` → `cycle_status = 'completed'`
2. **New Program**: `cycle_status = 'pending_transition'` → `cycle_status = 'active'`
3. **Program Status**: `auto_approved` → `active_by_client`

## Test Scenarios

### 1. **Immediate Manual Transition**
```bash
curl -X POST https://your-project.supabase.co/functions/v1/program-transition-manager \
  -H "Authorization: Bearer YOUR_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test-user-id",
    "programId": "new-program-id",
    "transitionType": "manual"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "transitionsProcessed": 1,
  "transitionsCompleted": 1,
  "transitionsFailed": 0,
  "errors": [],
  "executionTime": 850
}
```

### 2. **Batch Scheduled Transitions**
```bash
curl -X POST https://your-project.supabase.co/functions/v1/program-transition-manager \
  -H "Authorization: Bearer YOUR_SERVICE_KEY"
```

**Expected Response:**
```json
{
  "success": true,
  "transitionsProcessed": 5,
  "transitionsCompleted": 4,
  "transitionsFailed": 1,
  "errors": ["Transition abc: Program not found"],
  "executionTime": 2100
}
```

### 3. **Coach-Initiated Transition**
```bash
curl -X POST https://your-project.supabase.co/functions/v1/program-transition-manager \
  -H "Authorization: Bearer YOUR_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test-user-id",
    "transitionType": "coach_initiated",
    "forceTransition": true
  }'
```

## Transition Safety Features

### **1. Transaction-like Operations**
- Complete current program first
- Activate new program second
- Record transition third
- Log operation last

### **2. Error Recovery**
- Individual transition failures don't affect batch processing
- Detailed error messages for debugging
- Graceful degradation when services are unavailable

### **3. Data Integrity**
- Foreign key constraints ensure valid program references
- Unique constraints prevent duplicate transitions
- Audit trail in `program_transitions` table

### **4. Force Transition Option**
- Allows overriding safety checks for emergency situations
- Useful for coach-initiated transitions
- Logs force transitions for audit purposes

## Integration Points

### **With Auto-Approval Function**
```typescript
// Auto-approval can trigger immediate transitions
if (program.cycle_status === 'pending_transition') {
  await executeImmediateTransition(supabaseClient, {
    userId: program.user_id,
    toProgramId: program.id,
    transitionType: 'automatic'
  })
}
```

### **With Daily Scheduler**
```typescript
// Scheduler can schedule future transitions
await scheduleTransition(supabaseClient, {
  userId: user.user_id,
  toProgramId: newProgramId,
  transitionType: 'automatic',
  transitionDate: calculateTransitionDate(currentProgram)
})
```

## Monitoring & Observability

### **Key Metrics:**
- Transition success rate
- Average transition time
- Failed transition patterns
- User experience continuity

### **Logging:**
- All transitions logged in `operation_logs`
- Detailed error messages for debugging
- Performance metrics for optimization

### **Audit Trail:**
- Complete history in `program_transitions` table
- Links between old and new programs
- Transition types and timestamps

## Cron Job Integration

The transition manager can be called daily to process scheduled transitions:

```sql
-- Daily transition processor (11:00 AM UTC)
SELECT cron.schedule(
  'daily-transition-processor',
  '0 11 * * *',
  $$
  SELECT net.http_post(
    url := 'https://your-project.supabase.co/functions/v1/program-transition-manager',
    headers := '{"Authorization": "Bearer YOUR_SERVICE_KEY"}',
    body := '{}'
  );
  $$
);
```

## Benefits

1. **Seamless User Experience**: No interruption in program access
2. **Flexible Timing**: Support for immediate and scheduled transitions
3. **Complete Audit Trail**: Full history of all program changes
4. **Error Resilience**: Robust error handling and recovery
5. **Coach Control**: Manual override capabilities for special cases
