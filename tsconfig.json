{"compilerOptions": {"strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "noEmit": true, "jsx": "react-native", "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./hooks/*"], "@/utils/*": ["./src/utils/*"], "@/store/*": ["./src/store/*"], "@/services/*": ["./src/services/*"]}}, "extends": "expo/tsconfig.base", "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "app/**/*", "components/**/*", "hooks/**/*", "src/**/*"], "exclude": ["node_modules", ".expo", "dist", "web-build"]}