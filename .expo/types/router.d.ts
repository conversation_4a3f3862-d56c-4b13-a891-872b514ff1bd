/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/coachPortalInfo` | `/coachPortalInfo`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/forgotPassword` | `/forgotPassword`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(coach)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step1` | `/step1`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step2` | `/step2`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step3` | `/step3`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step4` | `/step4`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step5` | `/step5`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/submit` | `/submit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/welcome` | `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin/feedback-history` | `/checkin/feedback-history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin/form` | `/checkin/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin/history` | `/checkin/history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin` | `/checkin`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile/changePassword` | `/profile/changePassword`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile/edit` | `/profile/edit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile/intakeDetails` | `/profile/intakeDetails`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts/active-workout` | `/workouts/active-workout`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts` | `/workouts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts/workout-history` | `/workouts/workout-history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts/workout-summary` | `/workouts/workout-summary`; params?: Router.UnknownInputParams; } | { pathname: `${'/(coach)'}/checkin/[id]` | `/checkin/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(coach)'}/program/[id]` | `/program/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/checkin/[id]` | `/checkin/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/workouts/[id]` | `/workouts/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/workouts/exercise/[id]` | `/workouts/exercise/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/workouts/workout-history/[id]` | `/workouts/workout-history/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/coachPortalInfo` | `/coachPortalInfo`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/forgotPassword` | `/forgotPassword`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(coach)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(intake)'}/step1` | `/step1`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(intake)'}/step2` | `/step2`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(intake)'}/step3` | `/step3`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(intake)'}/step4` | `/step4`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(intake)'}/step5` | `/step5`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(intake)'}/submit` | `/submit`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(intake)'}/welcome` | `/welcome`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/checkin/feedback-history` | `/checkin/feedback-history`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/checkin/form` | `/checkin/form`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/checkin/history` | `/checkin/history`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/checkin` | `/checkin`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile/changePassword` | `/profile/changePassword`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile/edit` | `/profile/edit`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile/intakeDetails` | `/profile/intakeDetails`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/workouts/active-workout` | `/workouts/active-workout`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/workouts` | `/workouts`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/workouts/workout-history` | `/workouts/workout-history`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/workouts/workout-summary` | `/workouts/workout-summary`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(coach)'}/checkin/[id]` | `/checkin/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(coach)'}/program/[id]` | `/program/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(tabs)'}/checkin/[id]` | `/checkin/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(tabs)'}/workouts/[id]` | `/workouts/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(tabs)'}/workouts/exercise/[id]` | `/workouts/exercise/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `${'/(tabs)'}/workouts/workout-history/[id]` | `/workouts/workout-history/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/coachPortalInfo${`?${string}` | `#${string}` | ''}` | `/coachPortalInfo${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/forgotPassword${`?${string}` | `#${string}` | ''}` | `/forgotPassword${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/register${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `${'/(coach)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(intake)'}/step1${`?${string}` | `#${string}` | ''}` | `/step1${`?${string}` | `#${string}` | ''}` | `${'/(intake)'}/step2${`?${string}` | `#${string}` | ''}` | `/step2${`?${string}` | `#${string}` | ''}` | `${'/(intake)'}/step3${`?${string}` | `#${string}` | ''}` | `/step3${`?${string}` | `#${string}` | ''}` | `${'/(intake)'}/step4${`?${string}` | `#${string}` | ''}` | `/step4${`?${string}` | `#${string}` | ''}` | `${'/(intake)'}/step5${`?${string}` | `#${string}` | ''}` | `/step5${`?${string}` | `#${string}` | ''}` | `${'/(intake)'}/submit${`?${string}` | `#${string}` | ''}` | `/submit${`?${string}` | `#${string}` | ''}` | `${'/(intake)'}/welcome${`?${string}` | `#${string}` | ''}` | `/welcome${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/checkin/feedback-history${`?${string}` | `#${string}` | ''}` | `/checkin/feedback-history${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/checkin/form${`?${string}` | `#${string}` | ''}` | `/checkin/form${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/checkin/history${`?${string}` | `#${string}` | ''}` | `/checkin/history${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/checkin${`?${string}` | `#${string}` | ''}` | `/checkin${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile/changePassword${`?${string}` | `#${string}` | ''}` | `/profile/changePassword${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile/edit${`?${string}` | `#${string}` | ''}` | `/profile/edit${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile/intakeDetails${`?${string}` | `#${string}` | ''}` | `/profile/intakeDetails${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/workouts/active-workout${`?${string}` | `#${string}` | ''}` | `/workouts/active-workout${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/workouts${`?${string}` | `#${string}` | ''}` | `/workouts${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/workouts/workout-history${`?${string}` | `#${string}` | ''}` | `/workouts/workout-history${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/workouts/workout-summary${`?${string}` | `#${string}` | ''}` | `/workouts/workout-summary${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/coachPortalInfo` | `/coachPortalInfo`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/forgotPassword` | `/forgotPassword`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/register` | `/register`; params?: Router.UnknownInputParams; } | { pathname: `${'/(coach)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step1` | `/step1`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step2` | `/step2`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step3` | `/step3`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step4` | `/step4`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/step5` | `/step5`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/submit` | `/submit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(intake)'}/welcome` | `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin/feedback-history` | `/checkin/feedback-history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin/form` | `/checkin/form`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin/history` | `/checkin/history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/checkin` | `/checkin`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile/changePassword` | `/profile/changePassword`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile/edit` | `/profile/edit`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile/intakeDetails` | `/profile/intakeDetails`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts/active-workout` | `/workouts/active-workout`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts` | `/workouts`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts/workout-history` | `/workouts/workout-history`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/workouts/workout-summary` | `/workouts/workout-summary`; params?: Router.UnknownInputParams; } | `${'/(coach)'}/checkin/${Router.SingleRoutePart<T>}` | `/checkin/${Router.SingleRoutePart<T>}` | `${'/(coach)'}/program/${Router.SingleRoutePart<T>}` | `/program/${Router.SingleRoutePart<T>}` | `${'/(tabs)'}/checkin/${Router.SingleRoutePart<T>}` | `/checkin/${Router.SingleRoutePart<T>}` | `${'/(tabs)'}/workouts/${Router.SingleRoutePart<T>}` | `/workouts/${Router.SingleRoutePart<T>}` | `${'/(tabs)'}/workouts/exercise/${Router.SingleRoutePart<T>}` | `/workouts/exercise/${Router.SingleRoutePart<T>}` | `${'/(tabs)'}/workouts/workout-history/${Router.SingleRoutePart<T>}` | `/workouts/workout-history/${Router.SingleRoutePart<T>}` | { pathname: `${'/(coach)'}/checkin/[id]` | `/checkin/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(coach)'}/program/[id]` | `/program/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/checkin/[id]` | `/checkin/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/workouts/[id]` | `/workouts/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/workouts/exercise/[id]` | `/workouts/exercise/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `${'/(tabs)'}/workouts/workout-history/[id]` | `/workouts/workout-history/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
