import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PendingProgram {
  id: string
  user_id: string
  name: string
  description: string
  status: string
  client_name: string
  priority: 'overdue' | 'urgent' | 'normal' | 'low'
  days_remaining: number
  cycle_number: number
  review_deadline_date: string
  created_at: string
}

interface CoachAction {
  action: 'approve' | 'reject' | 'bulk_approve' | 'bulk_reject' | 'get_comparison'
  program_id?: string
  program_ids?: string[]
  coach_id: string
  coach_notes?: string
  rejection_reason?: string
  user_id?: string
}

interface DashboardResponse {
  success: boolean
  data?: any
  message?: string
  error?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const url = new URL(req.url)
    const operation = url.pathname.split('/').pop()

    console.log('CoachReviewDashboard.start', { operation, method: req.method })

    let response: DashboardResponse

    switch (req.method) {
      case 'GET':
        response = await handleGetRequest(supabaseClient, url)
        break
      case 'POST':
        const actionData: CoachAction = await req.json()
        response = await handleCoachAction(supabaseClient, actionData)
        break
      default:
        response = { success: false, error: 'Method not allowed' }
    }

    console.log('CoachReviewDashboard.completed', { success: response.success })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: response.success ? 200 : 400
    })
  } catch (error) {
    console.error('CoachReviewDashboard.error', error)
    
    const errorResponse: DashboardResponse = {
      success: false,
      error: error.message
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Handle GET requests for dashboard data
async function handleGetRequest(supabaseClient: any, url: URL): Promise<DashboardResponse> {
  const endpoint = url.searchParams.get('endpoint')
  const coachId = url.searchParams.get('coach_id')

  switch (endpoint) {
    case 'pending_reviews':
      return await getPendingReviews(supabaseClient)
    case 'coach_stats':
      return await getCoachStats(supabaseClient, coachId)
    case 'program_comparison':
      const userId = url.searchParams.get('user_id')
      return await getProgramComparison(supabaseClient, userId)
    default:
      return await getDashboardOverview(supabaseClient)
  }
}

// Get pending programs for review
async function getPendingReviews(supabaseClient: any): Promise<DashboardResponse> {
  try {
    const { data: pendingPrograms, error } = await supabaseClient
      .from('coach_pending_reviews')
      .select('*')
      .order('priority', { ascending: true })
      .order('days_remaining', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch pending reviews: ${error.message}`)
    }

    // Group by priority for better organization
    const groupedPrograms = {
      overdue: pendingPrograms?.filter((p: PendingProgram) => p.priority === 'overdue') || [],
      urgent: pendingPrograms?.filter((p: PendingProgram) => p.priority === 'urgent') || [],
      normal: pendingPrograms?.filter((p: PendingProgram) => p.priority === 'normal') || [],
      low: pendingPrograms?.filter((p: PendingProgram) => p.priority === 'low') || []
    }

    return {
      success: true,
      data: {
        total: pendingPrograms?.length || 0,
        grouped: groupedPrograms,
        all: pendingPrograms || []
      }
    }
  } catch (error) {
    console.error('CoachReviewDashboard.getPendingReviews.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get coach statistics
async function getCoachStats(supabaseClient: any, coachId?: string): Promise<DashboardResponse> {
  try {
    // Get overall stats
    const { data: totalPending } = await supabaseClient
      .from('workout_programs')
      .select('id', { count: 'exact' })
      .in('status', ['ai_generated_pending_review', 'scheduled_pending_review'])

    const { data: reviewedToday } = await supabaseClient
      .from('workout_programs')
      .select('id', { count: 'exact' })
      .not('coach_reviewed_at', 'is', null)
      .gte('coach_reviewed_at', new Date().toISOString().split('T')[0])

    const { data: approvedToday } = await supabaseClient
      .from('workout_programs')
      .select('id', { count: 'exact' })
      .eq('status', 'coach_approved')
      .gte('coach_reviewed_at', new Date().toISOString().split('T')[0])

    // Get coach-specific stats if coach_id provided
    let coachSpecificStats = null
    if (coachId) {
      const { data: coachReviews } = await supabaseClient
        .from('workout_programs')
        .select('id', { count: 'exact' })
        .eq('coach_id', coachId)
        .not('coach_reviewed_at', 'is', null)
        .gte('coach_reviewed_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())

      coachSpecificStats = {
        reviewsThisWeek: coachReviews?.length || 0
      }
    }

    return {
      success: true,
      data: {
        totalPending: totalPending?.length || 0,
        reviewedToday: reviewedToday?.length || 0,
        approvedToday: approvedToday?.length || 0,
        coachStats: coachSpecificStats
      }
    }
  } catch (error) {
    console.error('CoachReviewDashboard.getCoachStats.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get program comparison data
async function getProgramComparison(supabaseClient: any, userId?: string): Promise<DashboardResponse> {
  if (!userId) {
    return {
      success: false,
      error: 'User ID is required for program comparison'
    }
  }

  try {
    const { data: comparison, error } = await supabaseClient
      .rpc('get_program_comparison', { p_user_id: userId })

    if (error) {
      throw new Error(`Failed to get program comparison: ${error.message}`)
    }

    return {
      success: true,
      data: comparison?.[0] || { current_program: null, new_program: null, comparison_notes: 'No data available' }
    }
  } catch (error) {
    console.error('CoachReviewDashboard.getProgramComparison.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get dashboard overview
async function getDashboardOverview(supabaseClient: any): Promise<DashboardResponse> {
  try {
    const pendingReviews = await getPendingReviews(supabaseClient)
    const coachStats = await getCoachStats(supabaseClient)

    return {
      success: true,
      data: {
        pendingReviews: pendingReviews.data,
        stats: coachStats.data
      }
    }
  } catch (error) {
    console.error('CoachReviewDashboard.getDashboardOverview.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Handle coach actions (approve, reject, bulk operations)
async function handleCoachAction(supabaseClient: any, actionData: CoachAction): Promise<DashboardResponse> {
  try {
    switch (actionData.action) {
      case 'approve':
        return await approveProgram(supabaseClient, actionData)
      case 'reject':
        return await rejectProgram(supabaseClient, actionData)
      case 'bulk_approve':
        return await bulkApprovePrograms(supabaseClient, actionData)
      case 'bulk_reject':
        return await bulkRejectPrograms(supabaseClient, actionData)
      default:
        return {
          success: false,
          error: `Unknown action: ${actionData.action}`
        }
    }
  } catch (error) {
    console.error('CoachReviewDashboard.handleCoachAction.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Approve a single program
async function approveProgram(supabaseClient: any, actionData: CoachAction): Promise<DashboardResponse> {
  const { data: result, error } = await supabaseClient
    .rpc('approve_program_by_coach', {
      p_program_id: actionData.program_id,
      p_coach_id: actionData.coach_id,
      p_coach_notes: actionData.coach_notes || 'Approved by coach'
    })

  if (error) {
    throw new Error(`Failed to approve program: ${error.message}`)
  }

  return {
    success: result,
    message: result ? 'Program approved successfully' : 'Failed to approve program'
  }
}

// Reject a single program
async function rejectProgram(supabaseClient: any, actionData: CoachAction): Promise<DashboardResponse> {
  const { data: result, error } = await supabaseClient
    .rpc('reject_program_by_coach', {
      p_program_id: actionData.program_id,
      p_coach_id: actionData.coach_id,
      p_rejection_reason: actionData.rejection_reason || 'Requires modifications'
    })

  if (error) {
    throw new Error(`Failed to reject program: ${error.message}`)
  }

  return {
    success: result,
    message: result ? 'Program rejected and marked for editing' : 'Failed to reject program'
  }
}

// Bulk approve programs
async function bulkApprovePrograms(supabaseClient: any, actionData: CoachAction): Promise<DashboardResponse> {
  if (!actionData.program_ids || actionData.program_ids.length === 0) {
    return {
      success: false,
      error: 'No program IDs provided for bulk approval'
    }
  }

  const { data: results, error } = await supabaseClient
    .rpc('bulk_approve_programs', {
      p_program_ids: actionData.program_ids,
      p_coach_id: actionData.coach_id,
      p_coach_notes: actionData.coach_notes || 'Bulk approved'
    })

  if (error) {
    throw new Error(`Failed to bulk approve programs: ${error.message}`)
  }

  const successCount = results?.filter((r: any) => r.success).length || 0
  const failureCount = results?.filter((r: any) => !r.success).length || 0

  return {
    success: true,
    data: {
      successCount,
      failureCount,
      results
    },
    message: `Bulk approval completed: ${successCount} successful, ${failureCount} failed`
  }
}

// Bulk reject programs
async function bulkRejectPrograms(supabaseClient: any, actionData: CoachAction): Promise<DashboardResponse> {
  if (!actionData.program_ids || actionData.program_ids.length === 0) {
    return {
      success: false,
      error: 'No program IDs provided for bulk rejection'
    }
  }

  // For bulk rejection, we'll update each program individually
  const results = []
  for (const programId of actionData.program_ids) {
    try {
      const { data: result, error } = await supabaseClient
        .rpc('reject_program_by_coach', {
          p_program_id: programId,
          p_coach_id: actionData.coach_id,
          p_rejection_reason: actionData.rejection_reason || 'Bulk rejection - requires review'
        })

      results.push({
        program_id: programId,
        success: !error && result,
        error_message: error?.message || null
      })
    } catch (error) {
      results.push({
        program_id: programId,
        success: false,
        error_message: error.message
      })
    }
  }

  const successCount = results.filter(r => r.success).length
  const failureCount = results.filter(r => !r.success).length

  return {
    success: true,
    data: {
      successCount,
      failureCount,
      results
    },
    message: `Bulk rejection completed: ${successCount} successful, ${failureCount} failed`
  }
}
