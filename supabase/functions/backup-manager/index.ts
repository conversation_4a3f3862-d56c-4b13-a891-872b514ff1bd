import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface BackupRequest {
  backupType: 'full' | 'incremental' | 'job_data' | 'user_data'
  retentionDays?: number
  includeBlobs?: boolean
}

interface BackupResponse {
  success: boolean
  backupId: string
  backupType: string
  dataSize: number
  tablesBackedUp: string[]
  executionTime: number
  errors: string[]
}

interface RecoveryRequest {
  backupId: string
  recoveryType: 'full' | 'selective'
  targetTables?: string[]
  pointInTime?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('BackupManager.start', { timestamp: new Date().toISOString() })

    // Default to automated backup for cron jobs
    const response = await performAutomatedBackup(supabaseClient)
    const executionTime = Date.now() - startTime
    response.executionTime = executionTime

    console.log('BackupManager.completed', { success: response.success })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('BackupManager.error', error)

    const executionTime = Date.now() - startTime
    const errorResponse = {
      success: false,
      error: error.message,
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Perform automated backup (called by cron)
async function performAutomatedBackup(supabaseClient: any): Promise<BackupResponse> {
  const backupId = crypto.randomUUID()

  console.log('BackupManager.performBackup.start', { backupId, type: 'job_data' })

  try {
    // Create job state backup using database function
    const { data: jobBackupId, error } = await supabaseClient.rpc('create_job_state_backup')

    if (error) {
      throw new Error(`Backup failed: ${error.message}`)
    }

    // Get backup details
    const { data: backupRecord } = await supabaseClient
      .from('backup_records')
      .select('*')
      .eq('id', jobBackupId)
      .single()

    return {
      success: true,
      backupId: jobBackupId,
      backupType: 'job_data',
      dataSize: backupRecord?.data_size_bytes || 0,
      tablesBackedUp: backupRecord?.tables_included || [],
      executionTime: 0,
      errors: []
    }
  } catch (error) {
    console.error('BackupManager.performBackup.error', { backupId, error })

    return {
      success: false,
      backupId,
      backupType: 'job_data',
      dataSize: 0,
      tablesBackedUp: [],
      executionTime: 0,
      errors: [error.message]
    }
  }
}

// Test disaster recovery status
async function testDisasterRecovery(supabaseClient: any): Promise<any> {
  try {
    const { data: recoveryStatus, error } = await supabaseClient.rpc('get_disaster_recovery_status')

    if (error) {
      throw new Error(`Recovery status check failed: ${error.message}`)
    }

    return {
      success: true,
      recoveryStatus: recoveryStatus || [],
      overallHealth: calculateOverallHealth(recoveryStatus || [])
    }
  } catch (error) {
    console.error('BackupManager.testDisasterRecovery.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Calculate overall system health
function calculateOverallHealth(components: any[]): string {
  if (components.length === 0) return 'Unknown'

  const avgHealth = components.reduce((sum, comp) => sum + comp.health_score, 0) / components.length

  if (avgHealth >= 90) return 'Healthy'
  if (avgHealth >= 70) return 'Warning'
  return 'Critical'
}




