import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ProgramTemplate {
  name: string
  description: string
  template_type: 'strength' | 'cardio' | 'hybrid' | 'rehabilitation' | 'sport_specific'
  target_experience: 'Beginner' | 'Intermediate' | 'Advanced'
  duration_weeks: number
  training_days_per_week: number
  template_data: any
  created_by: string
  is_public: boolean
}

interface ManualProgramRequest {
  user_id: string
  template_id?: string
  coach_id: string
  program_name?: string
  customizations?: any
}

interface TransitionRequest {
  user_id: string
  from_program_id: string
  to_program_id: string
  transition_date: string
  coach_id: string
  transition_notes?: string
}

interface ProgramManagementResponse {
  success: boolean
  data?: any
  message?: string
  error?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const url = new URL(req.url)
    const endpoint = url.pathname.split('/').pop()

    console.log('ProgramManagement.start', { endpoint, method: req.method })

    let response: ProgramManagementResponse

    switch (req.method) {
      case 'GET':
        response = await handleGetRequest(supabaseClient, url, endpoint)
        break
      case 'POST':
        response = await handlePostRequest(supabaseClient, req, endpoint)
        break
      case 'PUT':
        response = await handlePutRequest(supabaseClient, req, endpoint)
        break
      case 'DELETE':
        response = await handleDeleteRequest(supabaseClient, url, endpoint)
        break
      default:
        response = { success: false, error: 'Method not allowed' }
    }

    console.log('ProgramManagement.completed', { success: response.success })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: response.success ? 200 : 400
    })
  } catch (error) {
    console.error('ProgramManagement.error', error)
    
    const errorResponse: ProgramManagementResponse = {
      success: false,
      error: error.message
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Handle GET requests
async function handleGetRequest(supabaseClient: any, url: URL, endpoint?: string): Promise<ProgramManagementResponse> {
  const coachId = url.searchParams.get('coach_id')
  const programId = url.searchParams.get('program_id')

  switch (endpoint) {
    case 'templates':
      return await getTemplates(supabaseClient, url)
    case 'analytics':
      return await getProgramAnalytics(supabaseClient, programId, coachId)
    case 'transitions':
      return await getTransitions(supabaseClient, url)
    default:
      return await getProgramManagementOverview(supabaseClient, coachId)
  }
}

// Handle POST requests
async function handlePostRequest(supabaseClient: any, req: Request, endpoint?: string): Promise<ProgramManagementResponse> {
  const requestData = await req.json()

  switch (endpoint) {
    case 'create-template':
      return await createTemplate(supabaseClient, requestData)
    case 'create-program':
      return await createProgramFromTemplate(supabaseClient, requestData)
    case 'schedule-transition':
      return await scheduleTransition(supabaseClient, requestData)
    default:
      return { success: false, error: 'Unknown POST endpoint' }
  }
}

// Handle PUT requests
async function handlePutRequest(supabaseClient: any, req: Request, endpoint?: string): Promise<ProgramManagementResponse> {
  const requestData = await req.json()

  switch (endpoint) {
    case 'update-template':
      return await updateTemplate(supabaseClient, requestData)
    case 'update-program':
      return await updateProgram(supabaseClient, requestData)
    default:
      return { success: false, error: 'Unknown PUT endpoint' }
  }
}

// Handle DELETE requests
async function handleDeleteRequest(supabaseClient: any, url: URL, endpoint?: string): Promise<ProgramManagementResponse> {
  const templateId = url.searchParams.get('template_id')

  switch (endpoint) {
    case 'delete-template':
      return await deleteTemplate(supabaseClient, templateId)
    default:
      return { success: false, error: 'Unknown DELETE endpoint' }
  }
}

// Get program templates
async function getTemplates(supabaseClient: any, url: URL): Promise<ProgramManagementResponse> {
  try {
    const templateType = url.searchParams.get('type')
    const experience = url.searchParams.get('experience')
    const isPublic = url.searchParams.get('public')
    const coachId = url.searchParams.get('coach_id')

    let query = supabaseClient
      .from('program_templates')
      .select(`
        *,
        creator:profiles!created_by(full_name)
      `)
      .order('usage_count', { ascending: false })

    if (templateType) {
      query = query.eq('template_type', templateType)
    }
    if (experience) {
      query = query.eq('target_experience', experience)
    }
    if (isPublic === 'true') {
      query = query.eq('is_public', true)
    }
    if (coachId) {
      query = query.eq('created_by', coachId)
    }

    const { data: templates, error } = await query

    if (error) {
      throw new Error(`Failed to fetch templates: ${error.message}`)
    }

    return {
      success: true,
      data: {
        templates: templates || [],
        total: templates?.length || 0
      }
    }
  } catch (error) {
    console.error('ProgramManagement.getTemplates.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get program analytics
async function getProgramAnalytics(supabaseClient: any, programId?: string, coachId?: string): Promise<ProgramManagementResponse> {
  try {
    const { data: analytics, error } = await supabaseClient
      .rpc('get_program_analytics', {
        p_program_id: programId,
        p_coach_id: coachId,
        p_date_range_days: 30
      })

    if (error) {
      throw new Error(`Failed to fetch analytics: ${error.message}`)
    }

    // Group analytics by type
    const groupedAnalytics = {
      overall: analytics?.filter((a: any) => a.analytics_type === 'overall') || [],
      performance: analytics?.filter((a: any) => a.analytics_type === 'performance') || [],
      templates: analytics?.filter((a: any) => a.analytics_type === 'templates') || [],
      programSpecific: analytics?.filter((a: any) => a.analytics_type === 'program_specific') || []
    }

    return {
      success: true,
      data: {
        analytics: groupedAnalytics,
        raw: analytics || []
      }
    }
  } catch (error) {
    console.error('ProgramManagement.getProgramAnalytics.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get program transitions
async function getTransitions(supabaseClient: any, url: URL): Promise<ProgramManagementResponse> {
  try {
    const userId = url.searchParams.get('user_id')
    const status = url.searchParams.get('status')

    let query = supabaseClient
      .from('program_transitions')
      .select(`
        *,
        from_program:workout_programs!from_program_id(name, cycle_number),
        to_program:workout_programs!to_program_id(name, cycle_number),
        user:profiles!user_id(full_name)
      `)
      .order('transition_date', { ascending: false })

    if (userId) {
      query = query.eq('user_id', userId)
    }
    if (status) {
      query = query.eq('transition_status', status)
    }

    const { data: transitions, error } = await query

    if (error) {
      throw new Error(`Failed to fetch transitions: ${error.message}`)
    }

    return {
      success: true,
      data: {
        transitions: transitions || [],
        total: transitions?.length || 0
      }
    }
  } catch (error) {
    console.error('ProgramManagement.getTransitions.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Create a new template
async function createTemplate(supabaseClient: any, templateData: ProgramTemplate): Promise<ProgramManagementResponse> {
  try {
    const { data: newTemplate, error } = await supabaseClient
      .from('program_templates')
      .insert({
        name: templateData.name,
        description: templateData.description,
        template_type: templateData.template_type,
        target_experience: templateData.target_experience,
        duration_weeks: templateData.duration_weeks,
        training_days_per_week: templateData.training_days_per_week,
        template_data: templateData.template_data,
        created_by: templateData.created_by,
        is_public: templateData.is_public || false
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create template: ${error.message}`)
    }

    return {
      success: true,
      data: newTemplate,
      message: 'Template created successfully'
    }
  } catch (error) {
    console.error('ProgramManagement.createTemplate.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Create program from template
async function createProgramFromTemplate(supabaseClient: any, requestData: ManualProgramRequest): Promise<ProgramManagementResponse> {
  try {
    const { data: programId, error } = await supabaseClient
      .rpc('create_program_from_template', {
        p_user_id: requestData.user_id,
        p_template_id: requestData.template_id,
        p_coach_id: requestData.coach_id,
        p_program_name: requestData.program_name,
        p_customizations: requestData.customizations || {}
      })

    if (error) {
      throw new Error(`Failed to create program: ${error.message}`)
    }

    return {
      success: true,
      data: { program_id: programId },
      message: 'Program created successfully from template'
    }
  } catch (error) {
    console.error('ProgramManagement.createProgramFromTemplate.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Schedule program transition
async function scheduleTransition(supabaseClient: any, requestData: TransitionRequest): Promise<ProgramManagementResponse> {
  try {
    const { data: transitionId, error } = await supabaseClient
      .rpc('schedule_program_transition', {
        p_user_id: requestData.user_id,
        p_from_program_id: requestData.from_program_id,
        p_to_program_id: requestData.to_program_id,
        p_transition_date: requestData.transition_date,
        p_coach_id: requestData.coach_id,
        p_transition_notes: requestData.transition_notes
      })

    if (error) {
      throw new Error(`Failed to schedule transition: ${error.message}`)
    }

    return {
      success: true,
      data: { transition_id: transitionId },
      message: 'Program transition scheduled successfully'
    }
  } catch (error) {
    console.error('ProgramManagement.scheduleTransition.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Update template
async function updateTemplate(supabaseClient: any, updateData: any): Promise<ProgramManagementResponse> {
  try {
    const { data: updatedTemplate, error } = await supabaseClient
      .from('program_templates')
      .update({
        name: updateData.name,
        description: updateData.description,
        template_data: updateData.template_data,
        is_public: updateData.is_public,
        updated_at: new Date().toISOString()
      })
      .eq('id', updateData.template_id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update template: ${error.message}`)
    }

    return {
      success: true,
      data: updatedTemplate,
      message: 'Template updated successfully'
    }
  } catch (error) {
    console.error('ProgramManagement.updateTemplate.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Update program
async function updateProgram(supabaseClient: any, updateData: any): Promise<ProgramManagementResponse> {
  try {
    const { data: updatedProgram, error } = await supabaseClient
      .from('workout_programs')
      .update({
        name: updateData.name,
        description: updateData.description,
        coach_notes_for_client: updateData.coach_notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', updateData.program_id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update program: ${error.message}`)
    }

    return {
      success: true,
      data: updatedProgram,
      message: 'Program updated successfully'
    }
  } catch (error) {
    console.error('ProgramManagement.updateProgram.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Delete template
async function deleteTemplate(supabaseClient: any, templateId?: string): Promise<ProgramManagementResponse> {
  if (!templateId) {
    return {
      success: false,
      error: 'Template ID is required'
    }
  }

  try {
    const { error } = await supabaseClient
      .from('program_templates')
      .delete()
      .eq('id', templateId)

    if (error) {
      throw new Error(`Failed to delete template: ${error.message}`)
    }

    return {
      success: true,
      message: 'Template deleted successfully'
    }
  } catch (error) {
    console.error('ProgramManagement.deleteTemplate.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get program management overview
async function getProgramManagementOverview(supabaseClient: any, coachId?: string): Promise<ProgramManagementResponse> {
  try {
    const templates = await getTemplates(supabaseClient, new URL('http://localhost?coach_id=' + (coachId || '')))
    const analytics = await getProgramAnalytics(supabaseClient, undefined, coachId)
    const transitions = await getTransitions(supabaseClient, new URL('http://localhost?status=scheduled'))

    return {
      success: true,
      data: {
        templates: templates.data,
        analytics: analytics.data,
        transitions: transitions.data
      }
    }
  } catch (error) {
    console.error('ProgramManagement.getProgramManagementOverview.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}
