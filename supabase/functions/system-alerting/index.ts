import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AlertingRequest {
  alertType: 'system_failure' | 'job_failure' | 'performance_degradation' | 'capacity_warning' | 'sla_breach'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  details?: any
  source?: string
  userId?: string
  jobId?: string
}

interface AlertingResponse {
  success: boolean
  alertId: string
  notificationsSent: number
  channels: string[]
  errors: string[]
  executionTime: number
}

interface AlertChannel {
  type: 'email' | 'webhook' | 'slack' | 'discord'
  config: any
  enabled: boolean
  severityFilter: string[]
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const alertRequest: AlertingRequest = await req.json()
    
    console.log('SystemAlerting.start', { 
      alertType: alertRequest.alertType,
      severity: alertRequest.severity,
      title: alertRequest.title
    })

    // Store the alert in the database
    const alertId = await storeAlert(supabaseClient, alertRequest)
    
    // Get alert channels configuration
    const channels = await getAlertChannels(supabaseClient, alertRequest.severity)
    
    // Send notifications through configured channels
    const results = await sendNotifications(channels, alertRequest, alertId)

    const executionTime = Date.now() - startTime
    const response: AlertingResponse = {
      success: true,
      alertId,
      notificationsSent: results.successCount,
      channels: results.channels,
      errors: results.errors,
      executionTime
    }

    console.log('SystemAlerting.completed', response)

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('SystemAlerting.error', error)
    
    const executionTime = Date.now() - startTime
    const errorResponse: AlertingResponse = {
      success: false,
      alertId: '',
      notificationsSent: 0,
      channels: [],
      errors: [error.message],
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Store alert in database
async function storeAlert(supabaseClient: any, alertRequest: AlertingRequest): Promise<string> {
  try {
    const { data, error } = await supabaseClient
      .from('system_alerts')
      .insert({
        alert_type: alertRequest.alertType,
        severity: alertRequest.severity,
        title: alertRequest.title,
        message: alertRequest.message,
        details: alertRequest.details || {},
        source: alertRequest.source || 'system',
        user_id: alertRequest.userId,
        job_id: alertRequest.jobId,
        status: 'active',
        created_at: new Date().toISOString()
      })
      .select('id')
      .single()

    if (error) {
      console.error('SystemAlerting.storeAlert.error', error)
      throw new Error(`Failed to store alert: ${error.message}`)
    }

    return data.id
  } catch (error) {
    console.error('SystemAlerting.storeAlert.exception', error)
    throw error
  }
}

// Get configured alert channels
async function getAlertChannels(supabaseClient: any, severity: string): Promise<AlertChannel[]> {
  try {
    const { data, error } = await supabaseClient
      .from('alert_channels')
      .select('*')
      .eq('enabled', true)
      .contains('severity_filter', [severity])

    if (error) {
      console.error('SystemAlerting.getAlertChannels.error', error)
      return getDefaultChannels()
    }

    return data || getDefaultChannels()
  } catch (error) {
    console.error('SystemAlerting.getAlertChannels.exception', error)
    return getDefaultChannels()
  }
}

// Get default alert channels if none configured
function getDefaultChannels(): AlertChannel[] {
  return [
    {
      type: 'webhook',
      config: {
        url: Deno.env.get('ALERT_WEBHOOK_URL') || 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
        method: 'POST'
      },
      enabled: true,
      severityFilter: ['high', 'critical']
    }
  ]
}

// Send notifications through all configured channels
async function sendNotifications(
  channels: AlertChannel[], 
  alertRequest: AlertingRequest, 
  alertId: string
): Promise<{ successCount: number; channels: string[]; errors: string[] }> {
  const results = {
    successCount: 0,
    channels: [] as string[],
    errors: [] as string[]
  }

  for (const channel of channels) {
    try {
      const success = await sendNotificationToChannel(channel, alertRequest, alertId)
      if (success) {
        results.successCount++
        results.channels.push(channel.type)
      } else {
        results.errors.push(`Failed to send to ${channel.type}`)
      }
    } catch (error) {
      console.error('SystemAlerting.sendNotification.error', { channel: channel.type, error })
      results.errors.push(`${channel.type}: ${error.message}`)
    }
  }

  return results
}

// Send notification to a specific channel
async function sendNotificationToChannel(
  channel: AlertChannel, 
  alertRequest: AlertingRequest, 
  alertId: string
): Promise<boolean> {
  switch (channel.type) {
    case 'webhook':
      return await sendWebhookNotification(channel, alertRequest, alertId)
    case 'slack':
      return await sendSlackNotification(channel, alertRequest, alertId)
    case 'email':
      return await sendEmailNotification(channel, alertRequest, alertId)
    case 'discord':
      return await sendDiscordNotification(channel, alertRequest, alertId)
    default:
      console.warn('SystemAlerting.unknownChannelType', { type: channel.type })
      return false
  }
}

// Send webhook notification
async function sendWebhookNotification(
  channel: AlertChannel, 
  alertRequest: AlertingRequest, 
  alertId: string
): Promise<boolean> {
  try {
    const payload = {
      alertId,
      alertType: alertRequest.alertType,
      severity: alertRequest.severity,
      title: alertRequest.title,
      message: alertRequest.message,
      details: alertRequest.details,
      timestamp: new Date().toISOString(),
      source: alertRequest.source || 'fitness-app-system'
    }

    const response = await fetch(channel.config.url, {
      method: channel.config.method || 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...channel.config.headers
      },
      body: JSON.stringify(payload)
    })

    return response.ok
  } catch (error) {
    console.error('SystemAlerting.sendWebhook.error', error)
    return false
  }
}

// Send Slack notification
async function sendSlackNotification(
  channel: AlertChannel, 
  alertRequest: AlertingRequest, 
  alertId: string
): Promise<boolean> {
  try {
    const color = getSeverityColor(alertRequest.severity)
    const emoji = getSeverityEmoji(alertRequest.severity)
    
    const payload = {
      text: `${emoji} System Alert: ${alertRequest.title}`,
      attachments: [
        {
          color,
          fields: [
            {
              title: 'Alert Type',
              value: alertRequest.alertType,
              short: true
            },
            {
              title: 'Severity',
              value: alertRequest.severity.toUpperCase(),
              short: true
            },
            {
              title: 'Message',
              value: alertRequest.message,
              short: false
            },
            {
              title: 'Alert ID',
              value: alertId,
              short: true
            },
            {
              title: 'Timestamp',
              value: new Date().toISOString(),
              short: true
            }
          ]
        }
      ]
    }

    const response = await fetch(channel.config.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    })

    return response.ok
  } catch (error) {
    console.error('SystemAlerting.sendSlack.error', error)
    return false
  }
}

// Send email notification (placeholder - would need email service integration)
async function sendEmailNotification(
  channel: AlertChannel, 
  alertRequest: AlertingRequest, 
  alertId: string
): Promise<boolean> {
  try {
    // This would integrate with an email service like SendGrid, AWS SES, etc.
    console.log('SystemAlerting.sendEmail.placeholder', {
      to: channel.config.recipients,
      subject: `[${alertRequest.severity.toUpperCase()}] ${alertRequest.title}`,
      alertId
    })
    
    // For now, just return true as a placeholder
    return true
  } catch (error) {
    console.error('SystemAlerting.sendEmail.error', error)
    return false
  }
}

// Send Discord notification
async function sendDiscordNotification(
  channel: AlertChannel, 
  alertRequest: AlertingRequest, 
  alertId: string
): Promise<boolean> {
  try {
    const color = getSeverityColorCode(alertRequest.severity)
    const emoji = getSeverityEmoji(alertRequest.severity)
    
    const payload = {
      embeds: [
        {
          title: `${emoji} System Alert`,
          description: alertRequest.title,
          color,
          fields: [
            {
              name: 'Alert Type',
              value: alertRequest.alertType,
              inline: true
            },
            {
              name: 'Severity',
              value: alertRequest.severity.toUpperCase(),
              inline: true
            },
            {
              name: 'Message',
              value: alertRequest.message,
              inline: false
            }
          ],
          footer: {
            text: `Alert ID: ${alertId}`
          },
          timestamp: new Date().toISOString()
        }
      ]
    }

    const response = await fetch(channel.config.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    })

    return response.ok
  } catch (error) {
    console.error('SystemAlerting.sendDiscord.error', error)
    return false
  }
}

// Helper functions for formatting
function getSeverityColor(severity: string): string {
  switch (severity) {
    case 'critical': return 'danger'
    case 'high': return 'warning'
    case 'medium': return 'good'
    case 'low': return '#36a64f'
    default: return 'good'
  }
}

function getSeverityColorCode(severity: string): number {
  switch (severity) {
    case 'critical': return 0xFF0000 // Red
    case 'high': return 0xFF8C00 // Orange
    case 'medium': return 0xFFD700 // Gold
    case 'low': return 0x00FF00 // Green
    default: return 0x808080 // Gray
  }
}

function getSeverityEmoji(severity: string): string {
  switch (severity) {
    case 'critical': return '🚨'
    case 'high': return '⚠️'
    case 'medium': return '⚡'
    case 'low': return 'ℹ️'
    default: return '📢'
  }
}
