import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface JobRecord {
  job_id: string
  user_id: string
  job_type: string
  priority: number
  scheduled_date: string
  cycle_number: number | null
  job_metadata: any
}

interface ProcessorResponse {
  success: boolean
  processedJobs: number
  completedJobs: number
  failedJobs: number
  timeoutJobs: number
  cleanedUpJobs: number
  errors: string[]
  executionTime: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('JobQueueProcessor.start', { timestamp: new Date().toISOString() })

    // Handle job timeouts first
    const timeoutJobs = await handleJobTimeouts(supabaseClient)
    console.log('JobQueueProcessor.timeouts', { count: timeoutJobs })

    // Process pending jobs
    const results = await processJobQueue(supabaseClient)
    
    // Clean up old completed jobs
    const cleanedUpJobs = await cleanupOldJobs(supabaseClient)
    console.log('JobQueueProcessor.cleanup', { count: cleanedUpJobs })

    const executionTime = Date.now() - startTime
    const response: ProcessorResponse = {
      ...results,
      timeoutJobs,
      cleanedUpJobs,
      executionTime
    }

    console.log('JobQueueProcessor.completed', response)

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('JobQueueProcessor.error', error)
    
    const executionTime = Date.now() - startTime
    const errorResponse: ProcessorResponse = {
      success: false,
      processedJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      timeoutJobs: 0,
      cleanedUpJobs: 0,
      errors: [error.message],
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Handle job timeouts
async function handleJobTimeouts(supabaseClient: any): Promise<number> {
  try {
    const { data, error } = await supabaseClient.rpc('handle_job_timeouts')
    
    if (error) {
      console.error('JobQueueProcessor.handleTimeouts.error', error)
      return 0
    }
    
    return data || 0
  } catch (error) {
    console.error('JobQueueProcessor.handleTimeouts.exception', error)
    return 0
  }
}

// Process the job queue
async function processJobQueue(supabaseClient: any): Promise<Omit<ProcessorResponse, 'timeoutJobs' | 'cleanedUpJobs' | 'executionTime'>> {
  const results = {
    success: true,
    processedJobs: 0,
    completedJobs: 0,
    failedJobs: 0,
    errors: [] as string[]
  }

  // Process jobs one by one to maintain order and dependencies
  const maxJobsPerRun = 10 // Limit to prevent long-running executions
  
  for (let i = 0; i < maxJobsPerRun; i++) {
    const nextJob = await getNextJob(supabaseClient)
    if (!nextJob) {
      break // No more jobs to process
    }

    console.log('JobQueueProcessor.processJob.start', { 
      jobId: nextJob.job_id,
      jobType: nextJob.job_type,
      priority: nextJob.priority
    })

    const jobResult = await processJob(supabaseClient, nextJob)
    results.processedJobs++
    
    if (jobResult.success) {
      results.completedJobs++
    } else {
      results.failedJobs++
      results.errors.push(`Job ${nextJob.job_id}: ${jobResult.error}`)
    }
  }

  return results
}

// Get the next job to execute
async function getNextJob(supabaseClient: any): Promise<JobRecord | null> {
  try {
    const { data, error } = await supabaseClient.rpc('get_next_job_to_execute')
    
    if (error) {
      console.error('JobQueueProcessor.getNextJob.error', error)
      return null
    }
    
    return data && data.length > 0 ? data[0] : null
  } catch (error) {
    console.error('JobQueueProcessor.getNextJob.exception', error)
    return null
  }
}

// Process a single job
async function processJob(supabaseClient: any, job: JobRecord): Promise<{ success: boolean; error?: string }> {
  try {
    // Mark job as running
    const started = await startJobExecution(supabaseClient, job.job_id)
    if (!started) {
      return { success: false, error: 'Failed to mark job as running' }
    }

    // Execute the job based on its type
    const result = await executeJobByType(supabaseClient, job)
    
    // Mark job as completed or failed
    const completed = await completeJobExecution(
      supabaseClient, 
      job.job_id, 
      result.success, 
      result.error,
      result.metadata
    )
    
    if (!completed) {
      console.error('JobQueueProcessor.processJob.completeError', { jobId: job.job_id })
    }

    return result
  } catch (error) {
    console.error('JobQueueProcessor.processJob.exception', { jobId: job.job_id, error })
    
    // Try to mark job as failed
    await completeJobExecution(supabaseClient, job.job_id, false, error.message)
    
    return { success: false, error: error.message }
  }
}

// Execute job based on its type
async function executeJobByType(supabaseClient: any, job: JobRecord): Promise<{ success: boolean; error?: string; metadata?: any }> {
  const baseUrl = Deno.env.get('SUPABASE_URL')
  const serviceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
  
  const jobPayload = {
    userId: job.user_id,
    jobId: job.job_id,
    cycleNumber: job.cycle_number,
    metadata: job.job_metadata,
    source: 'job_queue'
  }

  let functionName: string
  let timeout = 30000 // Default 30 seconds

  switch (job.job_type) {
    case 'program_generation':
      functionName = 'generate-workout-program'
      timeout = 60000 // 60 seconds for program generation
      break
    case 'auto_approval':
      functionName = 'auto-approve-programs'
      break
    case 'transition':
      functionName = 'program-transition-manager'
      break
    case 'monitoring':
      functionName = 'system-health-monitor'
      timeout = 60000 // 60 seconds for monitoring
      break
    case 'cleanup':
      // Handle cleanup internally
      return await handleCleanupJob(supabaseClient, job)
    default:
      return { success: false, error: `Unknown job type: ${job.job_type}` }
  }

  try {
    const response = await fetch(`${baseUrl}/functions/v1/${functionName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${serviceKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(jobPayload),
      signal: AbortSignal.timeout(timeout)
    })

    if (!response.ok) {
      const errorText = await response.text()
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${errorText}`,
        metadata: { statusCode: response.status }
      }
    }

    const result = await response.json()
    return { 
      success: true, 
      metadata: { 
        statusCode: response.status,
        result: result
      }
    }
  } catch (error) {
    if (error.name === 'TimeoutError') {
      return { success: false, error: 'Job execution timed out' }
    }
    return { success: false, error: error.message }
  }
}

// Handle cleanup job internally
async function handleCleanupJob(supabaseClient: any, job: JobRecord): Promise<{ success: boolean; error?: string; metadata?: any }> {
  try {
    const daysToKeep = job.job_metadata?.daysToKeep || 30
    const { data, error } = await supabaseClient.rpc('cleanup_old_jobs', { p_days_to_keep: daysToKeep })
    
    if (error) {
      return { success: false, error: error.message }
    }
    
    return { 
      success: true, 
      metadata: { 
        cleanedUpCount: data,
        daysToKeep: daysToKeep
      }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Start job execution
async function startJobExecution(supabaseClient: any, jobId: string): Promise<boolean> {
  try {
    const { data, error } = await supabaseClient.rpc('start_job_execution', { p_job_id: jobId })
    return !error && data === true
  } catch (error) {
    console.error('JobQueueProcessor.startJobExecution.exception', { jobId, error })
    return false
  }
}

// Complete job execution
async function completeJobExecution(
  supabaseClient: any, 
  jobId: string, 
  success: boolean, 
  errorMessage?: string,
  metadata?: any
): Promise<boolean> {
  try {
    const { data, error } = await supabaseClient.rpc('complete_job_execution', {
      p_job_id: jobId,
      p_success: success,
      p_error_message: errorMessage,
      p_metadata: metadata
    })
    return !error && data === true
  } catch (error) {
    console.error('JobQueueProcessor.completeJobExecution.exception', { jobId, error })
    return false
  }
}

// Clean up old completed jobs
async function cleanupOldJobs(supabaseClient: any): Promise<number> {
  try {
    const { data, error } = await supabaseClient.rpc('cleanup_old_jobs', { p_days_to_keep: 30 })
    
    if (error) {
      console.error('JobQueueProcessor.cleanupOldJobs.error', error)
      return 0
    }
    
    return data || 0
  } catch (error) {
    console.error('JobQueueProcessor.cleanupOldJobs.exception', error)
    return 0
  }
}
