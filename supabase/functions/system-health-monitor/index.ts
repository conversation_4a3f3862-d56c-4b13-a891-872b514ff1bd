import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { EnhancedLogger } from '../_shared/enhanced-logging.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface HealthMetrics {
  timestamp: string
  scheduledJobs: {
    total: number
    pending: number
    running: number
    completed: number
    failed: number
    overdue: number
  }
  programGeneration: {
    last24Hours: number
    successRate: number
    averageExecutionTime: number
    failureReasons: Array<{ reason: string; count: number }>
  }
  transitions: {
    last24Hours: number
    successRate: number
    pendingTransitions: number
  }
  errors: {
    last24Hours: number
    criticalErrors: number
    topErrors: Array<{ operation: string; count: number; lastOccurrence: string }>
  }
  performance: {
    averageSchedulerTime: number
    averageGenerationTime: number
    averageTransitionTime: number
    slowOperations: Array<{ operation: string; maxTime: number; occurrences: number }>
  }
}

interface HealthResponse {
  success: boolean
  status: 'healthy' | 'warning' | 'critical'
  metrics: HealthMetrics
  alerts: string[]
  recommendations: string[]
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const logger = new EnhancedLogger(supabaseClient, 'HealthMonitor')
    await logger.logInfo('start', 'Health monitoring check started')

    const metrics = await collectHealthMetrics(supabaseClient)
    const { status, alerts, recommendations } = analyzeHealth(metrics)

    const response: HealthResponse = {
      success: true,
      status,
      metrics,
      alerts,
      recommendations
    }

    await logger.logInfo('completed', 'Health monitoring completed', {
      status,
      alertCount: alerts.length,
      recommendationCount: recommendations.length
    })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('HealthMonitor.error', error)
    
    const errorResponse: HealthResponse = {
      success: false,
      status: 'critical',
      metrics: {} as HealthMetrics,
      alerts: ['Health monitoring system failure'],
      recommendations: ['Check system logs and database connectivity']
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Collect comprehensive health metrics
async function collectHealthMetrics(supabaseClient: any): Promise<HealthMetrics> {
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  // Collect scheduled jobs metrics
  const scheduledJobs = await collectScheduledJobsMetrics(supabaseClient, yesterday)
  
  // Collect program generation metrics
  const programGeneration = await collectProgramGenerationMetrics(supabaseClient, yesterday)
  
  // Collect transition metrics
  const transitions = await collectTransitionMetrics(supabaseClient, yesterday)
  
  // Collect error metrics
  const errors = await collectErrorMetrics(supabaseClient, yesterday)
  
  // Collect performance metrics
  const performance = await collectPerformanceMetrics(supabaseClient, yesterday)

  return {
    timestamp: now.toISOString(),
    scheduledJobs,
    programGeneration,
    transitions,
    errors,
    performance
  }
}

// Collect scheduled jobs metrics
async function collectScheduledJobsMetrics(supabaseClient: any, since: Date) {
  const { data: jobs } = await supabaseClient
    .from('scheduled_generation_jobs')
    .select('execution_status, scheduled_date, created_at')
    .gte('created_at', since.toISOString())

  const today = new Date().toISOString().split('T')[0]
  
  return {
    total: jobs?.length || 0,
    pending: jobs?.filter(j => j.execution_status === 'pending').length || 0,
    running: jobs?.filter(j => j.execution_status === 'running').length || 0,
    completed: jobs?.filter(j => j.execution_status === 'completed').length || 0,
    failed: jobs?.filter(j => j.execution_status === 'failed').length || 0,
    overdue: jobs?.filter(j => j.execution_status === 'pending' && j.scheduled_date < today).length || 0
  }
}

// Collect program generation metrics
async function collectProgramGenerationMetrics(supabaseClient: any, since: Date) {
  const { data: logs } = await supabaseClient
    .from('operation_logs')
    .select('execution_status, execution_time_ms, error_details, created_at')
    .like('operation_type', '%generate%')
    .gte('created_at', since.toISOString())

  const total = logs?.length || 0
  const successful = logs?.filter(l => l.execution_status === 'success').length || 0
  const failed = logs?.filter(l => l.execution_status === 'error').length || 0
  
  const executionTimes = logs?.filter(l => l.execution_time_ms).map(l => l.execution_time_ms) || []
  const averageExecutionTime = executionTimes.length > 0 
    ? executionTimes.reduce((a, b) => a + b, 0) / executionTimes.length 
    : 0

  // Analyze failure reasons
  const failureReasons = logs?.filter(l => l.execution_status === 'error')
    .reduce((acc, log) => {
      const reason = log.error_details?.message || 'Unknown error'
      acc[reason] = (acc[reason] || 0) + 1
      return acc
    }, {} as Record<string, number>) || {}

  return {
    last24Hours: total,
    successRate: total > 0 ? (successful / total) * 100 : 100,
    averageExecutionTime,
    failureReasons: Object.entries(failureReasons)
      .map(([reason, count]) => ({ reason, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
  }
}

// Collect transition metrics
async function collectTransitionMetrics(supabaseClient: any, since: Date) {
  const { data: transitions } = await supabaseClient
    .from('program_transitions')
    .select('transition_status, created_at, completed_at')
    .gte('created_at', since.toISOString())

  const { data: pending } = await supabaseClient
    .from('program_transitions')
    .select('id')
    .eq('transition_status', 'scheduled')

  const total = transitions?.length || 0
  const completed = transitions?.filter(t => t.transition_status === 'completed').length || 0

  return {
    last24Hours: total,
    successRate: total > 0 ? (completed / total) * 100 : 100,
    pendingTransitions: pending?.length || 0
  }
}

// Collect error metrics
async function collectErrorMetrics(supabaseClient: any, since: Date) {
  const { data: errorLogs } = await supabaseClient
    .from('operation_logs')
    .select('operation_type, error_details, created_at')
    .eq('execution_status', 'error')
    .gte('created_at', since.toISOString())

  const total = errorLogs?.length || 0
  const critical = errorLogs?.filter(l => 
    l.error_details?.message?.toLowerCase().includes('critical') ||
    l.error_details?.message?.toLowerCase().includes('database') ||
    l.error_details?.message?.toLowerCase().includes('timeout')
  ).length || 0

  // Top errors by operation type
  const errorsByOperation = errorLogs?.reduce((acc, log) => {
    const operation = log.operation_type
    if (!acc[operation]) {
      acc[operation] = { count: 0, lastOccurrence: log.created_at }
    }
    acc[operation].count++
    if (log.created_at > acc[operation].lastOccurrence) {
      acc[operation].lastOccurrence = log.created_at
    }
    return acc
  }, {} as Record<string, { count: number; lastOccurrence: string }>) || {}

  return {
    last24Hours: total,
    criticalErrors: critical,
    topErrors: Object.entries(errorsByOperation)
      .map(([operation, data]) => ({ operation, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
  }
}

// Collect performance metrics
async function collectPerformanceMetrics(supabaseClient: any, since: Date) {
  const { data: perfLogs } = await supabaseClient
    .from('operation_logs')
    .select('operation_type, execution_time_ms')
    .eq('execution_status', 'success')
    .not('execution_time_ms', 'is', null)
    .gte('created_at', since.toISOString())

  const schedulerTimes = perfLogs?.filter(l => l.operation_type.includes('scheduler')).map(l => l.execution_time_ms) || []
  const generationTimes = perfLogs?.filter(l => l.operation_type.includes('generate')).map(l => l.execution_time_ms) || []
  const transitionTimes = perfLogs?.filter(l => l.operation_type.includes('transition')).map(l => l.execution_time_ms) || []

  // Find slow operations (> 10 seconds)
  const slowOperations = perfLogs?.filter(l => l.execution_time_ms > 10000)
    .reduce((acc, log) => {
      const operation = log.operation_type
      if (!acc[operation]) {
        acc[operation] = { maxTime: 0, occurrences: 0 }
      }
      acc[operation].occurrences++
      if (log.execution_time_ms > acc[operation].maxTime) {
        acc[operation].maxTime = log.execution_time_ms
      }
      return acc
    }, {} as Record<string, { maxTime: number; occurrences: number }>) || {}

  return {
    averageSchedulerTime: schedulerTimes.length > 0 ? schedulerTimes.reduce((a, b) => a + b, 0) / schedulerTimes.length : 0,
    averageGenerationTime: generationTimes.length > 0 ? generationTimes.reduce((a, b) => a + b, 0) / generationTimes.length : 0,
    averageTransitionTime: transitionTimes.length > 0 ? transitionTimes.reduce((a, b) => a + b, 0) / transitionTimes.length : 0,
    slowOperations: Object.entries(slowOperations)
      .map(([operation, data]) => ({ operation, ...data }))
      .sort((a, b) => b.maxTime - a.maxTime)
      .slice(0, 5)
  }
}

// Analyze health and generate alerts/recommendations
function analyzeHealth(metrics: HealthMetrics): {
  status: 'healthy' | 'warning' | 'critical'
  alerts: string[]
  recommendations: string[]
} {
  const alerts: string[] = []
  const recommendations: string[] = []
  let status: 'healthy' | 'warning' | 'critical' = 'healthy'

  // Check scheduled jobs
  if (metrics.scheduledJobs.overdue > 0) {
    alerts.push(`${metrics.scheduledJobs.overdue} scheduled jobs are overdue`)
    recommendations.push('Check scheduler execution and system resources')
    status = 'warning'
  }

  if (metrics.scheduledJobs.failed > metrics.scheduledJobs.completed * 0.1) {
    alerts.push(`High job failure rate: ${metrics.scheduledJobs.failed} failed vs ${metrics.scheduledJobs.completed} completed`)
    recommendations.push('Review job failure logs and improve error handling')
    status = 'warning'
  }

  // Check program generation
  if (metrics.programGeneration.successRate < 90) {
    alerts.push(`Low program generation success rate: ${metrics.programGeneration.successRate.toFixed(1)}%`)
    recommendations.push('Investigate program generation failures and improve reliability')
    status = 'warning'
  }

  if (metrics.programGeneration.averageExecutionTime > 30000) {
    alerts.push(`Slow program generation: ${(metrics.programGeneration.averageExecutionTime / 1000).toFixed(1)}s average`)
    recommendations.push('Optimize program generation performance')
    status = 'warning'
  }

  // Check errors
  if (metrics.errors.criticalErrors > 0) {
    alerts.push(`${metrics.errors.criticalErrors} critical errors in last 24 hours`)
    recommendations.push('Address critical errors immediately')
    status = 'critical'
  }

  if (metrics.errors.last24Hours > 50) {
    alerts.push(`High error count: ${metrics.errors.last24Hours} errors in last 24 hours`)
    recommendations.push('Review error patterns and improve system stability')
    if (status !== 'critical') status = 'warning'
  }

  // Check performance
  if (metrics.performance.slowOperations.length > 0) {
    const slowest = metrics.performance.slowOperations[0]
    alerts.push(`Slow operations detected: ${slowest.operation} taking up to ${(slowest.maxTime / 1000).toFixed(1)}s`)
    recommendations.push('Optimize slow operations and consider scaling resources')
    if (status !== 'critical') status = 'warning'
  }

  return { status, alerts, recommendations }
}
