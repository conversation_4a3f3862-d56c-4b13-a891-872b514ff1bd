import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface TransitionRequest {
  userId?: string
  programId?: string
  transitionType?: 'manual' | 'automatic' | 'coach_initiated'
  forceTransition?: boolean
}

interface PendingTransition {
  id: string
  user_id: string
  from_program_id: string | null
  to_program_id: string
  transition_date: string
  transition_status: string
  transition_type: string
  to_program_name: string
  to_program_cycle: number
  client_name: string
}

interface TransitionResponse {
  success: boolean
  transitionsProcessed: number
  transitionsCompleted: number
  transitionsFailed: number
  errors: string[]
  processedTransitions?: PendingTransition[]
  executionTime: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()
  
  try {
    console.log('TransitionManager.start', { timestamp: new Date().toISOString() })
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const requestBody: TransitionRequest = await req.json().catch(() => ({}))
    
    let results: Omit<TransitionResponse, 'executionTime'>

    if (requestBody.userId || requestBody.programId) {
      // Handle specific user/program transition
      results = await processSpecificTransition(supabaseClient, requestBody)
    } else {
      // Handle batch processing of scheduled transitions
      results = await processPendingTransitions(supabaseClient)
    }
    
    const executionTime = Date.now() - startTime
    const response: TransitionResponse = {
      ...results,
      executionTime
    }

    console.log('TransitionManager.completed', response)

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('TransitionManager.error', error)
    
    const executionTime = Date.now() - startTime
    const errorResponse: TransitionResponse = {
      success: false,
      transitionsProcessed: 0,
      transitionsCompleted: 0,
      transitionsFailed: 0,
      errors: [error.message],
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Process specific user or program transition
async function processSpecificTransition(
  supabaseClient: any,
  request: TransitionRequest
): Promise<Omit<TransitionResponse, 'executionTime'>> {
  const results = {
    success: true,
    transitionsProcessed: 0,
    transitionsCompleted: 0,
    transitionsFailed: 0,
    errors: [] as string[],
    processedTransitions: [] as PendingTransition[]
  }

  try {
    if (request.programId) {
      // Transition specific program
      const transition = await executeSpecificProgramTransition(
        supabaseClient, 
        request.programId, 
        request.transitionType || 'manual',
        request.forceTransition || false
      )
      
      if (transition.success) {
        results.transitionsCompleted++
        results.processedTransitions.push(transition.data)
      } else {
        results.transitionsFailed++
        results.errors.push(transition.error)
      }
      results.transitionsProcessed++
      
    } else if (request.userId) {
      // Transition user's pending program
      const transition = await executeUserTransition(
        supabaseClient, 
        request.userId, 
        request.transitionType || 'manual'
      )
      
      if (transition.success) {
        results.transitionsCompleted++
        results.processedTransitions.push(transition.data)
      } else {
        results.transitionsFailed++
        results.errors.push(transition.error)
      }
      results.transitionsProcessed++
    }
  } catch (error) {
    results.errors.push(error.message)
    results.transitionsFailed++
  }

  return results
}

// Process all pending transitions scheduled for today or overdue
async function processPendingTransitions(
  supabaseClient: any
): Promise<Omit<TransitionResponse, 'executionTime'>> {
  const results = {
    success: true,
    transitionsProcessed: 0,
    transitionsCompleted: 0,
    transitionsFailed: 0,
    errors: [] as string[],
    processedTransitions: [] as PendingTransition[]
  }

  // Find pending transitions due today or overdue
  const pendingTransitions = await findPendingTransitions(supabaseClient)
  console.log('TransitionManager.pendingTransitions', { count: pendingTransitions.length })

  for (const transition of pendingTransitions) {
    try {
      console.log('TransitionManager.processTransition.start', {
        transitionId: transition.id,
        userId: transition.user_id,
        programId: transition.to_program_id
      })

      const result = await executeTransition(supabaseClient, transition)
      
      if (result.success) {
        results.transitionsCompleted++
        results.processedTransitions.push(transition)
      } else {
        results.transitionsFailed++
        results.errors.push(`Transition ${transition.id}: ${result.error}`)
      }
      
      results.transitionsProcessed++

    } catch (error) {
      console.error('TransitionManager.processTransition.error', {
        transitionId: transition.id,
        error: error.message
      })
      results.errors.push(`Transition ${transition.id}: ${error.message}`)
      results.transitionsFailed++
    }
  }

  return results
}

// Find transitions scheduled for today or overdue
async function findPendingTransitions(supabaseClient: any): Promise<PendingTransition[]> {
  const today = new Date().toISOString().split('T')[0]
  
  const { data, error } = await supabaseClient
    .from('program_transitions')
    .select(`
      id,
      user_id,
      from_program_id,
      to_program_id,
      transition_date,
      transition_status,
      transition_type,
      workout_programs!program_transitions_to_program_id_fkey(name, cycle_number),
      profiles!inner(full_name)
    `)
    .eq('transition_status', 'scheduled')
    .lte('transition_date', today)
    .order('transition_date', { ascending: true })

  if (error) {
    console.error('TransitionManager.findPendingTransitions.error', error)
    throw new Error(`Failed to find pending transitions: ${error.message}`)
  }

  if (!data || data.length === 0) {
    return []
  }

  return data.map(t => ({
    id: t.id,
    user_id: t.user_id,
    from_program_id: t.from_program_id,
    to_program_id: t.to_program_id,
    transition_date: t.transition_date,
    transition_status: t.transition_status,
    transition_type: t.transition_type,
    to_program_name: t.workout_programs.name,
    to_program_cycle: t.workout_programs.cycle_number,
    client_name: t.profiles.full_name
  }))
}

// Execute a specific transition
async function executeTransition(
  supabaseClient: any,
  transition: PendingTransition
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('TransitionManager.executeTransition.start', {
      transitionId: transition.id,
      userId: transition.user_id,
      fromProgram: transition.from_program_id,
      toProgram: transition.to_program_id
    })

    // 1. Mark current active program as completed (if exists)
    if (transition.from_program_id) {
      const { error: completeError } = await supabaseClient
        .from('workout_programs')
        .update({ 
          cycle_status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', transition.from_program_id)

      if (completeError) {
        console.error('TransitionManager.completeProgram.error', completeError)
        return { success: false, error: `Failed to complete previous program: ${completeError.message}` }
      }
    } else {
      // Mark any active program for this user as completed
      const { error: completeError } = await supabaseClient
        .from('workout_programs')
        .update({ 
          cycle_status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('user_id', transition.user_id)
        .eq('cycle_status', 'active')

      if (completeError) {
        console.error('TransitionManager.completeActiveProgram.error', completeError)
        // Don't fail the transition if this fails
      }
    }

    // 2. Activate the new program
    const { error: activateError } = await supabaseClient
      .from('workout_programs')
      .update({
        cycle_status: 'active',
        status: 'active_by_client',
        client_start_date: new Date().toISOString().split('T')[0],
        cycle_start_date: new Date().toISOString().split('T')[0],
        updated_at: new Date().toISOString()
      })
      .eq('id', transition.to_program_id)

    if (activateError) {
      console.error('TransitionManager.activateProgram.error', activateError)
      return { success: false, error: `Failed to activate new program: ${activateError.message}` }
    }

    // 3. Update transition status
    const { error: transitionError } = await supabaseClient
      .from('program_transitions')
      .update({
        transition_status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', transition.id)

    if (transitionError) {
      console.error('TransitionManager.updateTransition.error', transitionError)
      // Don't fail the transition if this fails - the important work is done
    }

    // 4. Log the transition
    await supabaseClient
      .from('operation_logs')
      .insert({
        operation_type: 'program_transition',
        user_id: transition.user_id,
        program_id: transition.to_program_id,
        operation_data: {
          transition_id: transition.id,
          from_program_id: transition.from_program_id,
          to_program_id: transition.to_program_id,
          transition_type: transition.transition_type,
          cycle_number: transition.to_program_cycle
        },
        execution_status: 'success'
      })

    console.log('TransitionManager.executeTransition.success', {
      transitionId: transition.id,
      userId: transition.user_id,
      newProgramId: transition.to_program_id
    })

    return { success: true }

  } catch (error) {
    console.error('TransitionManager.executeTransition.exception', {
      transitionId: transition.id,
      error: error.message
    })
    return { success: false, error: error.message }
  }
}

// Execute transition for specific program
async function executeSpecificProgramTransition(
  supabaseClient: any,
  programId: string,
  transitionType: string,
  forceTransition: boolean
): Promise<{ success: boolean; data?: any; error?: string }> {
  // Implementation for specific program transition
  // This would be similar to executeTransition but for a specific program
  // For brevity, returning a placeholder
  return { 
    success: false, 
    error: 'Specific program transition not implemented in this version' 
  }
}

// Execute transition for specific user
async function executeUserTransition(
  supabaseClient: any,
  userId: string,
  transitionType: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  // Implementation for user-specific transition
  // This would find the user's pending transition and execute it
  // For brevity, returning a placeholder
  return { 
    success: false, 
    error: 'User-specific transition not implemented in this version' 
  }
}
