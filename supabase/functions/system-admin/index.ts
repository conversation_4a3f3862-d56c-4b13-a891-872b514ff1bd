import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UserRoleUpdate {
  user_id: string
  new_role: string
  admin_id: string
  reason?: string
}

interface ConfigUpdate {
  config_key: string
  config_value: any
  admin_id: string
  description?: string
}

interface SystemAdminResponse {
  success: boolean
  data?: any
  message?: string
  error?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const url = new URL(req.url)
    const endpoint = url.pathname.split('/').pop()

    console.log('SystemAdmin.start', { endpoint, method: req.method })

    let response: SystemAdminResponse

    switch (req.method) {
      case 'GET':
        response = await handleGetRequest(supabaseClient, url, endpoint)
        break
      case 'POST':
        response = await handlePostRequest(supabaseClient, req, endpoint)
        break
      case 'PUT':
        response = await handlePutRequest(supabaseClient, req, endpoint)
        break
      default:
        response = { success: false, error: 'Method not allowed' }
    }

    console.log('SystemAdmin.completed', { success: response.success })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: response.success ? 200 : 400
    })
  } catch (error) {
    console.error('SystemAdmin.error', error)
    
    const errorResponse: SystemAdminResponse = {
      success: false,
      error: error.message
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Handle GET requests
async function handleGetRequest(supabaseClient: any, url: URL, endpoint?: string): Promise<SystemAdminResponse> {
  switch (endpoint) {
    case 'health':
      return await getSystemHealth(supabaseClient)
    case 'users':
      return await getUsers(supabaseClient, url)
    case 'config':
      return await getSystemConfig(supabaseClient)
    case 'audit-log':
      return await getAuditLog(supabaseClient, url)
    case 'job-queue':
      return await getJobQueueStatus(supabaseClient)
    default:
      return await getAdminDashboard(supabaseClient)
  }
}

// Handle POST requests
async function handlePostRequest(supabaseClient: any, req: Request, endpoint?: string): Promise<SystemAdminResponse> {
  const requestData = await req.json()

  switch (endpoint) {
    case 'update-role':
      return await updateUserRole(supabaseClient, requestData)
    case 'update-config':
      return await updateConfig(supabaseClient, requestData)
    case 'restart-job':
      return await restartFailedJob(supabaseClient, requestData)
    default:
      return { success: false, error: 'Unknown POST endpoint' }
  }
}

// Handle PUT requests
async function handlePutRequest(supabaseClient: any, req: Request, endpoint?: string): Promise<SystemAdminResponse> {
  const requestData = await req.json()

  switch (endpoint) {
    case 'system-maintenance':
      return await performSystemMaintenance(supabaseClient, requestData)
    default:
      return { success: false, error: 'Unknown PUT endpoint' }
  }
}

// Get system health dashboard
async function getSystemHealth(supabaseClient: any): Promise<SystemAdminResponse> {
  try {
    const { data: healthData, error } = await supabaseClient
      .rpc('get_system_health_dashboard')

    if (error) {
      throw new Error(`Failed to fetch system health: ${error.message}`)
    }

    // Calculate overall system status
    const criticalComponents = healthData?.filter((h: any) => h.status === 'Critical').length || 0
    const warningComponents = healthData?.filter((h: any) => h.status === 'Warning').length || 0
    
    const overallStatus = criticalComponents > 0 ? 'Critical' : 
                         warningComponents > 0 ? 'Warning' : 'Healthy'

    return {
      success: true,
      data: {
        overallStatus,
        components: healthData || [],
        summary: {
          total: healthData?.length || 0,
          healthy: healthData?.filter((h: any) => h.status === 'Healthy').length || 0,
          warning: warningComponents,
          critical: criticalComponents
        }
      }
    }
  } catch (error) {
    console.error('SystemAdmin.getSystemHealth.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get users with filtering and pagination
async function getUsers(supabaseClient: any, url: URL): Promise<SystemAdminResponse> {
  try {
    const role = url.searchParams.get('role')
    const search = url.searchParams.get('search')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const offset = parseInt(url.searchParams.get('offset') || '0')

    let query = supabaseClient
      .from('profiles')
      .select('id, full_name, role, age, training_experience_level, created_at, updated_at')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (role) {
      query = query.eq('role', role)
    }
    if (search) {
      query = query.ilike('full_name', `%${search}%`)
    }

    const { data: users, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch users: ${error.message}`)
    }

    return {
      success: true,
      data: {
        users: users || [],
        total: count || 0,
        pagination: {
          limit,
          offset,
          hasMore: (count || 0) > offset + limit
        }
      }
    }
  } catch (error) {
    console.error('SystemAdmin.getUsers.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get system configuration
async function getSystemConfig(supabaseClient: any): Promise<SystemAdminResponse> {
  try {
    const { data: config, error } = await supabaseClient
      .from('system_configuration')
      .select(`
        *,
        updater:profiles!updated_by(full_name)
      `)
      .order('config_type', { ascending: true })
      .order('config_key', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch system config: ${error.message}`)
    }

    // Group by config type
    const groupedConfig = {
      system: config?.filter((c: any) => c.config_type === 'system') || [],
      ai: config?.filter((c: any) => c.config_type === 'ai') || [],
      scheduling: config?.filter((c: any) => c.config_type === 'scheduling') || [],
      notifications: config?.filter((c: any) => c.config_type === 'notifications') || [],
      performance: config?.filter((c: any) => c.config_type === 'performance') || []
    }

    return {
      success: true,
      data: {
        config: groupedConfig,
        all: config || []
      }
    }
  } catch (error) {
    console.error('SystemAdmin.getSystemConfig.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get audit log
async function getAuditLog(supabaseClient: any, url: URL): Promise<SystemAdminResponse> {
  try {
    const actionType = url.searchParams.get('action_type')
    const resourceType = url.searchParams.get('resource_type')
    const userId = url.searchParams.get('user_id')
    const limit = parseInt(url.searchParams.get('limit') || '100')

    let query = supabaseClient
      .from('audit_log')
      .select(`
        *,
        user:profiles!user_id(full_name)
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (actionType) {
      query = query.eq('action_type', actionType)
    }
    if (resourceType) {
      query = query.eq('resource_type', resourceType)
    }
    if (userId) {
      query = query.eq('user_id', userId)
    }

    const { data: auditLog, error } = await query

    if (error) {
      throw new Error(`Failed to fetch audit log: ${error.message}`)
    }

    return {
      success: true,
      data: {
        auditLog: auditLog || [],
        total: auditLog?.length || 0
      }
    }
  } catch (error) {
    console.error('SystemAdmin.getAuditLog.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get job queue status
async function getJobQueueStatus(supabaseClient: any): Promise<SystemAdminResponse> {
  try {
    const { data: queueStatus, error } = await supabaseClient
      .from('job_queue_status')
      .select('*')

    const { data: recentJobs, error: jobsError } = await supabaseClient
      .from('scheduled_generation_jobs')
      .select('id, job_type, execution_status, created_at, started_at, completed_at, error_message')
      .order('created_at', { ascending: false })
      .limit(20)

    if (error || jobsError) {
      throw new Error(`Failed to fetch job queue status: ${error?.message || jobsError?.message}`)
    }

    return {
      success: true,
      data: {
        queueStatus: queueStatus || [],
        recentJobs: recentJobs || []
      }
    }
  } catch (error) {
    console.error('SystemAdmin.getJobQueueStatus.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Update user role
async function updateUserRole(supabaseClient: any, roleData: UserRoleUpdate): Promise<SystemAdminResponse> {
  try {
    const { data: result, error } = await supabaseClient
      .rpc('update_user_role', {
        p_user_id: roleData.user_id,
        p_new_role: roleData.new_role,
        p_admin_id: roleData.admin_id,
        p_reason: roleData.reason
      })

    if (error) {
      throw new Error(`Failed to update user role: ${error.message}`)
    }

    return {
      success: result,
      message: result ? 'User role updated successfully' : 'Failed to update user role'
    }
  } catch (error) {
    console.error('SystemAdmin.updateUserRole.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Update system configuration
async function updateConfig(supabaseClient: any, configData: ConfigUpdate): Promise<SystemAdminResponse> {
  try {
    const { data: result, error } = await supabaseClient
      .rpc('update_system_config', {
        p_config_key: configData.config_key,
        p_config_value: configData.config_value,
        p_admin_id: configData.admin_id,
        p_description: configData.description
      })

    if (error) {
      throw new Error(`Failed to update system config: ${error.message}`)
    }

    return {
      success: result,
      message: result ? 'System configuration updated successfully' : 'Failed to update configuration'
    }
  } catch (error) {
    console.error('SystemAdmin.updateConfig.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Restart failed job
async function restartFailedJob(supabaseClient: any, jobData: any): Promise<SystemAdminResponse> {
  try {
    const { data: result, error } = await supabaseClient
      .from('scheduled_generation_jobs')
      .update({
        execution_status: 'pending',
        retry_count: 0,
        error_message: null,
        started_at: null,
        completed_at: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', jobData.job_id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to restart job: ${error.message}`)
    }

    return {
      success: true,
      data: result,
      message: 'Job restarted successfully'
    }
  } catch (error) {
    console.error('SystemAdmin.restartFailedJob.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Perform system maintenance
async function performSystemMaintenance(supabaseClient: any, maintenanceData: any): Promise<SystemAdminResponse> {
  try {
    const results = []

    // Clean up old jobs
    if (maintenanceData.cleanup_jobs) {
      const { data: cleanedJobs, error } = await supabaseClient.rpc('cleanup_old_jobs', { p_days_to_keep: 30 })
      if (!error) {
        results.push(`Cleaned up ${cleanedJobs} old jobs`)
      }
    }

    // Clean up old alerts
    if (maintenanceData.cleanup_alerts) {
      const { data: cleanedAlerts, error } = await supabaseClient
        .from('system_alerts')
        .delete()
        .eq('status', 'resolved')
        .lt('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .select('id')

      if (!error) {
        results.push(`Cleaned up ${cleanedAlerts?.length || 0} old alerts`)
      }
    }

    // Clean up old audit logs
    if (maintenanceData.cleanup_audit_logs) {
      const { data: cleanedLogs, error } = await supabaseClient
        .from('audit_log')
        .delete()
        .lt('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString())
        .select('id')

      if (!error) {
        results.push(`Cleaned up ${cleanedLogs?.length || 0} old audit logs`)
      }
    }

    return {
      success: true,
      data: { results },
      message: `System maintenance completed: ${results.join(', ')}`
    }
  } catch (error) {
    console.error('SystemAdmin.performSystemMaintenance.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get admin dashboard overview
async function getAdminDashboard(supabaseClient: any): Promise<SystemAdminResponse> {
  try {
    const health = await getSystemHealth(supabaseClient)
    const jobQueue = await getJobQueueStatus(supabaseClient)
    
    // Get recent activity summary
    const { data: recentActivity } = await supabaseClient
      .from('audit_log')
      .select('action_type, created_at')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(10)

    return {
      success: true,
      data: {
        systemHealth: health.data,
        jobQueue: jobQueue.data,
        recentActivity: recentActivity || []
      }
    }
  } catch (error) {
    console.error('SystemAdmin.getAdminDashboard.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}
