import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface OverdueProgram {
  id: string
  user_id: string
  name: string
  status: string
  review_deadline_date: string
  cycle_number: number
  cycle_status: string
  generation_type: string
  full_name: string
}

interface AutoApprovalResponse {
  success: boolean
  approvedPrograms: number
  transitionedPrograms: number
  scheduledTransitions: number
  errors: string[]
  overduePrograms?: OverdueProgram[]
  executionTime: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()
  
  try {
    console.log('AutoApproval.start', { timestamp: new Date().toISOString() })
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Find programs past review deadline
    const overduePrograms = await findOverduePrograms(supabaseClient)
    console.log('AutoApproval.overduePrograms', { count: overduePrograms.length })

    // Process auto-approvals and transitions
    const results = await processAutoApprovals(supabaseClient, overduePrograms)
    
    // Log execution for monitoring
    await logAutoApprovalExecution(supabaseClient, results)
    
    const executionTime = Date.now() - startTime
    const response: AutoApprovalResponse = {
      ...results,
      executionTime,
      overduePrograms: overduePrograms.slice(0, 10) // Include first 10 for debugging
    }

    console.log('AutoApproval.completed', response)

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('AutoApproval.error', error)
    
    const executionTime = Date.now() - startTime
    const errorResponse: AutoApprovalResponse = {
      success: false,
      approvedPrograms: 0,
      transitionedPrograms: 0,
      scheduledTransitions: 0,
      errors: [error.message],
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Find programs past their review deadline
async function findOverduePrograms(supabaseClient: any): Promise<OverdueProgram[]> {
  const today = new Date().toISOString().split('T')[0]
  
  console.log('AutoApproval.findOverduePrograms', { today })

  const { data, error } = await supabaseClient
    .from('workout_programs')
    .select(`
      id,
      user_id,
      name,
      status,
      review_deadline_date,
      cycle_number,
      cycle_status,
      generation_type,
      profiles!inner(full_name)
    `)
    .in('status', ['ai_generated_pending_review', 'scheduled_pending_review'])
    .lte('review_deadline_date', today)
    .order('review_deadline_date', { ascending: true })

  if (error) {
    console.error('AutoApproval.findOverduePrograms.error', error)
    throw new Error(`Failed to find overdue programs: ${error.message}`)
  }

  if (!data || data.length === 0) {
    console.log('AutoApproval.findOverduePrograms.noPrograms')
    return []
  }

  return data.map(program => ({
    id: program.id,
    user_id: program.user_id,
    name: program.name,
    status: program.status,
    review_deadline_date: program.review_deadline_date,
    cycle_number: program.cycle_number,
    cycle_status: program.cycle_status,
    generation_type: program.generation_type,
    full_name: program.profiles.full_name
  }))
}

// Process auto-approvals and handle program transitions
async function processAutoApprovals(
  supabaseClient: any, 
  programs: OverdueProgram[]
): Promise<Omit<AutoApprovalResponse, 'executionTime' | 'overduePrograms'>> {
  const results = {
    success: true,
    approvedPrograms: 0,
    transitionedPrograms: 0,
    scheduledTransitions: 0,
    errors: [] as string[]
  }

  for (const program of programs) {
    try {
      console.log('AutoApproval.processProgram.start', { 
        programId: program.id,
        userId: program.user_id,
        cycleNumber: program.cycle_number,
        daysOverdue: calculateDaysOverdue(program.review_deadline_date)
      })

      // Auto-approve the program
      const approved = await autoApproveProgram(supabaseClient, program)
      if (approved) {
        results.approvedPrograms++
      }

      // Handle program transition based on cycle status
      if (program.cycle_status === 'pending_transition') {
        const transitioned = await transitionUserProgram(supabaseClient, program)
        if (transitioned) {
          results.transitionedPrograms++
        }
      } else {
        // Schedule transition for later (e.g., when current cycle ends)
        const scheduled = await scheduleTransition(supabaseClient, program)
        if (scheduled) {
          results.scheduledTransitions++
        }
      }

      console.log('AutoApproval.processProgram.success', { 
        programId: program.id,
        approved,
        transitioned: program.cycle_status === 'pending_transition'
      })

    } catch (error) {
      console.error('AutoApproval.processProgram.error', { 
        programId: program.id, 
        error: error.message 
      })
      results.errors.push(`Program ${program.id}: ${error.message}`)
    }
  }

  return results
}

// Auto-approve an overdue program
async function autoApproveProgram(
  supabaseClient: any, 
  program: OverdueProgram
): Promise<boolean> {
  try {
    const daysOverdue = calculateDaysOverdue(program.review_deadline_date)
    
    const { error } = await supabaseClient
      .from('workout_programs')
      .update({
        status: 'auto_approved',
        coach_reviewed_at: new Date().toISOString(),
        coach_notes_for_client: `Automatically approved - no coach review within deadline (${daysOverdue} days overdue)`
      })
      .eq('id', program.id)

    if (error) {
      console.error('AutoApproval.approveProgram.error', { programId: program.id, error })
      return false
    }

    // Log the auto-approval
    await supabaseClient
      .from('operation_logs')
      .insert({
        operation_type: 'auto_approval',
        user_id: program.user_id,
        program_id: program.id,
        operation_data: {
          original_deadline: program.review_deadline_date,
          days_overdue: daysOverdue,
          auto_approved_at: new Date().toISOString(),
          generation_type: program.generation_type
        },
        execution_status: 'success'
      })

    console.log('AutoApproval.approveProgram.success', { 
      programId: program.id,
      daysOverdue 
    })
    
    return true
  } catch (error) {
    console.error('AutoApproval.approveProgram.exception', { programId: program.id, error })
    return false
  }
}

// Transition user to new program (mark current as completed, activate new)
async function transitionUserProgram(
  supabaseClient: any, 
  program: OverdueProgram
): Promise<boolean> {
  try {
    // Start transaction-like operations
    
    // 1. Mark current active program as completed
    const { error: completeError } = await supabaseClient
      .from('workout_programs')
      .update({ cycle_status: 'completed' })
      .eq('user_id', program.user_id)
      .eq('cycle_status', 'active')

    if (completeError) {
      console.error('AutoApproval.transition.completeError', { 
        userId: program.user_id, 
        error: completeError 
      })
      return false
    }

    // 2. Activate new program
    const { error: activateError } = await supabaseClient
      .from('workout_programs')
      .update({
        cycle_status: 'active',
        status: 'active_by_client',
        client_start_date: new Date().toISOString().split('T')[0],
        cycle_start_date: new Date().toISOString().split('T')[0]
      })
      .eq('id', program.id)

    if (activateError) {
      console.error('AutoApproval.transition.activateError', { 
        programId: program.id, 
        error: activateError 
      })
      return false
    }

    // 3. Record transition
    const { error: transitionError } = await supabaseClient
      .from('program_transitions')
      .insert({
        user_id: program.user_id,
        to_program_id: program.id,
        transition_date: new Date().toISOString().split('T')[0],
        transition_status: 'completed',
        transition_type: 'automatic'
      })

    if (transitionError) {
      console.error('AutoApproval.transition.recordError', { 
        programId: program.id, 
        error: transitionError 
      })
      // Don't return false - transition was successful even if recording failed
    }

    console.log('AutoApproval.transition.success', { 
      userId: program.user_id,
      programId: program.id,
      cycleNumber: program.cycle_number
    })
    
    return true
  } catch (error) {
    console.error('AutoApproval.transition.exception', { 
      programId: program.id, 
      error: error.message 
    })
    return false
  }
}

// Schedule transition for when current cycle ends
async function scheduleTransition(
  supabaseClient: any, 
  program: OverdueProgram
): Promise<boolean> {
  try {
    // Calculate when current cycle should end (28 days from start)
    const { data: currentProgram } = await supabaseClient
      .from('workout_programs')
      .select('cycle_start_date, created_at')
      .eq('user_id', program.user_id)
      .eq('cycle_status', 'active')
      .single()

    if (!currentProgram) {
      console.log('AutoApproval.scheduleTransition.noCurrentProgram', { userId: program.user_id })
      return false
    }

    const cycleStartDate = new Date(currentProgram.cycle_start_date || currentProgram.created_at)
    const transitionDate = new Date(cycleStartDate.getTime() + 28 * 24 * 60 * 60 * 1000)
    const transitionDateStr = transitionDate.toISOString().split('T')[0]

    const { error } = await supabaseClient
      .rpc('schedule_generation_job', {
        p_user_id: program.user_id,
        p_scheduled_date: transitionDateStr,
        p_job_type: 'transition',
        p_cycle_number: program.cycle_number
      })

    if (error) {
      console.error('AutoApproval.scheduleTransition.error', { 
        userId: program.user_id, 
        error 
      })
      return false
    }

    console.log('AutoApproval.scheduleTransition.success', { 
      userId: program.user_id,
      programId: program.id,
      transitionDate: transitionDateStr
    })
    
    return true
  } catch (error) {
    console.error('AutoApproval.scheduleTransition.exception', { 
      programId: program.id, 
      error: error.message 
    })
    return false
  }
}

// Calculate days overdue from review deadline
function calculateDaysOverdue(deadlineDate: string): number {
  const deadline = new Date(deadlineDate)
  const today = new Date()
  const diffTime = today.getTime() - deadline.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// Log auto-approval execution for monitoring
async function logAutoApprovalExecution(
  supabaseClient: any, 
  results: Omit<AutoApprovalResponse, 'executionTime' | 'overduePrograms'>
): Promise<void> {
  try {
    const { error } = await supabaseClient
      .from('operation_logs')
      .insert({
        operation_type: 'auto_approval_batch',
        operation_data: results,
        execution_status: results.success ? 'success' : 'error',
        created_at: new Date().toISOString()
      })

    if (error) {
      console.error('AutoApproval.logExecution.error', error)
    }
  } catch (error) {
    console.error('AutoApproval.logExecution.exception', error)
  }
}
