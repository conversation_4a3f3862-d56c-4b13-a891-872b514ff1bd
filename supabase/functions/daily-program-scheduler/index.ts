import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import {
  EnhancedLogger,
  JobStatusManager,
  RetryManager,
  PerformanceMonitor,
  ErrorAggregator
} from '../_shared/enhanced-logging.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EligibleUser {
  user_id: string
  cycle_number: number
  cycle_start_date: string | null
  created_at: string
  full_name: string
  program_id: string
}

interface SchedulerResponse {
  success: boolean
  processedUsers: number
  scheduledJobs: number
  generatedPrograms: number
  errors: string[]
  eligibleUsers?: EligibleUser[]
  executionTime: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Initialize enhanced logging and monitoring
    const logger = new EnhancedLogger(supabaseClient, 'DailyScheduler')
    const jobManager = new JobStatusManager(supabaseClient, logger)
    const retryManager = new RetryManager(logger)
    const performanceMonitor = new PerformanceMonitor(logger)
    const errorAggregator = new ErrorAggregator(logger)

    await logger.logInfo('start', 'Daily scheduler execution started', {
      timestamp: new Date().toISOString()
    })

    performanceMonitor.startTiming('daily-scheduler-execution')

    // Find users eligible for 21-day program generation
    const eligibleUsers = await findEligibleUsers(supabaseClient, logger)
    await logger.logInfo('eligibleUsers', `Found ${eligibleUsers.length} eligible users`)

    // Process each eligible user with enhanced error handling
    const results = await processEligibleUsers(
      supabaseClient,
      eligibleUsers,
      logger,
      jobManager,
      retryManager,
      errorAggregator
    )

    // Log all aggregated errors
    await errorAggregator.logAllErrors()

    const executionTime = await performanceMonitor.endTiming(
      'daily-scheduler-execution',
      'dailySchedulerExecution'
    )

    const response: SchedulerResponse = {
      ...results,
      executionTime,
      eligibleUsers: eligibleUsers.slice(0, 10) // Include first 10 for debugging
    }

    await logger.logSuccess('completed', response, executionTime)

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('DailyScheduler.error', error)
    
    const executionTime = Date.now() - startTime
    const errorResponse: SchedulerResponse = {
      success: false,
      processedUsers: 0,
      scheduledJobs: 0,
      generatedPrograms: 0,
      errors: [error.message],
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Find users eligible for 21-day program generation
async function findEligibleUsers(
  supabaseClient: any,
  logger: EnhancedLogger
): Promise<EligibleUser[]> {
  const targetDate = new Date()
  targetDate.setDate(targetDate.getDate() - 21) // 21 days ago
  const targetDateStr = targetDate.toISOString().split('T')[0]
  
  console.log('DailyScheduler.findEligibleUsers', { targetDate: targetDateStr })

  // Find users with active programs that started 21 days ago
  const { data, error } = await supabaseClient
    .from('workout_programs')
    .select(`
      id,
      user_id,
      cycle_number,
      cycle_start_date,
      created_at,
      profiles!inner(full_name)
    `)
    .eq('cycle_status', 'active')
    .eq('status', 'active_by_client')
    .or(`cycle_start_date.eq.${targetDateStr},and(cycle_start_date.is.null,created_at.gte.${targetDate.toISOString()},created_at.lt.${new Date(targetDate.getTime() + 24*60*60*1000).toISOString()})`)

  if (error) {
    console.error('DailyScheduler.findEligibleUsers.error', error)
    throw new Error(`Failed to find eligible users: ${error.message}`)
  }

  if (!data || data.length === 0) {
    console.log('DailyScheduler.findEligibleUsers.noUsers')
    return []
  }

  // Filter out users who already have scheduled jobs for today
  const usersWithoutJobs = []
  for (const user of data) {
    const hasExistingJob = await checkExistingScheduledJob(supabaseClient, user.user_id)
    if (!hasExistingJob) {
      usersWithoutJobs.push({
        user_id: user.user_id,
        cycle_number: user.cycle_number,
        cycle_start_date: user.cycle_start_date,
        created_at: user.created_at,
        full_name: user.profiles.full_name,
        program_id: user.id
      })
    }
  }

  console.log('DailyScheduler.eligibleUsersFiltered', { 
    total: data.length, 
    eligible: usersWithoutJobs.length 
  })

  return usersWithoutJobs
}

// Check if user already has a scheduled job for today
async function checkExistingScheduledJob(
  supabaseClient: any, 
  userId: string
): Promise<boolean> {
  const today = new Date().toISOString().split('T')[0]
  
  const { data, error } = await supabaseClient
    .from('scheduled_generation_jobs')
    .select('id')
    .eq('user_id', userId)
    .eq('job_type', 'program_generation')
    .eq('scheduled_date', today)
    .in('execution_status', ['pending', 'running', 'completed'])
    .limit(1)

  if (error) {
    console.error('DailyScheduler.checkExistingJob.error', { userId, error })
    return false // Assume no job exists if we can't check
  }

  return data && data.length > 0
}

// Process eligible users by scheduling jobs and triggering generation
async function processEligibleUsers(
  supabaseClient: any,
  users: EligibleUser[],
  logger: EnhancedLogger,
  jobManager: JobStatusManager,
  retryManager: RetryManager,
  errorAggregator: ErrorAggregator
): Promise<Omit<SchedulerResponse, 'executionTime' | 'eligibleUsers'>> {
  const results = {
    success: true,
    processedUsers: 0,
    scheduledJobs: 0,
    generatedPrograms: 0,
    errors: [] as string[]
  }

  for (const user of users) {
    try {
      console.log('DailyScheduler.processUser.start', { 
        userId: user.user_id, 
        currentCycle: user.cycle_number 
      })

      // Schedule the generation job
      const jobScheduled = await scheduleUserProgramGeneration(supabaseClient, user)
      if (jobScheduled) {
        results.scheduledJobs++
      }

      // Trigger immediate program generation
      const programGenerated = await triggerProgramGeneration(supabaseClient, user)
      if (programGenerated) {
        results.generatedPrograms++
      }

      results.processedUsers++
      
      console.log('DailyScheduler.processUser.success', { 
        userId: user.user_id,
        jobScheduled,
        programGenerated
      })

    } catch (error) {
      console.error('DailyScheduler.processUser.error', { 
        userId: user.user_id, 
        error: error.message 
      })
      results.errors.push(`User ${user.user_id}: ${error.message}`)
    }
  }

  return results
}

// Schedule a program generation job for a user
async function scheduleUserProgramGeneration(
  supabaseClient: any, 
  user: EligibleUser
): Promise<boolean> {
  try {
    const nextCycleNumber = user.cycle_number + 1
    const today = new Date().toISOString().split('T')[0]

    const { data, error } = await supabaseClient
      .rpc('schedule_generation_job', {
        p_user_id: user.user_id,
        p_scheduled_date: today,
        p_job_type: 'program_generation',
        p_cycle_number: nextCycleNumber
      })

    if (error) {
      console.error('DailyScheduler.scheduleJob.error', { userId: user.user_id, error })
      return false
    }

    console.log('DailyScheduler.scheduleJob.success', { 
      userId: user.user_id, 
      jobId: data,
      cycleNumber: nextCycleNumber 
    })
    
    return true
  } catch (error) {
    console.error('DailyScheduler.scheduleJob.exception', { userId: user.user_id, error })
    return false
  }
}

// Trigger immediate program generation
async function triggerProgramGeneration(
  supabaseClient: any, 
  user: EligibleUser
): Promise<boolean> {
  try {
    const nextCycleNumber = user.cycle_number + 1
    
    // Call the generate-workout-program function
    const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/generate-workout-program`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: user.user_id,
        generationType: 'scheduled',
        cycleNumber: nextCycleNumber,
        previousProgramId: user.program_id
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('DailyScheduler.triggerGeneration.httpError', { 
        userId: user.user_id, 
        status: response.status,
        error: errorText 
      })
      return false
    }

    const result = await response.json()
    
    if (!result.success) {
      console.error('DailyScheduler.triggerGeneration.functionError', { 
        userId: user.user_id, 
        result 
      })
      return false
    }

    console.log('DailyScheduler.triggerGeneration.success', { 
      userId: user.user_id,
      programId: result.programId,
      cycleNumber: result.cycleNumber
    })
    
    return true
  } catch (error) {
    console.error('DailyScheduler.triggerGeneration.exception', { 
      userId: user.user_id, 
      error: error.message 
    })
    return false
  }
}

// Log scheduler execution for monitoring
async function logSchedulerExecution(
  supabaseClient: any, 
  results: Omit<SchedulerResponse, 'executionTime' | 'eligibleUsers'>
): Promise<void> {
  try {
    const { error } = await supabaseClient
      .from('operation_logs')
      .insert({
        operation_type: 'daily_program_scheduler',
        operation_data: results,
        execution_status: results.success ? 'success' : 'error',
        created_at: new Date().toISOString()
      })

    if (error) {
      console.error('DailyScheduler.logExecution.error', error)
    }
  } catch (error) {
    console.error('DailyScheduler.logExecution.exception', error)
  }
}
