import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ClientSearchParams {
  searchTerm?: string
  statusFilter?: 'all' | 'active' | 'pending' | 'new' | 'established'
  limit?: number
}

interface ClientNote {
  client_id: string
  coach_id: string
  note_type: 'general' | 'progress' | 'concern' | 'goal_update' | 'communication'
  subject?: string
  note_content: string
  is_action_required?: boolean
  action_due_date?: string
}

interface ClientManagementResponse {
  success: boolean
  data?: any
  message?: string
  error?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const url = new URL(req.url)
    const endpoint = url.pathname.split('/').pop()

    console.log('ClientManagement.start', { endpoint, method: req.method })

    let response: ClientManagementResponse

    switch (req.method) {
      case 'GET':
        response = await handleGetRequest(supabaseClient, url, endpoint)
        break
      case 'POST':
        response = await handlePostRequest(supabaseClient, req, endpoint)
        break
      default:
        response = { success: false, error: 'Method not allowed' }
    }

    console.log('ClientManagement.completed', { success: response.success })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: response.success ? 200 : 400
    })
  } catch (error) {
    console.error('ClientManagement.error', error)
    
    const errorResponse: ClientManagementResponse = {
      success: false,
      error: error.message
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Handle GET requests
async function handleGetRequest(supabaseClient: any, url: URL, endpoint?: string): Promise<ClientManagementResponse> {
  const clientId = url.searchParams.get('client_id')
  const coachId = url.searchParams.get('coach_id')

  switch (endpoint) {
    case 'clients':
      return await getClientsList(supabaseClient, url)
    case 'client-details':
      return await getClientDetails(supabaseClient, clientId)
    case 'client-notes':
      return await getClientNotes(supabaseClient, clientId, coachId)
    case 'client-analytics':
      return await getClientAnalytics(supabaseClient, clientId)
    default:
      return await getClientsOverview(supabaseClient)
  }
}

// Handle POST requests
async function handlePostRequest(supabaseClient: any, req: Request, endpoint?: string): Promise<ClientManagementResponse> {
  const requestData = await req.json()

  switch (endpoint) {
    case 'add-note':
      return await addClientNote(supabaseClient, requestData)
    case 'update-note':
      return await updateClientNote(supabaseClient, requestData)
    default:
      return { success: false, error: 'Unknown POST endpoint' }
  }
}

// Get clients list with search and filtering
async function getClientsList(supabaseClient: any, url: URL): Promise<ClientManagementResponse> {
  try {
    const searchTerm = url.searchParams.get('search') || ''
    const statusFilter = url.searchParams.get('status') || 'all'
    const limit = parseInt(url.searchParams.get('limit') || '50')

    const { data: clients, error } = await supabaseClient
      .rpc('search_clients', {
        p_search_term: searchTerm,
        p_status_filter: statusFilter,
        p_limit: limit
      })

    if (error) {
      throw new Error(`Failed to fetch clients: ${error.message}`)
    }

    return {
      success: true,
      data: {
        clients: clients || [],
        total: clients?.length || 0,
        filters: {
          searchTerm,
          statusFilter,
          limit
        }
      }
    }
  } catch (error) {
    console.error('ClientManagement.getClientsList.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get detailed client information
async function getClientDetails(supabaseClient: any, clientId?: string): Promise<ClientManagementResponse> {
  if (!clientId) {
    return {
      success: false,
      error: 'Client ID is required'
    }
  }

  try {
    const { data: clientDetails, error } = await supabaseClient
      .rpc('get_client_details', { p_user_id: clientId })

    if (error) {
      throw new Error(`Failed to fetch client details: ${error.message}`)
    }

    const details = clientDetails?.[0] || {}

    return {
      success: true,
      data: {
        clientInfo: details.client_info || {},
        programHistory: details.program_history || [],
        currentProgram: details.current_program || null,
        recentCheckins: details.recent_checkins || [],
        progressMetrics: details.progress_metrics || {}
      }
    }
  } catch (error) {
    console.error('ClientManagement.getClientDetails.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get client communication notes
async function getClientNotes(supabaseClient: any, clientId?: string, coachId?: string): Promise<ClientManagementResponse> {
  if (!clientId) {
    return {
      success: false,
      error: 'Client ID is required'
    }
  }

  try {
    let query = supabaseClient
      .from('client_communication_notes')
      .select(`
        *,
        coach:profiles!coach_id(full_name)
      `)
      .eq('client_id', clientId)
      .order('created_at', { ascending: false })

    if (coachId) {
      query = query.eq('coach_id', coachId)
    }

    const { data: notes, error } = await query

    if (error) {
      throw new Error(`Failed to fetch client notes: ${error.message}`)
    }

    // Group notes by type for better organization
    const groupedNotes = {
      general: notes?.filter(n => n.note_type === 'general') || [],
      progress: notes?.filter(n => n.note_type === 'progress') || [],
      concerns: notes?.filter(n => n.note_type === 'concern') || [],
      goalUpdates: notes?.filter(n => n.note_type === 'goal_update') || [],
      communication: notes?.filter(n => n.note_type === 'communication') || []
    }

    const actionRequired = notes?.filter(n => n.is_action_required && !n.is_resolved) || []

    return {
      success: true,
      data: {
        allNotes: notes || [],
        groupedNotes,
        actionRequired,
        totalNotes: notes?.length || 0
      }
    }
  } catch (error) {
    console.error('ClientManagement.getClientNotes.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get client analytics and progress tracking
async function getClientAnalytics(supabaseClient: any, clientId?: string): Promise<ClientManagementResponse> {
  if (!clientId) {
    return {
      success: false,
      error: 'Client ID is required'
    }
  }

  try {
    // Get program completion rates
    const { data: programStats } = await supabaseClient
      .from('workout_programs')
      .select('status, cycle_status, created_at, client_start_date, cycle_start_date')
      .eq('user_id', clientId)
      .order('cycle_number', { ascending: true })

    // Get check-in frequency
    const { data: checkinStats } = await supabaseClient
      .from('weekly_checkins')
      .select('checkin_date, training_performance_rating, recovery_rating, energy_levels_rating')
      .eq('user_id', clientId)
      .gte('checkin_date', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString())
      .order('checkin_date', { ascending: false })

    // Calculate analytics
    const analytics = {
      programStats: {
        totalPrograms: programStats?.length || 0,
        completedPrograms: programStats?.filter(p => p.status === 'completed_by_client').length || 0,
        activePrograms: programStats?.filter(p => p.cycle_status === 'active').length || 0,
        completionRate: programStats?.length > 0 
          ? Math.round((programStats.filter(p => p.status === 'completed_by_client').length / programStats.length) * 100)
          : 0
      },
      checkinStats: {
        totalCheckins: checkinStats?.length || 0,
        avgTrainingRating: checkinStats?.length > 0 
          ? Math.round((checkinStats.reduce((sum, c) => sum + (c.training_performance_rating || 0), 0) / checkinStats.length) * 10) / 10
          : 0,
        avgRecoveryRating: checkinStats?.length > 0 
          ? Math.round((checkinStats.reduce((sum, c) => sum + (c.recovery_rating || 0), 0) / checkinStats.length) * 10) / 10
          : 0,
        avgEnergyRating: checkinStats?.length > 0 
          ? Math.round((checkinStats.reduce((sum, c) => sum + (c.energy_levels_rating || 0), 0) / checkinStats.length) * 10) / 10
          : 0
      },
      engagement: {
        lastCheckin: checkinStats?.[0]?.checkin_date || null,
        checkinFrequency: calculateCheckinFrequency(checkinStats || []),
        programAdherence: calculateProgramAdherence(programStats || [])
      }
    }

    return {
      success: true,
      data: analytics
    }
  } catch (error) {
    console.error('ClientManagement.getClientAnalytics.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Add a new client note
async function addClientNote(supabaseClient: any, noteData: ClientNote): Promise<ClientManagementResponse> {
  try {
    const { data: newNote, error } = await supabaseClient
      .from('client_communication_notes')
      .insert({
        client_id: noteData.client_id,
        coach_id: noteData.coach_id,
        note_type: noteData.note_type,
        subject: noteData.subject,
        note_content: noteData.note_content,
        is_action_required: noteData.is_action_required || false,
        action_due_date: noteData.action_due_date
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to add client note: ${error.message}`)
    }

    return {
      success: true,
      data: newNote,
      message: 'Client note added successfully'
    }
  } catch (error) {
    console.error('ClientManagement.addClientNote.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Update an existing client note
async function updateClientNote(supabaseClient: any, updateData: any): Promise<ClientManagementResponse> {
  try {
    const { data: updatedNote, error } = await supabaseClient
      .from('client_communication_notes')
      .update({
        note_content: updateData.note_content,
        subject: updateData.subject,
        is_resolved: updateData.is_resolved,
        updated_at: new Date().toISOString()
      })
      .eq('id', updateData.note_id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update client note: ${error.message}`)
    }

    return {
      success: true,
      data: updatedNote,
      message: 'Client note updated successfully'
    }
  } catch (error) {
    console.error('ClientManagement.updateClientNote.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get clients overview
async function getClientsOverview(supabaseClient: any): Promise<ClientManagementResponse> {
  try {
    const { data: overview, error } = await supabaseClient
      .from('program_cycle_summary')
      .select('*')
      .order('latest_program_date', { ascending: false })
      .limit(20)

    if (error) {
      throw new Error(`Failed to fetch clients overview: ${error.message}`)
    }

    // Calculate summary statistics
    const stats = {
      totalClients: overview?.length || 0,
      activeClients: overview?.filter(c => c.active_cycles > 0).length || 0,
      newClients: overview?.filter(c => c.client_stage === 'New Client').length || 0,
      pendingReviews: overview?.reduce((sum, c) => sum + (c.pending_reviews || 0), 0) || 0
    }

    return {
      success: true,
      data: {
        clients: overview || [],
        stats
      }
    }
  } catch (error) {
    console.error('ClientManagement.getClientsOverview.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Helper function to calculate check-in frequency
function calculateCheckinFrequency(checkins: any[]): string {
  if (checkins.length === 0) return 'No data'
  
  const now = new Date()
  const recentCheckins = checkins.filter(c => {
    const checkinDate = new Date(c.checkin_date)
    const daysDiff = (now.getTime() - checkinDate.getTime()) / (1000 * 60 * 60 * 24)
    return daysDiff <= 30
  })

  const frequency = recentCheckins.length / 4 // Assuming 4 weeks in a month
  
  if (frequency >= 0.8) return 'Excellent'
  if (frequency >= 0.6) return 'Good'
  if (frequency >= 0.4) return 'Fair'
  return 'Poor'
}

// Helper function to calculate program adherence
function calculateProgramAdherence(programs: any[]): string {
  if (programs.length === 0) return 'No data'
  
  const completedPrograms = programs.filter(p => p.status === 'completed_by_client').length
  const adherenceRate = completedPrograms / programs.length
  
  if (adherenceRate >= 0.8) return 'Excellent'
  if (adherenceRate >= 0.6) return 'Good'
  if (adherenceRate >= 0.4) return 'Fair'
  return 'Needs Improvement'
}
