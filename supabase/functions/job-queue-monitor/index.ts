import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface MonitoringResponse {
  success: boolean
  queueStatus: any[]
  dependencies: any[]
  alerts: Alert[]
  metrics: QueueMetrics
  recommendations: string[]
  executionTime: number
}

interface Alert {
  severity: 'low' | 'medium' | 'high' | 'critical'
  type: string
  message: string
  count?: number
  details?: any
}

interface QueueMetrics {
  totalPendingJobs: number
  totalRunningJobs: number
  totalFailedJobs: number
  avgRetryCount: number
  oldestPendingJob: string | null
  jobsWithDependencies: number
  blockedJobs: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('JobQueueMonitor.start', { timestamp: new Date().toISOString() })

    // Get queue status
    const queueStatus = await getQueueStatus(supabaseClient)
    
    // Get job dependencies
    const dependencies = await getJobDependencies(supabaseClient)
    
    // Calculate metrics
    const metrics = await calculateQueueMetrics(supabaseClient)
    
    // Generate alerts
    const alerts = await generateAlerts(supabaseClient, metrics, queueStatus)
    
    // Generate recommendations
    const recommendations = generateRecommendations(metrics, alerts)

    const executionTime = Date.now() - startTime
    const response: MonitoringResponse = {
      success: true,
      queueStatus,
      dependencies,
      alerts,
      metrics,
      recommendations,
      executionTime
    }

    console.log('JobQueueMonitor.completed', {
      alertCount: alerts.length,
      pendingJobs: metrics.totalPendingJobs,
      failedJobs: metrics.totalFailedJobs
    })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('JobQueueMonitor.error', error)
    
    const executionTime = Date.now() - startTime
    const errorResponse: MonitoringResponse = {
      success: false,
      queueStatus: [],
      dependencies: [],
      alerts: [{
        severity: 'critical',
        type: 'monitoring_error',
        message: `Monitoring system error: ${error.message}`
      }],
      metrics: {
        totalPendingJobs: 0,
        totalRunningJobs: 0,
        totalFailedJobs: 0,
        avgRetryCount: 0,
        oldestPendingJob: null,
        jobsWithDependencies: 0,
        blockedJobs: 0
      },
      recommendations: [],
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Get current queue status
async function getQueueStatus(supabaseClient: any): Promise<any[]> {
  try {
    const { data, error } = await supabaseClient
      .from('job_queue_status')
      .select('*')
    
    if (error) {
      console.error('JobQueueMonitor.getQueueStatus.error', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('JobQueueMonitor.getQueueStatus.exception', error)
    return []
  }
}

// Get job dependencies
async function getJobDependencies(supabaseClient: any): Promise<any[]> {
  try {
    const { data, error } = await supabaseClient
      .from('job_dependencies')
      .select('*')
    
    if (error) {
      console.error('JobQueueMonitor.getJobDependencies.error', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('JobQueueMonitor.getJobDependencies.exception', error)
    return []
  }
}

// Calculate queue metrics
async function calculateQueueMetrics(supabaseClient: any): Promise<QueueMetrics> {
  try {
    const { data, error } = await supabaseClient
      .from('scheduled_generation_jobs')
      .select(`
        execution_status,
        retry_count,
        scheduled_date,
        depends_on_job_id,
        created_at
      `)
      .not('execution_status', 'in', '(completed,cancelled)')
    
    if (error) {
      console.error('JobQueueMonitor.calculateMetrics.error', error)
      return getEmptyMetrics()
    }

    const jobs = data || []
    
    const pendingJobs = jobs.filter(j => j.execution_status === 'pending')
    const runningJobs = jobs.filter(j => j.execution_status === 'running')
    const failedJobs = jobs.filter(j => j.execution_status === 'failed')
    const jobsWithDependencies = jobs.filter(j => j.depends_on_job_id !== null)
    
    // Find blocked jobs (dependencies failed/cancelled)
    const blockedJobs = await findBlockedJobs(supabaseClient)
    
    const avgRetryCount = jobs.length > 0 
      ? jobs.reduce((sum, job) => sum + (job.retry_count || 0), 0) / jobs.length 
      : 0

    const oldestPendingJob = pendingJobs.length > 0
      ? pendingJobs.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())[0].created_at
      : null

    return {
      totalPendingJobs: pendingJobs.length,
      totalRunningJobs: runningJobs.length,
      totalFailedJobs: failedJobs.length,
      avgRetryCount: Math.round(avgRetryCount * 100) / 100,
      oldestPendingJob,
      jobsWithDependencies: jobsWithDependencies.length,
      blockedJobs: blockedJobs.length
    }
  } catch (error) {
    console.error('JobQueueMonitor.calculateMetrics.exception', error)
    return getEmptyMetrics()
  }
}

// Find blocked jobs
async function findBlockedJobs(supabaseClient: any): Promise<any[]> {
  try {
    const { data, error } = await supabaseClient
      .from('job_dependencies')
      .select('*')
      .eq('dependency_state', 'blocked')
    
    if (error) {
      console.error('JobQueueMonitor.findBlockedJobs.error', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('JobQueueMonitor.findBlockedJobs.exception', error)
    return []
  }
}

// Generate alerts based on metrics
async function generateAlerts(supabaseClient: any, metrics: QueueMetrics, queueStatus: any[]): Promise<Alert[]> {
  const alerts: Alert[] = []
  
  // High number of failed jobs
  if (metrics.totalFailedJobs > 10) {
    alerts.push({
      severity: 'high',
      type: 'high_failure_rate',
      message: `High number of failed jobs: ${metrics.totalFailedJobs}`,
      count: metrics.totalFailedJobs
    })
  }
  
  // Jobs stuck in pending for too long
  if (metrics.oldestPendingJob) {
    const oldestJobAge = Date.now() - new Date(metrics.oldestPendingJob).getTime()
    const hoursOld = oldestJobAge / (1000 * 60 * 60)
    
    if (hoursOld > 24) {
      alerts.push({
        severity: 'high',
        type: 'stale_pending_jobs',
        message: `Jobs pending for over 24 hours (oldest: ${Math.round(hoursOld)} hours)`,
        details: { hoursOld: Math.round(hoursOld) }
      })
    } else if (hoursOld > 6) {
      alerts.push({
        severity: 'medium',
        type: 'old_pending_jobs',
        message: `Jobs pending for over 6 hours (oldest: ${Math.round(hoursOld)} hours)`,
        details: { hoursOld: Math.round(hoursOld) }
      })
    }
  }
  
  // High retry count
  if (metrics.avgRetryCount > 2) {
    alerts.push({
      severity: 'medium',
      type: 'high_retry_rate',
      message: `High average retry count: ${metrics.avgRetryCount}`,
      details: { avgRetryCount: metrics.avgRetryCount }
    })
  }
  
  // Blocked jobs
  if (metrics.blockedJobs > 0) {
    alerts.push({
      severity: 'high',
      type: 'blocked_jobs',
      message: `Jobs blocked by failed dependencies: ${metrics.blockedJobs}`,
      count: metrics.blockedJobs
    })
  }
  
  // Queue backlog
  if (metrics.totalPendingJobs > 50) {
    alerts.push({
      severity: 'medium',
      type: 'queue_backlog',
      message: `Large queue backlog: ${metrics.totalPendingJobs} pending jobs`,
      count: metrics.totalPendingJobs
    })
  }
  
  // Long-running jobs
  const longRunningJobs = await findLongRunningJobs(supabaseClient)
  if (longRunningJobs.length > 0) {
    alerts.push({
      severity: 'medium',
      type: 'long_running_jobs',
      message: `Jobs running for over 30 minutes: ${longRunningJobs.length}`,
      count: longRunningJobs.length,
      details: longRunningJobs
    })
  }
  
  return alerts
}

// Find long-running jobs
async function findLongRunningJobs(supabaseClient: any): Promise<any[]> {
  try {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString()
    
    const { data, error } = await supabaseClient
      .from('scheduled_generation_jobs')
      .select('id, job_type, started_at, user_id')
      .eq('execution_status', 'running')
      .lt('started_at', thirtyMinutesAgo)
    
    if (error) {
      console.error('JobQueueMonitor.findLongRunningJobs.error', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('JobQueueMonitor.findLongRunningJobs.exception', error)
    return []
  }
}

// Generate recommendations
function generateRecommendations(metrics: QueueMetrics, alerts: Alert[]): string[] {
  const recommendations: string[] = []
  
  if (metrics.totalFailedJobs > 5) {
    recommendations.push('Review failed job error messages to identify common issues')
    recommendations.push('Consider increasing retry limits or adjusting retry delays')
  }
  
  if (metrics.avgRetryCount > 1.5) {
    recommendations.push('Investigate root causes of job failures to reduce retry frequency')
  }
  
  if (metrics.totalPendingJobs > 20) {
    recommendations.push('Consider increasing job processing frequency or parallel execution')
  }
  
  if (metrics.blockedJobs > 0) {
    recommendations.push('Review and resolve failed dependency jobs to unblock waiting jobs')
  }
  
  const criticalAlerts = alerts.filter(a => a.severity === 'critical')
  if (criticalAlerts.length > 0) {
    recommendations.push('Address critical alerts immediately to prevent system degradation')
  }
  
  const highAlerts = alerts.filter(a => a.severity === 'high')
  if (highAlerts.length > 2) {
    recommendations.push('Multiple high-severity issues detected - consider system maintenance')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Job queue is operating normally')
  }
  
  return recommendations
}

// Get empty metrics object
function getEmptyMetrics(): QueueMetrics {
  return {
    totalPendingJobs: 0,
    totalRunningJobs: 0,
    totalFailedJobs: 0,
    avgRetryCount: 0,
    oldestPendingJob: null,
    jobsWithDependencies: 0,
    blockedJobs: 0
  }
}
