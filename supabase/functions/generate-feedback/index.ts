import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "npm:@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface CheckInData {
  id: string;
  user_id: string;
  checkin_date: string;
  wins?: string;
  challenges?: string;
  progress_reflection?: string;
  training_performance_rating?: number;
  recovery_rating?: number;
  energy_levels_rating?: number;
  additional_notes_for_coach?: string;
}

interface WorkoutLog {
  id: string;
  workout_name_actual: string;
  completed_at: string;
  duration_seconds: number;
  overall_session_rpe?: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    const requestBody = await req.json();
    console.log("GenerateFeedback.requestReceived", { body: requestBody });

    // Extract checkInId directly from the request body
    const checkInId = requestBody.checkInId;

    if (!checkInId) {
      console.error("GenerateFeedback.missingCheckInId", { requestBody });
      throw new Error("Check-in ID is required");
    }

    console.log(`GenerateFeedback.start ${checkInId}`);

    // Fetch check-in data
    const { data: checkIn, error: checkInError } = await supabaseClient
      .from("weekly_checkins")
      .select("*")
      .eq("id", checkInId)
      .single();

    if (checkInError) {
      console.error("GenerateFeedback.fetchCheckIn.failure", checkInError);
      throw new Error(`Failed to fetch check-in data: ${checkInError.message}`);
    }

    if (!checkIn) {
      console.log("GenerateFeedback.checkInNotFound", { checkInId });
      throw new Error("Check-in not found");
    }

    if (!checkIn.user_id) {
      console.error("GenerateFeedback.missingUserId", { checkIn });
      throw new Error("User ID is missing from check-in data");
    }

    // Fetch user's recent workout logs (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: workoutLogs, error: workoutLogsError } = await supabaseClient
      .from("workout_logs")
      .select("id, workout_name_actual, completed_at, duration_seconds, overall_session_rpe")
      .eq("user_id", checkIn.user_id)
      .gte("completed_at", sevenDaysAgo.toISOString())
      .order("completed_at", { ascending: false });

    if (workoutLogsError) {
      console.error("GenerateFeedback.fetchWorkoutLogs.failure", workoutLogsError);
      throw new Error(`Failed to fetch workout logs: ${workoutLogsError.message}`);
    }

    // Check if feedback already exists for this check-in
    const { data: existingFeedback, error: feedbackError } = await supabaseClient
      .from("coach_feedback")
      .select("id")
      .eq("related_checkin_id", checkInId)
      .maybeSingle();

    if (feedbackError) {
      console.error("GenerateFeedback.checkExistingFeedback.failure", feedbackError);
      throw new Error(`Failed to check existing feedback: ${feedbackError.message}`);
    }

    if (existingFeedback) {
      console.log("GenerateFeedback.feedbackAlreadyExists", { feedbackId: existingFeedback.id });
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: "Feedback already exists for this check-in",
          feedbackId: existingFeedback.id
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    // Generate feedback using AI
    const feedback = await generateFeedback(checkIn, workoutLogs);
    console.log("GenerateFeedback.AIGenerated", { feedbackLength: feedback.length });

    // Store feedback in database
    const { data: savedFeedback, error: saveFeedbackError } = await supabaseClient
      .from("coach_feedback")
      .insert({
        user_id: checkIn.user_id,
        related_checkin_id: checkInId,
        feedback_content: feedback,
        status: "draft_by_ai"
      })
      .select()
      .single();

    if (saveFeedbackError) {
      console.error("GenerateFeedback.saveFeedback.failure", saveFeedbackError);
      throw new Error(`Failed to save feedback: ${saveFeedbackError.message}`);
    }

    console.log("GenerateFeedback.success", { feedbackId: savedFeedback.id });

    return new Response(
      JSON.stringify({ 
        success: true, 
        feedbackId: savedFeedback.id 
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );

  } catch (error) {
    console.error("GenerateFeedback.failure", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});

async function generateFeedback(checkIn: CheckInData, workoutLogs: WorkoutLog[]): Promise<string> {
  try {
    // Construct AI prompt
    const prompt = constructAIPrompt(checkIn, workoutLogs);
    console.log("GenerateFeedback.OpenAI.request", { promptLength: prompt.length });

    // Call OpenAI API
    const openAIResponse = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${Deno.env.get("OPENAI_API_KEY")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4-turbo-preview",
        messages: [
          {
            role: "system",
            content: "You are an expert fitness coach providing personalized feedback to clients based on their weekly check-ins and workout data. Your feedback should be encouraging, specific, and actionable. Focus on celebrating wins, addressing challenges, and providing guidance for the coming week."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    if (!openAIResponse.ok) {
      const errorText = await openAIResponse.text();
      console.error("GenerateFeedback.OpenAI.failure", { status: openAIResponse.status, error: errorText });
      throw new Error(`OpenAI API error: ${openAIResponse.status} ${errorText}`);
    }

    const aiResult = await openAIResponse.json();
    console.log("GenerateFeedback.OpenAI.response", { status: "success" });

    // Extract feedback text from AI response
    const feedbackText = aiResult.choices[0].message.content;
    return feedbackText;
  } catch (error) {
    console.error("GenerateFeedback.generateFeedback.failure", error);
    // Provide a fallback feedback if AI generation fails
    return `Thank you for your weekly check-in! I've reviewed your progress and will provide personalized feedback soon. Keep up the good work and stay consistent with your training program.`;
  }
}

function constructAIPrompt(checkIn: CheckInData, workoutLogs: WorkoutLog[]): string {
  const checkInDate = new Date(checkIn.checkin_date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const workoutSummary = workoutLogs.length > 0
    ? `The client completed ${workoutLogs.length} workouts in the past week. ${
        workoutLogs.map(log => {
          const date = new Date(log.completed_at).toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
          });
          const duration = Math.floor(log.duration_seconds / 60);
          return `${date}: ${log.workout_name_actual} (${duration} minutes, RPE: ${log.overall_session_rpe || 'not rated'})`;
        }).join('. ')
      }`
    : "The client did not log any workouts in the past week.";

  return `
Weekly Check-in Summary (${checkInDate}):

Client Wins:
${checkIn.wins || "No specific wins mentioned."}

Client Challenges:
${checkIn.challenges || "No specific challenges mentioned."}

Client's Progress Reflection:
${checkIn.progress_reflection || "No progress reflection provided."}

Performance Ratings:
- Training Performance: ${checkIn.training_performance_rating || 'Not rated'}/10
- Recovery Quality: ${checkIn.recovery_rating || 'Not rated'}/10
- Energy Levels: ${checkIn.energy_levels_rating || 'Not rated'}/10

Additional Notes from Client:
${checkIn.additional_notes_for_coach || "No additional notes provided."}

Recent Workout Activity:
${workoutSummary}

Based on this information, please provide personalized coaching feedback that:
1. Acknowledges and celebrates the client's wins
2. Addresses their challenges with practical solutions
3. Comments on their training performance, recovery, and energy levels
4. Provides specific guidance for the coming week
5. Ends with motivational encouragement

Keep the tone supportive, professional, and personalized. The feedback should be 3-4 paragraphs long.
`;
}