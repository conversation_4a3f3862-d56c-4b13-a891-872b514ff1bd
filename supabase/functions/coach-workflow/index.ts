import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CoachPreferences {
  coach_id: string
  preference_type: 'dashboard_layout' | 'keyboard_shortcuts' | 'notification_settings' | 'workflow_settings'
  preference_data: any
}

interface BulkOperation {
  operation: 'approve' | 'reject' | 'priority_high' | 'priority_normal'
  program_ids: string[]
  coach_id: string
  notes?: string
}

interface WorkflowResponse {
  success: boolean
  data?: any
  message?: string
  error?: string
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const url = new URL(req.url)
    const endpoint = url.pathname.split('/').pop()

    console.log('CoachWorkflow.start', { endpoint, method: req.method })

    let response: WorkflowResponse

    switch (req.method) {
      case 'GET':
        response = await handleGetRequest(supabaseClient, url, endpoint)
        break
      case 'POST':
        response = await handlePostRequest(supabaseClient, req, endpoint)
        break
      case 'PUT':
        response = await handlePutRequest(supabaseClient, req, endpoint)
        break
      default:
        response = { success: false, error: 'Method not allowed' }
    }

    console.log('CoachWorkflow.completed', { success: response.success })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: response.success ? 200 : 400
    })
  } catch (error) {
    console.error('CoachWorkflow.error', error)
    
    const errorResponse: WorkflowResponse = {
      success: false,
      error: error.message
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Handle GET requests
async function handleGetRequest(supabaseClient: any, url: URL, endpoint?: string): Promise<WorkflowResponse> {
  const coachId = url.searchParams.get('coach_id')

  switch (endpoint) {
    case 'preferences':
      return await getCoachPreferences(supabaseClient, coachId)
    case 'performance':
      return await getCoachPerformance(supabaseClient, coachId, url)
    case 'workload':
      return await getCoachWorkload(supabaseClient, coachId)
    case 'shortcuts':
      return await getKeyboardShortcuts(supabaseClient, coachId)
    default:
      return await getWorkflowOverview(supabaseClient, coachId)
  }
}

// Handle POST requests
async function handlePostRequest(supabaseClient: any, req: Request, endpoint?: string): Promise<WorkflowResponse> {
  const requestData = await req.json()

  switch (endpoint) {
    case 'bulk-operation':
      return await executeBulkOperation(supabaseClient, requestData)
    case 'assign-coach':
      return await assignOptimalCoach(supabaseClient)
    case 'update-workload':
      return await updateCoachWorkload(supabaseClient, requestData)
    default:
      return { success: false, error: 'Unknown POST endpoint' }
  }
}

// Handle PUT requests
async function handlePutRequest(supabaseClient: any, req: Request, endpoint?: string): Promise<WorkflowResponse> {
  const requestData = await req.json()

  switch (endpoint) {
    case 'preferences':
      return await updateCoachPreferences(supabaseClient, requestData)
    default:
      return { success: false, error: 'Unknown PUT endpoint' }
  }
}

// Get coach preferences
async function getCoachPreferences(supabaseClient: any, coachId?: string): Promise<WorkflowResponse> {
  if (!coachId) {
    return { success: false, error: 'Coach ID is required' }
  }

  try {
    const { data: preferences, error } = await supabaseClient
      .from('coach_preferences')
      .select('*')
      .eq('coach_id', coachId)

    if (error) {
      throw new Error(`Failed to fetch preferences: ${error.message}`)
    }

    // Group preferences by type
    const groupedPreferences = {
      dashboard_layout: preferences?.find(p => p.preference_type === 'dashboard_layout')?.preference_data || getDefaultDashboardLayout(),
      keyboard_shortcuts: preferences?.find(p => p.preference_type === 'keyboard_shortcuts')?.preference_data || getDefaultKeyboardShortcuts(),
      notification_settings: preferences?.find(p => p.preference_type === 'notification_settings')?.preference_data || getDefaultNotificationSettings(),
      workflow_settings: preferences?.find(p => p.preference_type === 'workflow_settings')?.preference_data || getDefaultWorkflowSettings()
    }

    return {
      success: true,
      data: {
        preferences: groupedPreferences,
        raw: preferences || []
      }
    }
  } catch (error) {
    console.error('CoachWorkflow.getCoachPreferences.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get coach performance metrics
async function getCoachPerformance(supabaseClient: any, coachId?: string, url?: URL): Promise<WorkflowResponse> {
  if (!coachId) {
    return { success: false, error: 'Coach ID is required' }
  }

  try {
    const days = parseInt(url?.searchParams.get('days') || '30')
    
    const { data: metrics, error } = await supabaseClient
      .rpc('get_coach_performance_metrics', {
        p_coach_id: coachId,
        p_days: days
      })

    if (error) {
      throw new Error(`Failed to fetch performance metrics: ${error.message}`)
    }

    // Calculate performance score
    const performanceScore = calculatePerformanceScore(metrics || [])

    return {
      success: true,
      data: {
        metrics: metrics || [],
        performanceScore,
        period: `${days} days`
      }
    }
  } catch (error) {
    console.error('CoachWorkflow.getCoachPerformance.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get coach workload
async function getCoachWorkload(supabaseClient: any, coachId?: string): Promise<WorkflowResponse> {
  if (!coachId) {
    return { success: false, error: 'Coach ID is required' }
  }

  try {
    const { data: workload, error } = await supabaseClient
      .from('coach_workload')
      .select('*')
      .eq('coach_id', coachId)
      .gte('date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('date', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch workload: ${error.message}`)
    }

    const todayWorkload = workload?.find(w => w.date === new Date().toISOString().split('T')[0])

    return {
      success: true,
      data: {
        currentWorkload: todayWorkload || {
          assigned_reviews: 0,
          completed_reviews: 0,
          workload_score: 0,
          is_available: true
        },
        weeklyWorkload: workload || []
      }
    }
  } catch (error) {
    console.error('CoachWorkflow.getCoachWorkload.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get keyboard shortcuts
async function getKeyboardShortcuts(supabaseClient: any, coachId?: string): Promise<WorkflowResponse> {
  try {
    const shortcuts = {
      global: {
        'Ctrl+/': 'Show help',
        'Ctrl+K': 'Quick search',
        'Ctrl+N': 'New program',
        'Ctrl+R': 'Refresh dashboard'
      },
      review: {
        'A': 'Approve program',
        'R': 'Reject program',
        'E': 'Edit program',
        'N': 'Next program',
        'P': 'Previous program',
        'Ctrl+A': 'Select all',
        'Ctrl+Shift+A': 'Bulk approve selected',
        'Ctrl+Shift+R': 'Bulk reject selected'
      },
      navigation: {
        'G then D': 'Go to dashboard',
        'G then R': 'Go to reviews',
        'G then C': 'Go to clients',
        'G then P': 'Go to programs',
        'G then S': 'Go to settings'
      }
    }

    return {
      success: true,
      data: { shortcuts }
    }
  } catch (error) {
    console.error('CoachWorkflow.getKeyboardShortcuts.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Execute bulk operation
async function executeBulkOperation(supabaseClient: any, operationData: BulkOperation): Promise<WorkflowResponse> {
  try {
    const { data: results, error } = await supabaseClient
      .rpc('execute_bulk_operation', {
        p_operation: operationData.operation,
        p_program_ids: operationData.program_ids,
        p_coach_id: operationData.coach_id,
        p_notes: operationData.notes
      })

    if (error) {
      throw new Error(`Failed to execute bulk operation: ${error.message}`)
    }

    const successCount = results?.filter((r: any) => r.success).length || 0
    const failureCount = results?.filter((r: any) => !r.success).length || 0

    return {
      success: true,
      data: {
        results: results || [],
        summary: {
          total: operationData.program_ids.length,
          successful: successCount,
          failed: failureCount
        }
      },
      message: `Bulk ${operationData.operation} completed: ${successCount} successful, ${failureCount} failed`
    }
  } catch (error) {
    console.error('CoachWorkflow.executeBulkOperation.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Assign optimal coach
async function assignOptimalCoach(supabaseClient: any): Promise<WorkflowResponse> {
  try {
    const { data: coachId, error } = await supabaseClient
      .rpc('assign_optimal_coach')

    if (error) {
      throw new Error(`Failed to assign optimal coach: ${error.message}`)
    }

    return {
      success: true,
      data: { assigned_coach_id: coachId },
      message: 'Optimal coach assigned successfully'
    }
  } catch (error) {
    console.error('CoachWorkflow.assignOptimalCoach.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Update coach preferences
async function updateCoachPreferences(supabaseClient: any, preferencesData: CoachPreferences): Promise<WorkflowResponse> {
  try {
    const { data: updatedPreferences, error } = await supabaseClient
      .from('coach_preferences')
      .upsert({
        coach_id: preferencesData.coach_id,
        preference_type: preferencesData.preference_type,
        preference_data: preferencesData.preference_data,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update preferences: ${error.message}`)
    }

    return {
      success: true,
      data: updatedPreferences,
      message: 'Preferences updated successfully'
    }
  } catch (error) {
    console.error('CoachWorkflow.updateCoachPreferences.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Update coach workload
async function updateCoachWorkload(supabaseClient: any, workloadData: any): Promise<WorkflowResponse> {
  try {
    const { data: updatedWorkload, error } = await supabaseClient
      .from('coach_workload')
      .upsert({
        coach_id: workloadData.coach_id,
        date: workloadData.date || new Date().toISOString().split('T')[0],
        completed_reviews: workloadData.completed_reviews,
        avg_review_time_minutes: workloadData.avg_review_time_minutes,
        is_available: workloadData.is_available,
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update workload: ${error.message}`)
    }

    return {
      success: true,
      data: updatedWorkload,
      message: 'Workload updated successfully'
    }
  } catch (error) {
    console.error('CoachWorkflow.updateCoachWorkload.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Get workflow overview
async function getWorkflowOverview(supabaseClient: any, coachId?: string): Promise<WorkflowResponse> {
  try {
    const preferences = await getCoachPreferences(supabaseClient, coachId)
    const performance = await getCoachPerformance(supabaseClient, coachId, new URL('http://localhost?days=7'))
    const workload = await getCoachWorkload(supabaseClient, coachId)

    return {
      success: true,
      data: {
        preferences: preferences.data,
        performance: performance.data,
        workload: workload.data
      }
    }
  } catch (error) {
    console.error('CoachWorkflow.getWorkflowOverview.error', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Helper functions for default settings
function getDefaultDashboardLayout() {
  return {
    layout: 'grid',
    widgets: ['pending_reviews', 'client_overview', 'performance_metrics', 'recent_activity'],
    columns: 2,
    theme: 'light'
  }
}

function getDefaultKeyboardShortcuts() {
  return {
    enabled: true,
    custom_shortcuts: {},
    show_hints: true
  }
}

function getDefaultNotificationSettings() {
  return {
    email_notifications: true,
    browser_notifications: true,
    urgent_only: false,
    quiet_hours: { start: '22:00', end: '08:00' }
  }
}

function getDefaultWorkflowSettings() {
  return {
    auto_assign: true,
    bulk_operations_enabled: true,
    quick_approve_threshold: 3,
    show_program_comparison: true
  }
}

// Calculate performance score
function calculatePerformanceScore(metrics: any[]): number {
  const totalReviews = metrics.find(m => m.metric_name === 'total_reviews')?.metric_value || 0
  const approvalRate = metrics.find(m => m.metric_name === 'approval_rate')?.metric_value || 0
  const avgReviewTime = metrics.find(m => m.metric_name === 'avg_review_time')?.metric_value || 0
  const dailyAvg = metrics.find(m => m.metric_name === 'daily_avg_reviews')?.metric_value || 0

  // Weighted score calculation
  const reviewsScore = Math.min(totalReviews / 50 * 25, 25) // Max 25 points for 50+ reviews
  const approvalScore = approvalRate * 0.3 // Max 30 points for 100% approval
  const speedScore = Math.max(25 - (avgReviewTime / 60 * 5), 0) // Max 25 points, penalty for slow reviews
  const consistencyScore = Math.min(dailyAvg / 5 * 20, 20) // Max 20 points for 5+ daily reviews

  return Math.round(reviewsScore + approvalScore + speedScore + consistencyScore)
}
