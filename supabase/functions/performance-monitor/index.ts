import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PerformanceMetric {
  type: string
  name: string
  value: number
  unit?: string
  tags?: any
}

interface PerformanceThreshold {
  metricName: string
  warningThreshold: number
  criticalThreshold: number
  operator: 'gt' | 'lt' | 'eq'
}

interface MonitoringResponse {
  success: boolean
  metricsCollected: number
  alertsTriggered: number
  systemHealth: 'healthy' | 'warning' | 'critical'
  metrics: PerformanceMetric[]
  alerts: any[]
  executionTime: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    console.log('PerformanceMonitor.start', { timestamp: new Date().toISOString() })

    // Collect system performance metrics
    const metrics = await collectPerformanceMetrics(supabaseClient)
    
    // Store metrics in database
    const storedMetrics = await storeMetrics(supabaseClient, metrics)
    
    // Check thresholds and trigger alerts
    const alerts = await checkThresholdsAndAlert(supabaseClient, metrics)
    
    // Determine overall system health
    const systemHealth = determineSystemHealth(metrics, alerts)

    const executionTime = Date.now() - startTime
    const response: MonitoringResponse = {
      success: true,
      metricsCollected: storedMetrics,
      alertsTriggered: alerts.length,
      systemHealth,
      metrics: metrics.slice(0, 10), // Include first 10 metrics for debugging
      alerts,
      executionTime
    }

    console.log('PerformanceMonitor.completed', {
      metricsCollected: storedMetrics,
      alertsTriggered: alerts.length,
      systemHealth
    })

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('PerformanceMonitor.error', error)
    
    const executionTime = Date.now() - startTime
    const errorResponse: MonitoringResponse = {
      success: false,
      metricsCollected: 0,
      alertsTriggered: 0,
      systemHealth: 'critical',
      metrics: [],
      alerts: [],
      executionTime
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// Collect various performance metrics
async function collectPerformanceMetrics(supabaseClient: any): Promise<PerformanceMetric[]> {
  const metrics: PerformanceMetric[] = []

  try {
    // Database performance metrics
    const dbMetrics = await collectDatabaseMetrics(supabaseClient)
    metrics.push(...dbMetrics)

    // Job queue metrics
    const queueMetrics = await collectJobQueueMetrics(supabaseClient)
    metrics.push(...queueMetrics)

    // System resource metrics
    const resourceMetrics = await collectResourceMetrics()
    metrics.push(...resourceMetrics)

    // Edge Function performance metrics
    const functionMetrics = await collectFunctionMetrics(supabaseClient)
    metrics.push(...functionMetrics)

    console.log('PerformanceMonitor.collectMetrics.success', { count: metrics.length })
    return metrics
  } catch (error) {
    console.error('PerformanceMonitor.collectMetrics.error', error)
    return []
  }
}

// Collect database performance metrics
async function collectDatabaseMetrics(supabaseClient: any): Promise<PerformanceMetric[]> {
  const metrics: PerformanceMetric[] = []

  try {
    // Active connections
    const { data: connections } = await supabaseClient
      .rpc('pg_stat_activity_count')
      .single()
    
    if (connections) {
      metrics.push({
        type: 'database',
        name: 'active_connections',
        value: connections.count || 0,
        unit: 'connections'
      })
    }

    // Database size
    const { data: dbSize } = await supabaseClient
      .rpc('pg_database_size_mb')
      .single()
    
    if (dbSize) {
      metrics.push({
        type: 'database',
        name: 'database_size',
        value: dbSize.size_mb || 0,
        unit: 'MB'
      })
    }

    // Query performance - average execution time
    const { data: queryStats } = await supabaseClient
      .from('scheduled_generation_jobs')
      .select('started_at, completed_at')
      .not('started_at', 'is', null)
      .not('completed_at', 'is', null)
      .gte('completed_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .limit(100)

    if (queryStats && queryStats.length > 0) {
      const avgExecutionTime = queryStats.reduce((sum, job) => {
        const duration = new Date(job.completed_at).getTime() - new Date(job.started_at).getTime()
        return sum + duration
      }, 0) / queryStats.length

      metrics.push({
        type: 'database',
        name: 'avg_job_execution_time',
        value: Math.round(avgExecutionTime),
        unit: 'ms'
      })
    }

  } catch (error) {
    console.error('PerformanceMonitor.collectDatabaseMetrics.error', error)
  }

  return metrics
}

// Collect job queue performance metrics
async function collectJobQueueMetrics(supabaseClient: any): Promise<PerformanceMetric[]> {
  const metrics: PerformanceMetric[] = []

  try {
    const { data: queueStats } = await supabaseClient
      .from('scheduled_generation_jobs')
      .select('execution_status, retry_count, created_at')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())

    if (queueStats) {
      const pendingJobs = queueStats.filter(j => j.execution_status === 'pending').length
      const runningJobs = queueStats.filter(j => j.execution_status === 'running').length
      const failedJobs = queueStats.filter(j => j.execution_status === 'failed').length
      const completedJobs = queueStats.filter(j => j.execution_status === 'completed').length
      
      const avgRetryCount = queueStats.length > 0 
        ? queueStats.reduce((sum, job) => sum + (job.retry_count || 0), 0) / queueStats.length 
        : 0

      metrics.push(
        { type: 'job_queue', name: 'pending_jobs', value: pendingJobs, unit: 'jobs' },
        { type: 'job_queue', name: 'running_jobs', value: runningJobs, unit: 'jobs' },
        { type: 'job_queue', name: 'failed_jobs', value: failedJobs, unit: 'jobs' },
        { type: 'job_queue', name: 'completed_jobs', value: completedJobs, unit: 'jobs' },
        { type: 'job_queue', name: 'avg_retry_count', value: Math.round(avgRetryCount * 100) / 100, unit: 'retries' }
      )

      // Calculate success rate
      const totalJobs = completedJobs + failedJobs
      const successRate = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 100
      metrics.push({
        type: 'job_queue',
        name: 'success_rate',
        value: Math.round(successRate * 100) / 100,
        unit: 'percent'
      })
    }

  } catch (error) {
    console.error('PerformanceMonitor.collectJobQueueMetrics.error', error)
  }

  return metrics
}

// Collect system resource metrics
async function collectResourceMetrics(): Promise<PerformanceMetric[]> {
  const metrics: PerformanceMetric[] = []

  try {
    // Memory usage (Deno-specific)
    const memoryUsage = Deno.memoryUsage()
    metrics.push({
      type: 'system',
      name: 'memory_used',
      value: Math.round(memoryUsage.rss / 1024 / 1024),
      unit: 'MB'
    })

    // Current timestamp for uptime calculation
    metrics.push({
      type: 'system',
      name: 'timestamp',
      value: Date.now(),
      unit: 'ms'
    })

  } catch (error) {
    console.error('PerformanceMonitor.collectResourceMetrics.error', error)
  }

  return metrics
}

// Collect Edge Function performance metrics
async function collectFunctionMetrics(supabaseClient: any): Promise<PerformanceMetric[]> {
  const metrics: PerformanceMetric[] = []

  try {
    // Get recent cron job execution logs
    const { data: cronLogs } = await supabaseClient
      .from('cron_job_logs')
      .select('job_name, status, execution_duration_ms')
      .gte('execution_time', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .order('execution_time', { ascending: false })
      .limit(100)

    if (cronLogs && cronLogs.length > 0) {
      // Calculate average execution time per function
      const functionStats = cronLogs.reduce((acc, log) => {
        if (!acc[log.job_name]) {
          acc[log.job_name] = { total: 0, count: 0, failures: 0 }
        }
        acc[log.job_name].total += log.execution_duration_ms || 0
        acc[log.job_name].count++
        if (log.status === 'failed') {
          acc[log.job_name].failures++
        }
        return acc
      }, {} as any)

      for (const [functionName, stats] of Object.entries(functionStats as any)) {
        const avgDuration = stats.count > 0 ? stats.total / stats.count : 0
        const failureRate = stats.count > 0 ? (stats.failures / stats.count) * 100 : 0

        metrics.push(
          {
            type: 'edge_function',
            name: 'avg_execution_time',
            value: Math.round(avgDuration),
            unit: 'ms',
            tags: { function: functionName }
          },
          {
            type: 'edge_function',
            name: 'failure_rate',
            value: Math.round(failureRate * 100) / 100,
            unit: 'percent',
            tags: { function: functionName }
          }
        )
      }
    }

  } catch (error) {
    console.error('PerformanceMonitor.collectFunctionMetrics.error', error)
  }

  return metrics
}

// Store metrics in database
async function storeMetrics(supabaseClient: any, metrics: PerformanceMetric[]): Promise<number> {
  let storedCount = 0

  for (const metric of metrics) {
    try {
      await supabaseClient.rpc('record_performance_metric', {
        p_metric_type: metric.type,
        p_metric_name: metric.name,
        p_metric_value: metric.value,
        p_metric_unit: metric.unit,
        p_tags: metric.tags || {},
        p_source: 'performance_monitor'
      })
      storedCount++
    } catch (error) {
      console.error('PerformanceMonitor.storeMetric.error', { metric: metric.name, error })
    }
  }

  return storedCount
}

// Check thresholds and trigger alerts
async function checkThresholdsAndAlert(supabaseClient: any, metrics: PerformanceMetric[]): Promise<any[]> {
  const alerts: any[] = []
  
  // Define performance thresholds
  const thresholds: PerformanceThreshold[] = [
    { metricName: 'pending_jobs', warningThreshold: 20, criticalThreshold: 50, operator: 'gt' },
    { metricName: 'failed_jobs', warningThreshold: 5, criticalThreshold: 15, operator: 'gt' },
    { metricName: 'avg_retry_count', warningThreshold: 1.5, criticalThreshold: 3, operator: 'gt' },
    { metricName: 'success_rate', warningThreshold: 90, criticalThreshold: 80, operator: 'lt' },
    { metricName: 'avg_execution_time', warningThreshold: 30000, criticalThreshold: 60000, operator: 'gt' },
    { metricName: 'memory_used', warningThreshold: 500, criticalThreshold: 1000, operator: 'gt' }
  ]

  for (const threshold of thresholds) {
    const metric = metrics.find(m => m.name === threshold.metricName)
    if (!metric) continue

    const breached = checkThreshold(metric.value, threshold)
    if (breached) {
      const alert = await triggerAlert(supabaseClient, metric, threshold, breached)
      if (alert) alerts.push(alert)
    }
  }

  return alerts
}

// Check if a metric breaches its threshold
function checkThreshold(value: number, threshold: PerformanceThreshold): 'warning' | 'critical' | null {
  switch (threshold.operator) {
    case 'gt':
      if (value > threshold.criticalThreshold) return 'critical'
      if (value > threshold.warningThreshold) return 'warning'
      break
    case 'lt':
      if (value < threshold.criticalThreshold) return 'critical'
      if (value < threshold.warningThreshold) return 'warning'
      break
    case 'eq':
      if (value === threshold.criticalThreshold) return 'critical'
      if (value === threshold.warningThreshold) return 'warning'
      break
  }
  return null
}

// Trigger an alert
async function triggerAlert(
  supabaseClient: any, 
  metric: PerformanceMetric, 
  threshold: PerformanceThreshold, 
  severity: 'warning' | 'critical'
): Promise<any> {
  try {
    const alertPayload = {
      alertType: 'performance_degradation',
      severity: severity === 'critical' ? 'critical' : 'medium',
      title: `Performance Alert: ${metric.name}`,
      message: `Metric ${metric.name} is ${metric.value}${metric.unit || ''}, exceeding ${severity} threshold of ${severity === 'critical' ? threshold.criticalThreshold : threshold.warningThreshold}${metric.unit || ''}`,
      details: {
        metricName: metric.name,
        metricValue: metric.value,
        metricUnit: metric.unit,
        threshold: severity === 'critical' ? threshold.criticalThreshold : threshold.warningThreshold,
        operator: threshold.operator
      },
      source: 'performance_monitor'
    }

    // Call the system-alerting function
    const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/system-alerting`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(alertPayload)
    })

    if (response.ok) {
      const result = await response.json()
      return result
    } else {
      console.error('PerformanceMonitor.triggerAlert.httpError', { status: response.status })
      return null
    }
  } catch (error) {
    console.error('PerformanceMonitor.triggerAlert.error', error)
    return null
  }
}

// Determine overall system health
function determineSystemHealth(metrics: PerformanceMetric[], alerts: any[]): 'healthy' | 'warning' | 'critical' {
  const criticalAlerts = alerts.filter(a => a.severity === 'critical')
  const warningAlerts = alerts.filter(a => a.severity === 'medium' || a.severity === 'high')

  if (criticalAlerts.length > 0) return 'critical'
  if (warningAlerts.length > 2) return 'warning'
  return 'healthy'
}
