// Enhanced logging and error handling system for scheduled operations

export interface LogEntry {
  operation_type: string
  user_id?: string
  program_id?: string
  job_id?: string
  operation_data: any
  execution_status: 'success' | 'error' | 'warning' | 'info'
  error_details?: any
  execution_time_ms?: number
  retry_count?: number
  created_at?: string
}

export interface ErrorContext {
  operation: string
  userId?: string
  programId?: string
  jobId?: string
  additionalData?: any
}

export interface RetryConfig {
  maxRetries: number
  retryDelayMs: number
  exponentialBackoff: boolean
}

// Enhanced logger class for structured logging
export class EnhancedLogger {
  private supabaseClient: any
  private context: string

  constructor(supabaseClient: any, context: string) {
    this.supabaseClient = supabaseClient
    this.context = context
  }

  // Log successful operation
  async logSuccess(
    operation: string, 
    data: any, 
    executionTime?: number,
    userId?: string,
    programId?: string
  ): Promise<void> {
    await this.log({
      operation_type: `${this.context}.${operation}`,
      user_id: userId,
      program_id: programId,
      operation_data: data,
      execution_status: 'success',
      execution_time_ms: executionTime
    })

    console.log(`${this.context}.${operation}.success`, {
      userId,
      programId,
      executionTime,
      data
    })
  }

  // Log error with context
  async logError(
    operation: string,
    error: Error | string,
    context?: ErrorContext,
    executionTime?: number
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error
    const errorStack = error instanceof Error ? error.stack : undefined

    await this.log({
      operation_type: `${this.context}.${operation}`,
      user_id: context?.userId,
      program_id: context?.programId,
      job_id: context?.jobId,
      operation_data: context?.additionalData || {},
      execution_status: 'error',
      error_details: {
        message: errorMessage,
        stack: errorStack,
        context: context
      },
      execution_time_ms: executionTime
    })

    console.error(`${this.context}.${operation}.error`, {
      error: errorMessage,
      context,
      executionTime
    })
  }

  // Log warning
  async logWarning(
    operation: string,
    message: string,
    data?: any,
    userId?: string,
    programId?: string
  ): Promise<void> {
    await this.log({
      operation_type: `${this.context}.${operation}`,
      user_id: userId,
      program_id: programId,
      operation_data: data || {},
      execution_status: 'warning',
      error_details: { warning: message }
    })

    console.warn(`${this.context}.${operation}.warning`, {
      message,
      userId,
      programId,
      data
    })
  }

  // Log info
  async logInfo(
    operation: string,
    message: string,
    data?: any,
    userId?: string,
    programId?: string
  ): Promise<void> {
    await this.log({
      operation_type: `${this.context}.${operation}`,
      user_id: userId,
      program_id: programId,
      operation_data: data || {},
      execution_status: 'info',
      error_details: { info: message }
    })

    console.log(`${this.context}.${operation}.info`, {
      message,
      userId,
      programId,
      data
    })
  }

  // Internal log method
  private async log(entry: LogEntry): Promise<void> {
    try {
      await this.supabaseClient
        .from('operation_logs')
        .insert({
          ...entry,
          created_at: entry.created_at || new Date().toISOString()
        })
    } catch (error) {
      console.error('EnhancedLogger.log.failed', {
        error: error.message,
        entry
      })
      // Don't throw - logging failures shouldn't break operations
    }
  }
}

// Job status manager for scheduled_generation_jobs
export class JobStatusManager {
  private supabaseClient: any
  private logger: EnhancedLogger

  constructor(supabaseClient: any, logger: EnhancedLogger) {
    this.supabaseClient = supabaseClient
    this.logger = logger
  }

  // Start job execution
  async startJob(jobId: string): Promise<boolean> {
    try {
      const { error } = await this.supabaseClient
        .rpc('update_job_status', {
          p_job_id: jobId,
          p_status: 'running'
        })

      if (error) {
        await this.logger.logError('startJob', error, { jobId })
        return false
      }

      await this.logger.logInfo('startJob', 'Job started successfully', { jobId })
      return true

    } catch (error) {
      await this.logger.logError('startJob', error, { jobId })
      return false
    }
  }

  // Complete job successfully
  async completeJob(jobId: string, resultData?: any): Promise<boolean> {
    try {
      const { error } = await this.supabaseClient
        .rpc('update_job_status', {
          p_job_id: jobId,
          p_status: 'completed'
        })

      if (error) {
        await this.logger.logError('completeJob', error, { jobId })
        return false
      }

      await this.logger.logSuccess('completeJob', { jobId, resultData })
      return true

    } catch (error) {
      await this.logger.logError('completeJob', error, { jobId })
      return false
    }
  }

  // Fail job with error details
  async failJob(jobId: string, error: Error | string): Promise<boolean> {
    try {
      const errorMessage = error instanceof Error ? error.message : error

      const { error: updateError } = await this.supabaseClient
        .rpc('update_job_status', {
          p_job_id: jobId,
          p_status: 'failed',
          p_error_message: errorMessage
        })

      if (updateError) {
        await this.logger.logError('failJob', updateError, { jobId })
        return false
      }

      await this.logger.logError('failJob', error, { jobId })
      return true

    } catch (updateError) {
      await this.logger.logError('failJob', updateError, { jobId })
      return false
    }
  }

  // Check if job should be retried
  async shouldRetryJob(jobId: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabaseClient
        .from('scheduled_generation_jobs')
        .select('retry_count, max_retries')
        .eq('id', jobId)
        .single()

      if (error || !data) {
        await this.logger.logError('shouldRetryJob', error || 'Job not found', { jobId })
        return false
      }

      const shouldRetry = data.retry_count < data.max_retries
      await this.logger.logInfo('shouldRetryJob', `Retry check: ${shouldRetry}`, {
        jobId,
        retryCount: data.retry_count,
        maxRetries: data.max_retries
      })

      return shouldRetry

    } catch (error) {
      await this.logger.logError('shouldRetryJob', error, { jobId })
      return false
    }
  }
}

// Retry mechanism with exponential backoff
export class RetryManager {
  private logger: EnhancedLogger

  constructor(logger: EnhancedLogger) {
    this.logger = logger
  }

  // Execute operation with retry logic
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig,
    context: ErrorContext
  ): Promise<T> {
    let lastError: Error | null = null
    let attempt = 0

    while (attempt <= config.maxRetries) {
      try {
        const startTime = Date.now()
        const result = await operation()
        const executionTime = Date.now() - startTime

        if (attempt > 0) {
          await this.logger.logSuccess(
            'retrySuccess',
            { attempt, executionTime },
            executionTime,
            context.userId,
            context.programId
          )
        }

        return result

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        attempt++

        await this.logger.logWarning(
          'retryAttempt',
          `Attempt ${attempt} failed: ${lastError.message}`,
          { attempt, maxRetries: config.maxRetries },
          context.userId,
          context.programId
        )

        if (attempt <= config.maxRetries) {
          const delay = config.exponentialBackoff 
            ? config.retryDelayMs * Math.pow(2, attempt - 1)
            : config.retryDelayMs

          await this.sleep(delay)
        }
      }
    }

    // All retries exhausted
    await this.logger.logError(
      'retryExhausted',
      lastError || new Error('Unknown error'),
      { ...context, totalAttempts: attempt }
    )

    throw lastError || new Error('Operation failed after all retries')
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Performance monitor for tracking execution times
export class PerformanceMonitor {
  private logger: EnhancedLogger
  private startTimes: Map<string, number> = new Map()

  constructor(logger: EnhancedLogger) {
    this.logger = logger
  }

  // Start timing an operation
  startTiming(operationId: string): void {
    this.startTimes.set(operationId, Date.now())
  }

  // End timing and log performance
  async endTiming(
    operationId: string,
    operation: string,
    userId?: string,
    programId?: string,
    additionalData?: any
  ): Promise<number> {
    const startTime = this.startTimes.get(operationId)
    if (!startTime) {
      await this.logger.logWarning('endTiming', 'No start time found for operation', {
        operationId,
        operation
      })
      return 0
    }

    const executionTime = Date.now() - startTime
    this.startTimes.delete(operationId)

    // Log performance metrics
    await this.logger.logInfo(
      'performance',
      `Operation completed in ${executionTime}ms`,
      {
        operation,
        executionTime,
        ...additionalData
      },
      userId,
      programId
    )

    // Log warning for slow operations (> 10 seconds)
    if (executionTime > 10000) {
      await this.logger.logWarning(
        'slowOperation',
        `Operation took ${executionTime}ms (> 10s)`,
        {
          operation,
          executionTime,
          ...additionalData
        },
        userId,
        programId
      )
    }

    return executionTime
  }
}

// Error aggregator for batch operations
export class ErrorAggregator {
  private errors: Array<{ context: ErrorContext; error: Error | string }> = []
  private logger: EnhancedLogger

  constructor(logger: EnhancedLogger) {
    this.logger = logger
  }

  // Add error to aggregator
  addError(error: Error | string, context: ErrorContext): void {
    this.errors.push({ error, context })
  }

  // Get all errors
  getErrors(): Array<{ context: ErrorContext; error: Error | string }> {
    return [...this.errors]
  }

  // Get error count
  getErrorCount(): number {
    return this.errors.length
  }

  // Log all aggregated errors
  async logAllErrors(): Promise<void> {
    for (const { error, context } of this.errors) {
      await this.logger.logError(
        'batchError',
        error,
        context
      )
    }
  }

  // Clear all errors
  clear(): void {
    this.errors = []
  }
}
