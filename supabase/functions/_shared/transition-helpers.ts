// Shared transition helper functions for program lifecycle management

export interface TransitionResult {
  success: boolean
  transitionId?: string
  error?: string
  data?: any
}

export interface ProgramTransitionData {
  userId: string
  fromProgramId?: string
  toProgramId: string
  transitionType: 'automatic' | 'manual' | 'coach_initiated'
  transitionDate?: string
  forceTransition?: boolean
}

// Execute immediate program transition
export async function executeImmediateTransition(
  supabaseClient: any,
  transitionData: ProgramTransitionData
): Promise<TransitionResult> {
  try {
    console.log('TransitionHelper.executeImmediate.start', transitionData)

    // Start transaction-like operations
    const transitionDate = transitionData.transitionDate || new Date().toISOString().split('T')[0]

    // 1. Complete current active program
    const completeResult = await completeCurrentProgram(supabaseClient, transitionData.userId)
    if (!completeResult.success && !transitionData.forceTransition) {
      return { success: false, error: `Failed to complete current program: ${completeResult.error}` }
    }

    // 2. Activate new program
    const activateResult = await activateNewProgram(supabaseClient, transitionData.toProgramId)
    if (!activateResult.success) {
      return { success: false, error: `Failed to activate new program: ${activateResult.error}` }
    }

    // 3. Record transition
    const recordResult = await recordTransition(supabaseClient, {
      user_id: transitionData.userId,
      from_program_id: transitionData.fromProgramId || completeResult.data?.programId,
      to_program_id: transitionData.toProgramId,
      transition_date: transitionDate,
      transition_type: transitionData.transitionType,
      transition_status: 'completed'
    })

    // 4. Log operation
    await logTransitionOperation(supabaseClient, {
      operation_type: 'immediate_transition',
      user_id: transitionData.userId,
      program_id: transitionData.toProgramId,
      operation_data: {
        from_program_id: transitionData.fromProgramId,
        transition_type: transitionData.transitionType,
        transition_date: transitionDate
      },
      execution_status: 'success'
    })

    console.log('TransitionHelper.executeImmediate.success', {
      userId: transitionData.userId,
      transitionId: recordResult.data?.id,
      newProgramId: transitionData.toProgramId
    })

    return {
      success: true,
      transitionId: recordResult.data?.id,
      data: {
        userId: transitionData.userId,
        fromProgramId: completeResult.data?.programId,
        toProgramId: transitionData.toProgramId,
        transitionDate
      }
    }

  } catch (error) {
    console.error('TransitionHelper.executeImmediate.error', error)
    return { success: false, error: error.message }
  }
}

// Schedule future program transition
export async function scheduleTransition(
  supabaseClient: any,
  transitionData: ProgramTransitionData
): Promise<TransitionResult> {
  try {
    console.log('TransitionHelper.schedule.start', transitionData)

    const transitionDate = transitionData.transitionDate || calculateNextTransitionDate(supabaseClient, transitionData.userId)

    // Record scheduled transition
    const recordResult = await recordTransition(supabaseClient, {
      user_id: transitionData.userId,
      from_program_id: transitionData.fromProgramId,
      to_program_id: transitionData.toProgramId,
      transition_date: transitionDate,
      transition_type: transitionData.transitionType,
      transition_status: 'scheduled'
    })

    if (!recordResult.success) {
      return { success: false, error: `Failed to record scheduled transition: ${recordResult.error}` }
    }

    // Schedule transition job
    const { error: jobError } = await supabaseClient
      .rpc('schedule_generation_job', {
        p_user_id: transitionData.userId,
        p_scheduled_date: transitionDate,
        p_job_type: 'transition',
        p_cycle_number: null // Will be determined at execution time
      })

    if (jobError) {
      console.error('TransitionHelper.schedule.jobError', jobError)
      // Don't fail the transition scheduling if job creation fails
    }

    console.log('TransitionHelper.schedule.success', {
      userId: transitionData.userId,
      transitionId: recordResult.data?.id,
      scheduledDate: transitionDate
    })

    return {
      success: true,
      transitionId: recordResult.data?.id,
      data: {
        userId: transitionData.userId,
        scheduledDate: transitionDate,
        toProgramId: transitionData.toProgramId
      }
    }

  } catch (error) {
    console.error('TransitionHelper.schedule.error', error)
    return { success: false, error: error.message }
  }
}

// Complete current active program for user
async function completeCurrentProgram(
  supabaseClient: any,
  userId: string
): Promise<TransitionResult> {
  try {
    const { data, error } = await supabaseClient
      .from('workout_programs')
      .update({ 
        cycle_status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('cycle_status', 'active')
      .select('id')
      .single()

    if (error) {
      console.error('TransitionHelper.completeProgram.error', error)
      return { success: false, error: error.message }
    }

    return { 
      success: true, 
      data: { programId: data?.id } 
    }

  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Activate new program
async function activateNewProgram(
  supabaseClient: any,
  programId: string
): Promise<TransitionResult> {
  try {
    const today = new Date().toISOString().split('T')[0]

    const { error } = await supabaseClient
      .from('workout_programs')
      .update({
        cycle_status: 'active',
        status: 'active_by_client',
        client_start_date: today,
        cycle_start_date: today,
        updated_at: new Date().toISOString()
      })
      .eq('id', programId)

    if (error) {
      console.error('TransitionHelper.activateProgram.error', error)
      return { success: false, error: error.message }
    }

    return { success: true }

  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Record transition in database
async function recordTransition(
  supabaseClient: any,
  transitionData: {
    user_id: string
    from_program_id?: string
    to_program_id: string
    transition_date: string
    transition_type: string
    transition_status: string
  }
): Promise<TransitionResult> {
  try {
    const { data, error } = await supabaseClient
      .from('program_transitions')
      .insert({
        user_id: transitionData.user_id,
        from_program_id: transitionData.from_program_id,
        to_program_id: transitionData.to_program_id,
        transition_date: transitionData.transition_date,
        transition_type: transitionData.transition_type,
        transition_status: transitionData.transition_status,
        created_at: new Date().toISOString(),
        completed_at: transitionData.transition_status === 'completed' ? new Date().toISOString() : null
      })
      .select('id')
      .single()

    if (error) {
      console.error('TransitionHelper.recordTransition.error', error)
      return { success: false, error: error.message }
    }

    return { 
      success: true, 
      data: { id: data.id } 
    }

  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Log transition operation
async function logTransitionOperation(
  supabaseClient: any,
  logData: {
    operation_type: string
    user_id: string
    program_id: string
    operation_data: any
    execution_status: string
  }
): Promise<void> {
  try {
    await supabaseClient
      .from('operation_logs')
      .insert({
        operation_type: logData.operation_type,
        user_id: logData.user_id,
        program_id: logData.program_id,
        operation_data: logData.operation_data,
        execution_status: logData.execution_status,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('TransitionHelper.logOperation.error', error)
    // Don't throw - logging failures shouldn't break transitions
  }
}

// Calculate next transition date (28 days from current cycle start)
async function calculateNextTransitionDate(
  supabaseClient: any,
  userId: string
): Promise<string> {
  try {
    const { data } = await supabaseClient
      .from('workout_programs')
      .select('cycle_start_date, created_at')
      .eq('user_id', userId)
      .eq('cycle_status', 'active')
      .single()

    if (data) {
      const cycleStart = new Date(data.cycle_start_date || data.created_at)
      const transitionDate = new Date(cycleStart.getTime() + 28 * 24 * 60 * 60 * 1000)
      return transitionDate.toISOString().split('T')[0]
    }

    // Fallback to 28 days from today
    const fallbackDate = new Date(Date.now() + 28 * 24 * 60 * 60 * 1000)
    return fallbackDate.toISOString().split('T')[0]

  } catch (error) {
    console.error('TransitionHelper.calculateDate.error', error)
    // Fallback to 28 days from today
    const fallbackDate = new Date(Date.now() + 28 * 24 * 60 * 60 * 1000)
    return fallbackDate.toISOString().split('T')[0]
  }
}

// Check if user has pending transitions
export async function hasPendingTransitions(
  supabaseClient: any,
  userId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabaseClient
      .from('program_transitions')
      .select('id')
      .eq('user_id', userId)
      .eq('transition_status', 'scheduled')
      .limit(1)

    if (error) {
      console.error('TransitionHelper.checkPending.error', error)
      return false
    }

    return data && data.length > 0

  } catch (error) {
    console.error('TransitionHelper.checkPending.exception', error)
    return false
  }
}

// Get user's transition history
export async function getUserTransitionHistory(
  supabaseClient: any,
  userId: string,
  limit: number = 10
): Promise<any[]> {
  try {
    const { data, error } = await supabaseClient
      .from('program_transitions')
      .select(`
        *,
        from_program:workout_programs!program_transitions_from_program_id_fkey(name, cycle_number),
        to_program:workout_programs!program_transitions_to_program_id_fkey(name, cycle_number)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('TransitionHelper.getHistory.error', error)
      return []
    }

    return data || []

  } catch (error) {
    console.error('TransitionHelper.getHistory.exception', error)
    return []
  }
}
