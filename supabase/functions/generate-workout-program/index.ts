import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface IntakeData {
  id: string
  intake_gender: 'Woman' | 'Man'
  age: number
  height_cm: number
  weight_kg: number
  primary_fitness_goal: string[]
  training_experience_level: 'Beginner' | 'Intermediate' | 'Advanced'
  goal_timeline_months?: number
  equipment_access_type: 'Full Gym' | 'Home Gym Basic' | 'Bodyweight Only'
  available_equipment?: Record<string, any>
  custom_equipment_notes?: string
  training_days_per_week: number
  preferred_training_days?: string[]
  preferred_session_duration_minutes: number
  injuries_limitations?: string
  training_preferences_notes?: string
  has_specific_event?: boolean
  intake_status: string
}

interface Exercise {
  id: string
  name: string
  description: string
  target_muscles_primary: string[]
  target_muscles_secondary: string[]
  equipment_required: string[]
  difficulty_level: string
}

interface WorkoutProgram {
  id: string
  user_id: string
  name: string
  description: string
  status: 'ai_generated_pending_review' | 'scheduled_pending_review' | 'coach_edited' | 'coach_approved' | 'auto_approved' | 'active_by_client' | 'completed_by_client' | 'archived'
  generated_by_ai_at: string
  created_at: string
  cycle_number?: number
  cycle_start_date?: string
  cycle_status?: 'active' | 'pending_transition' | 'completed' | 'archived'
  generation_type?: 'initial' | 'scheduled' | 'manual'
  previous_program_id?: string
  review_deadline_date?: string
}

interface GenerationRequest {
  userId?: string
  generationType?: 'initial' | 'scheduled' | 'manual'
  cycleNumber?: number
  previousProgramId?: string
  coachNotes?: string
  isRegeneration?: boolean
  // Legacy webhook support
  record?: { id: string }
  type?: string
  table?: string
}

interface GenerationResponse {
  success: boolean
  programId?: string
  message?: string
  cycleNumber?: number
  reviewDeadline?: string
  generationType?: string
  error?: string
}

interface AIWorkoutProgram {
  program: {
    name: string
    description: string
    weeks: Array<{
      week_number: number
      notes: string
      workouts: Array<{
        day_identifier: string
        title: string
        description: string
        estimated_duration_minutes: number
        order_in_week: number
        exercises: Array<{
          exercise_name: string
          order_in_workout: number
          prescribed_sets: number
          prescribed_reps_min?: number
          prescribed_reps_max?: number
          prescribed_duration_seconds?: number
          prescribed_rir?: number
          prescribed_rpe?: number
          prescribed_tempo?: string
          rest_period_seconds_after_set: number
          notes?: string
        }>
      }>
    }>
  }
}

// Helper function to acquire advisory lock with retry mechanism
async function acquireLockWithRetry(supabaseClient: any, lockId: number, userId: string): Promise<boolean> {
  const maxAttempts = 3
  const retryDelays = [2000, 4000, 4000] // 2s, 4s, 4s intervals

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    console.log(`GenerateProgram.lockAttempt ${userId}`, {
      attempt,
      maxAttempts,
      lockId,
      timestamp: new Date().toISOString()
    })

    try {
      const { data: lockResult, error: lockError } = await supabaseClient
        .rpc('pg_try_advisory_lock', { key: lockId })

      if (lockError) {
        console.error(`GenerateProgram.lockError ${userId}`, {
          attempt,
          lockId,
          error: lockError,
          timestamp: new Date().toISOString()
        })

        // If this is the last attempt, don't retry
        if (attempt === maxAttempts) {
          throw new Error(`Failed to acquire lock: ${lockError.message}`)
        }

        // Wait before retrying
        const delay = retryDelays[attempt - 1]
        console.log(`GenerateProgram.lockRetryWait ${userId}`, {
          attempt,
          delay,
          nextAttempt: attempt + 1,
          timestamp: new Date().toISOString()
        })
        await new Promise(resolve => setTimeout(resolve, delay))
        continue
      }

      if (lockResult) {
        console.log(`GenerateProgram.lockAcquired ${userId}`, {
          attempt,
          lockId,
          timestamp: new Date().toISOString()
        })
        return true
      } else {
        console.log(`GenerateProgram.lockBusy ${userId}`, {
          attempt,
          lockId,
          message: 'Lock is busy, will retry if attempts remaining',
          timestamp: new Date().toISOString()
        })

        // If this is the last attempt, return false
        if (attempt === maxAttempts) {
          console.log(`GenerateProgram.lockFailedAllAttempts ${userId}`, {
            maxAttempts,
            lockId,
            timestamp: new Date().toISOString()
          })
          return false
        }

        // Wait before retrying
        const delay = retryDelays[attempt - 1]
        console.log(`GenerateProgram.lockRetryWait ${userId}`, {
          attempt,
          delay,
          nextAttempt: attempt + 1,
          timestamp: new Date().toISOString()
        })
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    } catch (error) {
      console.error(`GenerateProgram.lockException ${userId}`, {
        attempt,
        lockId,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      })

      // If this is the last attempt, throw the error
      if (attempt === maxAttempts) {
        throw error
      }

      // Wait before retrying
      const delay = retryDelays[attempt - 1]
      console.log(`GenerateProgram.lockRetryWait ${userId}`, {
        attempt,
        delay,
        nextAttempt: attempt + 1,
        timestamp: new Date().toISOString()
      })
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  return false
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Declare variables outside try block for error handling access
  let supabaseClient: any
  let userId: string = ''
  let generationType: 'initial' | 'scheduled' | 'manual' = 'initial'
  let cycleNumber: number | undefined
  let previousProgramId: string | undefined

  try {
    supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const requestBody: GenerationRequest = await req.json()
    console.log('GenerateProgram.requestReceived', { body: requestBody })

    // Handle different payload formats
    if (requestBody.record && requestBody.record.id) {
      // Legacy webhook payload format: { type: "UPDATE", table: "profiles", record: { id: "...", ... }, old_record: { ... } }
      userId = requestBody.record.id
      generationType = 'initial' // Legacy webhooks are always initial generation
      console.log('GenerateProgram.webhookPayload', { userId, type: requestBody.type, table: requestBody.table })
    } else if (requestBody.userId) {
      // Enhanced API call format: { userId: "...", generationType: "...", cycleNumber: ... }
      userId = requestBody.userId
      generationType = requestBody.generationType || 'initial'
      cycleNumber = requestBody.cycleNumber
      previousProgramId = requestBody.previousProgramId
      console.log('GenerateProgram.enhancedCall', {
        userId,
        generationType,
        cycleNumber,
        previousProgramId
      })
    } else {
      console.error('GenerateProgram.invalidPayload', { requestBody })
      throw new Error('User ID is required. Expected either { userId: "..." } or webhook payload with record.id')
    }

    console.log(`GenerateProgram.start ${userId}`, {
      generationType,
      cycleNumber,
      previousProgramId,
      timestamp: new Date().toISOString()
    })

    // Use advisory lock to prevent race conditions from multiple simultaneous calls
    const lockId = parseInt(userId.replace(/-/g, '').substring(0, 8), 16) // Convert UUID to integer for lock
    console.log(`GenerateProgram.acquiringLock ${userId}`, {
      lockId,
      lockIdHex: lockId.toString(16),
      timestamp: new Date().toISOString()
    })

    // Attempt to acquire lock with retry mechanism
    const lockAcquired = await acquireLockWithRetry(supabaseClient, lockId, userId)

    if (!lockAcquired) {
      console.log(`GenerateProgram.lockBusyAfterRetries ${userId}`, {
        lockId,
        message: 'Unable to acquire lock after 3 attempts - another process may be generating a program',
        timestamp: new Date().toISOString()
      })
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Unable to acquire processing lock after retries',
          message: 'Program generation is busy, please try again later',
          userId,
          lockId,
          timestamp: new Date().toISOString()
        }),
        {
          status: 429, // Too Many Requests - appropriate for lock contention
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log(`GenerateProgram.lockAcquired ${userId}`, {
      lockId,
      timestamp: new Date().toISOString()
    })

    try {
      // Fetch user's intake data
      const { data: intakeData, error: intakeError } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (intakeError) {
        console.error('GenerateProgram.fetchIntake.failure', intakeError)
        throw new Error(`Failed to fetch intake data: ${intakeError.message}`)
      }

      if (!intakeData || intakeData.intake_status !== 'completed') {
        console.log('GenerateProgram.intakeNotCompleted', { userId, status: intakeData?.intake_status })
        throw new Error('Intake not completed')
      }

      // Enhanced duplicate prevention based on generation type
      const eligibilityCheck = await checkGenerationEligibility(
        supabaseClient,
        userId,
        generationType,
        cycleNumber
      )

      if (!eligibilityCheck.eligible) {
        console.log(`GenerateProgram.notEligible ${userId}`, {
          reason: eligibilityCheck.reason,
          data: eligibilityCheck.data,
          generationType,
          cycleNumber,
          timestamp: new Date().toISOString()
        })

        return new Response(
          JSON.stringify({
            success: true,
            message: eligibilityCheck.reason,
            userId,
            generationType,
            timestamp: new Date().toISOString(),
            ...eligibilityCheck.data
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      console.log(`GenerateProgram.eligible ${userId}`, {
        generationType,
        cycleNumber,
        eligibilityCheck
      })

      // Check for existing in-progress generation to ensure sequential processing
      const { data: existingTracking } = await supabaseClient
        .from('generation_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'in_progress')
        .order('started_at', { ascending: false })
        .limit(1)
        .single()

      if (existingTracking) {
        const startedAt = new Date(existingTracking.started_at)
        const minutesElapsed = (Date.now() - startedAt.getTime()) / (1000 * 60)

        if (minutesElapsed < 10) {
          // Generation still active - return success to prevent webhook retry
          console.log(`GenerateProgram.generationInProgress ${userId}`, {
            trackingId: existingTracking.id,
            minutesElapsed,
            generationType: existingTracking.generation_type
          })

          const response = {
            success: true,
            message: 'Generation already in progress',
            trackingId: existingTracking.id,
            userId,
            generationType,
            timestamp: new Date().toISOString()
          }

          return new Response(
            JSON.stringify(response),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        } else {
          // Mark stale record as failed
          console.log(`GenerateProgram.cleaningStaleRecord ${userId}`, {
            trackingId: existingTracking.id,
            minutesElapsed
          })

          await supabaseClient
            .from('generation_tracking')
            .update({
              status: 'failed',
              error_message: `Stale record after ${minutesElapsed} minutes`,
              completed_at: new Date().toISOString()
            })
            .eq('id', existingTracking.id)
        }
      }

      // Create a generation tracking record to prevent race conditions
      // This serves as an additional safeguard beyond the advisory lock
      const generationTrackingId = `${userId}-${generationType}-${Date.now()}`
      console.log(`GenerateProgram.creatingTrackingRecord ${userId}`, {
        generationTrackingId,
        timestamp: new Date().toISOString()
      })

      try {
        // Insert a temporary tracking record
        const { error: trackingError } = await supabaseClient
          .from('generation_tracking')
          .insert({
            id: generationTrackingId,
            user_id: userId,
            generation_type: generationType,
            cycle_number: cycleNumber,
            status: 'in_progress',
            started_at: new Date().toISOString()
          })

        if (trackingError) {
          console.error(`GenerateProgram.trackingRecordFailed ${userId}`, trackingError)
          throw new Error(`Failed to create tracking record: ${trackingError.message}`)
        }
      } catch (trackingException) {
        console.error(`GenerateProgram.trackingException ${userId}`, trackingException)
        const errorMessage = trackingException instanceof Error ? trackingException.message : String(trackingException)
        throw new Error(`Failed to create tracking record: ${errorMessage}`)
      }

    // Fetch available exercises
    const { data: exercises, error: exercisesError } = await supabaseClient
      .from('exercises')
      .select('*')

    if (exercisesError) {
      console.error('GenerateProgram.fetchExercises.failure', exercisesError)
      throw new Error(`Failed to fetch exercises: ${exercisesError.message}`)
    }

    // Filter exercises based on equipment access
    const availableExercises = filterExercisesByEquipment(exercises, intakeData)
    const exerciseNames = availableExercises.map(ex => ex.name)

    // Construct AI prompt
    const prompt = constructAIPrompt(intakeData, exerciseNames)
    console.log('GenerateProgram.OpenAI.request', { promptLength: prompt.length, exerciseCount: exerciseNames.length })

    // Call OpenAI API with retry logic and timeout
    let aiResult;
    let workoutProgram: AIWorkoutProgram | null = null;

    // Retry configuration
    const maxRetries = 2;
    let retryCount = 0;
    let lastError: Error | null = null;
    
    while (retryCount < maxRetries) {
      try {
        const attemptStartTime = Date.now();
        console.log(`GenerateProgram.OpenAI.attempt ${retryCount + 1} of ${maxRetries}`, {
          timestamp: new Date().toISOString(),
          promptLength: prompt.length,
          exerciseCount: exerciseNames.length
        });

        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 300000); // 300second timeout (5 minutes)
        
        try {
          // Call OpenAI API with timeout
          const openAIResponse = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'gpt-4.1-mini',
              messages: [
                {
                  role: 'system',
                  content: 'You are an expert fitness program designer. Generate a 4-week structured workout program based on the user\'s profile. Output should be in JSON format, strictly adhering to the provided schema. Use only exercises from the provided list. For each exercise, specify sets, reps (as a range like 8-10 or single value), RPE or RIR, and rest time in seconds. Structure workouts per day for each of the 4 weeks. IMPORTANT: Your response must be valid JSON only, with no additional text or formatting.'
                },
                {
                  role: 'user',
                  content: prompt
                }
              ],
              response_format: { type: 'json_object' },
              temperature: 0.5
            }),
            signal: controller.signal
          });
          
          // Clear timeout since request completed
          clearTimeout(timeoutId);
          
          if (!openAIResponse.ok) {
            const errorText = await openAIResponse.text();
            console.error(`GenerateProgram.OpenAI.failure attempt ${retryCount + 1}`, { 
              status: openAIResponse.status, 
              error: errorText 
            });
            throw new Error(`OpenAI API error: ${openAIResponse.status} ${errorText}`);
          }
          
          aiResult = await openAIResponse.json();
          const attemptElapsed = Date.now() - attemptStartTime;
          console.log('GenerateProgram.OpenAI.response', {
            status: 'success',
            attemptElapsed,
            attempt: retryCount + 1
          });
          
          // Parse AI response with proper error handling
          try {
            const aiContent = aiResult.choices[0].message.content;
            
            // Log a preview of the content for debugging
            console.log('GenerateProgram.AIContent.preview', { 
              contentLength: aiContent.length,
              preview: aiContent.substring(0, 100) + '...'
            });
            
            // Try to parse the JSON content
            workoutProgram = JSON.parse(aiContent);
            
            // Validate the parsed content has the expected structure
            if (!workoutProgram || !workoutProgram.program || !workoutProgram.program.weeks) {
              throw new Error('Invalid program structure in AI response');
            }
            
            console.log('GenerateProgram.ParseJSON.success', { 
              programName: workoutProgram.program.name,
              weeksCount: workoutProgram.program.weeks.length
            });
            
            // If we got here, we have a valid response, so break out of the retry loop
            break;
          } catch (parseError) {
            // Log the error and the raw content for debugging
            const errorMessage = parseError instanceof Error ? parseError.message : String(parseError)
            console.error(`GenerateProgram.ParseJSON.failure attempt ${retryCount + 1}`, {
              error: errorMessage,
              rawContentLength: aiResult.choices[0].message.content.length,
              rawContentPreview: aiResult.choices[0].message.content.substring(0, 200) + '...'
            });

            // Store the error to throw if all retries fail
            lastError = new Error(`Failed to parse AI response: ${errorMessage}`);
            
            // Increment retry count and continue loop
            retryCount++;
            
            // Add exponential backoff delay
            if (retryCount < maxRetries) {
              const delayMs = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
              console.log(`GenerateProgram.Retry.delay ${delayMs}ms before attempt ${retryCount + 1}`);
              await new Promise(resolve => setTimeout(resolve, delayMs));
            }
          }
        } catch (fetchError) {
          // Clear timeout in case of error
          clearTimeout(timeoutId);

          // Check if the error was due to timeout
          if (fetchError instanceof Error && fetchError.name === 'AbortError') {
            console.error(`GenerateProgram.OpenAI.timeout attempt ${retryCount + 1}`);
            lastError = new Error('OpenAI API request timed out after 300 seconds');
          } else {
            console.error(`GenerateProgram.OpenAI.fetchError attempt ${retryCount + 1}`, fetchError);
            lastError = fetchError instanceof Error ? fetchError : new Error(String(fetchError));
          }
          
          // Increment retry count and continue loop
          retryCount++;
          
          // Add exponential backoff delay
          if (retryCount < maxRetries) {
            const delayMs = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
            console.log(`GenerateProgram.Retry.delay ${delayMs}ms before attempt ${retryCount + 1}`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
          }
        }
      } catch (error) {
        console.error(`GenerateProgram.Retry.error attempt ${retryCount + 1}`, error);
        lastError = error instanceof Error ? error : new Error(String(error));
        retryCount++;
        
        // Add exponential backoff delay
        if (retryCount < maxRetries) {
          const delayMs = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
          console.log(`GenerateProgram.Retry.delay ${delayMs}ms before attempt ${retryCount + 1}`);
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      }
    }
    
    // If we've exhausted all retries and still don't have a valid response, throw the last error
    if (!workoutProgram) {
      console.error('GenerateProgram.AllRetriesFailed', { attempts: maxRetries });
      throw lastError || new Error('Failed to generate workout program after multiple attempts');
    }

      // Determine cycle information
      const finalCycleNumber = await determineCycleNumber(
        supabaseClient,
        userId,
        generationType,
        cycleNumber
      )

      const cycleInfo = {
        cycleNumber: finalCycleNumber,
        generationType,
        previousProgramId
      }

      // Add detailed OpenAI response logging for debugging
      console.log('GenerateProgram.OpenAI.fullResponse', {
        programName: workoutProgram.program.name,
        programDescription: workoutProgram.program.description,
        totalWeeks: workoutProgram.program.weeks.length,
        totalWorkouts: workoutProgram.program.weeks.reduce((total, week) => total + week.workouts.length, 0),
        totalExercises: workoutProgram.program.weeks.reduce((total, week) =>
          total + week.workouts.reduce((weekTotal, workout) => weekTotal + workout.exercises.length, 0), 0),
        sampleWeek: workoutProgram.program.weeks[0] ? {
          weekNumber: workoutProgram.program.weeks[0].week_number,
          workoutCount: workoutProgram.program.weeks[0].workouts.length,
          firstWorkout: workoutProgram.program.weeks[0].workouts[0] ? {
            title: workoutProgram.program.weeks[0].workouts[0].title,
            exerciseCount: workoutProgram.program.weeks[0].workouts[0].exercises.length
          } : null
        } : null
      })

      // Validate and store program in database with cycle information
      console.log(`GenerateProgram.DBStore.starting ${userId}`, {
        timestamp: new Date().toISOString(),
        programSize: {
          weeks: workoutProgram.program.weeks.length,
          totalWorkouts: workoutProgram.program.weeks.reduce((total, week) => total + week.workouts.length, 0),
          totalExercises: workoutProgram.program.weeks.reduce((total, week) =>
            total + week.workouts.reduce((weekTotal, workout) => weekTotal + workout.exercises.length, 0), 0)
        }
      })

      // Apply timeout to database storage operation (60 seconds with chunked inserts)
      const programId = await withTimeout(
        storeWorkoutProgram(
          supabaseClient,
          userId,
          workoutProgram,
          intakeData,
          availableExercises,
          cycleInfo
        ),
        60000,
        'Database storage operation'
      )

      // Record scheduled job completion if this was a scheduled generation
      if (generationType === 'scheduled') {
        await recordScheduledJobCompletion(supabaseClient, userId, finalCycleNumber)
      }

      console.log('GenerateProgram.DBStore.success', {
        programId,
        cycleNumber: finalCycleNumber,
        generationType,
        timestamp: new Date().toISOString()
      })

      const response: GenerationResponse = {
        success: true,
        programId,
        cycleNumber: finalCycleNumber,
        generationType,
        reviewDeadline: cycleInfo.generationType === 'scheduled'
          ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          : undefined
      }

      console.log(`GenerateProgram.success ${userId}`, {
        programId,
        cycleNumber: finalCycleNumber,
        generationType,
        programName: workoutProgram.program.name,
        totalWeeks: workoutProgram.program.weeks.length,
        timestamp: new Date().toISOString()
      })

      // Mark tracking record as completed only after successful database storage
      try {
        await supabaseClient
          .from('generation_tracking')
          .update({
            status: 'completed',
            completed_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('generation_type', generationType)
          .eq('status', 'in_progress')

        console.log(`GenerateProgram.trackingRecordCompleted ${userId}`)
      } catch (cleanupError) {
        console.warn(`GenerateProgram.trackingUpdateFailed ${userId}`, cleanupError)
      }

      return new Response(
        JSON.stringify(response),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )

    } finally {
      // Clean up any remaining in-progress tracking records (fallback)
      try {
        await supabaseClient
          .from('generation_tracking')
          .delete()
          .eq('user_id', userId)
          .eq('generation_type', generationType)
          .eq('status', 'in_progress')

        console.log(`GenerateProgram.trackingRecordCleaned ${userId}`)
      } catch (cleanupError) {
        console.warn(`GenerateProgram.trackingCleanupFailed ${userId}`, cleanupError)
      }

      // Always release the advisory lock
      console.log(`GenerateProgram.releasingLock ${userId}`, {
        lockId,
        timestamp: new Date().toISOString()
      })

      try {
        const { error: unlockError } = await supabaseClient.rpc('pg_advisory_unlock', { key: lockId })
        if (unlockError) {
          console.error(`GenerateProgram.unlockError ${userId}`, { lockId, error: unlockError })
        } else {
          console.log(`GenerateProgram.lockReleased ${userId}`, {
            lockId,
            timestamp: new Date().toISOString()
          })
        }
      } catch (unlockException) {
        console.error(`GenerateProgram.unlockException ${userId}`, {
          lockId,
          exception: unlockException,
          timestamp: new Date().toISOString()
        })
      }
    }

  } catch (error) {
    console.error('GenerateProgram.failure', error)

    // Update tracking record to mark as failed
    try {
      const errorMessage = error instanceof Error ? error.message : String(error)
      await supabaseClient
        .from('generation_tracking')
        .update({
          status: 'failed',
          error_message: errorMessage,
          completed_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('generation_type', generationType)
        .eq('status', 'in_progress')

      console.log(`GenerateProgram.trackingRecordMarkedFailed ${userId}`)
    } catch (trackingError) {
      console.warn(`GenerateProgram.failedTrackingUpdateFailed ${userId}`, trackingError)
    }

    const errorMessage = error instanceof Error ? error.message : String(error)
    return new Response(
      JSON.stringify({ success: false, error: errorMessage }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

function filterExercisesByEquipment(exercises: Exercise[], intakeData: IntakeData): Exercise[] {
  const { equipment_access_type, available_equipment } = intakeData

  return exercises.filter(exercise => {
    const requiredEquipment = exercise.equipment_required || []

    if (equipment_access_type === 'Bodyweight Only') {
      return requiredEquipment.length === 0 || 
             requiredEquipment.every(eq => ['Bodyweight', 'Yoga Mat'].includes(eq))
    }

    if (equipment_access_type === 'Home Gym Basic') {
      const userEquipment = available_equipment || {}
      const availableEquipmentTypes = [
        'Bodyweight',
        'Yoga Mat',
        ...(userEquipment.barbell_plates ? ['Barbell', 'Plates'] : []),
        ...(userEquipment.dumbbells ? ['Dumbbells'] : []),
        ...(userEquipment.resistance_bands ? ['Resistance Bands'] : []),
      ]

      return requiredEquipment.every(eq => availableEquipmentTypes.includes(eq))
    }

    // Full Gym - all exercises available
    return true
  })
}

function constructAIPrompt(intakeData: IntakeData, exerciseNames: string[]): string {
  const userProfile = {
    gender: intakeData.intake_gender,
    age: intakeData.age,
    height_cm: intakeData.height_cm,
    weight_kg: intakeData.weight_kg,
    goals: intakeData.primary_fitness_goal,
    experience: intakeData.training_experience_level,
    timeline_months: intakeData.goal_timeline_months,
    equipment_access: intakeData.equipment_access_type,
    training_days_per_week: intakeData.training_days_per_week,
    preferred_session_duration: intakeData.preferred_session_duration_minutes,
    injuries_limitations: intakeData.injuries_limitations,
    training_preferences: intakeData.training_preferences_notes,
    has_specific_event: intakeData.has_specific_event
  }

  return JSON.stringify({
    user_profile: userProfile,
    available_exercises: exerciseNames,
    requirements: {
      duration_weeks: 4,
      output_format: {
        program: {
          name: "string - descriptive program name",
          description: "string - program overview",
          weeks: [
            {
              week_number: "number - 1 to 4",
              notes: "string - week-specific notes",
              workouts: [
                {
                  day_identifier: "string - e.g., 'Day 1', 'Monday'",
                  title: "string - workout title",
                  description: "string - workout focus",
                  estimated_duration_minutes: "number - estimated time",
                  order_in_week: "number - sequence in week",
                  exercises: [
                    {
                      exercise_name: "string - must match available_exercises exactly",
                      order_in_workout: "number - sequence in workout",
                      prescribed_sets: "number - number of sets",
                      prescribed_reps_min: "number - minimum reps (optional)",
                      prescribed_reps_max: "number - maximum reps (optional)",
                      prescribed_duration_seconds: "number - for time-based exercises (optional)",
                      prescribed_rir: "number - reps in reserve 0-10 (optional)",
                      prescribed_rpe: "number - rate of perceived exertion 1-10 (optional)",
                      prescribed_tempo: "string - tempo notation like '2-1-2-1' (optional)",
                      rest_period_seconds_after_set: "number - rest time in seconds",
                      notes: "string - exercise-specific notes (optional)"
                    }
                  ]
                }
              ]
            }
          ]
        }
      }
    }
  })
}

async function storeWorkoutProgram(
  supabaseClient: any,
  userId: string,
  workoutProgram: AIWorkoutProgram,
  intakeData: IntakeData,
  availableExercises: Exercise[],
  cycleInfo: {
    cycleNumber: number
    generationType: 'initial' | 'scheduled' | 'manual'
    previousProgramId?: string
  }
): Promise<string> {
  const startTime = Date.now()
  console.log(`GenerateProgram.DBStore.starting ${userId}`, {
    timestamp: new Date().toISOString(),
    operation: 'storeWorkoutProgram'
  })

  const { program } = workoutProgram

  // Create exercise name to ID mapping
  const exerciseMap = new Map(availableExercises.map(ex => [ex.name, ex.id]))

  // Determine program status based on generation type
  const programStatus = cycleInfo.generationType === 'scheduled'
    ? 'scheduled_pending_review'
    : 'ai_generated_pending_review'

  // Calculate review deadline (7 days from now for scheduled programs)
  const reviewDeadline = cycleInfo.generationType === 'scheduled'
    ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    : null

  let programData: any = null

  try {
    // Step 1: Insert workout program with enhanced cycle information
    console.log(`GenerateProgram.DBStore.insertingProgram ${userId}`, {
      timestamp: new Date().toISOString(),
      programName: program.name
    })

    const { data: programResult, error: programError } = await supabaseClient
      .from('workout_programs')
      .insert({
        user_id: userId,
        name: program.name,
        description: program.description,
        duration_weeks: 4,
        status: programStatus,
        ai_prompt_data: intakeData,
        generated_by_ai_at: new Date().toISOString(),
        cycle_number: cycleInfo.cycleNumber,
        cycle_start_date: cycleInfo.generationType === 'initial' ? new Date().toISOString().split('T')[0] : null,
        cycle_status: cycleInfo.generationType === 'initial' ? 'active' : 'pending_transition',
        generation_type: cycleInfo.generationType,
        previous_program_id: cycleInfo.previousProgramId,
        review_deadline_date: reviewDeadline,
        auto_generated_at: cycleInfo.generationType === 'scheduled' ? new Date().toISOString() : null
      })
      .select()
      .single()

    if (programError) {
      console.error('GenerateProgram.DBStore.programFailure', programError)
      throw new Error(`Failed to store workout program: ${programError.message}`)
    }

    programData = programResult
    const programId = programData.id
    console.log(`GenerateProgram.DBStore.programCreated ${userId}`, {
      programId,
      elapsed: Date.now() - startTime
    })

    // Step 2: Bulk insert program weeks
    console.log(`GenerateProgram.DBStore.insertingWeeks ${userId}`, {
      weekCount: program.weeks.length,
      elapsed: Date.now() - startTime
    })

    const weekInserts = program.weeks.map(week => ({
      workout_program_id: programId,
      week_number: week.week_number,
      notes: week.notes
    }))

    const { data: weekData, error: weekError } = await supabaseClient
      .from('program_weeks')
      .insert(weekInserts)
      .select()

    if (weekError) {
      console.error('GenerateProgram.DBStore.weekFailure', weekError)
      throw new Error(`Failed to store program weeks: ${weekError.message}`)
    }

    console.log(`GenerateProgram.DBStore.weeksCreated ${userId}`, {
      weekCount: weekData.length,
      elapsed: Date.now() - startTime
    })

    // Step 3: Bulk insert workouts
    const workoutInserts: any[] = []
    const workoutToWeekMap = new Map()

    program.weeks.forEach((week, weekIndex) => {
      const weekId = weekData[weekIndex].id
      week.workouts.forEach((workout, workoutIndex) => {
        const workoutKey = `${weekIndex}-${workoutIndex}`
        workoutToWeekMap.set(workoutKey, { weekId, workout })
        workoutInserts.push({
          program_week_id: weekId,
          day_identifier: workout.day_identifier,
          title: workout.title,
          description: workout.description,
          estimated_duration_minutes: workout.estimated_duration_minutes,
          order_in_week: workout.order_in_week
        })
      })
    })

    console.log(`GenerateProgram.DBStore.insertingWorkouts ${userId}`, {
      workoutCount: workoutInserts.length,
      elapsed: Date.now() - startTime
    })

    const { data: workoutData, error: workoutError } = await supabaseClient
      .from('workouts')
      .insert(workoutInserts)
      .select()

    if (workoutError) {
      console.error('GenerateProgram.DBStore.workoutFailure', workoutError)
      throw new Error(`Failed to store workouts: ${workoutError.message}`)
    }

    console.log(`GenerateProgram.DBStore.workoutsCreated ${userId}`, {
      workoutCount: workoutData.length,
      elapsed: Date.now() - startTime
    })

    // Step 4: Bulk insert workout exercises
    const exerciseInserts: any[] = []
    let workoutDataIndex = 0

    program.weeks.forEach((week) => {
      week.workouts.forEach((workout) => {
        const workoutId = workoutData[workoutDataIndex].id
        workoutDataIndex++

        workout.exercises.forEach(exercise => {
          const exerciseId = exerciseMap.get(exercise.exercise_name)
          if (!exerciseId) {
            console.warn('GenerateProgram.ExerciseMapping.issue', {
              aiExerciseName: exercise.exercise_name,
              mappedTo: 'not_found'
            })
            throw new Error(`Exercise not found: ${exercise.exercise_name}`)
          }

          // Check if AI provided prescribed_sets, default to 1 if missing
          const prescribedSets = exercise.prescribed_sets || 1
          if (!exercise.prescribed_sets) {
            console.warn('GenerateProgram.MissingSets.defaultApplied', {
              exerciseName: exercise.exercise_name,
              workoutTitle: workout.title,
              defaultedTo: 1
            })
          }

          exerciseInserts.push({
            workout_id: workoutId,
            exercise_id: exerciseId,
            order_in_workout: exercise.order_in_workout,
            prescribed_sets: prescribedSets,
            prescribed_reps_min: exercise.prescribed_reps_min,
            prescribed_reps_max: exercise.prescribed_reps_max,
            prescribed_duration_seconds: exercise.prescribed_duration_seconds,
            prescribed_rir: exercise.prescribed_rir,
            prescribed_rpe: exercise.prescribed_rpe,
            prescribed_tempo: exercise.prescribed_tempo,
            rest_period_seconds_after_set: exercise.rest_period_seconds_after_set,
            notes: exercise.notes
          })
        })
      })
    })

    console.log(`GenerateProgram.DBStore.insertingExercises ${userId}`, {
      exerciseCount: exerciseInserts.length,
      elapsed: Date.now() - startTime
    })

    // Insert exercises in chunks to avoid timeout on large programs
    const chunkSize = 50 // Process 50 exercises at a time
    const chunks = []
    for (let i = 0; i < exerciseInserts.length; i += chunkSize) {
      chunks.push(exerciseInserts.slice(i, i + chunkSize))
    }

    console.log(`GenerateProgram.DBStore.exerciseChunks ${userId}`, {
      totalExercises: exerciseInserts.length,
      chunkCount: chunks.length,
      chunkSize
    })

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      const chunkStartTime = Date.now()
      const elapsed = chunkStartTime - startTime

      console.log(`GenerateProgram.DBStore.insertingChunk ${userId}`, {
        chunkIndex: i + 1,
        chunkSize: chunk.length,
        elapsed
      })

      // Check if we're approaching timeout
      if (elapsed > 50000) { // 50 seconds
        console.warn(`GenerateProgram.DBStore.approachingTimeout ${userId}`, {
          elapsed,
          remainingChunks: chunks.length - i,
          chunkIndex: i + 1
        })
      }

      const { error: exerciseError } = await supabaseClient
        .from('workout_exercises')
        .insert(chunk)

      if (exerciseError) {
        console.error('GenerateProgram.DBStore.exerciseChunkFailure', {
          chunkIndex: i + 1,
          error: exerciseError,
          chunkElapsed: Date.now() - chunkStartTime
        })
        throw new Error(`Failed to store workout exercises chunk ${i + 1}: ${exerciseError.message}`)
      }

      console.log(`GenerateProgram.DBStore.chunkComplete ${userId}`, {
        chunkIndex: i + 1,
        chunkElapsed: Date.now() - chunkStartTime,
        totalElapsed: Date.now() - startTime
      })
    }

    const totalElapsed = Date.now() - startTime
    console.log(`GenerateProgram.DBStore.allDataStored ${userId}`, {
      programId,
      totalElapsed,
      summary: {
        weeks: weekData.length,
        workouts: workoutData.length,
        exercises: exerciseInserts.length,
        exerciseChunks: chunks.length
      },
      performance: {
        averageTimePerExercise: Math.round(totalElapsed / exerciseInserts.length),
        timePerChunk: Math.round(totalElapsed / chunks.length)
      }
    })

    return programId

  } catch (error) {
    console.error(`GenerateProgram.DBStore.failure ${userId}`, {
      error: error instanceof Error ? error.message : String(error),
      elapsed: Date.now() - startTime
    })

    // If we have a programId, try to clean up the partially created program
    if (programData?.id) {
      try {
        console.log(`GenerateProgram.DBStore.cleanup ${userId}`, { programId: programData.id })
        await supabaseClient
          .from('workout_programs')
          .delete()
          .eq('id', programData.id)
      } catch (cleanupError) {
        console.error(`GenerateProgram.DBStore.cleanupFailed ${userId}`, cleanupError)
      }
    }

    throw error
  }
}

// Enhanced duplicate prevention for hybrid system
async function checkGenerationEligibility(
  supabaseClient: any,
  userId: string,
  generationType: 'initial' | 'scheduled' | 'manual',
  cycleNumber?: number
): Promise<{ eligible: boolean; reason: string; data?: any }> {

  console.log(`GenerateProgram.checkEligibility ${userId}`, { generationType, cycleNumber })

  // First, check for any programs created in the last 5 minutes (race condition prevention)
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString()

  const { data: recentPrograms, error: recentError } = await supabaseClient
    .from('workout_programs')
    .select('id, user_id, name, status, generated_by_ai_at, created_at, generation_type')
    .eq('user_id', userId)
    .gte('created_at', fiveMinutesAgo)
    .order('created_at', { ascending: false })

  if (recentError) {
    console.error('GenerateProgram.checkRecentPrograms.error', recentError)
    throw new Error(`Failed to check recent programs: ${recentError.message}`)
  }

  if (recentPrograms && recentPrograms.length > 0) {
    const recentProgram = recentPrograms[0]
    const createdAt = new Date(recentProgram.created_at)
    const now = new Date()
    const minutesSinceCreation = Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60))

    console.log(`GenerateProgram.recentProgramFound ${userId}`, {
      programId: recentProgram.id,
      minutesSinceCreation,
      createdAt: createdAt.toISOString(),
      generationType: recentProgram.generation_type
    })

    return {
      eligible: false,
      reason: 'Recent program already exists (race condition prevented)',
      data: {
        programId: recentProgram.id,
        minutesSinceCreation,
        createdAt: createdAt.toISOString(),
        existingGenerationType: recentProgram.generation_type,
        requestedGenerationType: generationType
      }
    }
  }

  // Check for existing active programs
  const { data: existingPrograms, error: programsError } = await supabaseClient
    .from('workout_programs')
    .select('id, user_id, name, description, status, generated_by_ai_at, created_at, cycle_number, cycle_status, generation_type')
    .eq('user_id', userId)
    .in('status', [
      'ai_generated_pending_review',
      'scheduled_pending_review',
      'coach_approved',
      'auto_approved',
      'active_by_client'
    ])
    .order('created_at', { ascending: false })
    .limit(1)

  if (programsError) {
    throw new Error(`Failed to check existing programs: ${programsError.message}`)
  }

  if (existingPrograms && existingPrograms.length > 0) {
    const existingProgram = existingPrograms[0] as WorkoutProgram
    const generatedAt = new Date(existingProgram.generated_by_ai_at || existingProgram.created_at)
    const now = new Date()
    const minutesSinceGeneration = Math.floor((now.getTime() - generatedAt.getTime()) / (1000 * 60))

    console.log(`GenerateProgram.existingProgramFound ${userId}`, {
      programId: existingProgram.id,
      status: existingProgram.status,
      minutesSinceGeneration,
      generationType: existingProgram.generation_type
    })

    // Generation type specific logic
    if (generationType === 'initial') {
      // For initial generation, check if user already has any active program
      return {
        eligible: false,
        reason: 'User already has an active program',
        data: {
          programId: existingProgram.id,
          status: existingProgram.status,
          cycleNumber: existingProgram.cycle_number
        }
      }
    } else if (generationType === 'scheduled') {
      // For scheduled generation, check if it's time for next cycle
      const daysSinceGeneration = Math.floor((now.getTime() - generatedAt.getTime()) / (1000 * 60 * 60 * 24))

      if (daysSinceGeneration < 21) {
        return {
          eligible: false,
          reason: 'Too early for scheduled generation (minimum 21 days)',
          data: {
            programId: existingProgram.id,
            daysSinceGeneration,
            minimumDays: 21
          }
        }
      }

      // Check if there's already a pending scheduled program for this cycle
      if (cycleNumber && existingProgram.cycle_number === cycleNumber) {
        return {
          eligible: false,
          reason: 'Program for this cycle already exists',
          data: {
            programId: existingProgram.id,
            existingCycle: existingProgram.cycle_number,
            requestedCycle: cycleNumber
          }
        }
      }
    } else if (generationType === 'manual') {
      // Manual generation is always allowed (coach override)
      console.log('GenerateProgram.manualOverride', {
        userId,
        existingProgram: existingProgram.id
      })
    }
  }

  return { eligible: true, reason: 'Generation allowed' }
}

// Determine cycle number for new program
async function determineCycleNumber(
  supabaseClient: any,
  userId: string,
  generationType: 'initial' | 'scheduled' | 'manual',
  requestedCycleNumber?: number
): Promise<number> {

  if (generationType === 'initial') {
    return 1
  }

  if (requestedCycleNumber) {
    return requestedCycleNumber
  }

  // Get the highest cycle number for this user
  const { data, error } = await supabaseClient
    .from('workout_programs')
    .select('cycle_number')
    .eq('user_id', userId)
    .order('cycle_number', { ascending: false })
    .limit(1)

  if (error) {
    console.error('GenerateProgram.determineCycleNumber.error', error)
    return 1
  }

  return data?.[0]?.cycle_number ? data[0].cycle_number + 1 : 1
}

// Record completion of scheduled job
async function recordScheduledJobCompletion(
  supabaseClient: any,
  userId: string,
  cycleNumber: number
): Promise<void> {
  try {
    const { error } = await supabaseClient
      .from('scheduled_generation_jobs')
      .update({
        execution_status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('cycle_number', cycleNumber)
      .eq('job_type', 'program_generation')
      .eq('execution_status', 'pending')

    if (error) {
      console.error('GenerateProgram.recordJobCompletion.error', error)
      // Don't throw - this is not critical for program generation
    } else {
      console.log('GenerateProgram.recordJobCompletion.success', {
        userId,
        cycleNumber
      })
    }
  } catch (error) {
    console.error('GenerateProgram.recordJobCompletion.exception', error)
    // Don't throw - this is not critical for program generation
  }
}

// Timeout wrapper for database operations
async function withTimeout<T>(
  operation: Promise<T>,
  timeoutMs: number,
  operationName: string
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`${operationName} timed out after ${timeoutMs}ms`))
    }, timeoutMs)
  })

  return Promise.race([operation, timeoutPromise])
}
