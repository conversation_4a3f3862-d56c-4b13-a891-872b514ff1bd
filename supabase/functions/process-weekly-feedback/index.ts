import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface WeeklyCheckIn {
  id: string;
  user_id: string;
  program_week_identifier?: string;
  checkin_date: string;
  submitted_at: string;
  wins?: string;
  challenges?: string;
  progress_reflection?: string;
  training_performance_rating?: number;
  recovery_rating?: number;
  energy_levels_rating?: number;
  additional_notes_for_coach?: string;
}

interface WorkoutLog {
  id: string;
  user_id: string;
  workout_name_actual: string;
  started_at: string;
  completed_at: string;
  duration_seconds: number;
  overall_session_rpe?: number;
  client_notes_for_session?: string;
}

interface Profile {
  id: string;
  full_name: string;
  training_experience_level?: string;
  primary_fitness_goal?: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client with service role key for admin access
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Parse request body
    const requestData = await req.json();
    console.log("ProcessWeeklyFeedback.start", { requestData });

    // Extract check-in ID and user ID from request
    const { checkInId, userId } = requestData;

    if (!checkInId || !userId) {
      throw new Error("Check-in ID and user ID are required");
    }

    // Fetch the check-in data
    const { data: checkIn, error: checkInError } = await supabaseClient
      .from("weekly_checkins")
      .select("*")
      .eq("id", checkInId)
      .eq("user_id", userId)
      .single();

    if (checkInError || !checkIn) {
      console.error("ProcessWeeklyFeedback.fetchCheckIn.failure", checkInError);
      throw new Error(`Failed to fetch check-in data: ${checkInError?.message || "Check-in not found"}`);
    }

    console.log("ProcessWeeklyFeedback.fetchCheckIn.success", { checkInId });

    // Fetch user profile for context
    const { data: profile, error: profileError } = await supabaseClient
      .from("profiles")
      .select("id, full_name, training_experience_level, primary_fitness_goal")
      .eq("id", userId)
      .single();

    if (profileError || !profile) {
      console.error("ProcessWeeklyFeedback.fetchProfile.failure", profileError);
      throw new Error(`Failed to fetch user profile: ${profileError?.message || "Profile not found"}`);
    }

    // Determine the date range for workout logs (7 days before check-in date)
    const checkInDate = new Date(checkIn.checkin_date);
    const weekStartDate = new Date(checkInDate);
    weekStartDate.setDate(checkInDate.getDate() - 6); // 6 days before check-in date (which is Sunday)
    
    // Format dates for query
    const weekStartISO = weekStartDate.toISOString();
    const checkInDateISO = checkInDate.toISOString();

    // Fetch workout logs for the week
    const { data: workoutLogs, error: workoutLogsError } = await supabaseClient
      .from("workout_logs")
      .select("*")
      .eq("user_id", userId)
      .gte("completed_at", weekStartISO)
      .lte("completed_at", checkInDateISO)
      .order("completed_at", { ascending: true });

    if (workoutLogsError) {
      console.error("ProcessWeeklyFeedback.fetchWorkoutLogs.failure", workoutLogsError);
      throw new Error(`Failed to fetch workout logs: ${workoutLogsError.message}`);
    }

    console.log("ProcessWeeklyFeedback.fetchWorkoutLogs.success", { 
      workoutCount: workoutLogs?.length || 0,
      weekStart: weekStartISO,
      weekEnd: checkInDateISO
    });

    // Get workout exercise logs for each workout
    const workoutDetails = [];
    
    if (workoutLogs && workoutLogs.length > 0) {
      for (const workout of workoutLogs) {
        const { data: exerciseLogs, error: exerciseLogsError } = await supabaseClient
          .from("workout_exercise_logs")
          .select(`
            id,
            exercise_id,
            sets_logged,
            exercise_notes,
            exercises (
              name,
              target_muscles_primary
            )
          `)
          .eq("workout_log_id", workout.id);

        if (exerciseLogsError) {
          console.error("ProcessWeeklyFeedback.fetchExerciseLogs.failure", { 
            workoutId: workout.id,
            error: exerciseLogsError 
          });
          // Continue with other workouts even if one fails
          continue;
        }

        workoutDetails.push({
          ...workout,
          exercises: exerciseLogs || []
        });
      }
    }

    // Construct the prompt for the AI
    const prompt = constructAIPrompt(checkIn, profile, workoutDetails);
    console.log("ProcessWeeklyFeedback.OpenAI.request", { promptLength: prompt.length });

    // Call OpenAI API
    const openAIResponse = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${Deno.env.get("OPENAI_API_KEY")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4-turbo-preview",
        messages: [
          {
            role: "system",
            content: "You are an expert fitness coach providing personalized feedback on a client's weekly check-in. Your feedback should be encouraging, specific, and actionable. Focus on addressing their wins, challenges, and progress. Provide guidance based on their workout data and ratings. Keep your tone supportive and motivational."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });

    if (!openAIResponse.ok) {
      const errorText = await openAIResponse.text();
      console.error("ProcessWeeklyFeedback.OpenAI.failure", { 
        status: openAIResponse.status, 
        error: errorText 
      });
      throw new Error(`OpenAI API error: ${openAIResponse.status} ${errorText}`);
    }

    const aiResult = await openAIResponse.json();
    const feedbackContent = aiResult.choices[0].message.content;
    console.log("ProcessWeeklyFeedback.OpenAI.response", { 
      status: "success", 
      contentLength: feedbackContent.length 
    });

    // Store the AI-generated feedback in the database
    const { data: feedbackData, error: feedbackError } = await supabaseClient
      .from("coach_feedback")
      .insert({
        user_id: userId,
        related_checkin_id: checkInId,
        feedback_content: feedbackContent,
        status: "draft_by_ai"
      })
      .select()
      .single();

    if (feedbackError) {
      console.error("ProcessWeeklyFeedback.DBStore.failure", feedbackError);
      throw new Error(`Failed to store feedback: ${feedbackError.message}`);
    }

    console.log("ProcessWeeklyFeedback.DBStore.success", { feedbackId: feedbackData.id });

    return new Response(
      JSON.stringify({ 
        success: true, 
        feedbackId: feedbackData.id 
      }),
      { 
        headers: { 
          ...corsHeaders, 
          "Content-Type": "application/json" 
        } 
      }
    );

  } catch (error) {
    console.error("ProcessWeeklyFeedback.failure", error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          "Content-Type": "application/json" 
        }
      }
    );
  }
});

/**
 * Constructs a detailed prompt for the AI based on check-in data, user profile, and workout logs
 */
function constructAIPrompt(
  checkIn: WeeklyCheckIn, 
  profile: Profile, 
  workoutLogs: any[]
): string {
  // Format workout summary
  const workoutSummary = workoutLogs.map(workout => {
    const exerciseSummary = workout.exercises.map((ex: any) => {
      const exerciseName = ex.exercises?.name || "Unknown exercise";
      const targetMuscles = ex.exercises?.target_muscles_primary?.join(", ") || "N/A";
      const setCount = ex.sets_logged?.length || 0;
      
      return `- ${exerciseName} (${targetMuscles}): ${setCount} sets${ex.exercise_notes ? ` - Note: ${ex.exercise_notes}` : ''}`;
    }).join("\n");

    return `
Workout: ${workout.workout_name_actual}
Date: ${new Date(workout.completed_at).toLocaleDateString()}
Duration: ${Math.floor(workout.duration_seconds / 60)} minutes
Overall RPE: ${workout.overall_session_rpe || "Not rated"}
Exercises:
${exerciseSummary}
${workout.client_notes_for_session ? `Notes: ${workout.client_notes_for_session}` : ''}
`;
  }).join("\n");

  // Format ratings as stars for better readability
  const formatRating = (rating?: number) => {
    if (!rating) return "Not provided";
    return `${rating}/10`;
  };

  // Construct the full prompt
  return `
CLIENT WEEKLY CHECK-IN FEEDBACK REQUEST

Client: ${profile.full_name}
Experience Level: ${profile.training_experience_level || "Unknown"}
Primary Goals: ${profile.primary_fitness_goal?.join(", ") || "Unknown"}
Check-in Date: ${new Date(checkIn.checkin_date).toLocaleDateString()}

CLIENT SELF-ASSESSMENT:
${checkIn.wins ? `Wins This Week: ${checkIn.wins}` : "Wins: Not provided"}
${checkIn.challenges ? `Challenges Faced: ${checkIn.challenges}` : "Challenges: Not provided"}
${checkIn.progress_reflection ? `Progress Reflection: ${checkIn.progress_reflection}` : "Progress Reflection: Not provided"}

CLIENT RATINGS:
Training Performance: ${formatRating(checkIn.training_performance_rating)}
Recovery Quality: ${formatRating(checkIn.recovery_rating)}
Energy Levels: ${formatRating(checkIn.energy_levels_rating)}

${checkIn.additional_notes_for_coach ? `Additional Notes: ${checkIn.additional_notes_for_coach}` : ""}

WORKOUT DATA FOR THE WEEK:
${workoutLogs.length > 0 ? workoutSummary : "No workouts logged this week."}

INSTRUCTIONS:
Based on the client's check-in data and workout information above, please provide personalized feedback that:
1. Acknowledges their wins and progress
2. Addresses their challenges with practical solutions
3. Provides specific guidance based on their performance, recovery, and energy ratings
4. Includes actionable recommendations for the coming week
5. Maintains an encouraging and supportive tone throughout

Your feedback should be comprehensive but concise (300-500 words), formatted in clear paragraphs, and focused on helping the client make progress toward their goals.
`;
}