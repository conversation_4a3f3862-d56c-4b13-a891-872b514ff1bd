/*
# Enable Multi-Select Fitness Goals

1. Database Changes
   - Update `profiles` table: Change `primary_fitness_goal` from TEXT to TEXT[]
   - Add validation constraint to ensure 1-3 goals are selected

2. Data Migration
   - Convert existing single goal strings to arrays
   - Handle null values appropriately
*/

-- First, add a temporary column for the array version
ALTER TABLE public.profiles 
ADD COLUMN primary_fitness_goals_array TEXT[];

-- Migrate existing data: convert single goals to arrays
UPDATE public.profiles 
SET primary_fitness_goals_array = CASE 
  WHEN primary_fitness_goal IS NOT NULL THEN ARRAY[primary_fitness_goal]
  ELSE NULL
END;

-- Drop the old column and rename the new one
ALTER TABLE public.profiles 
DROP COLUMN primary_fitness_goal;

ALTER TABLE public.profiles 
RENAME COLUMN primary_fitness_goals_array TO primary_fitness_goal;

-- Add constraint to ensure 1-3 goals are selected
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_primary_fitness_goal_check 
CHECK (
  primary_fitness_goal IS NULL OR 
  (array_length(primary_fitness_goal, 1) >= 1 AND array_length(primary_fitness_goal, 1) <= 3)
);

-- Update the intake completion status function to handle array
CREATE OR REPLACE FUNCTION public.update_intake_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if intake data is complete
  IF NEW.intake_gender IS NOT NULL 
     AND NEW.age IS NOT NULL 
     AND NEW.height_cm IS NOT NULL 
     AND NEW.weight_kg IS NOT NULL 
     AND NEW.primary_fitness_goal IS NOT NULL 
     AND array_length(NEW.primary_fitness_goal, 1) >= 1
     AND NEW.training_experience_level IS NOT NULL 
     AND NEW.equipment_access_type IS NOT NULL 
     AND NEW.training_days_per_week IS NOT NULL 
     AND NEW.preferred_session_duration_minutes IS NOT NULL THEN
    
    -- Mark intake as completed if it wasn't already
    IF OLD.intake_status != 'completed' THEN
      NEW.intake_status = 'completed';
      NEW.intake_completed_at = NOW();
    END IF;
  ELSE
    -- Mark as in progress if some data exists but not complete
    IF NEW.intake_status = 'not_started' AND (
       NEW.intake_gender IS NOT NULL 
       OR NEW.age IS NOT NULL 
       OR NEW.height_cm IS NOT NULL 
       OR NEW.weight_kg IS NOT NULL
    ) THEN
      NEW.intake_status = 'in_progress';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;