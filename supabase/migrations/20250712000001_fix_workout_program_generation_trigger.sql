-- Clean up intake completion status function
-- This migration removes HTTP calls from the database function and prepares for webhook-based approach

-- Create or replace the function to update intake completion status (without HTTP calls)
CREATE OR REPLACE FUNCTION update_intake_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If the status was already 'completed', keep it completed and exit
  IF OLD.intake_status = 'completed' THEN
    NEW.intake_status = 'completed';
    RETURN NEW;
  END IF;

  -- Check if intake is completed (has all required fields)
  -- goal_timeline_months is only required if has_specific_event is true
  IF NEW.intake_gender IS NOT NULL
     AND NEW.age IS NOT NULL
     AND NEW.height_cm IS NOT NULL
     AND NEW.weight_kg IS NOT NULL
     AND NEW.primary_fitness_goal IS NOT NULL
     AND array_length(NEW.primary_fitness_goal, 1) > 0
     AND NEW.training_experience_level IS NOT NULL
     AND NEW.equipment_access_type IS NOT NULL
     AND NEW.training_days_per_week IS NOT NULL
     AND NEW.preferred_session_duration_minutes IS NOT NULL
     AND (
       -- If has_specific_event is true, goal_timeline_months must be provided
       (NEW.has_specific_event = true AND NEW.goal_timeline_months IS NOT NULL) OR
       -- If has_specific_event is false or null, goal_timeline_months is not required
       (NEW.has_specific_event = false OR NEW.has_specific_event IS NULL)
     ) THEN

    NEW.intake_status = 'completed';
    NEW.intake_completed_at = now();

    -- Note: Workout program generation will be triggered by database webhook
    -- configured to fire when intake_status changes to 'completed'

  ELSIF NEW.intake_gender IS NOT NULL
        OR NEW.age IS NOT NULL
        OR NEW.height_cm IS NOT NULL
        OR NEW.weight_kg IS NOT NULL
        OR (NEW.primary_fitness_goal IS NOT NULL AND array_length(NEW.primary_fitness_goal, 1) > 0)
        OR NEW.training_experience_level IS NOT NULL
        OR NEW.equipment_access_type IS NOT NULL
        OR NEW.training_days_per_week IS NOT NULL
        OR NEW.preferred_session_duration_minutes IS NOT NULL THEN

    NEW.intake_status = 'in_progress';
  ELSE
    -- If no data is present, set to not_started
    NEW.intake_status = 'not_started';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION update_intake_completion_status() OWNER TO postgres;

-- Add helpful comment
COMMENT ON FUNCTION update_intake_completion_status() IS 'Updates intake completion status and triggers AI workout program generation when intake is completed for the first time';

-- Ensure the trigger exists (it should already exist, but this ensures it's properly configured)
DROP TRIGGER IF EXISTS on_profile_intake_updated ON profiles;
CREATE TRIGGER on_profile_intake_updated
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_intake_completion_status();

-- Add comment to explain the trigger
COMMENT ON TRIGGER on_profile_intake_updated ON profiles IS 'Triggers intake completion status update and AI workout program generation when profile is updated';
