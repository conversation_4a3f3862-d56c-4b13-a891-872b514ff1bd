/*
  # Fix Webhook Function Name

  1. Changes
    - Rename the webhook function to better reflect its purpose
    - Update the trigger to use the renamed function
    - This ensures clarity in the database schema

  2. Security
    - Maintains existing security context
*/

-- Drop the existing trigger
DROP TRIGGER IF EXISTS generate_feedback_webhook ON weekly_checkins;

-- Drop the existing function with incorrect name
DROP FUNCTION IF EXISTS generate_workout_program_webhook();

-- Create a properly named function
CREATE OR REPLACE FUNCTION generate_feedback_webhook()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM
    net.http_post(
      url := current_setting('app.settings.supabase_url') || '/functions/v1/generate-feedback',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key')
      ),
      body := jsonb_build_object(
        'type', TG_OP,
        'table', TG_TABLE_NAME,
        'record', row_to_json(NEW)
      )
    );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger with the correct function name
CREATE TRIGGER generate_feedback_webhook
  AFTER INSERT ON weekly_checkins
  FOR EACH ROW
  EXECUTE FUNCTION generate_feedback_webhook();

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION generate_feedback_webhook() OWNER TO postgres;

-- Add helpful comment
COMMENT ON FUNCTION generate_feedback_webhook() IS 'Triggers the generate-feedback Edge Function when a new weekly check-in is submitted';