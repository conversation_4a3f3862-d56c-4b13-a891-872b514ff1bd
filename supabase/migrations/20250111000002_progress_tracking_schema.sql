/*
  # Progress Tracking Database Schema

  1. Enhanced workout logging system
    - `workout_logs` - Individual workout session records
    - `exercise_logs` - Individual exercise performance within workouts
    - `set_logs` - Individual set performance data

  2. Progress tracking system
    - `progress_photos` - Progress photo management
    - `body_measurements` - Body measurement tracking
    - `fitness_goals` - Goal setting and tracking
    - `achievements` - Achievement system

  3. Analytics and insights
    - Views and functions for analytics
    - Performance trend calculations
    - Progress insights generation

  4. RLS policies for all tables
*/

-- Enhanced workout logging tables
CREATE TABLE IF NOT EXISTS workout_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  workout_id UUID NOT NULL REFERENCES workouts(id) ON DELETE CASCADE,
  program_id UUID NOT NULL REFERENCES workout_programs(id) ON DELETE CASCADE,
  started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  duration_seconds INTEGER,
  total_exercises INTEGER DEFAULT 0,
  completed_exercises INTEGER DEFAULT 0,
  total_sets INTEGER DEFAULT 0,
  completed_sets INTEGER DEFAULT 0,
  total_reps INTEGER DEFAULT 0,
  total_weight_kg NUMERIC DEFAULT 0,
  average_rpe NUMERIC CHECK (average_rpe >= 1 AND average_rpe <= 10),
  session_rpe NUMERIC CHECK (session_rpe >= 1 AND session_rpe <= 10),
  mood TEXT CHECK (mood IN ('great', 'good', 'okay', 'tired', 'struggled')),
  notes TEXT,
  estimated_calories INTEGER,
  is_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS exercise_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workout_log_id UUID NOT NULL REFERENCES workout_logs(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  exercise_id UUID NOT NULL REFERENCES exercises(id) ON DELETE CASCADE,
  workout_exercise_id UUID REFERENCES workout_exercises(id) ON DELETE SET NULL,
  exercise_order INTEGER NOT NULL,
  target_sets INTEGER,
  target_reps INTEGER,
  target_weight_kg NUMERIC,
  target_duration_seconds INTEGER,
  completed_sets INTEGER DEFAULT 0,
  total_reps INTEGER DEFAULT 0,
  total_weight_kg NUMERIC DEFAULT 0,
  total_duration_seconds INTEGER DEFAULT 0,
  average_rpe NUMERIC CHECK (average_rpe >= 1 AND average_rpe <= 10),
  notes TEXT,
  is_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS set_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  exercise_log_id UUID NOT NULL REFERENCES exercise_logs(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  set_number INTEGER NOT NULL,
  target_reps INTEGER,
  actual_reps INTEGER,
  target_weight_kg NUMERIC,
  actual_weight_kg NUMERIC,
  target_duration_seconds INTEGER,
  actual_duration_seconds INTEGER,
  rpe NUMERIC CHECK (rpe >= 1 AND rpe <= 10),
  rest_seconds INTEGER,
  notes TEXT,
  is_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Progress photos table
CREATE TABLE IF NOT EXISTS progress_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  photo_type TEXT CHECK (photo_type IN ('front', 'side', 'back', 'custom')) DEFAULT 'front',
  caption TEXT,
  weight_kg NUMERIC,
  body_fat_percentage NUMERIC,
  taken_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_public BOOLEAN DEFAULT false,
  tags TEXT[],
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Body measurements table
CREATE TABLE IF NOT EXISTS body_measurements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  measurement_date DATE NOT NULL DEFAULT CURRENT_DATE,
  weight_kg NUMERIC,
  height_cm NUMERIC,
  body_fat_percentage NUMERIC,
  muscle_mass_kg NUMERIC,
  -- Circumference measurements in cm
  chest_cm NUMERIC,
  waist_cm NUMERIC,
  hips_cm NUMERIC,
  bicep_left_cm NUMERIC,
  bicep_right_cm NUMERIC,
  thigh_left_cm NUMERIC,
  thigh_right_cm NUMERIC,
  neck_cm NUMERIC,
  forearm_left_cm NUMERIC,
  forearm_right_cm NUMERIC,
  calf_left_cm NUMERIC,
  calf_right_cm NUMERIC,
  -- Additional metrics
  resting_heart_rate INTEGER,
  blood_pressure_systolic INTEGER,
  blood_pressure_diastolic INTEGER,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, measurement_date)
);

-- Fitness goals table
CREATE TABLE IF NOT EXISTS fitness_goals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  goal_type TEXT NOT NULL CHECK (goal_type IN (
    'weight_loss', 'weight_gain', 'muscle_gain', 'strength', 
    'endurance', 'body_fat', 'measurement', 'performance', 'habit'
  )),
  title TEXT NOT NULL,
  description TEXT,
  target_value NUMERIC,
  current_value NUMERIC DEFAULT 0,
  unit TEXT, -- kg, lbs, cm, inches, reps, seconds, etc.
  target_date DATE,
  priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
  status TEXT CHECK (status IN ('active', 'completed', 'paused', 'cancelled')) DEFAULT 'active',
  progress_percentage NUMERIC DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  milestone_values NUMERIC[],
  milestone_dates DATE[],
  achieved_milestones INTEGER DEFAULT 0,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Achievements table
CREATE TABLE IF NOT EXISTS achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_type TEXT NOT NULL CHECK (achievement_type IN (
    'workout_streak', 'total_workouts', 'weight_milestone', 'strength_gain',
    'consistency', 'program_completion', 'personal_record', 'goal_achievement'
  )),
  title TEXT NOT NULL,
  description TEXT,
  icon TEXT, -- Icon name or emoji
  badge_color TEXT DEFAULT '#FFD700',
  points INTEGER DEFAULT 0,
  achieved_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  related_workout_log_id UUID REFERENCES workout_logs(id) ON DELETE SET NULL,
  related_goal_id UUID REFERENCES fitness_goals(id) ON DELETE SET NULL,
  metadata JSONB, -- Additional achievement data
  is_featured BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Personal records table
CREATE TABLE IF NOT EXISTS personal_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  exercise_id UUID NOT NULL REFERENCES exercises(id) ON DELETE CASCADE,
  record_type TEXT NOT NULL CHECK (record_type IN ('1rm', 'max_reps', 'max_weight', 'max_volume', 'best_time')),
  value NUMERIC NOT NULL,
  unit TEXT NOT NULL, -- kg, lbs, reps, seconds, etc.
  achieved_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  workout_log_id UUID REFERENCES workout_logs(id) ON DELETE SET NULL,
  exercise_log_id UUID REFERENCES exercise_logs(id) ON DELETE SET NULL,
  previous_record_value NUMERIC,
  improvement_percentage NUMERIC,
  notes TEXT,
  is_verified BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, exercise_id, record_type)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_workout_logs_user_id ON workout_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_workout_logs_completed_at ON workout_logs (completed_at);
CREATE INDEX IF NOT EXISTS idx_workout_logs_program_id ON workout_logs (program_id);

CREATE INDEX IF NOT EXISTS idx_exercise_logs_workout_log_id ON exercise_logs (workout_log_id);
CREATE INDEX IF NOT EXISTS idx_exercise_logs_user_id ON exercise_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_exercise_logs_exercise_id ON exercise_logs (exercise_id);

CREATE INDEX IF NOT EXISTS idx_set_logs_exercise_log_id ON set_logs (exercise_log_id);
CREATE INDEX IF NOT EXISTS idx_set_logs_user_id ON set_logs (user_id);

CREATE INDEX IF NOT EXISTS idx_progress_photos_user_id ON progress_photos (user_id);
CREATE INDEX IF NOT EXISTS idx_progress_photos_taken_at ON progress_photos (taken_at);

CREATE INDEX IF NOT EXISTS idx_body_measurements_user_id ON body_measurements (user_id);
CREATE INDEX IF NOT EXISTS idx_body_measurements_date ON body_measurements (measurement_date);

CREATE INDEX IF NOT EXISTS idx_fitness_goals_user_id ON fitness_goals (user_id);
CREATE INDEX IF NOT EXISTS idx_fitness_goals_status ON fitness_goals (status);
CREATE INDEX IF NOT EXISTS idx_fitness_goals_target_date ON fitness_goals (target_date);

CREATE INDEX IF NOT EXISTS idx_achievements_user_id ON achievements (user_id);
CREATE INDEX IF NOT EXISTS idx_achievements_achieved_at ON achievements (achieved_at);

CREATE INDEX IF NOT EXISTS idx_personal_records_user_id ON personal_records (user_id);
CREATE INDEX IF NOT EXISTS idx_personal_records_exercise_id ON personal_records (exercise_id);

-- Enable RLS on all tables
ALTER TABLE workout_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE exercise_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE set_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE body_measurements ENABLE ROW LEVEL SECURITY;
ALTER TABLE fitness_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE personal_records ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Workout logs policies
CREATE POLICY "Users can manage their own workout logs"
  ON workout_logs
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Coaches can view client workout logs"
  ON workout_logs
  FOR SELECT
  TO authenticated
  USING (
    user_id IN (
      SELECT client_id FROM coach_client_relationships 
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

-- Exercise logs policies
CREATE POLICY "Users can manage their own exercise logs"
  ON exercise_logs
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Coaches can view client exercise logs"
  ON exercise_logs
  FOR SELECT
  TO authenticated
  USING (
    user_id IN (
      SELECT client_id FROM coach_client_relationships 
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

-- Set logs policies
CREATE POLICY "Users can manage their own set logs"
  ON set_logs
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Progress photos policies
CREATE POLICY "Users can manage their own progress photos"
  ON progress_photos
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Public progress photos are viewable by all"
  ON progress_photos
  FOR SELECT
  TO authenticated
  USING (is_public = true);

-- Body measurements policies
CREATE POLICY "Users can manage their own body measurements"
  ON body_measurements
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Coaches can view client body measurements"
  ON body_measurements
  FOR SELECT
  TO authenticated
  USING (
    user_id IN (
      SELECT client_id FROM coach_client_relationships 
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

-- Fitness goals policies
CREATE POLICY "Users can manage their own fitness goals"
  ON fitness_goals
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Achievements policies
CREATE POLICY "Users can view their own achievements"
  ON achievements
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can create achievements"
  ON achievements
  FOR INSERT
  TO service_role
  WITH CHECK (true);

-- Personal records policies
CREATE POLICY "Users can manage their own personal records"
  ON personal_records
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_workout_logs_updated_at BEFORE UPDATE ON workout_logs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_exercise_logs_updated_at BEFORE UPDATE ON exercise_logs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_body_measurements_updated_at BEFORE UPDATE ON body_measurements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fitness_goals_updated_at BEFORE UPDATE ON fitness_goals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Analytics functions
CREATE OR REPLACE FUNCTION get_user_workout_stats(
  p_user_id UUID,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
  total_workouts INTEGER,
  completed_workouts INTEGER,
  completion_rate NUMERIC,
  total_duration_hours NUMERIC,
  average_session_duration_minutes NUMERIC,
  total_exercises INTEGER,
  total_sets INTEGER,
  total_reps INTEGER,
  total_weight_kg NUMERIC,
  average_rpe NUMERIC,
  workout_streak INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_start_date DATE := COALESCE(p_start_date, CURRENT_DATE - INTERVAL '30 days');
  v_end_date DATE := COALESCE(p_end_date, CURRENT_DATE);
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_workouts,
    COUNT(*) FILTER (WHERE is_completed = true)::INTEGER as completed_workouts,
    ROUND(
      (COUNT(*) FILTER (WHERE is_completed = true)::NUMERIC / NULLIF(COUNT(*), 0)) * 100,
      2
    ) as completion_rate,
    ROUND(SUM(duration_seconds) / 3600.0, 2) as total_duration_hours,
    ROUND(AVG(duration_seconds) / 60.0, 1) as average_session_duration_minutes,
    SUM(total_exercises)::INTEGER as total_exercises,
    SUM(total_sets)::INTEGER as total_sets,
    SUM(total_reps)::INTEGER as total_reps,
    SUM(total_weight_kg) as total_weight_kg,
    ROUND(AVG(session_rpe), 1) as average_rpe,
    -- Calculate current workout streak
    (
      SELECT COUNT(*)
      FROM (
        SELECT DATE(completed_at) as workout_date
        FROM workout_logs
        WHERE user_id = p_user_id
          AND is_completed = true
          AND completed_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(completed_at)
        ORDER BY workout_date DESC
      ) consecutive_days
    )::INTEGER as workout_streak
  FROM workout_logs
  WHERE user_id = p_user_id
    AND DATE(started_at) BETWEEN v_start_date AND v_end_date;
END;
$$;

-- Function to get strength progression for an exercise
CREATE OR REPLACE FUNCTION get_exercise_progression(
  p_user_id UUID,
  p_exercise_id UUID,
  p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
  workout_date DATE,
  max_weight_kg NUMERIC,
  total_volume_kg NUMERIC,
  max_reps INTEGER,
  average_rpe NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    DATE(wl.completed_at) as workout_date,
    MAX(sl.actual_weight_kg) as max_weight_kg,
    SUM(sl.actual_weight_kg * sl.actual_reps) as total_volume_kg,
    MAX(sl.actual_reps) as max_reps,
    ROUND(AVG(sl.rpe), 1) as average_rpe
  FROM workout_logs wl
  JOIN exercise_logs el ON wl.id = el.workout_log_id
  JOIN set_logs sl ON el.id = sl.exercise_log_id
  WHERE wl.user_id = p_user_id
    AND el.exercise_id = p_exercise_id
    AND wl.is_completed = true
    AND sl.is_completed = true
  GROUP BY DATE(wl.completed_at)
  ORDER BY workout_date DESC
  LIMIT p_limit;
END;
$$;

-- Function to calculate and update personal records
CREATE OR REPLACE FUNCTION update_personal_record(
  p_user_id UUID,
  p_exercise_id UUID,
  p_record_type TEXT,
  p_value NUMERIC,
  p_unit TEXT,
  p_workout_log_id UUID DEFAULT NULL,
  p_exercise_log_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_record NUMERIC;
  v_improvement NUMERIC;
BEGIN
  -- Get current record
  SELECT value INTO v_current_record
  FROM personal_records
  WHERE user_id = p_user_id
    AND exercise_id = p_exercise_id
    AND record_type = p_record_type;

  -- Check if this is a new record
  IF v_current_record IS NULL OR p_value > v_current_record THEN
    -- Calculate improvement percentage
    v_improvement := CASE
      WHEN v_current_record IS NOT NULL AND v_current_record > 0 THEN
        ROUND(((p_value - v_current_record) / v_current_record) * 100, 2)
      ELSE 0
    END;

    -- Insert or update the record
    INSERT INTO personal_records (
      user_id, exercise_id, record_type, value, unit,
      workout_log_id, exercise_log_id, previous_record_value, improvement_percentage
    )
    VALUES (
      p_user_id, p_exercise_id, p_record_type, p_value, p_unit,
      p_workout_log_id, p_exercise_log_id, v_current_record, v_improvement
    )
    ON CONFLICT (user_id, exercise_id, record_type)
    DO UPDATE SET
      value = EXCLUDED.value,
      achieved_at = NOW(),
      workout_log_id = EXCLUDED.workout_log_id,
      exercise_log_id = EXCLUDED.exercise_log_id,
      previous_record_value = personal_records.value,
      improvement_percentage = EXCLUDED.improvement_percentage;

    RETURN true;
  END IF;

  RETURN false;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_user_workout_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_exercise_progression TO authenticated;
GRANT EXECUTE ON FUNCTION update_personal_record TO authenticated, service_role;
