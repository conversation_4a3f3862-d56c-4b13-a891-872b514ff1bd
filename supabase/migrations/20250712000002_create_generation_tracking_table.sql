-- Create generation tracking table for enhanced duplicate prevention
-- This table helps track workout program generation processes to prevent race conditions

CREATE TABLE IF NOT EXISTS generation_tracking (
  id TEXT PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  generation_type TEXT NOT NULL CHECK (generation_type IN ('initial', 'scheduled', 'manual')),
  cycle_number INTEGER,
  status TEXT NOT NULL CHECK (status IN ('in_progress', 'completed', 'failed')),
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS generation_tracking_user_id_idx ON generation_tracking(user_id);
CREATE INDEX IF NOT EXISTS generation_tracking_status_idx ON generation_tracking(status);
CREATE INDEX IF NOT EXISTS generation_tracking_started_at_idx ON generation_tracking(started_at);

-- Add RLS policy
ALTER TABLE generation_tracking ENABLE ROW LEVEL SECURITY;

-- Only service role can access this table (it's for internal tracking)
CREATE POLICY "Service role can manage generation tracking" ON generation_tracking
  FOR ALL USING (auth.role() = 'service_role');

-- Add helpful comment
COMMENT ON TABLE generation_tracking IS 'Tracks workout program generation processes to prevent race conditions and duplicate generations';

-- Create a function to clean up old tracking records (older than 1 hour)
CREATE OR REPLACE FUNCTION cleanup_old_generation_tracking()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM generation_tracking 
  WHERE started_at < NOW() - INTERVAL '1 hour';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION cleanup_old_generation_tracking() OWNER TO postgres;

COMMENT ON FUNCTION cleanup_old_generation_tracking() IS 'Cleans up old generation tracking records older than 1 hour';
