-- Remove the problematic webhook trigger that causes duplicate AI program generation
-- This trigger fires on every profile update and sends empty payloads to the Edge Function

-- Drop the problematic webhook trigger
DROP TRIGGER IF EXISTS generate_workout_program_webhook ON public.profiles;

-- Add helpful comment explaining the removal
COMMENT ON TABLE public.profiles IS 'Profiles table - removed generate_workout_program_webhook trigger to prevent duplicate AI program generation. AI generation is now handled only by the on_profile_intake_updated trigger when intake_status changes to completed.';
