/*
  # Add Intake Data to Profiles Table

  1. New Columns Added to Profiles
    - All intake form fields moved to profiles table
    - Metric preference fields (height_unit, weight_unit)
    - Intake completion status tracking

  2. Data Migration
    - Migrate existing intake_forms data to profiles
    - Preserve all existing data relationships

  3. Security
    - Update RLS policies for new columns
    - Maintain existing access controls

  4. Cleanup
    - Remove intake_forms table after migration
    - Update triggers and functions
*/

-- Add all intake form columns to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS intake_gender TEXT CHECK (intake_gender IN ('Woman', 'Man'));
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS age INTEGER CHECK (age > 0 AND age < 120);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS height_cm INTEGER CHECK (height_cm > 50 AND height_cm < 300);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS weight_kg NUMERIC(5,2) CHECK (weight_kg > 20 AND weight_kg < 500);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS primary_fitness_goal TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS training_experience_level TEXT CHECK (training_experience_level IN ('Beginner', 'Intermediate', 'Advanced'));
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS goal_timeline_months INTEGER;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS equipment_access_type TEXT CHECK (equipment_access_type IN ('Full Gym', 'Home Gym Basic', 'Bodyweight Only'));
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS available_equipment JSONB;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS custom_equipment_notes TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS training_days_per_week INTEGER CHECK (training_days_per_week >= 2 AND training_days_per_week <= 6);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS preferred_training_days TEXT[];
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS preferred_session_duration_minutes INTEGER;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS injuries_limitations TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS training_preferences_notes TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS has_specific_event BOOLEAN DEFAULT false;

-- Add metric preference columns
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS height_unit TEXT CHECK (height_unit IN ('cm', 'ft')) DEFAULT 'cm';
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS weight_unit TEXT CHECK (weight_unit IN ('kg', 'lbs')) DEFAULT 'kg';

-- Add intake completion tracking
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS intake_completed_at TIMESTAMPTZ;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS intake_status TEXT CHECK (intake_status IN ('not_started', 'in_progress', 'completed')) DEFAULT 'not_started';

-- Migrate existing data from intake_forms to profiles (if intake_forms table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'intake_forms') THEN
    -- Update profiles with data from intake_forms
    UPDATE public.profiles 
    SET 
      intake_gender = intake_forms.gender,
      age = intake_forms.age,
      height_cm = intake_forms.height_cm,
      weight_kg = intake_forms.weight_kg,
      primary_fitness_goal = intake_forms.primary_fitness_goal,
      training_experience_level = intake_forms.training_experience_level,
      goal_timeline_months = intake_forms.goal_timeline_months,
      equipment_access_type = intake_forms.equipment_access_type,
      available_equipment = intake_forms.available_equipment,
      custom_equipment_notes = intake_forms.custom_equipment_notes,
      training_days_per_week = intake_forms.training_days_per_week,
      preferred_training_days = intake_forms.preferred_training_days,
      preferred_session_duration_minutes = intake_forms.preferred_session_duration_minutes,
      injuries_limitations = intake_forms.injuries_limitations,
      training_preferences_notes = intake_forms.training_preferences_notes,
      has_specific_event = COALESCE(intake_forms.has_specific_event, false),
      intake_completed_at = intake_forms.submitted_at,
      intake_status = CASE 
        WHEN intake_forms.status = 'submitted' THEN 'completed'
        WHEN intake_forms.status = 'draft' THEN 'in_progress'
        ELSE 'not_started'
      END,
      -- Update gender_preference based on intake_gender if not already set
      gender_preference = COALESCE(profiles.gender_preference, intake_forms.gender, 'Neutral')
    FROM public.intake_forms
    WHERE profiles.id = intake_forms.user_id;
  END IF;
END $$;

-- Update the handle_new_user function to initialize new columns
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    full_name, 
    gender_preference,
    height_unit,
    weight_unit,
    intake_status
  )
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'),
    'Neutral',
    'cm',
    'kg',
    'not_started'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create function to update intake completion status
CREATE OR REPLACE FUNCTION public.update_intake_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if all required intake fields are filled
  IF NEW.intake_gender IS NOT NULL 
     AND NEW.age IS NOT NULL 
     AND NEW.height_cm IS NOT NULL 
     AND NEW.weight_kg IS NOT NULL 
     AND NEW.primary_fitness_goal IS NOT NULL 
     AND NEW.training_experience_level IS NOT NULL 
     AND NEW.equipment_access_type IS NOT NULL 
     AND NEW.training_days_per_week IS NOT NULL 
     AND NEW.preferred_session_duration_minutes IS NOT NULL THEN
    
    -- Mark intake as completed if not already
    IF OLD.intake_status != 'completed' THEN
      NEW.intake_status = 'completed';
      NEW.intake_completed_at = NOW();
    END IF;
  ELSE
    -- Mark as in progress if some fields are filled
    IF NEW.intake_status = 'not_started' AND (
       NEW.intake_gender IS NOT NULL 
       OR NEW.age IS NOT NULL 
       OR NEW.height_cm IS NOT NULL 
       OR NEW.weight_kg IS NOT NULL
    ) THEN
      NEW.intake_status = 'in_progress';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for intake completion status
DROP TRIGGER IF EXISTS on_profile_intake_updated ON public.profiles;
CREATE TRIGGER on_profile_intake_updated
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_intake_completion_status();

-- Update RLS policies to include new columns (existing policies should cover them)
-- The existing "Users can view their own profile" and "Users can update their own profile" 
-- policies will automatically apply to the new columns

-- Add indexes for commonly queried fields
CREATE INDEX IF NOT EXISTS profiles_intake_status_idx ON public.profiles (intake_status);
CREATE INDEX IF NOT EXISTS profiles_training_experience_idx ON public.profiles (training_experience_level);
CREATE INDEX IF NOT EXISTS profiles_equipment_access_idx ON public.profiles (equipment_access_type);

-- Clean up: Drop intake_forms table and related objects (if they exist)
DO $$
BEGIN
  -- Drop triggers first
  IF EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_intake_form_updated') THEN
    DROP TRIGGER on_intake_form_updated ON public.intake_forms;
  END IF;
  
  IF EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_intake_submitted_update_profile_gender') THEN
    DROP TRIGGER on_intake_submitted_update_profile_gender ON public.intake_forms;
  END IF;
  
  -- Drop functions
  IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'update_profile_gender_from_intake') THEN
    DROP FUNCTION public.update_profile_gender_from_intake();
  END IF;
  
  -- Drop table
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'intake_forms') THEN
    DROP TABLE public.intake_forms;
  END IF;
END $$;

-- Add helpful comments
COMMENT ON COLUMN public.profiles.intake_gender IS 'Gender selected during intake (separate from theme preference)';
COMMENT ON COLUMN public.profiles.height_unit IS 'User preferred unit for height display (cm or ft)';
COMMENT ON COLUMN public.profiles.weight_unit IS 'User preferred unit for weight display (kg or lbs)';
COMMENT ON COLUMN public.profiles.intake_status IS 'Status of intake form completion';
COMMENT ON COLUMN public.profiles.intake_completed_at IS 'Timestamp when intake was completed';