-- Create system alerts table for storing alert history
CREATE TABLE IF NOT EXISTS system_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type TEXT NOT NULL CHECK (alert_type IN ('system_failure', 'job_failure', 'performance_degradation', 'capacity_warning', 'sla_breach')),
  severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  details JSONB DEFAULT '{}',
  source TEXT DEFAULT 'system',
  user_id UUID REFERENCES auth.users(id),
  job_id UUID REFERENCES scheduled_generation_jobs(id),
  status TEXT CHECK (status IN ('active', 'acknowledged', 'resolved')) DEFAULT 'active',
  acknowledged_at TIMESTAMPTZ,
  acknowledged_by TEXT,
  resolved_at TIMESTAMPTZ,
  resolved_by TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create alert channels configuration table
CREATE TABLE IF NOT EXISTS alert_channels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('email', 'webhook', 'slack', 'discord')),
  config JSONB NOT NULL,
  enabled BOOLEAN DEFAULT true,
  severity_filter TEXT[] DEFAULT ARRAY['high', 'critical'],
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_type TEXT NOT NULL,
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  metric_unit TEXT,
  tags JSONB DEFAULT '{}',
  recorded_at TIMESTAMPTZ DEFAULT NOW(),
  source TEXT DEFAULT 'system'
);

-- Create SLA monitoring table
CREATE TABLE IF NOT EXISTS sla_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_name TEXT NOT NULL,
  metric_name TEXT NOT NULL,
  target_value NUMERIC NOT NULL,
  actual_value NUMERIC NOT NULL,
  measurement_period TEXT NOT NULL, -- 'hourly', 'daily', 'weekly', 'monthly'
  period_start TIMESTAMPTZ NOT NULL,
  period_end TIMESTAMPTZ NOT NULL,
  status TEXT CHECK (status IN ('met', 'breached', 'at_risk')) NOT NULL,
  breach_details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE system_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE alert_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE sla_metrics ENABLE ROW LEVEL SECURITY;

-- Create policies for service role access
CREATE POLICY "Service role can manage system alerts" ON system_alerts
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage alert channels" ON alert_channels
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage performance metrics" ON performance_metrics
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage SLA metrics" ON sla_metrics
  FOR ALL USING (auth.role() = 'service_role');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_system_alerts_severity_status ON system_alerts(severity, status, created_at);
CREATE INDEX IF NOT EXISTS idx_system_alerts_type_created ON system_alerts(alert_type, created_at);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type_recorded ON performance_metrics(metric_type, metric_name, recorded_at);
CREATE INDEX IF NOT EXISTS idx_sla_metrics_service_period ON sla_metrics(service_name, period_start, period_end);

-- Create functions for alert management
CREATE OR REPLACE FUNCTION acknowledge_alert(
  p_alert_id UUID,
  p_acknowledged_by TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  alert_updated BOOLEAN := FALSE;
BEGIN
  UPDATE system_alerts
  SET 
    status = 'acknowledged',
    acknowledged_at = NOW(),
    acknowledged_by = p_acknowledged_by,
    updated_at = NOW()
  WHERE id = p_alert_id
    AND status = 'active'
  RETURNING TRUE INTO alert_updated;
  
  RETURN COALESCE(alert_updated, FALSE);
END;
$$;

CREATE OR REPLACE FUNCTION resolve_alert(
  p_alert_id UUID,
  p_resolved_by TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  alert_updated BOOLEAN := FALSE;
BEGIN
  UPDATE system_alerts
  SET 
    status = 'resolved',
    resolved_at = NOW(),
    resolved_by = p_resolved_by,
    updated_at = NOW()
  WHERE id = p_alert_id
    AND status IN ('active', 'acknowledged')
  RETURNING TRUE INTO alert_updated;
  
  RETURN COALESCE(alert_updated, FALSE);
END;
$$;

-- Create function to record performance metrics
CREATE OR REPLACE FUNCTION record_performance_metric(
  p_metric_type TEXT,
  p_metric_name TEXT,
  p_metric_value NUMERIC,
  p_metric_unit TEXT DEFAULT NULL,
  p_tags JSONB DEFAULT '{}',
  p_source TEXT DEFAULT 'system'
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
  metric_id UUID;
BEGIN
  INSERT INTO performance_metrics (
    metric_type,
    metric_name,
    metric_value,
    metric_unit,
    tags,
    source
  ) VALUES (
    p_metric_type,
    p_metric_name,
    p_metric_value,
    p_metric_unit,
    p_tags,
    p_source
  ) RETURNING id INTO metric_id;
  
  RETURN metric_id;
END;
$$;

-- Create function to check SLA breaches
CREATE OR REPLACE FUNCTION check_sla_breaches()
RETURNS TABLE(
  service_name TEXT,
  metric_name TEXT,
  target_value NUMERIC,
  actual_value NUMERIC,
  breach_percentage NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sm.service_name,
    sm.metric_name,
    sm.target_value,
    sm.actual_value,
    ROUND(((sm.actual_value - sm.target_value) / sm.target_value * 100), 2) as breach_percentage
  FROM sla_metrics sm
  WHERE sm.status = 'breached'
    AND sm.period_end >= NOW() - INTERVAL '24 hours'
  ORDER BY breach_percentage DESC;
END;
$$;

-- Create views for monitoring dashboards
CREATE OR REPLACE VIEW alert_summary AS
SELECT 
  alert_type,
  severity,
  status,
  COUNT(*) as alert_count,
  MIN(created_at) as first_alert,
  MAX(created_at) as latest_alert
FROM system_alerts
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY alert_type, severity, status
ORDER BY 
  CASE severity 
    WHEN 'critical' THEN 1 
    WHEN 'high' THEN 2 
    WHEN 'medium' THEN 3 
    WHEN 'low' THEN 4 
  END,
  alert_count DESC;

CREATE OR REPLACE VIEW performance_summary AS
SELECT 
  metric_type,
  metric_name,
  COUNT(*) as measurement_count,
  AVG(metric_value) as avg_value,
  MIN(metric_value) as min_value,
  MAX(metric_value) as max_value,
  STDDEV(metric_value) as stddev_value,
  MAX(recorded_at) as latest_measurement
FROM performance_metrics
WHERE recorded_at >= NOW() - INTERVAL '24 hours'
GROUP BY metric_type, metric_name
ORDER BY metric_type, metric_name;

CREATE OR REPLACE VIEW sla_status AS
SELECT 
  service_name,
  metric_name,
  measurement_period,
  COUNT(*) as total_measurements,
  COUNT(*) FILTER (WHERE status = 'met') as met_count,
  COUNT(*) FILTER (WHERE status = 'breached') as breach_count,
  COUNT(*) FILTER (WHERE status = 'at_risk') as at_risk_count,
  ROUND(
    (COUNT(*) FILTER (WHERE status = 'met')::NUMERIC / COUNT(*)::NUMERIC) * 100, 
    2
  ) as success_rate
FROM sla_metrics
WHERE period_end >= NOW() - INTERVAL '7 days'
GROUP BY service_name, metric_name, measurement_period
ORDER BY success_rate ASC;

-- Insert default alert channels
INSERT INTO alert_channels (name, type, config, severity_filter) VALUES
('Default Webhook', 'webhook', '{"url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "method": "POST"}', ARRAY['high', 'critical']),
('System Admin Email', 'email', '{"recipients": ["<EMAIL>"], "smtp_config": {}}', ARRAY['critical']),
('Development Slack', 'slack', '{"webhookUrl": "https://hooks.slack.com/services/YOUR/DEV/WEBHOOK"}', ARRAY['medium', 'high', 'critical'])
ON CONFLICT DO NOTHING;

-- Grant permissions
GRANT EXECUTE ON FUNCTION acknowledge_alert TO postgres, service_role;
GRANT EXECUTE ON FUNCTION resolve_alert TO postgres, service_role;
GRANT EXECUTE ON FUNCTION record_performance_metric TO postgres, service_role;
GRANT EXECUTE ON FUNCTION check_sla_breaches TO postgres, service_role;

GRANT SELECT ON alert_summary TO postgres, service_role;
GRANT SELECT ON performance_summary TO postgres, service_role;
GRANT SELECT ON sla_status TO postgres, service_role;
