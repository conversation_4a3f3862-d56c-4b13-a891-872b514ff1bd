/*
  # Add has_specific_event column to intake_forms table

  1. Changes
    - Add `has_specific_event` column to `intake_forms` table
    - Column type: BOOLEAN with default value false
    - This resolves the frontend error where the column was missing from the schema

  2. Security
    - No RLS policy changes needed as existing policies cover all columns
*/

-- Add the missing has_specific_event column to intake_forms table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'intake_forms' AND column_name = 'has_specific_event'
  ) THEN
    ALTER TABLE public.intake_forms ADD COLUMN has_specific_event BOOLEAN DEFAULT false;
  END IF;
END $$;