/*
  # Fix Weekly Check-ins and Profiles Relationship

  1. Changes
    - Add foreign key constraint between weekly_checkins.user_id and profiles.id
    - This enables direct joins between weekly_checkins and profiles tables
    - Ensures data integrity by enforcing that check-ins can only be created for valid profiles

  2. Security
    - No changes to RLS policies needed
    - Existing policies already control access appropriately
*/

-- Add foreign key constraint to enable direct relationship between weekly_checkins and profiles
DO $$
BEGIN
  -- Check if the foreign key constraint doesn't already exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'weekly_checkins_user_id_profiles_fkey'
    AND table_name = 'weekly_checkins'
  ) THEN
    -- Add the foreign key constraint
    ALTER TABLE weekly_checkins 
    ADD CONSTRAINT weekly_checkins_user_id_profiles_fkey 
    FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;
  END IF;
END $$;