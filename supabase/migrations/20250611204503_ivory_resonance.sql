/*
  # Create workout exercises table

  1. New Tables
    - `workout_exercises`
      - `id` (uuid, primary key)
      - `workout_id` (uuid, foreign key to workouts.id)
      - `exercise_id` (uuid, foreign key to exercises.id)
      - `order_in_workout` (integer)
      - `prescribed_sets` (integer)
      - `prescribed_reps_min` (integer, nullable)
      - `prescribed_reps_max` (integer, nullable)
      - `prescribed_duration_seconds` (integer, nullable)
      - `prescribed_rir` (integer, nullable)
      - `prescribed_rpe` (numeric, nullable)
      - `prescribed_tempo` (text, nullable)
      - `rest_period_seconds_after_set` (integer)
      - `notes` (text)

  2. Security
    - Enable RLS on `workout_exercises` table
    - Add policies for users to read their own workout exercises
*/

CREATE TABLE IF NOT EXISTS workout_exercises (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  workout_id uuid NOT NULL REFERENCES workouts(id) ON DELETE CASCADE,
  exercise_id uuid NOT NULL REFERENCES exercises(id),
  order_in_workout integer NOT NULL,
  prescribed_sets integer NOT NULL,
  prescribed_reps_min integer,
  prescribed_reps_max integer,
  prescribed_duration_seconds integer,
  prescribed_rir integer CHECK (prescribed_rir >= 0 AND prescribed_rir <= 10),
  prescribed_rpe numeric(3,1) CHECK (prescribed_rpe >= 1.0 AND prescribed_rpe <= 10.0),
  prescribed_tempo text,
  rest_period_seconds_after_set integer DEFAULT 60,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE workout_exercises ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users can read their own workout exercises"
  ON workout_exercises
  FOR SELECT
  TO authenticated
  USING (
    workout_id IN (
      SELECT w.id FROM workouts w
      JOIN program_weeks pw ON w.program_week_id = pw.id
      JOIN workout_programs wp ON pw.workout_program_id = wp.id
      WHERE wp.user_id = auth.uid()
    )
  );

CREATE POLICY "Service role can manage workout exercises"
  ON workout_exercises
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Trigger for updated_at
CREATE TRIGGER on_workout_exercises_updated
  BEFORE UPDATE ON workout_exercises
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Indexes
CREATE INDEX workout_exercises_workout_id_idx ON workout_exercises (workout_id);
CREATE INDEX workout_exercises_exercise_id_idx ON workout_exercises (exercise_id);
CREATE INDEX workout_exercises_order_idx ON workout_exercises (order_in_workout);