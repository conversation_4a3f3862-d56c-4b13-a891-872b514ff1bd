-- Add RLS policy to allow anon role to insert profiles during user registration
-- This is needed for the handle_new_user trigger to work during signup

CREATE POLICY "Allow anon to insert profile during registration"
  ON public.profiles
  FOR INSERT
  TO anon
  WITH CHECK (true);

-- Note: This policy allows anon users to insert, but the handle_new_user trigger
-- ensures that profiles are only created with the correct user ID from auth.users
-- The trigger runs in the context of the signup process, so it's secure