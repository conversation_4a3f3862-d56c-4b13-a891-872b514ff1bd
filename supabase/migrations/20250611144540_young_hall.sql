/*
  # Create intake forms table

  1. New Tables
    - `intake_forms`
      - `id` (uuid, primary key)
      - `user_id` (uuid, unique, foreign key to auth.users.id)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
      - `submitted_at` (timestamp, nullable)
      - `status` (text enum: draft, submitted, processing, completed)
      - Personal basics: gender, age, height_cm, weight_kg
      - Goals & experience: primary_fitness_goal, training_experience_level, goal_timeline_months
      - Equipment: equipment_access_type, available_equipment (jsonb), custom_equipment_notes
      - Schedule: training_days_per_week, preferred_training_days, preferred_session_duration_minutes
      - Preferences: injuries_limitations, training_preferences_notes

  2. Security
    - Enable RLS on `intake_forms` table
    - Add policy for users to manage their own intake form
    - Add policy for service role to insert profiles

  3. Triggers
    - Auto-update `updated_at` timestamp
    - Update profile gender preference when intake is submitted
*/

CREATE TABLE IF NOT EXISTS public.intake_forms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID UNIQUE NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  submitted_at TIMESTAMPTZ,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'processing', 'completed')),
  
  -- Step 1: Personal Basics
  gender TEXT CHECK (gender IN ('Woman', 'Man')),
  age INTEGER CHECK (age > 0 AND age < 120),
  height_cm INTEGER CHECK (height_cm > 50 AND height_cm < 300),
  weight_kg NUMERIC(5,2) CHECK (weight_kg > 20 AND weight_kg < 500),
  
  -- Step 2: Fitness Goals & Experience
  primary_fitness_goal TEXT,
  training_experience_level TEXT CHECK (training_experience_level IN ('Beginner', 'Intermediate', 'Advanced')),
  goal_timeline_months INTEGER,
  
  -- Step 3: Equipment Access
  equipment_access_type TEXT CHECK (equipment_access_type IN ('Full Gym', 'Home Gym Basic', 'Bodyweight Only')),
  available_equipment JSONB,
  custom_equipment_notes TEXT,
  
  -- Step 4: Schedule & Availability
  training_days_per_week INTEGER CHECK (training_days_per_week >= 2 AND training_days_per_week <= 6),
  preferred_training_days TEXT[],
  preferred_session_duration_minutes INTEGER,
  
  -- Step 5: Injuries & Preferences
  injuries_limitations TEXT,
  training_preferences_notes TEXT
);

-- Enable RLS
ALTER TABLE public.intake_forms ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can manage their own intake form"
  ON public.intake_forms FOR ALL
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Updated at trigger
CREATE TRIGGER on_intake_form_updated
  BEFORE UPDATE ON public.intake_forms
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Trigger to update profiles.gender_preference upon intake submission
CREATE OR REPLACE FUNCTION public.update_profile_gender_from_intake()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'submitted' AND NEW.gender IS NOT NULL THEN
    UPDATE public.profiles
    SET gender_preference = NEW.gender
    WHERE id = NEW.user_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_intake_submitted_update_profile_gender
  AFTER UPDATE OF status ON public.intake_forms
  FOR EACH ROW
  WHEN (OLD.status IS DISTINCT FROM 'submitted' AND NEW.status = 'submitted')
  EXECUTE FUNCTION public.update_profile_gender_from_intake();