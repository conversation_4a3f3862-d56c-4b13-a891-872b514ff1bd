-- Production Setup Migration
-- This migration sets up production-specific configurations

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create production-specific indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_verified 
ON auth.users (email) WHERE email_confirmed_at IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_workout_logs_user_date 
ON workout_logs (user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_conversation_timestamp 
ON messages (conversation_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_tracking_user_type_date 
ON progress_tracking (user_id, type, date DESC);

-- Create production monitoring views
CREATE OR REPLACE VIEW system_health AS
SELECT 
  'database' as component,
  CASE 
    WHEN pg_is_in_recovery() THEN 'replica'
    ELSE 'primary'
  END as status,
  pg_database_size(current_database()) as size_bytes,
  (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
  now() as checked_at;

-- Create audit log table for production
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  table_name TEXT NOT NULL,
  operation TEXT NOT NULL,
  old_data JSONB,
  new_data JSONB,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'DELETE' THEN
    INSERT INTO audit_logs (table_name, operation, old_data, user_id)
    VALUES (TG_TABLE_NAME, TG_OP, to_jsonb(OLD), auth.uid());
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_logs (table_name, operation, old_data, new_data, user_id)
    VALUES (TG_TABLE_NAME, TG_OP, to_jsonb(OLD), to_jsonb(NEW), auth.uid());
    RETURN NEW;
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO audit_logs (table_name, operation, new_data, user_id)
    VALUES (TG_TABLE_NAME, TG_OP, to_jsonb(NEW), auth.uid());
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_users_trigger
  AFTER INSERT OR UPDATE OR DELETE ON users
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_workout_programs_trigger
  AFTER INSERT OR UPDATE OR DELETE ON workout_programs
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Create production-specific RLS policies
-- Rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  identifier TEXT NOT NULL,
  requests INTEGER DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier_window 
ON rate_limits (identifier, window_start);

-- Create function to clean up old rate limit records
CREATE OR REPLACE FUNCTION cleanup_rate_limits()
RETURNS void AS $$
BEGIN
  DELETE FROM rate_limits 
  WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create scheduled job to clean up rate limits (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-rate-limits', '*/5 * * * *', 'SELECT cleanup_rate_limits();');

-- Create production backup verification function
CREATE OR REPLACE FUNCTION verify_backup_integrity()
RETURNS TABLE (
  table_name TEXT,
  row_count BIGINT,
  last_updated TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    schemaname || '.' || tablename as table_name,
    n_tup_ins + n_tup_upd + n_tup_del as row_count,
    GREATEST(
      COALESCE(last_vacuum, '1970-01-01'::timestamp),
      COALESCE(last_autovacuum, '1970-01-01'::timestamp),
      COALESCE(last_analyze, '1970-01-01'::timestamp),
      COALESCE(last_autoanalyze, '1970-01-01'::timestamp)
    ) as last_updated
  FROM pg_stat_user_tables
  ORDER BY row_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create production metrics collection
CREATE TABLE IF NOT EXISTS system_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  tags JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_system_metrics_name_time 
ON system_metrics (metric_name, created_at DESC);

-- Function to collect database metrics
CREATE OR REPLACE FUNCTION collect_db_metrics()
RETURNS void AS $$
BEGIN
  -- Database size
  INSERT INTO system_metrics (metric_name, metric_value)
  VALUES ('database_size_bytes', pg_database_size(current_database()));
  
  -- Active connections
  INSERT INTO system_metrics (metric_name, metric_value)
  VALUES ('active_connections', (
    SELECT count(*) FROM pg_stat_activity WHERE state = 'active'
  ));
  
  -- Cache hit ratio
  INSERT INTO system_metrics (metric_name, metric_value)
  VALUES ('cache_hit_ratio', (
    SELECT 
      CASE 
        WHEN blks_read + blks_hit = 0 THEN 0
        ELSE blks_hit::float / (blks_read + blks_hit)
      END
    FROM pg_stat_database 
    WHERE datname = current_database()
  ));
  
  -- Table sizes
  INSERT INTO system_metrics (metric_name, metric_value, tags)
  SELECT 
    'table_size_bytes',
    pg_total_relation_size(schemaname||'.'||tablename),
    jsonb_build_object('table', schemaname||'.'||tablename)
  FROM pg_tables 
  WHERE schemaname = 'public';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create production-specific security policies
-- Ensure RLS is enabled on all user tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Create comprehensive RLS policies for production
CREATE POLICY "Users can only access their own data" ON users
  FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can only access their workout programs" ON workout_programs
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their workout logs" ON workout_logs
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their progress data" ON progress_tracking
  FOR ALL USING (auth.uid() = user_id);

-- Message policies (users can see messages in their conversations)
CREATE POLICY "Users can access their messages" ON messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM conversations 
      WHERE conversations.id = messages.conversation_id 
      AND (
        conversations.client_id = auth.uid() OR 
        conversations.coach_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can send messages in their conversations" ON messages
  FOR INSERT WITH CHECK (
    sender_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM conversations 
      WHERE conversations.id = conversation_id 
      AND (
        conversations.client_id = auth.uid() OR 
        conversations.coach_id = auth.uid()
      )
    )
  );

-- Create production health check function
CREATE OR REPLACE FUNCTION health_check()
RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  result := jsonb_build_object(
    'status', 'healthy',
    'timestamp', now(),
    'database', jsonb_build_object(
      'connected', true,
      'size_mb', round(pg_database_size(current_database()) / 1024.0 / 1024.0, 2),
      'active_connections', (SELECT count(*) FROM pg_stat_activity WHERE state = 'active')
    ),
    'version', version()
  );
  
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  RETURN jsonb_build_object(
    'status', 'unhealthy',
    'error', SQLERRM,
    'timestamp', now()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON system_health TO authenticated;
GRANT EXECUTE ON FUNCTION health_check() TO anon, authenticated;

-- Create production-ready storage policies
INSERT INTO storage.buckets (id, name, public) 
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public) 
VALUES ('progress-photos', 'progress-photos', false)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public) 
VALUES ('exercise-media', 'exercise-media', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatars" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own avatars" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'avatars' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own avatars" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'avatars' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Progress photos policies
CREATE POLICY "Users can access their own progress photos" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'progress-photos' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can upload their own progress photos" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'progress-photos' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Exercise media policies (public read, admin write)
CREATE POLICY "Exercise media is publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'exercise-media');

-- Add production-specific constraints
ALTER TABLE users ADD CONSTRAINT users_email_format 
  CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE workout_logs ADD CONSTRAINT workout_logs_duration_positive 
  CHECK (duration > 0);

ALTER TABLE progress_tracking ADD CONSTRAINT progress_tracking_value_positive 
  CHECK (value > 0);

-- Create production notification
DO $$
BEGIN
  RAISE NOTICE 'Production database setup completed successfully';
  RAISE NOTICE 'Database size: % MB', round(pg_database_size(current_database()) / 1024.0 / 1024.0, 2);
  RAISE NOTICE 'Total tables: %', (SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public');
  RAISE NOTICE 'RLS enabled on all user tables';
  RAISE NOTICE 'Audit logging configured';
  RAISE NOTICE 'Performance indexes created';
END $$;
