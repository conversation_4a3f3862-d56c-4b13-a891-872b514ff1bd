/*
  # Create program weeks table

  1. New Tables
    - `program_weeks`
      - `id` (uuid, primary key)
      - `workout_program_id` (uuid, foreign key to workout_programs.id)
      - `week_number` (integer)
      - `notes` (text)

  2. Security
    - Enable RLS on `program_weeks` table
    - Add policies for users to read their own program weeks
*/

CREATE TABLE IF NOT EXISTS program_weeks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  workout_program_id uuid NOT NULL REFERENCES workout_programs(id) ON DELETE CASCADE,
  week_number integer NOT NULL CHECK (week_number >= 1 AND week_number <= 12),
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE (workout_program_id, week_number)
);

-- Enable RLS
ALTER TABLE program_weeks ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users can read their own program weeks"
  ON program_weeks
  FOR SELECT
  TO authenticated
  USING (
    workout_program_id IN (
      SELECT id FROM workout_programs WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Service role can manage program weeks"
  ON program_weeks
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Trigger for updated_at
CREATE TRIGGER on_program_weeks_updated
  BEFORE UPDATE ON program_weeks
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Indexes
CREATE INDEX program_weeks_workout_program_id_idx ON program_weeks (workout_program_id);
CREATE INDEX program_weeks_week_number_idx ON program_weeks (week_number);