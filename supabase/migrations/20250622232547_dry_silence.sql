/*
  # Add foreign key relationship between weekly_checkins and profiles

  1. Changes
    - Add foreign key constraint from weekly_checkins.user_id to profiles.id
    - This enables Supabase PostgREST to join these tables using the select syntax

  2. Security
    - No changes to existing RLS policies
    - Maintains existing data integrity
*/

-- Add foreign key constraint to enable direct relationship between weekly_checkins and profiles
DO $$
BEGIN
  -- Check if the foreign key constraint doesn't already exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'weekly_checkins_user_id_profiles_fkey'
    AND table_name = 'weekly_checkins'
  ) THEN
    -- Add the foreign key constraint
    ALTER TABLE weekly_checkins 
    ADD CONSTRAINT weekly_checkins_user_id_profiles_fkey 
    FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;
  END IF;
END $$;