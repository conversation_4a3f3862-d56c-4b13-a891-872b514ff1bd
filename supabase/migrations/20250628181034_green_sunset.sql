/*
  # Add coach_reviewed_at column to weekly_checkins table

  1. Schema Changes
    - Add `coach_reviewed_at` column to `weekly_checkins` table
    - Column type: TIMESTAMPTZ (timestamp with timezone)
    - Nullable: true (coaches may not have reviewed all check-ins yet)
    - Default: null

  2. Purpose
    - Track when a coach has reviewed a client's weekly check-in
    - Used for filtering unreviewed check-ins in coach dashboard
    - Helps coaches manage their workflow and prioritize feedback

  3. Index
    - Add index on coach_reviewed_at for efficient querying of unreviewed check-ins
*/

-- Add the coach_reviewed_at column to weekly_checkins table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'weekly_checkins' AND column_name = 'coach_reviewed_at'
  ) THEN
    ALTER TABLE weekly_checkins ADD COLUMN coach_reviewed_at timestamptz;
  END IF;
END $$;

-- Add index for efficient querying of unreviewed check-ins
CREATE INDEX IF NOT EXISTS weekly_checkins_coach_reviewed_at_idx 
ON weekly_checkins (coach_reviewed_at);

-- Add index for efficient querying of unreviewed check-ins by user
CREATE INDEX IF NOT EXISTS weekly_checkins_user_coach_reviewed_idx 
ON weekly_checkins (user_id, coach_reviewed_at);