/*
  # Fix Premature OpenAI API Call Issue

  1. Problem
    - Database trigger automatically marks intake as 'completed' based solely on field population
    - This triggers OpenAI API calls before user clicks submit button on step 5
    - User intent (explicit submission) is not considered in completion logic

  2. Solution
    - Add intake_explicitly_submitted column to track user submission intent
    - Update trigger to require BOTH field completeness AND explicit submission
    - Maintain existing field validation requirements
    - Preserve draft saving functionality for step navigation

  3. Changes
    - Add intake_explicitly_submitted BOOLEAN column to profiles table
    - Update update_intake_completion_status() function with dual validation logic
    - Set default FALSE for new column (existing users unaffected)
    - Add index for performance optimization

  4. Rollback
    - To rollback: ALTER TABLE profiles DROP COLUMN intake_explicitly_submitted;
    - Restore original trigger function logic
*/

-- Add explicit submission tracking column to profiles table
ALTER TABLE profiles 
ADD COLUMN intake_explicitly_submitted BOOLEAN DEFAULT FALSE NOT NULL;

-- Add index for performance optimization on intake status queries
CREATE INDEX IF NOT EXISTS profiles_intake_submission_idx 
ON profiles (intake_status, intake_explicitly_submitted);

-- Update existing completed intakes to have explicit submission flag set
-- This ensures existing users are not affected by the change
UPDATE profiles 
SET intake_explicitly_submitted = TRUE 
WHERE intake_status = 'completed';

-- Create or replace the function to update intake completion status with dual validation
CREATE OR REPLACE FUNCTION update_intake_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If the status was already 'completed', keep it completed and exit
  IF OLD.intake_status = 'completed' THEN
    NEW.intake_status = 'completed';
    RETURN NEW;
  END IF;

  -- DUAL VALIDATION: Check BOTH field completeness AND explicit submission
  -- Only mark as completed when user has explicitly submitted AND all fields are complete
  IF NEW.intake_explicitly_submitted = TRUE  -- Condition 1: User clicked submit button
     AND NEW.intake_gender IS NOT NULL       -- Condition 2: All required fields populated
     AND NEW.age IS NOT NULL
     AND NEW.height_cm IS NOT NULL
     AND NEW.weight_kg IS NOT NULL
     AND NEW.primary_fitness_goal IS NOT NULL
     AND array_length(NEW.primary_fitness_goal, 1) > 0
     AND NEW.training_experience_level IS NOT NULL
     AND NEW.equipment_access_type IS NOT NULL
     AND NEW.training_days_per_week IS NOT NULL
     AND NEW.preferred_session_duration_minutes IS NOT NULL
     AND (
       -- Conditional validation for goal_timeline_months based on has_specific_event
       (NEW.has_specific_event = true AND NEW.goal_timeline_months IS NOT NULL) OR
       (NEW.has_specific_event = false OR NEW.has_specific_event IS NULL)
     ) THEN

    -- ONLY NOW mark as completed (this triggers OpenAI API via webhook)
    NEW.intake_status = 'completed';
    NEW.intake_completed_at = now();

  ELSIF NEW.intake_explicitly_submitted = FALSE  -- User hasn't submitted yet
        AND (NEW.intake_gender IS NOT NULL        -- But has some data (draft state)
        OR NEW.age IS NOT NULL 
        OR NEW.height_cm IS NOT NULL 
        OR NEW.weight_kg IS NOT NULL 
        OR (NEW.primary_fitness_goal IS NOT NULL AND array_length(NEW.primary_fitness_goal, 1) > 0)
        OR NEW.training_experience_level IS NOT NULL 
        OR NEW.equipment_access_type IS NOT NULL 
        OR NEW.training_days_per_week IS NOT NULL 
        OR NEW.preferred_session_duration_minutes IS NOT NULL) THEN

    -- Mark as in progress (draft state - step navigation)
    NEW.intake_status = 'in_progress';
    
  ELSE
    -- No data present
    NEW.intake_status = 'not_started';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION update_intake_completion_status() OWNER TO postgres;

-- Ensure the trigger exists and uses the updated function
DROP TRIGGER IF EXISTS on_profile_intake_updated ON profiles;
CREATE TRIGGER on_profile_intake_updated
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_intake_completion_status();

-- Add helpful comments
COMMENT ON COLUMN profiles.intake_explicitly_submitted IS 'Tracks whether user has explicitly submitted the intake form via submit button (vs just having complete data)';
COMMENT ON FUNCTION update_intake_completion_status() IS 'Updates intake completion status requiring BOTH field completeness AND explicit submission to prevent premature OpenAI API calls';
COMMENT ON TRIGGER on_profile_intake_updated ON profiles IS 'Triggers intake completion status update with dual validation (data + user intent)';
