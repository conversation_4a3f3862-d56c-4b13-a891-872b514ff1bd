/*
  # Add role column to profiles table

  1. Changes
    - Add `role` column to `profiles` table
    - Set default role as 'client' for new users
    - Add constraint to ensure valid role values
    - Update existing users to have 'client' role by default

  2. Security
    - Existing RLS policies will automatically apply to the new column
    - No additional policies needed for basic role functionality

  3. Functions
    - Update `handle_new_user` function to set default role
*/

-- Add role column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS role TEXT 
CHECK (role IN ('client', 'coach', 'admin')) 
DEFAULT 'client';

-- Update existing users to have 'client' role if role is null
UPDATE public.profiles 
SET role = 'client' 
WHERE role IS NULL;

-- Make role column NOT NULL after setting defaults
ALTER TABLE public.profiles 
ALTER COLUMN role SET NOT NULL;

-- Update the handle_new_user function to include role
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    full_name, 
    gender_preference,
    height_unit,
    weight_unit,
    intake_status,
    role
  )
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'),
    'Neutral',
    'cm',
    'kg',
    'not_started',
    'client'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION public.handle_new_user() OWNER TO postgres;

-- Add index for role column for efficient queries
CREATE INDEX IF NOT EXISTS profiles_role_idx ON public.profiles (role);

-- Add helpful comment
COMMENT ON COLUMN public.profiles.role IS 'User role in the system (client, coach, admin)';