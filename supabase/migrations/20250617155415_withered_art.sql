/*
  # Add RLS policies for coach role

  1. New Policies
    - Allow coaches to read all client profiles
    - Allow coaches to read all workout programs
    - Allow coaches to update workout programs (status, notes, etc.)
    - Allow coaches to read program weeks, workouts, and workout exercises

  2. Security
    - Coaches can only read/update data, not delete
    - Coaches cannot access other coaches' data
    - Maintains existing client access controls
*/

-- Allow coaches to read all client profiles
CREATE POLICY "Coaches can read client profiles"
  ON public.profiles
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles coach_profile 
      WHERE coach_profile.id = auth.uid() 
      AND coach_profile.role = 'coach'
    )
    AND role = 'client'
  );

-- Allow coaches to read all workout programs
CREATE POLICY "Coaches can read all workout programs"
  ON public.workout_programs
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles coach_profile 
      WHERE coach_profile.id = auth.uid() 
      AND coach_profile.role = 'coach'
    )
  );

-- Allow coaches to update workout programs
CREATE POLICY "Coaches can update workout programs"
  ON public.workout_programs
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles coach_profile 
      WHERE coach_profile.id = auth.uid() 
      AND coach_profile.role = 'coach'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles coach_profile 
      WHERE coach_profile.id = auth.uid() 
      AND coach_profile.role = 'coach'
    )
  );

-- Allow coaches to read program weeks
CREATE POLICY "Coaches can read program weeks"
  ON public.program_weeks
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles coach_profile 
      WHERE coach_profile.id = auth.uid() 
      AND coach_profile.role = 'coach'
    )
  );

-- Allow coaches to read workouts
CREATE POLICY "Coaches can read workouts"
  ON public.workouts
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles coach_profile 
      WHERE coach_profile.id = auth.uid() 
      AND coach_profile.role = 'coach'
    )
  );

-- Allow coaches to read workout exercises
CREATE POLICY "Coaches can read workout exercises"
  ON public.workout_exercises
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles coach_profile 
      WHERE coach_profile.id = auth.uid() 
      AND coach_profile.role = 'coach'
    )
  );