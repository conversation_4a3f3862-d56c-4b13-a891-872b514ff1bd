/*
  # Create workout programs table

  1. New Tables
    - `workout_programs`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users.id)
      - `name` (text)
      - `description` (text)
      - `duration_weeks` (integer, default 4)
      - `status` (text enum)
      - `ai_prompt_data` (jsonb)
      - `generated_by_ai_at` (timestamp)
      - `coach_id` (uuid, nullable foreign key to profiles.id)
      - `coach_reviewed_at` (timestamp, nullable)
      - `coach_notes_for_client` (text, nullable)
      - `client_start_date` (date, nullable)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `workout_programs` table
    - Add policies for users to read their own programs
    - Add policies for coaches to read programs pending review
*/

CREATE TABLE IF NOT EXISTS workout_programs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  duration_weeks integer DEFAULT 4,
  status text CHECK (status IN (
    'ai_generated_pending_review',
    'coach_edited',
    'coach_approved',
    'active_by_client',
    'completed_by_client',
    'archived'
  )) DEFAULT 'ai_generated_pending_review',
  ai_prompt_data jsonb,
  generated_by_ai_at timestamptz,
  coach_id uuid REFERENCES public.profiles(id),
  coach_reviewed_at timestamptz,
  coach_notes_for_client text,
  client_start_date date,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE workout_programs ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users can read their own workout programs"
  ON workout_programs
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own workout programs"
  ON workout_programs
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service role can manage workout programs"
  ON workout_programs
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Trigger for updated_at
CREATE TRIGGER on_workout_programs_updated
  BEFORE UPDATE ON workout_programs
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Indexes
CREATE INDEX workout_programs_user_id_idx ON workout_programs (user_id);
CREATE INDEX workout_programs_status_idx ON workout_programs (status);
CREATE INDEX workout_programs_coach_id_idx ON workout_programs (coach_id);