/*
  # Update profiles table and intake completion trigger

  1. Changes
    - Add intake completion status tracking to profiles
    - <PERSON><PERSON> trigger to update intake status when completed
    - Updated logic to conditionally check goal_timeline_months only when has_specific_event is true

  2. Security
    - Update existing RLS policies if needed
*/

-- Add intake completion status if not exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'intake_completed_at'
  ) THEN
    ALTER TABLE profiles ADD COLUMN intake_completed_at timestamptz;
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'intake_status'
  ) THEN
    ALTER TABLE profiles ADD COLUMN intake_status text CHECK (intake_status IN ('not_started', 'in_progress', 'completed')) DEFAULT 'not_started';
  END IF;
END $$;

-- Create function to update intake completion status
CREATE OR REPLACE FUNCTION update_intake_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if intake is completed (has all required fields)
  -- goal_timeline_months is only required if has_specific_event is true
  IF NEW.intake_gender IS NOT NULL 
     AND NEW.age IS NOT NULL 
     AND NEW.height_cm IS NOT NULL 
     AND NEW.weight_kg IS NOT NULL 
     AND NEW.primary_fitness_goal IS NOT NULL 
     AND array_length(NEW.primary_fitness_goal, 1) > 0
     AND NEW.training_experience_level IS NOT NULL 
     AND NEW.equipment_access_type IS NOT NULL 
     AND NEW.training_days_per_week IS NOT NULL 
     AND NEW.preferred_session_duration_minutes IS NOT NULL 
     AND (
       -- If has_specific_event is true, goal_timeline_months must be provided
       (NEW.has_specific_event = true AND NEW.goal_timeline_months IS NOT NULL) OR
       -- If has_specific_event is false or null, goal_timeline_months is not required
       (NEW.has_specific_event = false OR NEW.has_specific_event IS NULL)
     )
     AND (OLD.intake_status IS NULL OR OLD.intake_status != 'completed') THEN
    
    NEW.intake_status = 'completed';
    NEW.intake_completed_at = now();
    
    -- Note: AI program generation will be triggered via Database Webhook
    -- configured in Supabase Dashboard to call the generate-workout-program Edge Function
    
  ELSIF NEW.intake_gender IS NOT NULL 
        OR NEW.age IS NOT NULL 
        OR NEW.height_cm IS NOT NULL 
        OR NEW.weight_kg IS NOT NULL 
        OR (NEW.primary_fitness_goal IS NOT NULL AND array_length(NEW.primary_fitness_goal, 1) > 0)
        OR NEW.training_experience_level IS NOT NULL 
        OR NEW.equipment_access_type IS NOT NULL 
        OR NEW.training_days_per_week IS NOT NULL 
        OR NEW.preferred_session_duration_minutes IS NOT NULL THEN
    
    NEW.intake_status = 'in_progress';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION update_intake_completion_status() OWNER TO postgres;

-- Create or replace the trigger
DROP TRIGGER IF EXISTS on_profile_intake_updated ON profiles;
CREATE TRIGGER on_profile_intake_updated
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_intake_completion_status();

-- Add index for intake status
CREATE INDEX IF NOT EXISTS profiles_intake_status_idx ON profiles (intake_status);