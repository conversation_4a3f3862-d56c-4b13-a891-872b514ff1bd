-- Configure the service role key for cron jobs
-- This needs to be set with the actual service role key from the Supabase project

-- Create a secure function to set the service role key
CREATE OR REPLACE FUNCTION configure_service_role_key(p_key TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Set the service role key as a database setting
  PERFORM set_config('app.settings.service_role_key', p_key, false);
  
  -- Also set it in the postgresql.conf equivalent for persistence
  -- This requires superuser privileges, so we'll use ALTER SYSTEM
  EXECUTE format('ALTER SYSTEM SET app.settings.service_role_key = %L', p_key);
  
  -- Reload configuration
  SELECT pg_reload_conf();
END;
$$;

-- Grant execute permission to postgres role
GRANT EXECUTE ON FUNCTION configure_service_role_key TO postgres;

-- Create a function to test cron job connectivity
CREATE OR REPLACE FUNCTION test_cron_connectivity()
RETURNS TABLE(
  job_name TEXT,
  test_result TEXT,
  response_status INTEGER,
  error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  service_key TEXT;
  test_jobs TEXT[] := ARRAY[
    'daily-program-scheduler',
    'auto-approve-programs', 
    'program-transition-manager',
    'system-health-monitor'
  ];
  job_name_var TEXT;
  response_record RECORD;
BEGIN
  -- Get the service role key
  service_key := current_setting('app.settings.service_role_key', true);
  
  IF service_key IS NULL OR service_key = 'CONFIGURE_SERVICE_ROLE_KEY' THEN
    job_name := 'configuration';
    test_result := 'FAILED';
    response_status := 0;
    error_message := 'Service role key not configured';
    RETURN NEXT;
    RETURN;
  END IF;
  
  -- Test each Edge Function endpoint
  FOREACH job_name_var IN ARRAY test_jobs
  LOOP
    BEGIN
      -- Make a test request to each function
      SELECT INTO response_record supabase_functions.http_request(
        'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/' || job_name_var,
        'POST',
        '{"Content-Type":"application/json","Authorization":"Bearer ' || service_key || '"}',
        '{"source":"test","timestamp":"' || now()::text || '"}',
        '10000'
      );
      
      job_name := job_name_var;
      test_result := 'SUCCESS';
      response_status := response_record.status_code;
      error_message := '';
      
    EXCEPTION WHEN OTHERS THEN
      job_name := job_name_var;
      test_result := 'FAILED';
      response_status := 0;
      error_message := SQLERRM;
    END;
    
    RETURN NEXT;
  END LOOP;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION test_cron_connectivity TO postgres;

-- Create a function to manually trigger cron jobs for testing
CREATE OR REPLACE FUNCTION trigger_cron_job(p_job_name TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  service_key TEXT;
  response_record RECORD;
  start_time TIMESTAMPTZ;
  end_time TIMESTAMPTZ;
  duration_ms INTEGER;
BEGIN
  start_time := clock_timestamp();
  
  -- Get the service role key
  service_key := current_setting('app.settings.service_role_key', true);
  
  IF service_key IS NULL OR service_key = 'CONFIGURE_SERVICE_ROLE_KEY' THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'Service role key not configured'
    );
  END IF;
  
  -- Log the start of execution
  PERFORM log_cron_execution(p_job_name, 'started');
  
  BEGIN
    -- Make the request to the Edge Function
    SELECT INTO response_record supabase_functions.http_request(
      'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/' || p_job_name,
      'POST',
      '{"Content-Type":"application/json","Authorization":"Bearer ' || service_key || '"}',
      '{"source":"manual_trigger","timestamp":"' || now()::text || '"}',
      '60000'
    );
    
    end_time := clock_timestamp();
    duration_ms := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    -- Log successful completion
    PERFORM log_cron_execution(
      p_job_name, 
      'completed',
      jsonb_build_object(
        'status_code', response_record.status_code,
        'response', response_record.content
      ),
      NULL,
      duration_ms
    );
    
    RETURN jsonb_build_object(
      'success', true,
      'status_code', response_record.status_code,
      'response', response_record.content,
      'duration_ms', duration_ms
    );
    
  EXCEPTION WHEN OTHERS THEN
    end_time := clock_timestamp();
    duration_ms := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    -- Log the failure
    PERFORM log_cron_execution(
      p_job_name,
      'failed',
      NULL,
      SQLERRM,
      duration_ms
    );
    
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM,
      'duration_ms', duration_ms
    );
  END;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION trigger_cron_job TO postgres;

-- Create a function to get cron job statistics
CREATE OR REPLACE FUNCTION get_cron_job_stats(p_days INTEGER DEFAULT 7)
RETURNS TABLE(
  job_name TEXT,
  total_executions BIGINT,
  successful_executions BIGINT,
  failed_executions BIGINT,
  success_rate NUMERIC,
  avg_duration_ms NUMERIC,
  last_execution TIMESTAMPTZ,
  last_status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cjl.job_name,
    COUNT(*) as total_executions,
    COUNT(*) FILTER (WHERE cjl.status = 'completed') as successful_executions,
    COUNT(*) FILTER (WHERE cjl.status = 'failed') as failed_executions,
    ROUND(
      (COUNT(*) FILTER (WHERE cjl.status = 'completed')::NUMERIC / COUNT(*)::NUMERIC) * 100, 
      2
    ) as success_rate,
    ROUND(AVG(cjl.execution_duration_ms), 2) as avg_duration_ms,
    MAX(cjl.execution_time) as last_execution,
    (SELECT status FROM cron_job_logs WHERE job_name = cjl.job_name ORDER BY execution_time DESC LIMIT 1) as last_status
  FROM cron_job_logs cjl
  WHERE cjl.execution_time >= NOW() - INTERVAL '%s days' % p_days
  GROUP BY cjl.job_name
  ORDER BY cjl.job_name;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_cron_job_stats TO postgres;
