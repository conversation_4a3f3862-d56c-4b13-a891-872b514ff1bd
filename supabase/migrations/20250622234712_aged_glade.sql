/*
  # Create Webhook for Automatic Feedback Generation

  1. Changes
    - Create a database webhook that triggers the generate-feedback Edge Function
    - Webhook activates when a new weekly check-in is inserted
    - This automates the process of generating AI feedback for client check-ins

  2. Security
    - Webhook uses service role key for proper authorization
    - Only triggers on INSERT operations to weekly_checkins table
*/

-- Create a webhook to trigger the generate-feedback function when a new check-in is submitted
CREATE OR REPLACE FUNCTION generate_workout_program_webhook()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM
    net.http_post(
      url := current_setting('app.settings.supabase_url') || '/functions/v1/generate-feedback',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key')
      ),
      body := jsonb_build_object(
        'type', TG_OP,
        'table', TG_TABLE_NAME,
        'record', row_to_json(NEW)
      )
    );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger on weekly_checkins table
DROP TRIGGER IF EXISTS generate_feedback_webhook ON weekly_checkins;
CREATE TRIGGER generate_feedback_webhook
  AFTER INSERT ON weekly_checkins
  FOR EACH ROW
  EXECUTE FUNCTION generate_workout_program_webhook();

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION generate_workout_program_webhook() OWNER TO postgres;

-- Add helpful comment
COMMENT ON FUNCTION generate_workout_program_webhook() IS 'Triggers the generate-feedback Edge Function when a new weekly check-in is submitted';