/*
  # Add Push Notification Support

  1. Add push notification fields to profiles table
    - `push_token` (text) - Expo push token
    - `push_token_updated_at` (timestamptz) - When token was last updated
    - `push_notifications_enabled` (boolean) - User preference for notifications

  2. Create push notification logs table for tracking sent notifications
    - Track notification delivery and engagement

  3. Add RLS policies for push notification data
*/

-- Add push notification and device tracking fields to profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS push_token TEXT,
ADD COLUMN IF NOT EXISTS push_token_updated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS push_notifications_enabled BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS device_id TEXT,
ADD COLUMN IF NOT EXISTS device_info JSONB,
ADD COLUMN IF NOT EXISTS last_seen_at TIMESTAMPTZ;

-- Create push notification logs table
CREATE TABLE IF NOT EXISTS push_notification_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL CHECK (notification_type IN (
    'message', 
    'workout_reminder', 
    'coach_feedback', 
    'program_update', 
    'check_in_reminder'
  )),
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  data JSONB,
  push_token TEXT NOT NULL,
  sent_at TIMESTAMPTZ DEFAULT NOW(),
  delivery_status TEXT CHECK (delivery_status IN ('sent', 'delivered', 'failed')) DEFAULT 'sent',
  delivery_response JSONB,
  opened_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on push notification logs
ALTER TABLE push_notification_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for push notification logs
CREATE POLICY "Users can read their own notification logs"
  ON push_notification_logs
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Service role can manage notification logs"
  ON push_notification_logs
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_push_notification_logs_user_id ON push_notification_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_push_notification_logs_sent_at ON push_notification_logs (sent_at);
CREATE INDEX IF NOT EXISTS idx_push_notification_logs_type ON push_notification_logs (notification_type);
CREATE INDEX IF NOT EXISTS idx_profiles_push_token ON public.profiles (push_token) WHERE push_token IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_profiles_device_id ON public.profiles (device_id) WHERE device_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_profiles_last_seen ON public.profiles (last_seen_at);

-- Function to send push notification (to be called from Edge Functions)
CREATE OR REPLACE FUNCTION send_push_notification(
  p_user_id UUID,
  p_notification_type TEXT,
  p_title TEXT,
  p_body TEXT,
  p_data JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_push_token TEXT;
  v_notifications_enabled BOOLEAN;
  v_log_id UUID;
BEGIN
  -- Get user's push token and notification preference
  SELECT push_token, push_notifications_enabled
  INTO v_push_token, v_notifications_enabled
  FROM public.profiles
  WHERE id = p_user_id;

  -- Check if user has push notifications enabled and has a token
  IF v_push_token IS NULL OR v_notifications_enabled = FALSE THEN
    RAISE NOTICE 'Push notifications not available for user %', p_user_id;
    RETURN NULL;
  END IF;

  -- Log the notification attempt
  INSERT INTO push_notification_logs (
    user_id,
    notification_type,
    title,
    body,
    data,
    push_token
  )
  VALUES (
    p_user_id,
    p_notification_type,
    p_title,
    p_body,
    p_data,
    v_push_token
  )
  RETURNING id INTO v_log_id;

  -- Return the log ID for tracking
  RETURN v_log_id;
END;
$$;

-- Function to update notification delivery status
CREATE OR REPLACE FUNCTION update_notification_delivery_status(
  p_log_id UUID,
  p_delivery_status TEXT,
  p_delivery_response JSONB DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE push_notification_logs
  SET 
    delivery_status = p_delivery_status,
    delivery_response = p_delivery_response
  WHERE id = p_log_id;
END;
$$;

-- Function to mark notification as opened
CREATE OR REPLACE FUNCTION mark_notification_opened(
  p_log_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE push_notification_logs
  SET opened_at = NOW()
  WHERE id = p_log_id AND opened_at IS NULL;
END;
$$;

-- Function to get user's notification preferences
CREATE OR REPLACE FUNCTION get_notification_preferences(
  p_user_id UUID
)
RETURNS TABLE (
  push_notifications_enabled BOOLEAN,
  has_push_token BOOLEAN,
  push_token_updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.push_notifications_enabled,
    (p.push_token IS NOT NULL) as has_push_token,
    p.push_token_updated_at
  FROM public.profiles p
  WHERE p.id = p_user_id;
END;
$$;

-- Function to clean up old notification logs (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_notification_logs(
  p_days_to_keep INTEGER DEFAULT 90
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_deleted_count INTEGER;
BEGIN
  DELETE FROM push_notification_logs
  WHERE sent_at < NOW() - INTERVAL '1 day' * p_days_to_keep;
  
  GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
  
  RETURN v_deleted_count;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION send_push_notification TO service_role;
GRANT EXECUTE ON FUNCTION update_notification_delivery_status TO service_role;
GRANT EXECUTE ON FUNCTION mark_notification_opened TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_preferences TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_notification_logs TO service_role;
