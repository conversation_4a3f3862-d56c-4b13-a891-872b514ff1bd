/*
  # Grant INSERT privilege on profiles table

  1. Grant INSERT privilege to public role
    - This allows the handle_new_user trigger function to insert into profiles table
    - RLS policies will still control which inserts are allowed
    - The existing "Allow anon to insert profile during registration" policy will handle authorization

  2. Security
    - Basic INSERT privilege is required for any database operation
    - RLS policies provide the fine-grained access control on top of this
    - This follows PostgreSQL's security model: privileges + RLS
*/

-- Grant INSERT privilege on profiles table to public role
-- This is required for the handle_new_user trigger function to work
GRANT INSERT ON public.profiles TO public;

-- Verify that RLS is still enabled (it should be)
-- RLS policies will control which inserts are actually allowed
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND tablename = 'profiles';