/*
  # Messaging Database Schema

  1. Coach-Client Communication System
    - `conversations` - Chat conversations between coaches and clients
    - `messages` - Individual messages within conversations
    - `message_attachments` - File attachments for messages
    - `message_reactions` - Emoji reactions to messages

  2. Enhanced Check-in System
    - `check_in_templates` - Coach-defined check-in form templates
    - `check_in_responses` - Client responses to check-ins
    - `check_in_feedback` - Coach feedback on client check-ins

  3. Coach Feedback System
    - `coach_feedback` - General coach feedback on workouts/progress
    - `program_modification_requests` - Client requests for program changes
    - `coach_notes` - Private coach notes about clients

  4. Notification System
    - `notification_preferences` - User notification settings
    - `push_notifications` - Push notification logs

  5. RLS policies for secure communication
*/

-- Conversations table for organizing messages
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coach_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT,
  last_message_at TIMESTAMPTZ DEFAULT NOW(),
  last_message_preview TEXT,
  is_archived BOOLEA<PERSON> DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(coach_id, client_id)
);

-- Messages table for individual messages
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  message_type TEXT CHECK (message_type IN ('text', 'image', 'file', 'system')) DEFAULT 'text',
  reply_to_id UUID REFERENCES messages(id) ON DELETE SET NULL,
  is_edited BOOLEAN DEFAULT false,
  edited_at TIMESTAMPTZ,
  is_deleted BOOLEAN DEFAULT false,
  deleted_at TIMESTAMPTZ,
  metadata JSONB, -- For storing additional message data
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Message attachments for files and images
CREATE TABLE IF NOT EXISTS message_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_type TEXT NOT NULL, -- image, video, document, etc.
  file_size INTEGER,
  mime_type TEXT,
  thumbnail_url TEXT, -- For images/videos
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Message reactions (emoji reactions)
CREATE TABLE IF NOT EXISTS message_reactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  emoji TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(message_id, user_id, emoji)
);

-- Message read status
CREATE TABLE IF NOT EXISTS message_read_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  read_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(message_id, user_id)
);

-- Check-in templates created by coaches
CREATE TABLE IF NOT EXISTS check_in_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coach_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  questions JSONB NOT NULL, -- Array of question objects
  frequency TEXT CHECK (frequency IN ('weekly', 'biweekly', 'monthly')) DEFAULT 'weekly',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Client responses to check-ins
CREATE TABLE IF NOT EXISTS check_in_responses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id UUID NOT NULL REFERENCES check_in_templates(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  coach_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  responses JSONB NOT NULL, -- Client answers to questions
  week_start_date DATE NOT NULL,
  submitted_at TIMESTAMPTZ DEFAULT NOW(),
  status TEXT CHECK (status IN ('submitted', 'reviewed', 'feedback_given')) DEFAULT 'submitted',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(template_id, client_id, week_start_date)
);

-- Coach feedback on check-ins
CREATE TABLE IF NOT EXISTS check_in_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  check_in_response_id UUID NOT NULL REFERENCES check_in_responses(id) ON DELETE CASCADE,
  coach_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  feedback_text TEXT NOT NULL,
  recommendations JSONB, -- Structured recommendations
  priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- General coach feedback on workouts and progress
CREATE TABLE IF NOT EXISTS coach_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coach_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  feedback_type TEXT CHECK (feedback_type IN ('workout', 'progress', 'general', 'program_adjustment')) NOT NULL,
  related_id UUID, -- workout_log_id, program_id, etc.
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  action_items JSONB, -- Structured action items
  priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Program modification requests from clients
CREATE TABLE IF NOT EXISTS program_modification_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  coach_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  program_id UUID NOT NULL REFERENCES workout_programs(id) ON DELETE CASCADE,
  request_type TEXT CHECK (request_type IN ('exercise_swap', 'schedule_change', 'intensity_adjustment', 'injury_accommodation', 'equipment_limitation', 'other')) NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  current_details JSONB, -- What they want to change
  requested_changes JSONB, -- What they want it changed to
  reason TEXT NOT NULL,
  urgency TEXT CHECK (urgency IN ('low', 'medium', 'high')) DEFAULT 'medium',
  status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'implemented')) DEFAULT 'pending',
  coach_response TEXT,
  coach_responded_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Private coach notes about clients
CREATE TABLE IF NOT EXISTS coach_notes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coach_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  note_type TEXT CHECK (note_type IN ('general', 'medical', 'behavioral', 'progress', 'goal')) DEFAULT 'general',
  title TEXT,
  content TEXT NOT NULL,
  tags TEXT[],
  is_important BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification preferences
CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  push_enabled BOOLEAN DEFAULT true,
  email_enabled BOOLEAN DEFAULT true,
  message_notifications BOOLEAN DEFAULT true,
  feedback_notifications BOOLEAN DEFAULT true,
  check_in_reminders BOOLEAN DEFAULT true,
  program_updates BOOLEAN DEFAULT true,
  achievement_notifications BOOLEAN DEFAULT true,
  marketing_notifications BOOLEAN DEFAULT false,
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  timezone TEXT DEFAULT 'UTC',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Push notification logs
CREATE TABLE IF NOT EXISTS push_notification_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL,
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  data JSONB,
  status TEXT CHECK (status IN ('sent', 'delivered', 'failed', 'clicked')) DEFAULT 'sent',
  sent_at TIMESTAMPTZ DEFAULT NOW(),
  delivered_at TIMESTAMPTZ,
  clicked_at TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversations_coach_client ON conversations (coach_id, client_id);
CREATE INDEX IF NOT EXISTS idx_conversations_last_message ON conversations (last_message_at DESC);

CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages (conversation_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages (sender_id);

CREATE INDEX IF NOT EXISTS idx_message_attachments_message ON message_attachments (message_id);

CREATE INDEX IF NOT EXISTS idx_message_reactions_message ON message_reactions (message_id);
CREATE INDEX IF NOT EXISTS idx_message_reactions_user ON message_reactions (user_id);

CREATE INDEX IF NOT EXISTS idx_message_read_status_user ON message_read_status (user_id);
CREATE INDEX IF NOT EXISTS idx_message_read_status_message ON message_read_status (message_id);

CREATE INDEX IF NOT EXISTS idx_check_in_templates_coach ON check_in_templates (coach_id);
CREATE INDEX IF NOT EXISTS idx_check_in_templates_active ON check_in_templates (is_active);

CREATE INDEX IF NOT EXISTS idx_check_in_responses_client ON check_in_responses (client_id);
CREATE INDEX IF NOT EXISTS idx_check_in_responses_coach ON check_in_responses (coach_id);
CREATE INDEX IF NOT EXISTS idx_check_in_responses_week ON check_in_responses (week_start_date);

CREATE INDEX IF NOT EXISTS idx_check_in_feedback_response ON check_in_feedback (check_in_response_id);
CREATE INDEX IF NOT EXISTS idx_check_in_feedback_coach ON check_in_feedback (coach_id);

CREATE INDEX IF NOT EXISTS idx_coach_feedback_client ON coach_feedback (client_id);
CREATE INDEX IF NOT EXISTS idx_coach_feedback_coach ON coach_feedback (coach_id);
CREATE INDEX IF NOT EXISTS idx_coach_feedback_type ON coach_feedback (feedback_type);
CREATE INDEX IF NOT EXISTS idx_coach_feedback_unread ON coach_feedback (client_id, is_read);

CREATE INDEX IF NOT EXISTS idx_program_modification_requests_client ON program_modification_requests (client_id);
CREATE INDEX IF NOT EXISTS idx_program_modification_requests_coach ON program_modification_requests (coach_id);
CREATE INDEX IF NOT EXISTS idx_program_modification_requests_status ON program_modification_requests (status);

CREATE INDEX IF NOT EXISTS idx_coach_notes_client ON coach_notes (client_id);
CREATE INDEX IF NOT EXISTS idx_coach_notes_coach ON coach_notes (coach_id);
CREATE INDEX IF NOT EXISTS idx_coach_notes_important ON coach_notes (coach_id, is_important);

CREATE INDEX IF NOT EXISTS idx_notification_preferences_user ON notification_preferences (user_id);

CREATE INDEX IF NOT EXISTS idx_push_notification_logs_user ON push_notification_logs (user_id);
CREATE INDEX IF NOT EXISTS idx_push_notification_logs_type ON push_notification_logs (notification_type);
CREATE INDEX IF NOT EXISTS idx_push_notification_logs_status ON push_notification_logs (status);

-- Enable RLS on all tables
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_read_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE check_in_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE check_in_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE check_in_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE coach_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE program_modification_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE coach_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_notification_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Conversations policies
CREATE POLICY "Users can view conversations they are part of"
  ON conversations
  FOR SELECT
  TO authenticated
  USING (coach_id = auth.uid() OR client_id = auth.uid());

CREATE POLICY "Coaches can create conversations with their clients"
  ON conversations
  FOR INSERT
  TO authenticated
  WITH CHECK (
    coach_id = auth.uid() AND
    client_id IN (
      SELECT client_id FROM coach_client_relationships
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Participants can update conversations"
  ON conversations
  FOR UPDATE
  TO authenticated
  USING (coach_id = auth.uid() OR client_id = auth.uid())
  WITH CHECK (coach_id = auth.uid() OR client_id = auth.uid());

-- Messages policies
CREATE POLICY "Users can view messages in their conversations"
  ON messages
  FOR SELECT
  TO authenticated
  USING (
    conversation_id IN (
      SELECT id FROM conversations
      WHERE coach_id = auth.uid() OR client_id = auth.uid()
    )
  );

CREATE POLICY "Users can send messages in their conversations"
  ON messages
  FOR INSERT
  TO authenticated
  WITH CHECK (
    sender_id = auth.uid() AND
    conversation_id IN (
      SELECT id FROM conversations
      WHERE coach_id = auth.uid() OR client_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own messages"
  ON messages
  FOR UPDATE
  TO authenticated
  USING (sender_id = auth.uid())
  WITH CHECK (sender_id = auth.uid());

-- Message attachments policies
CREATE POLICY "Users can view attachments in their conversations"
  ON message_attachments
  FOR SELECT
  TO authenticated
  USING (
    message_id IN (
      SELECT m.id FROM messages m
      JOIN conversations c ON m.conversation_id = c.id
      WHERE c.coach_id = auth.uid() OR c.client_id = auth.uid()
    )
  );

CREATE POLICY "Users can add attachments to their messages"
  ON message_attachments
  FOR INSERT
  TO authenticated
  WITH CHECK (
    message_id IN (
      SELECT id FROM messages WHERE sender_id = auth.uid()
    )
  );

-- Message reactions policies
CREATE POLICY "Users can view reactions in their conversations"
  ON message_reactions
  FOR SELECT
  TO authenticated
  USING (
    message_id IN (
      SELECT m.id FROM messages m
      JOIN conversations c ON m.conversation_id = c.id
      WHERE c.coach_id = auth.uid() OR c.client_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage their own reactions"
  ON message_reactions
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Message read status policies
CREATE POLICY "Users can manage their own read status"
  ON message_read_status
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Check-in templates policies
CREATE POLICY "Coaches can manage their own templates"
  ON check_in_templates
  FOR ALL
  TO authenticated
  USING (coach_id = auth.uid())
  WITH CHECK (coach_id = auth.uid());

CREATE POLICY "Clients can view their coach's active templates"
  ON check_in_templates
  FOR SELECT
  TO authenticated
  USING (
    is_active = true AND
    coach_id IN (
      SELECT coach_id FROM coach_client_relationships
      WHERE client_id = auth.uid() AND status = 'active'
    )
  );

-- Check-in responses policies
CREATE POLICY "Clients can manage their own check-in responses"
  ON check_in_responses
  FOR ALL
  TO authenticated
  USING (client_id = auth.uid())
  WITH CHECK (client_id = auth.uid());

CREATE POLICY "Coaches can view their clients' check-in responses"
  ON check_in_responses
  FOR SELECT
  TO authenticated
  USING (
    coach_id = auth.uid() OR
    client_id IN (
      SELECT client_id FROM coach_client_relationships
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

-- Check-in feedback policies
CREATE POLICY "Coaches can manage feedback on check-ins"
  ON check_in_feedback
  FOR ALL
  TO authenticated
  USING (coach_id = auth.uid())
  WITH CHECK (coach_id = auth.uid());

CREATE POLICY "Clients can view feedback on their check-ins"
  ON check_in_feedback
  FOR SELECT
  TO authenticated
  USING (
    check_in_response_id IN (
      SELECT id FROM check_in_responses WHERE client_id = auth.uid()
    )
  );

-- Coach feedback policies
CREATE POLICY "Clients can view their own feedback"
  ON coach_feedback
  FOR SELECT
  TO authenticated
  USING (client_id = auth.uid());

CREATE POLICY "Coaches can manage feedback for their clients"
  ON coach_feedback
  FOR ALL
  TO authenticated
  USING (
    coach_id = auth.uid() AND
    client_id IN (
      SELECT client_id FROM coach_client_relationships
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  )
  WITH CHECK (
    coach_id = auth.uid() AND
    client_id IN (
      SELECT client_id FROM coach_client_relationships
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

-- Program modification requests policies
CREATE POLICY "Clients can manage their own modification requests"
  ON program_modification_requests
  FOR ALL
  TO authenticated
  USING (client_id = auth.uid())
  WITH CHECK (client_id = auth.uid());

CREATE POLICY "Coaches can view and respond to their clients' requests"
  ON program_modification_requests
  FOR SELECT
  TO authenticated
  USING (
    coach_id = auth.uid() OR
    client_id IN (
      SELECT client_id FROM coach_client_relationships
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Coaches can update requests for their clients"
  ON program_modification_requests
  FOR UPDATE
  TO authenticated
  USING (
    coach_id = auth.uid() AND
    client_id IN (
      SELECT client_id FROM coach_client_relationships
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  )
  WITH CHECK (
    coach_id = auth.uid() AND
    client_id IN (
      SELECT client_id FROM coach_client_relationships
      WHERE coach_id = auth.uid() AND status = 'active'
    )
  );

-- Coach notes policies (private to coaches)
CREATE POLICY "Coaches can manage their own notes"
  ON coach_notes
  FOR ALL
  TO authenticated
  USING (coach_id = auth.uid())
  WITH CHECK (coach_id = auth.uid());

-- Notification preferences policies
CREATE POLICY "Users can manage their own notification preferences"
  ON notification_preferences
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Push notification logs policies
CREATE POLICY "Users can view their own notification logs"
  ON push_notification_logs
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can create notification logs"
  ON push_notification_logs
  FOR INSERT
  TO service_role
  WITH CHECK (true);

-- Triggers for updated_at timestamps
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_check_in_templates_updated_at BEFORE UPDATE ON check_in_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_check_in_responses_updated_at BEFORE UPDATE ON check_in_responses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_check_in_feedback_updated_at BEFORE UPDATE ON check_in_feedback
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_coach_feedback_updated_at BEFORE UPDATE ON coach_feedback
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_program_modification_requests_updated_at BEFORE UPDATE ON program_modification_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_coach_notes_updated_at BEFORE UPDATE ON coach_notes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at BEFORE UPDATE ON notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
