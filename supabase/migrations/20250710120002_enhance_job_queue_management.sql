-- Enhance the scheduled_generation_jobs table with priority management and dependencies

-- Add new columns for enhanced job queue management
ALTER TABLE scheduled_generation_jobs 
ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
ADD COLUMN IF NOT EXISTS depends_on_job_id UUID REFERENCES scheduled_generation_jobs(id),
ADD COLUMN IF NOT EXISTS next_retry_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS execution_timeout_seconds INTEGER DEFAULT 300,
ADD COLUMN IF NOT EXISTS job_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS cancelled_by TEXT,
ADD COLUMN IF NOT EXISTS cancellation_reason TEXT;

-- Add new execution statuses
ALTER TABLE scheduled_generation_jobs 
DROP CONSTRAINT IF EXISTS scheduled_generation_jobs_execution_status_check;

ALTER TABLE scheduled_generation_jobs 
ADD CONSTRAINT scheduled_generation_jobs_execution_status_check 
CHECK (execution_status IN ('pending', 'running', 'completed', 'failed', 'cancelled', 'timeout', 'waiting_dependency'));

-- Add new job types
ALTER TABLE scheduled_generation_jobs 
DROP CONSTRAINT IF EXISTS scheduled_generation_jobs_job_type_check;

ALTER TABLE scheduled_generation_jobs 
ADD CONSTRAINT scheduled_generation_jobs_job_type_check 
CHECK (job_type IN ('program_generation', 'auto_approval', 'transition', 'cleanup', 'monitoring'));

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_scheduled_jobs_priority_status ON scheduled_generation_jobs(priority DESC, execution_status, scheduled_date);
CREATE INDEX IF NOT EXISTS idx_scheduled_jobs_dependency ON scheduled_generation_jobs(depends_on_job_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_jobs_retry ON scheduled_generation_jobs(next_retry_at) WHERE next_retry_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_scheduled_jobs_timeout ON scheduled_generation_jobs(started_at, execution_timeout_seconds) WHERE execution_status = 'running';

-- Create a function to calculate next retry time with exponential backoff
CREATE OR REPLACE FUNCTION calculate_next_retry_time(
  retry_count INTEGER,
  base_delay_seconds INTEGER DEFAULT 60
) RETURNS TIMESTAMPTZ
LANGUAGE plpgsql
AS $$
BEGIN
  -- Exponential backoff: base_delay * (2 ^ retry_count) with jitter
  -- Cap at 1 hour maximum delay
  RETURN NOW() + INTERVAL '1 second' * LEAST(
    base_delay_seconds * POWER(2, retry_count) + (RANDOM() * 30),
    3600
  );
END;
$$;

-- Create a function to get the next job to execute
CREATE OR REPLACE FUNCTION get_next_job_to_execute()
RETURNS TABLE(
  job_id UUID,
  user_id UUID,
  job_type TEXT,
  priority INTEGER,
  scheduled_date DATE,
  cycle_number INTEGER,
  job_metadata JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sgj.id,
    sgj.user_id,
    sgj.job_type,
    sgj.priority,
    sgj.scheduled_date,
    sgj.cycle_number,
    sgj.job_metadata
  FROM scheduled_generation_jobs sgj
  WHERE sgj.execution_status = 'pending'
    AND sgj.scheduled_date <= CURRENT_DATE
    AND (sgj.depends_on_job_id IS NULL OR 
         EXISTS (
           SELECT 1 FROM scheduled_generation_jobs dep 
           WHERE dep.id = sgj.depends_on_job_id 
           AND dep.execution_status = 'completed'
         ))
  ORDER BY 
    sgj.priority DESC,
    sgj.scheduled_date ASC,
    sgj.created_at ASC
  LIMIT 1;
END;
$$;

-- Create a function to mark a job as running
CREATE OR REPLACE FUNCTION start_job_execution(p_job_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  job_updated BOOLEAN := FALSE;
BEGIN
  UPDATE scheduled_generation_jobs
  SET 
    execution_status = 'running',
    started_at = NOW()
  WHERE id = p_job_id
    AND execution_status = 'pending'
  RETURNING TRUE INTO job_updated;
  
  RETURN COALESCE(job_updated, FALSE);
END;
$$;

-- Create a function to complete a job
CREATE OR REPLACE FUNCTION complete_job_execution(
  p_job_id UUID,
  p_success BOOLEAN,
  p_error_message TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  job_updated BOOLEAN := FALSE;
BEGIN
  UPDATE scheduled_generation_jobs
  SET 
    execution_status = CASE WHEN p_success THEN 'completed' ELSE 'failed' END,
    completed_at = NOW(),
    error_message = p_error_message,
    job_metadata = COALESCE(job_metadata, '{}') || COALESCE(p_metadata, '{}')
  WHERE id = p_job_id
    AND execution_status = 'running'
  RETURNING TRUE INTO job_updated;
  
  RETURN COALESCE(job_updated, FALSE);
END;
$$;

-- Create a function to schedule a retry
CREATE OR REPLACE FUNCTION schedule_job_retry(
  p_job_id UUID,
  p_error_message TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  current_retry_count INTEGER;
  max_retry_count INTEGER;
  job_updated BOOLEAN := FALSE;
BEGIN
  -- Get current retry information
  SELECT retry_count, max_retries
  INTO current_retry_count, max_retry_count
  FROM scheduled_generation_jobs
  WHERE id = p_job_id;
  
  -- Check if we can retry
  IF current_retry_count < max_retry_count THEN
    UPDATE scheduled_generation_jobs
    SET 
      execution_status = 'pending',
      retry_count = retry_count + 1,
      next_retry_at = calculate_next_retry_time(retry_count + 1),
      error_message = p_error_message,
      started_at = NULL
    WHERE id = p_job_id
      AND execution_status IN ('failed', 'timeout')
    RETURNING TRUE INTO job_updated;
  ELSE
    -- Mark as permanently failed
    UPDATE scheduled_generation_jobs
    SET 
      execution_status = 'failed',
      error_message = COALESCE(p_error_message, 'Max retries exceeded'),
      completed_at = NOW()
    WHERE id = p_job_id
    RETURNING TRUE INTO job_updated;
  END IF;
  
  RETURN COALESCE(job_updated, FALSE);
END;
$$;

-- Create a function to cancel a job
CREATE OR REPLACE FUNCTION cancel_job(
  p_job_id UUID,
  p_cancelled_by TEXT,
  p_reason TEXT DEFAULT 'Manual cancellation'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  job_updated BOOLEAN := FALSE;
BEGIN
  UPDATE scheduled_generation_jobs
  SET 
    execution_status = 'cancelled',
    cancelled_at = NOW(),
    cancelled_by = p_cancelled_by,
    cancellation_reason = p_reason,
    completed_at = NOW()
  WHERE id = p_job_id
    AND execution_status IN ('pending', 'running', 'waiting_dependency')
  RETURNING TRUE INTO job_updated;
  
  RETURN COALESCE(job_updated, FALSE);
END;
$$;

-- Create a function to handle job timeouts
CREATE OR REPLACE FUNCTION handle_job_timeouts()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  timeout_count INTEGER := 0;
BEGIN
  UPDATE scheduled_generation_jobs
  SET 
    execution_status = 'timeout',
    error_message = 'Job execution timed out',
    completed_at = NOW()
  WHERE execution_status = 'running'
    AND started_at + INTERVAL '1 second' * execution_timeout_seconds < NOW()
  RETURNING COUNT(*) INTO timeout_count;
  
  RETURN timeout_count;
END;
$$;

-- Create a function to clean up old completed jobs
CREATE OR REPLACE FUNCTION cleanup_old_jobs(p_days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  DELETE FROM scheduled_generation_jobs
  WHERE execution_status IN ('completed', 'failed', 'cancelled')
    AND completed_at < NOW() - INTERVAL '1 day' * p_days_to_keep
  RETURNING COUNT(*) INTO deleted_count;
  
  RETURN deleted_count;
END;
$$;

-- Create a view for job queue monitoring
CREATE OR REPLACE VIEW job_queue_status AS
SELECT 
  execution_status,
  job_type,
  priority,
  COUNT(*) as job_count,
  MIN(scheduled_date) as earliest_scheduled,
  MAX(scheduled_date) as latest_scheduled,
  AVG(retry_count) as avg_retry_count
FROM scheduled_generation_jobs
WHERE execution_status NOT IN ('completed', 'cancelled')
GROUP BY execution_status, job_type, priority
ORDER BY priority DESC, execution_status;

-- Create a view for job dependencies
CREATE OR REPLACE VIEW job_dependencies AS
SELECT 
  j.id as job_id,
  j.job_type,
  j.execution_status,
  j.priority,
  j.scheduled_date,
  dep.id as dependency_id,
  dep.job_type as dependency_type,
  dep.execution_status as dependency_status,
  CASE 
    WHEN dep.execution_status = 'completed' THEN 'ready'
    WHEN dep.execution_status IN ('failed', 'cancelled', 'timeout') THEN 'blocked'
    ELSE 'waiting'
  END as dependency_state
FROM scheduled_generation_jobs j
LEFT JOIN scheduled_generation_jobs dep ON j.depends_on_job_id = dep.id
WHERE j.depends_on_job_id IS NOT NULL
ORDER BY j.priority DESC, j.scheduled_date;

-- Grant permissions
GRANT EXECUTE ON FUNCTION calculate_next_retry_time TO postgres, service_role;
GRANT EXECUTE ON FUNCTION get_next_job_to_execute TO postgres, service_role;
GRANT EXECUTE ON FUNCTION start_job_execution TO postgres, service_role;
GRANT EXECUTE ON FUNCTION complete_job_execution TO postgres, service_role;
GRANT EXECUTE ON FUNCTION schedule_job_retry TO postgres, service_role;
GRANT EXECUTE ON FUNCTION cancel_job TO postgres, service_role;
GRANT EXECUTE ON FUNCTION handle_job_timeouts TO postgres, service_role;
GRANT EXECUTE ON FUNCTION cleanup_old_jobs TO postgres, service_role;

GRANT SELECT ON job_queue_status TO postgres, service_role;
GRANT SELECT ON job_dependencies TO postgres, service_role;
