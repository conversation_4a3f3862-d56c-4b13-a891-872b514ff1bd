-- Fix duplicate AI program generation issue
-- This migration removes the direct HTTP call from the database trigger
-- to prevent duplicate program generation when both trigger and webhook are active

-- Update the intake completion status function to remove the direct HTTP call
CREATE OR REPLACE FUNCTION public.update_intake_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if all required intake fields are completed
  IF NEW.intake_gender IS NOT NULL 
     AND NEW.age IS NOT NULL 
     AND NEW.height_cm IS NOT NULL 
     AND NEW.weight_kg IS NOT NULL 
     AND NEW.primary_fitness_goal IS NOT NULL 
     AND array_length(NEW.primary_fitness_goal, 1) > 0
     AND NEW.training_experience_level IS NOT NULL 
     AND NEW.equipment_access_type IS NOT NULL 
     AND NEW.training_days_per_week IS NOT NULL 
     AND NEW.preferred_session_duration_minutes IS NOT NULL 
     AND (OLD.intake_status IS NULL OR OLD.intake_status != 'completed') THEN
    
    NEW.intake_status = 'completed';
    NEW.intake_completed_at = now();
    
    -- Note: AI program generation will be triggered via Database Webhook
    -- configured in Supabase Dashboard to call the generate-workout-program Edge Function
    -- We removed the direct HTTP call here to prevent duplicate generation
    
  ELSIF NEW.intake_gender IS NOT NULL 
        OR NEW.age IS NOT NULL 
        OR NEW.height_cm IS NOT NULL 
        OR NEW.weight_kg IS NOT NULL 
        OR (NEW.primary_fitness_goal IS NOT NULL AND array_length(NEW.primary_fitness_goal, 1) > 0)
        OR NEW.training_experience_level IS NOT NULL 
        OR NEW.equipment_access_type IS NOT NULL 
        OR NEW.training_days_per_week IS NOT NULL 
        OR NEW.preferred_session_duration_minutes IS NOT NULL THEN
    
    NEW.intake_status = 'in_progress';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION public.update_intake_completion_status() OWNER TO postgres;

-- Recreate the trigger to ensure it uses the updated function
DROP TRIGGER IF EXISTS on_profile_intake_updated ON public.profiles;
CREATE TRIGGER on_profile_intake_updated
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_intake_completion_status();

-- Add helpful comment
COMMENT ON FUNCTION public.update_intake_completion_status() IS 'Updates intake completion status without direct HTTP calls to prevent duplicate AI program generation. AI generation is handled by Database Webhook.';
