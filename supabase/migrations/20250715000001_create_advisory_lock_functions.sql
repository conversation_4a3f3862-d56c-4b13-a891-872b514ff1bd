-- Create advisory lock functions for Edge Function use
-- These functions are required for the generate-workout-program Edge Function
-- to prevent race conditions when multiple webhooks fire simultaneously

-- Function to try to acquire an advisory lock (non-blocking)
-- Returns true if lock was acquired, false if already held by another session
CREATE OR REPLACE FUNCTION public.pg_try_advisory_lock(key bigint)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT pg_try_advisory_lock(key);
$$;

-- Function to release an advisory lock
-- Returns true if lock was released, false if lock was not held by this session
CREATE OR REPLACE FUNCTION public.pg_advisory_unlock(key bigint)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT pg_advisory_unlock(key);
$$;

-- Set function owners to postgres for elevated privileges
ALTER FUNCTION public.pg_try_advisory_lock(bigint) OWNER TO postgres;
ALTER FUNCTION public.pg_advisory_unlock(bigint) OWNER TO postgres;

-- Add helpful comments
COMMENT ON FUNCTION public.pg_try_advisory_lock(bigint) IS 'Wrapper for PostgreSQL pg_try_advisory_lock function for use in Edge Functions via RPC calls. Prevents race conditions in workout program generation.';
COMMENT ON FUNCTION public.pg_advisory_unlock(bigint) IS 'Wrapper for PostgreSQL pg_advisory_unlock function for use in Edge Functions via RPC calls. Releases advisory locks acquired during workout program generation.';

-- Grant execute permissions to service role
GRANT EXECUTE ON FUNCTION public.pg_try_advisory_lock(bigint) TO service_role;
GRANT EXECUTE ON FUNCTION public.pg_advisory_unlock(bigint) TO service_role;
