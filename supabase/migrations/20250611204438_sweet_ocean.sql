/*
  # Create exercises table

  1. New Tables
    - `exercises`
      - `id` (uuid, primary key)
      - `name` (text, unique, not null)
      - `description` (text)
      - `video_url` (text)
      - `target_muscles_primary` (text array)
      - `target_muscles_secondary` (text array)
      - `equipment_required` (text array)
      - `difficulty_level` (text enum)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `exercises` table
    - Add policies for authenticated users to read exercises
    - Add policy for service role to manage exercises
*/

CREATE TABLE IF NOT EXISTS exercises (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  description text,
  video_url text,
  target_muscles_primary text[] DEFAULT '{}',
  target_muscles_secondary text[] DEFAULT '{}',
  equipment_required text[] DEFAULT '{}',
  difficulty_level text CHECK (difficulty_level IN ('Beginner', 'Intermediate', 'Advanced')) DEFAULT 'Beginner',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE exercises ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Authenticated users can read exercises"
  ON exercises
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Service role can manage exercises"
  ON exercises
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Trigger for updated_at
CREATE TRIGGER on_exercises_updated
  BEFORE UPDATE ON exercises
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Indexes
CREATE INDEX exercises_name_idx ON exercises (name);
CREATE INDEX exercises_difficulty_idx ON exercises (difficulty_level);
CREATE INDEX exercises_equipment_idx ON exercises USING GIN (equipment_required);
CREATE INDEX exercises_muscles_primary_idx ON exercises USING GIN (target_muscles_primary);