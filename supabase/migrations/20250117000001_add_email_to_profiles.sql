/*
  # Add email field to profiles table

  1. Changes
    - Add email column to profiles table
    - Update handle_new_user function to include email from auth.users
    - Create function to sync email changes from auth.users to profiles
    - Add trigger to keep emails in sync

  2. Security
    - Email field will be accessible through existing RLS policies
    - Only the user can update their own email through profile updates
*/

-- Add email column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS email TEXT;

-- Create index for email lookups
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles (email);

-- Update existing profiles with email addresses from auth.users
UPDATE public.profiles 
SET email = auth_users.email
FROM auth.users AS auth_users
WHERE public.profiles.id = auth_users.id
AND public.profiles.email IS NULL;

-- Update the handle_new_user function to include email
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    full_name, 
    email,
    gender_preference,
    height_unit,
    weight_unit,
    intake_status,
    role
  )
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'),
    NEW.email,
    'Neutral',
    'cm',
    'kg',
    'not_started',
    'client'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION public.handle_new_user() OWNER TO postgres;

-- Create function to sync email changes from auth.users to profiles
CREATE OR REPLACE FUNCTION public.sync_user_email()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the email in profiles table when it changes in auth.users
  IF OLD.email IS DISTINCT FROM NEW.email THEN
    UPDATE public.profiles 
    SET email = NEW.email,
        updated_at = NOW()
    WHERE id = NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION public.sync_user_email() OWNER TO postgres;

-- Create trigger to sync email changes
DROP TRIGGER IF EXISTS sync_user_email_trigger ON auth.users;
CREATE TRIGGER sync_user_email_trigger
  AFTER UPDATE ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_user_email();

-- Add comment for documentation
COMMENT ON COLUMN public.profiles.email IS 'User email address synced from auth.users table for easy access by coaches and admin features';
COMMENT ON FUNCTION public.sync_user_email() IS 'Automatically syncs email changes from auth.users to profiles table';
COMMENT ON TRIGGER sync_user_email_trigger ON auth.users IS 'Keeps email addresses in sync between auth.users and profiles tables';
