/*
  # Add coach_reviewed_at column to weekly_checkins table

  1. Changes
    - Add `coach_reviewed_at` column to `weekly_checkins` table
    - This column will store the timestamp when a coach reviews a check-in
    - Used to filter out reviewed check-ins from the coach dashboard

  2. Security
    - Add RLS policy for coaches to update the coach_reviewed_at column
    - Maintain existing access controls
*/

-- Add coach_reviewed_at column to weekly_checkins table
ALTER TABLE public.weekly_checkins
ADD COLUMN IF NOT EXISTS coach_reviewed_at timestamptz;

-- Add a comment to the new column for clarity
COMMENT ON COLUMN public.weekly_checkins.coach_reviewed_at IS 'Timestamp when a coach reviewed this check-in.';