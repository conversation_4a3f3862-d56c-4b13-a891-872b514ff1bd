-- Enable pg_cron extension for scheduled jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Grant necessary permissions for cron jobs
GRANT USAGE ON SCHEMA cron TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cron TO postgres;

-- Set up daily cron job for daily-program-scheduler (9:00 AM UTC)
SELECT cron.schedule(
  'daily-program-scheduler',
  '0 9 * * *',
  $$
  SELECT supabase_functions.http_request(
    'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/daily-program-scheduler',
    'POST',
    '{"Content-Type":"application/json","Authorization":"Bearer ' || current_setting('app.settings.service_role_key') || '"}',
    '{"source":"cron","timestamp":"' || now()::text || '"}',
    '30000'
  );
  $$
);

-- Set up daily cron job for auto-approve-programs (10:00 AM UTC)
SELECT cron.schedule(
  'auto-approve-programs',
  '0 10 * * *',
  $$
  SELECT supabase_functions.http_request(
    'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/auto-approve-programs',
    'POST',
    '{"Content-Type":"application/json","Authorization":"Bearer ' || current_setting('app.settings.service_role_key') || '"}',
    '{"source":"cron","timestamp":"' || now()::text || '"}',
    '30000'
  );
  $$
);

-- Set up daily cron job for program-transition-manager (11:00 AM UTC)
SELECT cron.schedule(
  'program-transition-manager',
  '0 11 * * *',
  $$
  SELECT supabase_functions.http_request(
    'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/program-transition-manager',
    'POST',
    '{"Content-Type":"application/json","Authorization":"Bearer ' || current_setting('app.settings.service_role_key') || '"}',
    '{"source":"cron","timestamp":"' || now()::text || '"}',
    '30000'
  );
  $$
);

-- Set up weekly system health monitoring (Sundays 8:00 AM UTC)
SELECT cron.schedule(
  'system-health-monitor',
  '0 8 * * 0',
  $$
  SELECT supabase_functions.http_request(
    'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/system-health-monitor',
    'POST',
    '{"Content-Type":"application/json","Authorization":"Bearer ' || current_setting('app.settings.service_role_key') || '"}',
    '{"source":"cron","timestamp":"' || now()::text || '"}',
    '60000'
  );
  $$
);

-- Create a function to safely get the service role key from environment
CREATE OR REPLACE FUNCTION get_service_role_key()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- This will need to be set as a database setting
  -- For now, we'll use a placeholder that needs to be configured
  RETURN current_setting('app.settings.service_role_key', true);
EXCEPTION
  WHEN OTHERS THEN
    -- Fallback - this should be configured properly in production
    RETURN 'CONFIGURE_SERVICE_ROLE_KEY';
END;
$$;

-- Create a table to track cron job executions for monitoring
CREATE TABLE IF NOT EXISTS cron_job_logs (
  id BIGSERIAL PRIMARY KEY,
  job_name TEXT NOT NULL,
  execution_time TIMESTAMPTZ DEFAULT NOW(),
  status TEXT NOT NULL, -- 'started', 'completed', 'failed'
  response_data JSONB,
  error_message TEXT,
  execution_duration_ms INTEGER
);

-- Enable RLS on cron job logs
ALTER TABLE cron_job_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for service role access
CREATE POLICY "Service role can manage cron job logs" ON cron_job_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cron_job_logs_job_name ON cron_job_logs(job_name);
CREATE INDEX IF NOT EXISTS idx_cron_job_logs_execution_time ON cron_job_logs(execution_time);
CREATE INDEX IF NOT EXISTS idx_cron_job_logs_status ON cron_job_logs(status);

-- Create a function to log cron job executions
CREATE OR REPLACE FUNCTION log_cron_execution(
  p_job_name TEXT,
  p_status TEXT,
  p_response_data JSONB DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL,
  p_execution_duration_ms INTEGER DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO cron_job_logs (
    job_name,
    status,
    response_data,
    error_message,
    execution_duration_ms
  ) VALUES (
    p_job_name,
    p_status,
    p_response_data,
    p_error_message,
    p_execution_duration_ms
  );
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION log_cron_execution TO postgres;
GRANT EXECUTE ON FUNCTION get_service_role_key TO postgres;

-- Create a view for cron job monitoring
CREATE OR REPLACE VIEW cron_job_status AS
SELECT 
  cj.jobname,
  cj.schedule,
  cj.active,
  cj.jobid,
  COALESCE(latest_log.last_execution, 'Never') as last_execution,
  COALESCE(latest_log.last_status, 'Unknown') as last_status,
  COALESCE(latest_log.error_message, '') as last_error
FROM cron.job cj
LEFT JOIN (
  SELECT DISTINCT ON (job_name)
    job_name,
    execution_time as last_execution,
    status as last_status,
    error_message
  FROM cron_job_logs
  ORDER BY job_name, execution_time DESC
) latest_log ON cj.jobname = latest_log.job_name
ORDER BY cj.jobname;

-- Grant access to the view
GRANT SELECT ON cron_job_status TO postgres, service_role;
