/*
  # <PERSON>ore missing auth trigger and update function security

  1. Security Updates
    - Update handle_new_user function with SECURITY DEFINER
    - Set function owner to postgres for elevated privileges
    - Update handle_updated_at function with SECURITY DEFINER

  2. Restore Missing Trigger
    - Recreate on_auth_user_created trigger on auth.users table
    - This trigger automatically creates profile records when users register

  3. Verify Function Exists
    - Ensure update_intake_completion_status function has proper security
*/

-- Update handle_new_user function with proper security context
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name)
  VALUES (NEW.id, COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'));
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION public.handle_new_user() OWNER TO postgres;

-- Update handle_updated_at function with proper security context
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres
ALTER FUNCTION public.handle_updated_at() OWNER TO postgres;

-- Update update_intake_completion_status function with proper security context
CREATE OR REPLACE FUNCTION public.update_intake_completion_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if intake data is complete
  IF NEW.intake_gender IS NOT NULL 
     AND NEW.age IS NOT NULL 
     AND NEW.height_cm IS NOT NULL 
     AND NEW.weight_kg IS NOT NULL 
     AND NEW.primary_fitness_goal IS NOT NULL 
     AND NEW.training_experience_level IS NOT NULL 
     AND NEW.equipment_access_type IS NOT NULL 
     AND NEW.training_days_per_week IS NOT NULL 
     AND NEW.preferred_session_duration_minutes IS NOT NULL THEN
    
    -- Mark intake as completed if it wasn't already
    IF OLD.intake_status != 'completed' THEN
      NEW.intake_status = 'completed';
      NEW.intake_completed_at = NOW();
    END IF;
  ELSE
    -- Mark as in progress if some data exists but not complete
    IF NEW.intake_status = 'not_started' AND (
       NEW.intake_gender IS NOT NULL 
       OR NEW.age IS NOT NULL 
       OR NEW.height_cm IS NOT NULL 
       OR NEW.weight_kg IS NOT NULL
    ) THEN
      NEW.intake_status = 'in_progress';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set function owner to postgres
ALTER FUNCTION public.update_intake_completion_status() OWNER TO postgres;

-- Drop the trigger if it exists (to avoid conflicts)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Recreate the missing trigger on auth.users table
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE PROCEDURE public.handle_new_user();

-- Verify the trigger was created successfully
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 
    FROM information_schema.triggers 
    WHERE trigger_name = 'on_auth_user_created' 
    AND event_object_table = 'users'
    AND event_object_schema = 'auth'
  ) THEN
    RAISE NOTICE 'SUCCESS: on_auth_user_created trigger has been restored';
  ELSE
    RAISE EXCEPTION 'FAILED: on_auth_user_created trigger was not created';
  END IF;
END $$;