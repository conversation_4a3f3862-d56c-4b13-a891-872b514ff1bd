-- Social Features Database Schema
-- This migration adds tables and functions for social features including profiles, connections, and interactions

-- User profiles table (extends the existing users table)
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
  username VA<PERSON>HA<PERSON>(30) UNIQUE,
  display_name VARCHA<PERSON>(100),
  bio TEXT,
  location VARCHAR(100),
  website VARCHAR(255),
  date_of_birth DATE,
  gender VARCHAR(20),
  fitness_level VARCHAR(20) CHECK (fitness_level IN ('beginner', 'intermediate', 'advanced')),
  primary_goals TEXT[],
  preferred_workout_types TEXT[],
  available_equipment TEXT[],
  
  -- Privacy settings
  profile_visibility VARCHAR(20) DEFAULT 'public' CHECK (profile_visibility IN ('public', 'friends', 'private')),
  show_workout_stats BOOLEAN DEFAULT true,
  show_progress_photos BOOLEAN DEFAULT false,
  show_achievements BOOLEAN DEFAULT true,
  allow_friend_requests BOOLEAN DEFAULT true,
  
  -- Social stats
  followers_count INTEGER DEFAULT 0,
  following_count INTEGER DEFAULT 0,
  workouts_shared_count INTEGER DEFAULT 0,
  achievements_count INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User connections (friends/followers)
CREATE TABLE IF NOT EXISTS user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  follower_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  following_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'blocked')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(follower_id, following_id),
  CHECK (follower_id != following_id)
);

-- Workout shares
CREATE TABLE IF NOT EXISTS workout_shares (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  workout_log_id UUID REFERENCES workout_logs(id) ON DELETE CASCADE,
  workout_program_id UUID REFERENCES workout_programs(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'friends', 'private')),
  
  -- Engagement metrics
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  shares_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CHECK (
    (workout_log_id IS NOT NULL AND workout_program_id IS NULL) OR
    (workout_log_id IS NULL AND workout_program_id IS NOT NULL)
  )
);

-- Social interactions (likes, comments)
CREATE TABLE IF NOT EXISTS social_interactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  target_type VARCHAR(20) NOT NULL CHECK (target_type IN ('workout_share', 'comment', 'challenge')),
  target_id UUID NOT NULL,
  interaction_type VARCHAR(20) NOT NULL CHECK (interaction_type IN ('like', 'comment', 'share', 'report')),
  content TEXT, -- For comments
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, target_type, target_id, interaction_type)
);

-- Fitness challenges
CREATE TABLE IF NOT EXISTS fitness_challenges (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  creator_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  challenge_type VARCHAR(50) NOT NULL, -- 'distance', 'reps', 'weight', 'duration', 'frequency'
  target_value NUMERIC,
  target_unit VARCHAR(20),
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  
  -- Challenge settings
  visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'friends', 'private')),
  max_participants INTEGER,
  requires_approval BOOLEAN DEFAULT false,
  
  -- Engagement metrics
  participants_count INTEGER DEFAULT 0,
  completed_count INTEGER DEFAULT 0,
  
  -- Rewards
  reward_type VARCHAR(50), -- 'badge', 'points', 'custom'
  reward_data JSONB,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Challenge participants
CREATE TABLE IF NOT EXISTS challenge_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  challenge_id UUID REFERENCES fitness_challenges(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('pending', 'active', 'completed', 'withdrawn')),
  current_progress NUMERIC DEFAULT 0,
  completion_date TIMESTAMP WITH TIME ZONE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(challenge_id, user_id)
);

-- User achievements and badges
CREATE TABLE IF NOT EXISTS user_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  achievement_type VARCHAR(50) NOT NULL,
  achievement_name VARCHAR(255) NOT NULL,
  description TEXT,
  icon_url VARCHAR(255),
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Achievement data
  criteria JSONB,
  progress_data JSONB,
  
  UNIQUE(user_id, achievement_type, achievement_name)
);

-- Activity feed
CREATE TABLE IF NOT EXISTS activity_feed (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  activity_type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Related entities
  related_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  related_workout_id UUID,
  related_challenge_id UUID REFERENCES fitness_challenges(id) ON DELETE CASCADE,
  related_achievement_id UUID REFERENCES user_achievements(id) ON DELETE CASCADE,
  
  -- Activity data
  activity_data JSONB,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leaderboards
CREATE TABLE IF NOT EXISTS leaderboards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  metric_type VARCHAR(50) NOT NULL, -- 'total_workouts', 'total_weight', 'streak_days', etc.
  time_period VARCHAR(20) NOT NULL CHECK (time_period IN ('daily', 'weekly', 'monthly', 'yearly', 'all_time')),
  category VARCHAR(50), -- 'strength', 'cardio', 'overall', etc.
  
  -- Leaderboard settings
  is_active BOOLEAN DEFAULT true,
  max_entries INTEGER DEFAULT 100,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leaderboard entries
CREATE TABLE IF NOT EXISTS leaderboard_entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  leaderboard_id UUID REFERENCES leaderboards(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  rank INTEGER NOT NULL,
  score NUMERIC NOT NULL,
  
  -- Additional data
  entry_data JSONB,
  
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(leaderboard_id, user_id, period_start, period_end)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_user_connections_follower ON user_connections(follower_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_following ON user_connections(following_id);
CREATE INDEX IF NOT EXISTS idx_user_connections_status ON user_connections(status);
CREATE INDEX IF NOT EXISTS idx_workout_shares_user_id ON workout_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_workout_shares_visibility ON workout_shares(visibility);
CREATE INDEX IF NOT EXISTS idx_workout_shares_created_at ON workout_shares(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_social_interactions_user_id ON social_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_social_interactions_target ON social_interactions(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_fitness_challenges_creator ON fitness_challenges(creator_id);
CREATE INDEX IF NOT EXISTS idx_fitness_challenges_dates ON fitness_challenges(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_challenge_participants_challenge ON challenge_participants(challenge_id);
CREATE INDEX IF NOT EXISTS idx_challenge_participants_user ON challenge_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_user_id ON activity_feed(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_created_at ON activity_feed(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_leaderboard_entries_leaderboard ON leaderboard_entries(leaderboard_id);
CREATE INDEX IF NOT EXISTS idx_leaderboard_entries_rank ON leaderboard_entries(leaderboard_id, rank);

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE fitness_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE challenge_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_feed ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboard_entries ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- User profiles policies
CREATE POLICY "Users can view public profiles" ON user_profiles
  FOR SELECT USING (
    profile_visibility = 'public' OR
    user_id = auth.uid() OR
    (profile_visibility = 'friends' AND EXISTS (
      SELECT 1 FROM user_connections 
      WHERE follower_id = auth.uid() 
      AND following_id = user_id 
      AND status = 'accepted'
    ))
  );

CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own profile" ON user_profiles
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- User connections policies
CREATE POLICY "Users can view their connections" ON user_connections
  FOR SELECT USING (follower_id = auth.uid() OR following_id = auth.uid());

CREATE POLICY "Users can create connections" ON user_connections
  FOR INSERT WITH CHECK (follower_id = auth.uid());

CREATE POLICY "Users can update their connections" ON user_connections
  FOR UPDATE USING (follower_id = auth.uid() OR following_id = auth.uid());

-- Workout shares policies
CREATE POLICY "Users can view public workout shares" ON workout_shares
  FOR SELECT USING (
    visibility = 'public' OR
    user_id = auth.uid() OR
    (visibility = 'friends' AND EXISTS (
      SELECT 1 FROM user_connections 
      WHERE follower_id = auth.uid() 
      AND following_id = user_id 
      AND status = 'accepted'
    ))
  );

CREATE POLICY "Users can manage their workout shares" ON workout_shares
  FOR ALL USING (user_id = auth.uid());

-- Social interactions policies
CREATE POLICY "Users can view interactions on visible content" ON social_interactions
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM workout_shares ws 
      WHERE ws.id::text = target_id 
      AND target_type = 'workout_share'
      AND (
        ws.visibility = 'public' OR
        ws.user_id = auth.uid() OR
        (ws.visibility = 'friends' AND EXISTS (
          SELECT 1 FROM user_connections 
          WHERE follower_id = auth.uid() 
          AND following_id = ws.user_id 
          AND status = 'accepted'
        ))
      )
    )
  );

CREATE POLICY "Users can create interactions" ON social_interactions
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Fitness challenges policies
CREATE POLICY "Users can view public challenges" ON fitness_challenges
  FOR SELECT USING (
    visibility = 'public' OR
    creator_id = auth.uid() OR
    (visibility = 'friends' AND EXISTS (
      SELECT 1 FROM user_connections 
      WHERE follower_id = auth.uid() 
      AND following_id = creator_id 
      AND status = 'accepted'
    ))
  );

CREATE POLICY "Users can manage their challenges" ON fitness_challenges
  FOR ALL USING (creator_id = auth.uid());

-- Challenge participants policies
CREATE POLICY "Users can view challenge participants" ON challenge_participants
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM fitness_challenges fc 
      WHERE fc.id = challenge_id 
      AND (
        fc.visibility = 'public' OR
        fc.creator_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can manage their participation" ON challenge_participants
  FOR ALL USING (user_id = auth.uid());

-- User achievements policies
CREATE POLICY "Users can view public achievements" ON user_achievements
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.user_id = user_achievements.user_id 
      AND up.show_achievements = true
      AND (
        up.profile_visibility = 'public' OR
        (up.profile_visibility = 'friends' AND EXISTS (
          SELECT 1 FROM user_connections 
          WHERE follower_id = auth.uid() 
          AND following_id = up.user_id 
          AND status = 'accepted'
        ))
      )
    )
  );

CREATE POLICY "System can insert achievements" ON user_achievements
  FOR INSERT WITH CHECK (true); -- Managed by functions

-- Activity feed policies
CREATE POLICY "Users can view relevant activity feed" ON activity_feed
  FOR SELECT USING (
    user_id = auth.uid() OR
    related_user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM user_connections 
      WHERE follower_id = auth.uid() 
      AND following_id = user_id 
      AND status = 'accepted'
    )
  );

-- Leaderboards policies (public read)
CREATE POLICY "Anyone can view active leaderboards" ON leaderboards
  FOR SELECT USING (is_active = true);

CREATE POLICY "Anyone can view leaderboard entries" ON leaderboard_entries
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM leaderboards 
      WHERE id = leaderboard_id 
      AND is_active = true
    )
  );

-- Functions for social features

-- Function to update user profile stats
CREATE OR REPLACE FUNCTION update_user_profile_stats()
RETURNS TRIGGER AS $$
BEGIN
  -- Update followers count
  IF TG_TABLE_NAME = 'user_connections' THEN
    IF TG_OP = 'INSERT' AND NEW.status = 'accepted' THEN
      UPDATE user_profiles 
      SET followers_count = followers_count + 1 
      WHERE user_id = NEW.following_id;
      
      UPDATE user_profiles 
      SET following_count = following_count + 1 
      WHERE user_id = NEW.follower_id;
    ELSIF TG_OP = 'DELETE' AND OLD.status = 'accepted' THEN
      UPDATE user_profiles 
      SET followers_count = GREATEST(0, followers_count - 1) 
      WHERE user_id = OLD.following_id;
      
      UPDATE user_profiles 
      SET following_count = GREATEST(0, following_count - 1) 
      WHERE user_id = OLD.follower_id;
    END IF;
  END IF;
  
  -- Update workout shares count
  IF TG_TABLE_NAME = 'workout_shares' THEN
    IF TG_OP = 'INSERT' THEN
      UPDATE user_profiles 
      SET workouts_shared_count = workouts_shared_count + 1 
      WHERE user_id = NEW.user_id;
    ELSIF TG_OP = 'DELETE' THEN
      UPDATE user_profiles 
      SET workouts_shared_count = GREATEST(0, workouts_shared_count - 1) 
      WHERE user_id = OLD.user_id;
    END IF;
  END IF;
  
  -- Update achievements count
  IF TG_TABLE_NAME = 'user_achievements' THEN
    IF TG_OP = 'INSERT' THEN
      UPDATE user_profiles 
      SET achievements_count = achievements_count + 1 
      WHERE user_id = NEW.user_id;
    ELSIF TG_OP = 'DELETE' THEN
      UPDATE user_profiles 
      SET achievements_count = GREATEST(0, achievements_count - 1) 
      WHERE user_id = OLD.user_id;
    END IF;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
CREATE TRIGGER update_user_profile_stats_connections
  AFTER INSERT OR DELETE ON user_connections
  FOR EACH ROW EXECUTE FUNCTION update_user_profile_stats();

CREATE TRIGGER update_user_profile_stats_shares
  AFTER INSERT OR DELETE ON workout_shares
  FOR EACH ROW EXECUTE FUNCTION update_user_profile_stats();

CREATE TRIGGER update_user_profile_stats_achievements
  AFTER INSERT OR DELETE ON user_achievements
  FOR EACH ROW EXECUTE FUNCTION update_user_profile_stats();

-- Function to create activity feed entries
CREATE OR REPLACE FUNCTION create_activity_feed_entry(
  p_user_id UUID,
  p_activity_type VARCHAR,
  p_title VARCHAR,
  p_description TEXT DEFAULT NULL,
  p_related_user_id UUID DEFAULT NULL,
  p_related_workout_id UUID DEFAULT NULL,
  p_related_challenge_id UUID DEFAULT NULL,
  p_related_achievement_id UUID DEFAULT NULL,
  p_activity_data JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO activity_feed (
    user_id, activity_type, title, description,
    related_user_id, related_workout_id, related_challenge_id, 
    related_achievement_id, activity_data
  ) VALUES (
    p_user_id, p_activity_type, p_title, p_description,
    p_related_user_id, p_related_workout_id, p_related_challenge_id,
    p_related_achievement_id, p_activity_data
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to initialize default leaderboards
CREATE OR REPLACE FUNCTION initialize_default_leaderboards()
RETURNS void AS $$
BEGIN
  -- Insert default leaderboards if they don't exist
  INSERT INTO leaderboards (name, description, metric_type, time_period, category, is_active, max_entries)
  VALUES
    ('Weekly Workout Warriors', 'Most workouts completed this week', 'total_workouts', 'weekly', 'overall', true, 100),
    ('Monthly Workout Warriors', 'Most workouts completed this month', 'total_workouts', 'monthly', 'overall', true, 100),
    ('All-Time Workout Warriors', 'Most workouts completed all time', 'total_workouts', 'all_time', 'overall', true, 100),

    ('Weekly Weight Lifters', 'Most weight lifted this week', 'total_weight', 'weekly', 'strength', true, 100),
    ('Monthly Weight Lifters', 'Most weight lifted this month', 'total_weight', 'monthly', 'strength', true, 100),
    ('All-Time Weight Lifters', 'Most weight lifted all time', 'total_weight', 'all_time', 'strength', true, 100),

    ('Weekly Streak Masters', 'Longest workout streak this week', 'streak_days', 'weekly', 'consistency', true, 100),
    ('Monthly Streak Masters', 'Longest workout streak this month', 'streak_days', 'monthly', 'consistency', true, 100),
    ('All-Time Streak Masters', 'Longest workout streak all time', 'streak_days', 'all_time', 'consistency', true, 100),

    ('Weekly Time Champions', 'Most workout time this week', 'total_duration', 'weekly', 'endurance', true, 100),
    ('Monthly Time Champions', 'Most workout time this month', 'total_duration', 'monthly', 'endurance', true, 100),
    ('All-Time Time Champions', 'Most workout time all time', 'total_duration', 'all_time', 'endurance', true, 100),

    ('Weekly Rating Leaders', 'Highest average workout rating this week', 'average_rating', 'weekly', 'quality', true, 100),
    ('Monthly Rating Leaders', 'Highest average workout rating this month', 'average_rating', 'monthly', 'quality', true, 100),
    ('All-Time Rating Leaders', 'Highest average workout rating all time', 'average_rating', 'all_time', 'quality', true, 100)
  ON CONFLICT (name) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update leaderboard entries (to be called by scheduled job)
CREATE OR REPLACE FUNCTION update_leaderboard_entries()
RETURNS void AS $$
DECLARE
  lb_record RECORD;
  start_date TIMESTAMP WITH TIME ZONE;
  end_date TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get current timestamp
  end_date := NOW();

  -- Loop through all active leaderboards
  FOR lb_record IN
    SELECT * FROM leaderboards WHERE is_active = true
  LOOP
    -- Calculate period dates
    CASE lb_record.time_period
      WHEN 'daily' THEN
        start_date := DATE_TRUNC('day', end_date);
      WHEN 'weekly' THEN
        start_date := DATE_TRUNC('week', end_date);
      WHEN 'monthly' THEN
        start_date := DATE_TRUNC('month', end_date);
      WHEN 'yearly' THEN
        start_date := DATE_TRUNC('year', end_date);
      ELSE
        start_date := '1970-01-01'::TIMESTAMP WITH TIME ZONE;
    END CASE;

    -- Clear existing entries for this period
    DELETE FROM leaderboard_entries
    WHERE leaderboard_id = lb_record.id
    AND period_start >= start_date
    AND period_end <= end_date;

    -- Calculate and insert new entries based on metric type
    CASE lb_record.metric_type
      WHEN 'total_workouts' THEN
        INSERT INTO leaderboard_entries (leaderboard_id, user_id, rank, score, period_start, period_end)
        SELECT
          lb_record.id,
          user_id,
          ROW_NUMBER() OVER (ORDER BY workout_count DESC),
          workout_count,
          start_date,
          end_date
        FROM (
          SELECT
            user_id,
            COUNT(*) as workout_count
          FROM workout_logs
          WHERE completed_at >= start_date AND completed_at <= end_date
          GROUP BY user_id
          ORDER BY workout_count DESC
          LIMIT lb_record.max_entries
        ) workout_counts;

      WHEN 'total_duration' THEN
        INSERT INTO leaderboard_entries (leaderboard_id, user_id, rank, score, period_start, period_end)
        SELECT
          lb_record.id,
          user_id,
          ROW_NUMBER() OVER (ORDER BY total_duration DESC),
          total_duration,
          start_date,
          end_date
        FROM (
          SELECT
            user_id,
            COALESCE(SUM(duration), 0) as total_duration
          FROM workout_logs
          WHERE completed_at >= start_date AND completed_at <= end_date
          AND duration IS NOT NULL
          GROUP BY user_id
          ORDER BY total_duration DESC
          LIMIT lb_record.max_entries
        ) duration_totals;

      WHEN 'average_rating' THEN
        INSERT INTO leaderboard_entries (leaderboard_id, user_id, rank, score, period_start, period_end)
        SELECT
          lb_record.id,
          user_id,
          ROW_NUMBER() OVER (ORDER BY avg_rating DESC),
          avg_rating,
          start_date,
          end_date
        FROM (
          SELECT
            user_id,
            AVG(rating) as avg_rating
          FROM workout_logs
          WHERE completed_at >= start_date AND completed_at <= end_date
          AND rating IS NOT NULL
          GROUP BY user_id
          HAVING COUNT(*) >= 3 -- Minimum 3 workouts for rating leaderboard
          ORDER BY avg_rating DESC
          LIMIT lb_record.max_entries
        ) rating_averages;
    END CASE;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initialize default leaderboards
SELECT initialize_default_leaderboards();

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
