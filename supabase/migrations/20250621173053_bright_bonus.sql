/*
  # Weekly Check-in and Coach Feedback System

  1. New Tables
    - `weekly_checkins`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to users)
      - `program_week_identifier` (text, nullable - program identifier)
      - `checkin_date` (date, not null - the Sunday this check-in pertains to)
      - `submitted_at` (timestamptz, not null - when submitted)
      - `wins` (text, nullable - client wins)
      - `challenges` (text, nullable - client challenges)
      - `progress_reflection` (text, nullable - progress reflection)
      - `training_performance_rating` (integer, nullable - 1-10 rating)
      - `recovery_rating` (integer, nullable - 1-10 rating)
      - `energy_levels_rating` (integer, nullable - 1-10 rating)
      - `additional_notes_for_coach` (text, nullable - notes for coach)
      - `created_at`, `updated_at` (timestamps)
    - `coach_feedback`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to users)
      - `coach_id` (uuid, foreign key to profiles where role is coach)
      - `related_checkin_id` (uuid, foreign key to weekly_checkins, nullable)
      - `feedback_content` (text, not null)
      - `status` (text enum - draft_by_ai, draft_by_coach, published, read_by_client)
      - `published_at` (timestamptz, nullable)
      - `read_at` (timestamptz, nullable - when read by client)
      - `created_at`, `updated_at` (timestamps)

  2. Security
    - Enable RLS on both tables
    - Users can manage their own check-ins
    - Users can read their own coach feedback
    - Coaches can read client check-ins and manage feedback for their clients
    - Service role can manage all data

  3. Indexes
    - Optimized for common queries by user_id and dates
    - Unique constraint on user_id + checkin_date for weekly_checkins
*/

-- Create weekly_checkins table
CREATE TABLE IF NOT EXISTS weekly_checkins (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  program_week_identifier text,
  checkin_date date NOT NULL,
  submitted_at timestamptz NOT NULL DEFAULT now(),
  wins text,
  challenges text,
  progress_reflection text,
  training_performance_rating integer CHECK (training_performance_rating >= 1 AND training_performance_rating <= 10),
  recovery_rating integer CHECK (recovery_rating >= 1 AND recovery_rating <= 10),
  energy_levels_rating integer CHECK (energy_levels_rating >= 1 AND energy_levels_rating <= 10),
  additional_notes_for_coach text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, checkin_date)
);

-- Create coach_feedback table
CREATE TABLE IF NOT EXISTS coach_feedback (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  coach_id uuid REFERENCES profiles(id),
  related_checkin_id uuid REFERENCES weekly_checkins(id),
  feedback_content text NOT NULL,
  status text NOT NULL DEFAULT 'draft_by_ai' CHECK (status IN ('draft_by_ai', 'draft_by_coach', 'published', 'read_by_client')),
  published_at timestamptz,
  read_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create indexes for weekly_checkins
CREATE INDEX IF NOT EXISTS weekly_checkins_user_id_idx ON weekly_checkins(user_id);
CREATE INDEX IF NOT EXISTS weekly_checkins_user_checkin_date_idx ON weekly_checkins(user_id, checkin_date DESC);
CREATE INDEX IF NOT EXISTS weekly_checkins_checkin_date_idx ON weekly_checkins(checkin_date);

-- Create indexes for coach_feedback
CREATE INDEX IF NOT EXISTS coach_feedback_user_id_idx ON coach_feedback(user_id);
CREATE INDEX IF NOT EXISTS coach_feedback_coach_id_idx ON coach_feedback(coach_id);
CREATE INDEX IF NOT EXISTS coach_feedback_status_idx ON coach_feedback(status);
CREATE INDEX IF NOT EXISTS coach_feedback_user_status_published_idx ON coach_feedback(user_id, status, published_at DESC);
CREATE INDEX IF NOT EXISTS coach_feedback_related_checkin_idx ON coach_feedback(related_checkin_id);

-- Enable Row Level Security
ALTER TABLE weekly_checkins ENABLE ROW LEVEL SECURITY;
ALTER TABLE coach_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies for weekly_checkins
CREATE POLICY "Users can manage their own weekly check-ins"
  ON weekly_checkins
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Coaches can read client weekly check-ins"
  ON weekly_checkins
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'coach'
    )
  );

CREATE POLICY "Service role can manage weekly check-ins"
  ON weekly_checkins
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- RLS Policies for coach_feedback
CREATE POLICY "Users can read their own coach feedback"
  ON coach_feedback
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can update feedback read status"
  ON coach_feedback
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Coaches can manage feedback for their clients"
  ON coach_feedback
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'coach'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'coach'
    )
  );

CREATE POLICY "Service role can manage coach feedback"
  ON coach_feedback
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Add updated_at triggers
CREATE TRIGGER on_weekly_checkins_updated
  BEFORE UPDATE ON weekly_checkins
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER on_coach_feedback_updated
  BEFORE UPDATE ON coach_feedback
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();