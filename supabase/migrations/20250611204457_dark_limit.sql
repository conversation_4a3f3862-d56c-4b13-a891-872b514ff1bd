/*
  # Create workouts table

  1. New Tables
    - `workouts`
      - `id` (uuid, primary key)
      - `program_week_id` (uuid, foreign key to program_weeks.id)
      - `day_identifier` (text)
      - `title` (text)
      - `description` (text)
      - `estimated_duration_minutes` (integer)
      - `order_in_week` (integer)

  2. Security
    - Enable RLS on `workouts` table
    - Add policies for users to read their own workouts
*/

CREATE TABLE IF NOT EXISTS workouts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  program_week_id uuid NOT NULL REFERENCES program_weeks(id) ON DELETE CASCADE,
  day_identifier text NOT NULL,
  title text NOT NULL,
  description text,
  estimated_duration_minutes integer,
  order_in_week integer NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE workouts ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users can read their own workouts"
  ON workouts
  FOR SELECT
  TO authenticated
  USING (
    program_week_id IN (
      SELECT pw.id FROM program_weeks pw
      JOIN workout_programs wp ON pw.workout_program_id = wp.id
      WHERE wp.user_id = auth.uid()
    )
  );

CREATE POLICY "Service role can manage workouts"
  ON workouts
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Trigger for updated_at
CREATE TRIGGER on_workouts_updated
  BEFORE UPDATE ON workouts
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Indexes
CREATE INDEX workouts_program_week_id_idx ON workouts (program_week_id);
CREATE INDEX workouts_order_in_week_idx ON workouts (order_in_week);