/*
  # Fix Generate Feedback Webhook

  1. Changes
    - Update the generate_feedback_webhook function to send a simplified payload
    - Send only the checkInId directly instead of the entire record object
    - This ensures the Edge Function receives data in the expected format

  2. Security
    - Maintains existing security context
    - Reduces data exposure by only sending the necessary ID
*/

-- Drop the existing trigger
DROP TRIGGER IF EXISTS generate_feedback_webhook ON weekly_checkins;

-- Update the webhook function to send a simpler payload
CREATE OR REPLACE FUNCTION generate_feedback_webhook()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM
    net.http_post(
      url := current_setting('app.settings.supabase_url') || '/functions/v1/generate-feedback',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key')
      ),
      -- Send only the checkInId directly, not nested in a record object
      body := jsonb_build_object('checkInId', NEW.id)
    );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger with the updated function
CREATE TRIGGER generate_feedback_webhook
  AFTER INSERT ON weekly_checkins
  FOR EACH ROW
  EXECUTE FUNCTION generate_feedback_webhook();

-- Set function owner to postgres for elevated privileges
ALTER FUNCTION generate_feedback_webhook() OWNER TO postgres;

-- Add helpful comment
COMMENT ON FUNCTION generate_feedback_webhook() IS 'Triggers the generate-feedback Edge Function when a new weekly check-in is submitted, sending only the checkInId';