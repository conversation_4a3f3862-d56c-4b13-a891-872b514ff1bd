-- Premium Subscription System Database Schema
-- This migration adds tables and functions for subscription management, billing, and premium features

-- Subscription plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  stripe_price_id VARCHAR(255) UNIQUE NOT NULL,
  stripe_product_id VARCHAR(255) NOT NULL,
  
  -- Pricing
  price_monthly DECIMAL(10,2),
  price_yearly DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Plan details
  billing_interval VARCHAR(20) NOT NULL CHECK (billing_interval IN ('month', 'year')),
  trial_period_days INTEGER DEFAULT 0,
  
  -- Features
  features JSONB DEFAULT '{}',
  limits JSONB DEFAULT '{}',
  
  -- Status
  is_active BOOLEAN DEFAULT true,
  is_popular BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  plan_id UUID REFERENCES subscription_plans(id) ON DELETE SET NULL,
  
  -- Stripe data
  stripe_customer_id VARCHAR(255),
  stripe_subscription_id VARCHAR(255) UNIQUE,
  stripe_payment_method_id VARCHAR(255),
  
  -- Subscription details
  status VARCHAR(50) NOT NULL DEFAULT 'inactive' CHECK (status IN (
    'incomplete', 'incomplete_expired', 'trialing', 'active', 
    'past_due', 'canceled', 'unpaid', 'paused'
  )),
  
  -- Dates
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_start TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  canceled_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  
  -- Billing
  amount DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'USD',
  billing_interval VARCHAR(20),
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Payment methods table
CREATE TABLE IF NOT EXISTS user_payment_methods (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  stripe_payment_method_id VARCHAR(255) UNIQUE NOT NULL,
  
  -- Payment method details
  type VARCHAR(50) NOT NULL, -- 'card', 'bank_account', etc.
  brand VARCHAR(50), -- 'visa', 'mastercard', etc.
  last4 VARCHAR(4),
  exp_month INTEGER,
  exp_year INTEGER,
  
  -- Status
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  
  -- Metadata
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Billing history table
CREATE TABLE IF NOT EXISTS billing_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE SET NULL,
  
  -- Stripe data
  stripe_invoice_id VARCHAR(255) UNIQUE,
  stripe_payment_intent_id VARCHAR(255),
  stripe_charge_id VARCHAR(255),
  
  -- Invoice details
  invoice_number VARCHAR(100),
  amount DECIMAL(10,2) NOT NULL,
  amount_paid DECIMAL(10,2) DEFAULT 0,
  amount_due DECIMAL(10,2) DEFAULT 0,
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Status
  status VARCHAR(50) NOT NULL CHECK (status IN (
    'draft', 'open', 'paid', 'uncollectible', 'void'
  )),
  
  -- Dates
  invoice_date TIMESTAMP WITH TIME ZONE NOT NULL,
  due_date TIMESTAMP WITH TIME ZONE,
  paid_at TIMESTAMP WITH TIME ZONE,
  
  -- Details
  description TEXT,
  invoice_url VARCHAR(500),
  pdf_url VARCHAR(500),
  
  -- Line items
  line_items JSONB DEFAULT '[]',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Promotional codes table
CREATE TABLE IF NOT EXISTS promotional_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code VARCHAR(50) UNIQUE NOT NULL,
  stripe_coupon_id VARCHAR(255),
  stripe_promotion_code_id VARCHAR(255),
  
  -- Discount details
  discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount')),
  discount_value DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Usage limits
  max_redemptions INTEGER,
  times_redeemed INTEGER DEFAULT 0,
  
  -- Validity
  valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  valid_until TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  
  -- Restrictions
  applicable_plans UUID[] DEFAULT '{}',
  first_time_customers_only BOOLEAN DEFAULT false,
  minimum_amount DECIMAL(10,2),
  
  -- Metadata
  description TEXT,
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Promo code usage tracking
CREATE TABLE IF NOT EXISTS promo_code_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  promo_code_id UUID REFERENCES promotional_codes(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE SET NULL,
  
  -- Usage details
  discount_amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  
  used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(promo_code_id, user_id)
);

-- Subscription analytics table
CREATE TABLE IF NOT EXISTS subscription_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  date DATE NOT NULL,
  
  -- Metrics
  new_subscriptions INTEGER DEFAULT 0,
  canceled_subscriptions INTEGER DEFAULT 0,
  active_subscriptions INTEGER DEFAULT 0,
  trial_subscriptions INTEGER DEFAULT 0,
  
  -- Revenue
  monthly_recurring_revenue DECIMAL(12,2) DEFAULT 0,
  annual_recurring_revenue DECIMAL(12,2) DEFAULT 0,
  total_revenue DECIMAL(12,2) DEFAULT 0,
  
  -- Churn
  churn_rate DECIMAL(5,4) DEFAULT 0,
  retention_rate DECIMAL(5,4) DEFAULT 0,
  
  -- By plan
  plan_metrics JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(date)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscription_plans_active ON subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_stripe_price ON subscription_plans(stripe_price_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_id ON user_subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_period ON user_subscriptions(current_period_start, current_period_end);
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON user_payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_default ON user_payment_methods(user_id, is_default);
CREATE INDEX IF NOT EXISTS idx_billing_history_user_id ON billing_history(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_history_status ON billing_history(status);
CREATE INDEX IF NOT EXISTS idx_billing_history_date ON billing_history(invoice_date);
CREATE INDEX IF NOT EXISTS idx_promotional_codes_code ON promotional_codes(code);
CREATE INDEX IF NOT EXISTS idx_promotional_codes_active ON promotional_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_promo_usage_user ON promo_code_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_analytics_date ON subscription_analytics(date);

-- Enable RLS on all tables
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE billing_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE promotional_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE promo_code_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Subscription plans (public read for active plans)
CREATE POLICY "Anyone can view active subscription plans" ON subscription_plans
  FOR SELECT USING (is_active = true);

-- User subscriptions (users can only see their own)
CREATE POLICY "Users can view their own subscription" ON user_subscriptions
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own subscription" ON user_subscriptions
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "System can manage subscriptions" ON user_subscriptions
  FOR ALL USING (true); -- Managed by functions

-- Payment methods (users can only see their own)
CREATE POLICY "Users can view their own payment methods" ON user_payment_methods
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own payment methods" ON user_payment_methods
  FOR ALL USING (user_id = auth.uid());

-- Billing history (users can only see their own)
CREATE POLICY "Users can view their own billing history" ON billing_history
  FOR SELECT USING (user_id = auth.uid());

-- Promotional codes (public read for active codes)
CREATE POLICY "Anyone can view active promotional codes" ON promotional_codes
  FOR SELECT USING (is_active = true AND valid_from <= NOW() AND (valid_until IS NULL OR valid_until >= NOW()));

-- Promo code usage (users can see their own usage)
CREATE POLICY "Users can view their own promo usage" ON promo_code_usage
  FOR SELECT USING (user_id = auth.uid());

-- Subscription analytics (admin only - will be handled by functions)
CREATE POLICY "Admin can view subscription analytics" ON subscription_analytics
  FOR SELECT USING (false); -- Will be accessed through functions

-- Functions for subscription management

-- Function to get user's current subscription
CREATE OR REPLACE FUNCTION get_user_subscription(p_user_id UUID DEFAULT auth.uid())
RETURNS TABLE (
  subscription_id UUID,
  plan_name VARCHAR,
  status VARCHAR,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  features JSONB,
  limits JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    us.id,
    sp.name,
    us.status,
    us.current_period_end,
    us.trial_end,
    sp.features,
    sp.limits
  FROM user_subscriptions us
  LEFT JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has premium access
CREATE OR REPLACE FUNCTION has_premium_access(p_user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
DECLARE
  subscription_status VARCHAR;
  trial_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
  SELECT status, trial_end INTO subscription_status, trial_end_date
  FROM user_subscriptions
  WHERE user_id = p_user_id;
  
  -- Check if user has active subscription or valid trial
  RETURN (
    subscription_status IN ('active', 'trialing') OR
    (subscription_status = 'trialing' AND trial_end_date > NOW())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update subscription analytics
CREATE OR REPLACE FUNCTION update_subscription_analytics(p_date DATE DEFAULT CURRENT_DATE)
RETURNS void AS $$
DECLARE
  new_subs INTEGER;
  canceled_subs INTEGER;
  active_subs INTEGER;
  trial_subs INTEGER;
  mrr DECIMAL(12,2);
  arr DECIMAL(12,2);
  total_rev DECIMAL(12,2);
BEGIN
  -- Calculate metrics for the date
  SELECT COUNT(*) INTO new_subs
  FROM user_subscriptions
  WHERE DATE(created_at) = p_date;
  
  SELECT COUNT(*) INTO canceled_subs
  FROM user_subscriptions
  WHERE DATE(canceled_at) = p_date;
  
  SELECT COUNT(*) INTO active_subs
  FROM user_subscriptions
  WHERE status = 'active';
  
  SELECT COUNT(*) INTO trial_subs
  FROM user_subscriptions
  WHERE status = 'trialing';
  
  -- Calculate revenue
  SELECT 
    COALESCE(SUM(CASE WHEN billing_interval = 'month' THEN amount ELSE 0 END), 0),
    COALESCE(SUM(CASE WHEN billing_interval = 'year' THEN amount ELSE 0 END), 0)
  INTO mrr, arr
  FROM user_subscriptions
  WHERE status = 'active';
  
  -- Get total revenue for the date
  SELECT COALESCE(SUM(amount_paid), 0) INTO total_rev
  FROM billing_history
  WHERE DATE(paid_at) = p_date AND status = 'paid';
  
  -- Insert or update analytics
  INSERT INTO subscription_analytics (
    date, new_subscriptions, canceled_subscriptions, active_subscriptions,
    trial_subscriptions, monthly_recurring_revenue, annual_recurring_revenue,
    total_revenue
  ) VALUES (
    p_date, new_subs, canceled_subs, active_subs, trial_subs, mrr, arr, total_rev
  )
  ON CONFLICT (date) DO UPDATE SET
    new_subscriptions = EXCLUDED.new_subscriptions,
    canceled_subscriptions = EXCLUDED.canceled_subscriptions,
    active_subscriptions = EXCLUDED.active_subscriptions,
    trial_subscriptions = EXCLUDED.trial_subscriptions,
    monthly_recurring_revenue = EXCLUDED.monthly_recurring_revenue,
    annual_recurring_revenue = EXCLUDED.annual_recurring_revenue,
    total_revenue = EXCLUDED.total_revenue;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update subscription analytics daily
CREATE OR REPLACE FUNCTION trigger_update_subscription_analytics()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM update_subscription_analytics(CURRENT_DATE);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_analytics_on_subscription_change
  AFTER INSERT OR UPDATE OR DELETE ON user_subscriptions
  FOR EACH ROW EXECUTE FUNCTION trigger_update_subscription_analytics();

CREATE TRIGGER update_analytics_on_billing_change
  AFTER INSERT OR UPDATE ON billing_history
  FOR EACH ROW EXECUTE FUNCTION trigger_update_subscription_analytics();

-- Function to initialize default subscription plans
CREATE OR REPLACE FUNCTION initialize_subscription_plans()
RETURNS void AS $$
BEGIN
  -- Insert default subscription plans if they don't exist
  INSERT INTO subscription_plans (
    name, description, stripe_price_id, stripe_product_id,
    price_monthly, price_yearly, billing_interval, trial_period_days,
    features, limits, is_active, is_popular, sort_order
  ) VALUES
    -- Free Plan
    (
      'Free',
      'Get started with basic features',
      'price_free',
      'prod_free',
      0, 0, 'month', 0,
      '{"basic_workouts": true, "progress_tracking": true, "community_access": true}',
      '{"max_programs": 1, "max_workouts_per_month": 10, "max_progress_photos": 5, "advanced_analytics": false, "priority_support": false, "custom_programs": false, "nutrition_plans": false, "video_workouts": false, "offline_access": false}',
      true, false, 1
    ),
    -- Basic Monthly
    (
      'Basic',
      'Essential features for serious fitness enthusiasts',
      'price_basic_monthly',
      'prod_basic',
      9.99, null, 'month', 7,
      '{"unlimited_workouts": true, "progress_tracking": true, "community_access": true, "basic_analytics": true, "email_support": true}',
      '{"max_programs": 3, "max_workouts_per_month": -1, "max_progress_photos": 20, "advanced_analytics": false, "priority_support": false, "custom_programs": false, "nutrition_plans": false, "video_workouts": true, "offline_access": true}',
      true, false, 2
    ),
    -- Basic Yearly
    (
      'Basic',
      'Essential features for serious fitness enthusiasts',
      'price_basic_yearly',
      'prod_basic',
      null, 99.99, 'year', 14,
      '{"unlimited_workouts": true, "progress_tracking": true, "community_access": true, "basic_analytics": true, "email_support": true}',
      '{"max_programs": 3, "max_workouts_per_month": -1, "max_progress_photos": 20, "advanced_analytics": false, "priority_support": false, "custom_programs": false, "nutrition_plans": false, "video_workouts": true, "offline_access": true}',
      true, false, 3
    ),
    -- Pro Monthly
    (
      'Pro',
      'Advanced features for fitness professionals and enthusiasts',
      'price_pro_monthly',
      'prod_pro',
      19.99, null, 'month', 14,
      '{"unlimited_workouts": true, "progress_tracking": true, "community_access": true, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true, "ai_coaching": true}',
      '{"max_programs": 10, "max_workouts_per_month": -1, "max_progress_photos": 100, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true}',
      true, true, 4
    ),
    -- Pro Yearly
    (
      'Pro',
      'Advanced features for fitness professionals and enthusiasts',
      'price_pro_yearly',
      'prod_pro',
      null, 199.99, 'year', 14,
      '{"unlimited_workouts": true, "progress_tracking": true, "community_access": true, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true, "ai_coaching": true}',
      '{"max_programs": 10, "max_workouts_per_month": -1, "max_progress_photos": 100, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true}',
      true, true, 5
    ),
    -- Elite Monthly
    (
      'Elite',
      'Premium features for serious athletes and coaches',
      'price_elite_monthly',
      'prod_elite',
      39.99, null, 'month', 14,
      '{"unlimited_workouts": true, "progress_tracking": true, "community_access": true, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true, "ai_coaching": true, "personal_coaching": true, "white_label": true}',
      '{"max_programs": -1, "max_workouts_per_month": -1, "max_progress_photos": -1, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true}',
      true, false, 6
    ),
    -- Elite Yearly
    (
      'Elite',
      'Premium features for serious athletes and coaches',
      'price_elite_yearly',
      'prod_elite',
      null, 399.99, 'year', 14,
      '{"unlimited_workouts": true, "progress_tracking": true, "community_access": true, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true, "ai_coaching": true, "personal_coaching": true, "white_label": true}',
      '{"max_programs": -1, "max_workouts_per_month": -1, "max_progress_photos": -1, "advanced_analytics": true, "priority_support": true, "custom_programs": true, "nutrition_plans": true, "video_workouts": true, "offline_access": true}',
      true, false, 7
    )
  ON CONFLICT (stripe_price_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check feature access
CREATE OR REPLACE FUNCTION check_feature_access(
  p_feature_name VARCHAR,
  p_user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
DECLARE
  user_features JSONB;
  subscription_status VARCHAR;
BEGIN
  -- Get user's subscription features
  SELECT sp.features, us.status INTO user_features, subscription_status
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id;

  -- If no subscription, check free plan features
  IF user_features IS NULL THEN
    SELECT features INTO user_features
    FROM subscription_plans
    WHERE name = 'Free' AND billing_interval = 'month';
  END IF;

  -- Check if subscription is active
  IF subscription_status NOT IN ('active', 'trialing') THEN
    SELECT features INTO user_features
    FROM subscription_plans
    WHERE name = 'Free' AND billing_interval = 'month';
  END IF;

  -- Return feature access
  RETURN COALESCE((user_features ->> p_feature_name)::BOOLEAN, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check usage limits
CREATE OR REPLACE FUNCTION check_usage_limit(
  p_limit_name VARCHAR,
  p_current_usage INTEGER,
  p_user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
DECLARE
  user_limits JSONB;
  limit_value INTEGER;
  subscription_status VARCHAR;
BEGIN
  -- Get user's subscription limits
  SELECT sp.limits, us.status INTO user_limits, subscription_status
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id;

  -- If no subscription, use free plan limits
  IF user_limits IS NULL THEN
    SELECT limits INTO user_limits
    FROM subscription_plans
    WHERE name = 'Free' AND billing_interval = 'month';
  END IF;

  -- Check if subscription is active
  IF subscription_status NOT IN ('active', 'trialing') THEN
    SELECT limits INTO user_limits
    FROM subscription_plans
    WHERE name = 'Free' AND billing_interval = 'month';
  END IF;

  -- Get the limit value
  limit_value := (user_limits ->> p_limit_name)::INTEGER;

  -- -1 means unlimited
  IF limit_value = -1 THEN
    RETURN true;
  END IF;

  -- Check if current usage is within limit
  RETURN p_current_usage < limit_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's current plan info
CREATE OR REPLACE FUNCTION get_user_plan_info(p_user_id UUID DEFAULT auth.uid())
RETURNS TABLE (
  plan_name VARCHAR,
  plan_tier VARCHAR,
  features JSONB,
  limits JSONB,
  status VARCHAR,
  trial_end TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    sp.name,
    CASE
      WHEN sp.name = 'Free' THEN 'free'
      WHEN sp.name = 'Basic' THEN 'basic'
      WHEN sp.name = 'Pro' THEN 'pro'
      WHEN sp.name = 'Elite' THEN 'elite'
      ELSE 'free'
    END as plan_tier,
    sp.features,
    sp.limits,
    us.status,
    us.trial_end,
    us.current_period_end
  FROM user_subscriptions us
  JOIN subscription_plans sp ON us.plan_id = sp.id
  WHERE us.user_id = p_user_id

  UNION ALL

  -- Return free plan if no subscription
  SELECT
    'Free'::VARCHAR,
    'free'::VARCHAR,
    sp.features,
    sp.limits,
    'free'::VARCHAR,
    NULL::TIMESTAMP WITH TIME ZONE,
    NULL::TIMESTAMP WITH TIME ZONE
  FROM subscription_plans sp
  WHERE sp.name = 'Free' AND sp.billing_interval = 'month'
  AND NOT EXISTS (
    SELECT 1 FROM user_subscriptions WHERE user_id = p_user_id
  )

  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initialize default subscription plans
SELECT initialize_subscription_plans();

-- Function to increment promo code usage
CREATE OR REPLACE FUNCTION increment_promo_usage(promo_code_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE promotional_codes
  SET times_redeemed = times_redeemed + 1
  WHERE id = promo_code_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get promo code statistics
CREATE OR REPLACE FUNCTION get_promo_code_stats(promo_code_id UUID)
RETURNS TABLE (
  total_usage INTEGER,
  total_discount_amount DECIMAL(12,2),
  unique_users INTEGER,
  conversion_rate DECIMAL(5,4)
) AS $$
DECLARE
  total_views INTEGER := 0;
BEGIN
  -- Get usage statistics
  SELECT
    COUNT(*)::INTEGER,
    COALESCE(SUM(discount_amount), 0)::DECIMAL(12,2),
    COUNT(DISTINCT user_id)::INTEGER
  INTO total_usage, total_discount_amount, unique_users
  FROM promo_code_usage
  WHERE promo_code_usage.promo_code_id = get_promo_code_stats.promo_code_id;

  -- For now, set conversion rate to 0 (would need view tracking to calculate properly)
  conversion_rate := 0;

  RETURN QUERY SELECT total_usage, total_discount_amount, unique_users, conversion_rate;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check trial eligibility
CREATE OR REPLACE FUNCTION check_trial_eligibility(p_user_id UUID DEFAULT auth.uid())
RETURNS TABLE (
  eligible BOOLEAN,
  reason VARCHAR,
  has_used_trial BOOLEAN,
  current_status VARCHAR
) AS $$
DECLARE
  subscription_record RECORD;
  trial_used BOOLEAN := false;
  current_sub_status VARCHAR := 'none';
BEGIN
  -- Check if user has any subscription history
  SELECT * INTO subscription_record
  FROM user_subscriptions
  WHERE user_id = p_user_id
  ORDER BY created_at DESC
  LIMIT 1;

  IF subscription_record IS NOT NULL THEN
    current_sub_status := subscription_record.status;

    -- Check if they've used a trial before
    IF subscription_record.trial_start IS NOT NULL THEN
      trial_used := true;
    END IF;

    -- If they have an active subscription, they're not eligible
    IF subscription_record.status IN ('active', 'trialing') THEN
      RETURN QUERY SELECT false, 'Already has active subscription', trial_used, current_sub_status;
      RETURN;
    END IF;

    -- If they've used a trial before, they're not eligible
    IF trial_used THEN
      RETURN QUERY SELECT false, 'Trial already used', trial_used, current_sub_status;
      RETURN;
    END IF;
  END IF;

  -- User is eligible for trial
  RETURN QUERY SELECT true, 'Eligible for trial', trial_used, current_sub_status;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create trial subscription
CREATE OR REPLACE FUNCTION create_trial_subscription(
  p_user_id UUID,
  p_plan_id UUID,
  p_trial_days INTEGER DEFAULT 14
)
RETURNS UUID AS $$
DECLARE
  subscription_id UUID;
  trial_start_date TIMESTAMP WITH TIME ZONE;
  trial_end_date TIMESTAMP WITH TIME ZONE;
BEGIN
  trial_start_date := NOW();
  trial_end_date := trial_start_date + (p_trial_days || ' days')::INTERVAL;

  INSERT INTO user_subscriptions (
    user_id,
    plan_id,
    status,
    trial_start,
    trial_end,
    current_period_start,
    current_period_end
  ) VALUES (
    p_user_id,
    p_plan_id,
    'trialing',
    trial_start_date,
    trial_end_date,
    trial_start_date,
    trial_end_date
  ) RETURNING id INTO subscription_id;

  RETURN subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get promotional campaigns
CREATE OR REPLACE FUNCTION get_active_promotions()
RETURNS TABLE (
  id UUID,
  title VARCHAR,
  description TEXT,
  discount_type VARCHAR,
  discount_value DECIMAL,
  code VARCHAR,
  valid_until TIMESTAMP WITH TIME ZONE,
  usage_count INTEGER,
  max_usage INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    pc.id,
    COALESCE(pc.description, 'Special Offer') as title,
    pc.description,
    pc.discount_type,
    pc.discount_value,
    pc.code,
    pc.valid_until,
    pc.times_redeemed as usage_count,
    pc.max_redemptions as max_usage
  FROM promotional_codes pc
  WHERE pc.is_active = true
  AND pc.valid_from <= NOW()
  AND (pc.valid_until IS NULL OR pc.valid_until >= NOW())
  AND (pc.max_redemptions IS NULL OR pc.times_redeemed < pc.max_redemptions)
  ORDER BY pc.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initialize some default promotional codes
INSERT INTO promotional_codes (
  code, description, discount_type, discount_value, currency,
  max_redemptions, valid_from, valid_until, is_active,
  first_time_customers_only, applicable_plans
) VALUES
  (
    'WELCOME20',
    'Welcome discount for new users',
    'percentage',
    20,
    'USD',
    1000,
    NOW(),
    NOW() + INTERVAL '30 days',
    true,
    true,
    '{}'
  ),
  (
    'STUDENT50',
    'Student discount',
    'percentage',
    50,
    'USD',
    null,
    NOW(),
    NOW() + INTERVAL '365 days',
    true,
    false,
    '{}'
  ),
  (
    'ANNUAL20',
    'Annual plan discount',
    'percentage',
    20,
    'USD',
    null,
    NOW(),
    NOW() + INTERVAL '90 days',
    true,
    false,
    '{}'
  )
ON CONFLICT (code) DO NOTHING;

-- Function to calculate churn rate
CREATE OR REPLACE FUNCTION calculate_churn_rate()
RETURNS DECIMAL AS $$
DECLARE
  total_active INTEGER;
  total_canceled INTEGER;
  churn_rate DECIMAL;
BEGIN
  -- Get active subscriptions at start of month
  SELECT COUNT(*) INTO total_active
  FROM user_subscriptions
  WHERE status IN ('active', 'trialing')
  AND created_at <= date_trunc('month', CURRENT_DATE);

  -- Get cancellations this month
  SELECT COUNT(*) INTO total_canceled
  FROM user_subscriptions
  WHERE canceled_at >= date_trunc('month', CURRENT_DATE)
  AND canceled_at < date_trunc('month', CURRENT_DATE) + INTERVAL '1 month';

  IF total_active > 0 THEN
    churn_rate := total_canceled::DECIMAL / total_active::DECIMAL;
  ELSE
    churn_rate := 0;
  END IF;

  RETURN churn_rate;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get churn by plan
CREATE OR REPLACE FUNCTION get_churn_by_plan()
RETURNS TABLE (
  plan_name VARCHAR,
  churn_rate DECIMAL,
  subscribers INTEGER,
  churned INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    sp.name,
    CASE
      WHEN COUNT(us.id) > 0 THEN
        COUNT(CASE WHEN us.canceled_at >= date_trunc('month', CURRENT_DATE) THEN 1 END)::DECIMAL / COUNT(us.id)::DECIMAL
      ELSE 0
    END as churn_rate,
    COUNT(us.id)::INTEGER as subscribers,
    COUNT(CASE WHEN us.canceled_at >= date_trunc('month', CURRENT_DATE) THEN 1 END)::INTEGER as churned
  FROM subscription_plans sp
  LEFT JOIN user_subscriptions us ON sp.id = us.plan_id
  WHERE sp.is_active = true
  GROUP BY sp.id, sp.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to identify at-risk users
CREATE OR REPLACE FUNCTION identify_at_risk_users()
RETURNS TABLE (
  user_id UUID,
  risk_score DECIMAL,
  factors TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    us.user_id,
    CASE
      WHEN us.status = 'past_due' THEN 0.9
      WHEN us.current_period_end < CURRENT_DATE + INTERVAL '7 days' THEN 0.7
      WHEN us.status = 'trialing' AND us.trial_end < CURRENT_DATE + INTERVAL '3 days' THEN 0.6
      ELSE 0.3
    END as risk_score,
    ARRAY[
      CASE WHEN us.status = 'past_due' THEN 'Payment overdue' END,
      CASE WHEN us.current_period_end < CURRENT_DATE + INTERVAL '7 days' THEN 'Subscription ending soon' END,
      CASE WHEN us.status = 'trialing' AND us.trial_end < CURRENT_DATE + INTERVAL '3 days' THEN 'Trial ending soon' END
    ]::TEXT[] as factors
  FROM user_subscriptions us
  WHERE us.status IN ('active', 'trialing', 'past_due')
  AND (
    us.status = 'past_due' OR
    us.current_period_end < CURRENT_DATE + INTERVAL '7 days' OR
    (us.status = 'trialing' AND us.trial_end < CURRENT_DATE + INTERVAL '3 days')
  )
  ORDER BY risk_score DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate revenue metrics
CREATE OR REPLACE FUNCTION calculate_revenue_metrics()
RETURNS TABLE (
  mrr DECIMAL,
  arr DECIMAL,
  arpu DECIMAL,
  ltv DECIMAL,
  cac DECIMAL,
  ltv_cac_ratio DECIMAL,
  payback_period DECIMAL
) AS $$
DECLARE
  monthly_revenue DECIMAL;
  annual_revenue DECIMAL;
  active_users INTEGER;
  avg_revenue_per_user DECIMAL;
  avg_customer_lifespan DECIMAL;
  customer_acquisition_cost DECIMAL;
BEGIN
  -- Calculate MRR
  SELECT COALESCE(SUM(amount), 0) INTO monthly_revenue
  FROM user_subscriptions
  WHERE status = 'active' AND billing_interval = 'month';

  -- Calculate ARR
  SELECT COALESCE(SUM(amount), 0) INTO annual_revenue
  FROM user_subscriptions
  WHERE status = 'active' AND billing_interval = 'year';

  -- Calculate ARPU
  SELECT COUNT(*) INTO active_users
  FROM user_subscriptions
  WHERE status IN ('active', 'trialing');

  IF active_users > 0 THEN
    avg_revenue_per_user := (monthly_revenue + (annual_revenue / 12)) / active_users;
  ELSE
    avg_revenue_per_user := 0;
  END IF;

  -- Estimate LTV (simplified calculation)
  avg_customer_lifespan := 24; -- Assume 24 months average lifespan
  ltv := avg_revenue_per_user * avg_customer_lifespan;

  -- Estimate CAC (simplified - would need marketing spend data)
  customer_acquisition_cost := 50; -- Placeholder value

  RETURN QUERY SELECT
    monthly_revenue,
    annual_revenue,
    avg_revenue_per_user,
    ltv,
    customer_acquisition_cost,
    CASE WHEN customer_acquisition_cost > 0 THEN ltv / customer_acquisition_cost ELSE 0 END,
    CASE WHEN avg_revenue_per_user > 0 THEN customer_acquisition_cost / avg_revenue_per_user ELSE 0 END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get cohort analysis
CREATE OR REPLACE FUNCTION get_cohort_analysis(cohort_type VARCHAR DEFAULT 'monthly')
RETURNS TABLE (
  cohort_period VARCHAR,
  period_number INTEGER,
  users INTEGER,
  retained_users INTEGER,
  retention_rate DECIMAL
) AS $$
BEGIN
  -- Simplified cohort analysis
  -- In a real implementation, this would be more complex
  RETURN QUERY
  SELECT
    to_char(date_trunc(cohort_type, us.created_at), 'YYYY-MM') as cohort_period,
    1 as period_number,
    COUNT(*)::INTEGER as users,
    COUNT(CASE WHEN us.status IN ('active', 'trialing') THEN 1 END)::INTEGER as retained_users,
    CASE
      WHEN COUNT(*) > 0 THEN
        COUNT(CASE WHEN us.status IN ('active', 'trialing') THEN 1 END)::DECIMAL / COUNT(*)::DECIMAL
      ELSE 0
    END as retention_rate
  FROM user_subscriptions us
  WHERE us.created_at >= CURRENT_DATE - INTERVAL '12 months'
  GROUP BY date_trunc(cohort_type, us.created_at)
  ORDER BY cohort_period;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
