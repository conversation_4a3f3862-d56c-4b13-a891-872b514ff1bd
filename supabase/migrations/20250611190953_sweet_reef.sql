/*
  # Fix Profile Insert Policy

  1. Security Policy Addition
    - Add INSERT policy for `profiles` table to allow users to create their own profile
    - This enables the `handle_new_user` trigger to successfully insert profile records during user registration

  2. Changes
    - Add RLS policy for INSERT operations on profiles table
    - Policy allows authenticated users to insert records where auth.uid() matches the profile id
*/

-- Add INSERT policy for profiles table
CREATE POLICY "Users can insert their own profile"
  ON public.profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);