/*
  # Remove redundant RLS policies from profiles table

  1. Changes
    - Remove duplicate INSERT policies for authenticated users
    - Keep only essential policies for proper functionality

  2. Rationale
    - Profile creation is handled automatically by handle_new_user trigger
    - Authenticated users don't need separate INSERT policies for their own profile
    - This streamlines the RLS setup and removes unnecessary policies

  3. Remaining policies after cleanup
    - INSERT policy for anon role (essential for registration trigger)
    - INSERT policy for service_role (though redundant, kept for clarity)
    - SELECT policy for authenticated users to read own profile
    - UPDATE policy for authenticated users to update own profile
*/

-- Remove the redundant INSERT policies for authenticated users
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;

-- Verify the remaining policies are correct
-- The following policies should remain:
-- 1. "Allow anon to insert profile during registration" (anon role) - ESSENTIAL
-- 2. "Service role can insert profiles" (service_role) - OK but redundant
-- 3. "Users can read own profile" (authenticated role) - ESSENTIAL
-- 4. "Users can update own profile" (authenticated role) - ESSENTIAL