/*
  # Create Workout Logging Tables

  1. New Tables
    - `workout_logs` - Stores completed workout sessions
    - `workout_exercise_logs` - Stores exercise data for each workout session

  2. Security
    - Enable RLS on both tables
    - Add policies for users to manage their own logs
    - Add policies for coaches to read client logs
    - Add policies for service role to manage all logs

  3. Indexes
    - Add indexes for common query patterns
    - Optimize for performance with user_id and date-based queries

  4. Triggers
    - Add triggers to maintain updated_at timestamps
*/

-- Create workout_logs table
CREATE TABLE IF NOT EXISTS workout_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  workout_program_id uuid REFERENCES workout_programs(id),
  planned_workout_id uuid REFERENCES workouts(id),
  workout_name_actual text NOT NULL,
  started_at timestamptz NOT NULL,
  completed_at timestamptz NOT NULL,
  duration_seconds integer NOT NULL,
  overall_session_rpe integer CHECK (overall_session_rpe >= 1 AND overall_session_rpe <= 10),
  client_notes_for_session text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create workout_exercise_logs table
CREATE TABLE IF NOT EXISTS workout_exercise_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  workout_log_id uuid NOT NULL REFERENCES workout_logs(id) ON DELETE CASCADE,
  exercise_id uuid NOT NULL REFERENCES exercises(id),
  planned_workout_exercise_id uuid REFERENCES workout_exercises(id),
  exercise_order integer NOT NULL,
  sets_logged jsonb NOT NULL DEFAULT '[]'::jsonb,
  exercise_notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE workout_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_exercise_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for workout_logs
CREATE POLICY "Users can manage their own workout logs"
  ON workout_logs
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create a helper function to check if a user is a coach
CREATE OR REPLACE FUNCTION is_coach()
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'coach'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE POLICY "Coaches can read client workout logs"
  ON workout_logs
  FOR SELECT
  TO authenticated
  USING (is_coach());

CREATE POLICY "Service role can manage workout logs"
  ON workout_logs
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Create policies for workout_exercise_logs
CREATE POLICY "Users can manage their own workout exercise logs"
  ON workout_exercise_logs
  FOR ALL
  TO authenticated
  USING (
    workout_log_id IN (
      SELECT id FROM workout_logs WHERE user_id = auth.uid()
    )
  )
  WITH CHECK (
    workout_log_id IN (
      SELECT id FROM workout_logs WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Coaches can read client workout exercise logs"
  ON workout_exercise_logs
  FOR SELECT
  TO authenticated
  USING (is_coach());

CREATE POLICY "Service role can manage workout exercise logs"
  ON workout_exercise_logs
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS workout_logs_user_id_idx ON workout_logs(user_id);
CREATE INDEX IF NOT EXISTS workout_logs_completed_at_idx ON workout_logs(completed_at DESC);
CREATE INDEX IF NOT EXISTS workout_logs_user_completed_idx ON workout_logs(user_id, completed_at DESC);
CREATE INDEX IF NOT EXISTS workout_logs_workout_program_id_idx ON workout_logs(workout_program_id);
CREATE INDEX IF NOT EXISTS workout_logs_planned_workout_id_idx ON workout_logs(planned_workout_id);

CREATE INDEX IF NOT EXISTS workout_exercise_logs_workout_log_id_idx ON workout_exercise_logs(workout_log_id);
CREATE INDEX IF NOT EXISTS workout_exercise_logs_exercise_id_idx ON workout_exercise_logs(exercise_id);
CREATE INDEX IF NOT EXISTS workout_exercise_logs_planned_workout_exercise_id_idx ON workout_exercise_logs(planned_workout_exercise_id);
CREATE INDEX IF NOT EXISTS workout_exercise_logs_exercise_order_idx ON workout_exercise_logs(exercise_order);

-- Create triggers for updated_at timestamps
CREATE TRIGGER on_workout_logs_updated
  BEFORE UPDATE ON workout_logs
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER on_workout_exercise_logs_updated
  BEFORE UPDATE ON workout_exercise_logs
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();