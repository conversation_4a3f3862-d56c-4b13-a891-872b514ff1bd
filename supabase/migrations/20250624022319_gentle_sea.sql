/*
  # Create Database Webhook for Weekly Check-ins

  1. New Trigger Function
    - `generate_feedback_webhook` - Calls the process-weekly-feedback Edge Function
    - Triggered after a new check-in is inserted
    - Sends check-in ID and user ID to the function

  2. Security
    - Uses supabase_functions.http_request for secure HTTP calls
    - Passes authentication via Bear<PERSON> token
    - <PERSON><PERSON><PERSON> handles content type and request body

  3. Trigger
    - Creates AFTER INSERT trigger on weekly_checkins table
    - Automatically processes new check-ins for feedback generation
*/

-- Create trigger function to call the process-weekly-feedback Edge Function
CREATE OR REPLACE FUNCTION generate_feedback_webhook()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM supabase_functions.http_request(
    'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/process-weekly-feedback',
    'POST',
    '{"Content-type":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlanZ6eHppeWJ0dnRnZGJrZmZxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MTQzMzUsImV4cCI6MjA2NTA5MDMzNX0.aLEgFQY30_JlIBtMcJNjKz9zAQ2yvYvUFR-8TCAeuZM"}',
    jsonb_build_object(
      'checkInId', NEW.id,
      'userId', NEW.user_id
    )::text,
    '5000'  -- 5 second timeout
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on weekly_checkins table
CREATE TRIGGER generate_workout_program_webhook
  AFTER INSERT ON public.weekly_checkins
  FOR EACH ROW
  EXECUTE FUNCTION generate_feedback_webhook();

-- Add comment to explain the trigger
COMMENT ON TRIGGER generate_workout_program_webhook ON public.weekly_checkins IS 
  'Triggers the process-weekly-feedback Edge Function when a new check-in is submitted';