# Test Daily Program Scheduler Function

## Function Overview
The `daily-program-scheduler` Edge Function runs daily via cron job to identify users eligible for 21-day program generation and automatically trigger the creation of their next 4-week program cycle.

## Key Features Implemented

### 1. **Eligible User Detection**
- Finds users with active programs that started exactly 21 days ago
- Uses both `cycle_start_date` and `created_at` for flexible date matching
- Filters out users who already have scheduled jobs for today

### 2. **Dual Processing Approach**
- **Job Scheduling**: Records job in `scheduled_generation_jobs` table
- **Immediate Generation**: Triggers program generation immediately

### 3. **Comprehensive Error Handling**
- Individual user processing errors don't stop the entire batch
- Detailed logging for monitoring and debugging
- Graceful degradation when services are unavailable

### 4. **Monitoring & Observability**
- Execution time tracking
- Detailed result metrics (processed, scheduled, generated, errors)
- Operation logging in `operation_logs` table

## Test Scenarios

### 1. **Normal Operation**
```bash
curl -X POST https://your-project.supabase.co/functions/v1/daily-program-scheduler \
  -H "Authorization: Bearer YOUR_SERVICE_KEY"
```

**Expected Response:**
```json
{
  "success": true,
  "processedUsers": 5,
  "scheduledJobs": 5,
  "generatedPrograms": 4,
  "errors": ["User xyz: Intake not completed"],
  "executionTime": 2500,
  "eligibleUsers": [...]
}
```

### 2. **No Eligible Users**
When no users are eligible for 21-day generation:
```json
{
  "success": true,
  "processedUsers": 0,
  "scheduledJobs": 0,
  "generatedPrograms": 0,
  "errors": [],
  "executionTime": 150
}
```

### 3. **Partial Failures**
When some users fail but others succeed:
```json
{
  "success": true,
  "processedUsers": 3,
  "scheduledJobs": 3,
  "generatedPrograms": 2,
  "errors": ["User abc: Failed to generate program"],
  "executionTime": 3200
}
```

## Database Integration

### **Tables Used:**
- `workout_programs` - Find eligible users with active programs
- `profiles` - Get user information
- `scheduled_generation_jobs` - Record and track scheduled jobs
- `operation_logs` - Log execution results for monitoring

### **Functions Called:**
- `schedule_generation_job()` - Safe job scheduling with conflict resolution
- `generate-workout-program` Edge Function - Trigger program generation

## Cron Job Configuration

This function is designed to be called daily by a cron job:

```sql
-- Daily program generation scheduler (9:00 AM UTC)
SELECT cron.schedule(
  'daily-program-generation',
  '0 9 * * *',
  $$
  SELECT net.http_post(
    url := 'https://your-project.supabase.co/functions/v1/daily-program-scheduler',
    headers := '{"Authorization": "Bearer YOUR_SERVICE_KEY"}',
    body := '{}'
  );
  $$
);
```

## Key Benefits

1. **Automatic Program Progression**: Users get new programs every 28 days without manual intervention
2. **Coach Review Window**: Programs are generated on day 21, giving coaches 7 days to review before day 28 activation
3. **Robust Error Handling**: Individual failures don't affect other users
4. **Comprehensive Monitoring**: Full visibility into scheduler performance
5. **Duplicate Prevention**: Won't create multiple jobs for the same user/date

## Performance Considerations

- Processes users in sequence to avoid overwhelming the system
- Uses efficient database queries with proper indexing
- Includes execution time tracking for performance monitoring
- Graceful error handling prevents cascading failures
