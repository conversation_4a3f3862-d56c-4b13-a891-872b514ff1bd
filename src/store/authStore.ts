import { create } from 'zustand';
import { supabase } from '../services/supabaseClient';
import { persist, PERSIST_CONFIGS } from '../utils/zustandPersistence';
import { Session } from '@supabase/supabase-js';

interface AuthState {
  session: Session | null;
  isLoading: boolean;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  setSession: (session: Session | null) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
  session: null,
  isLoading: false,

  setSession: (session) => {
    console.log('🔄 AuthStore: Setting session in authStore:', session ? 'authenticated' : 'null');
    set({ session });
  },

  signUp: async (email, password, fullName) => {
    set({ isLoading: true });
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });
      if (error) throw error;
    } catch (error) {
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  signIn: async (email, password) => {
    set({ isLoading: true });
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
    } catch (error) {
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  signOut: async () => {
    console.log('🚪 AuthStore: signOut function called - starting logout process');
    set({ isLoading: true });
    try {
      console.log('📞 AuthStore: Calling supabase.auth.signOut()...');
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('❌ AuthStore: Supabase signOut error:', error);
        throw error;
      }
      
      console.log('✅ AuthStore: Supabase signOut completed successfully');
      console.log('🔄 AuthStore: Manually setting session to null in Zustand store');
      
      // Immediately set session to null to trigger navigation
      set({ session: null });
      console.log('✅ AuthStore: Session manually set to null after signOut');
      
    } catch (error) {
      console.error('❌ AuthStore: Error in signOut function:', error);
      throw error;
    } finally {
      console.log('🏁 AuthStore: Setting isLoading to false');
      set({ isLoading: false });
      console.log('🏁 AuthStore: signOut function completed');
    }
  },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        session: state.session,
      }),
    }
  )
);