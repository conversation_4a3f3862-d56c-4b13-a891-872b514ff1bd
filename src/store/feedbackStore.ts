import { create } from 'zustand';
import { feedbackService, CoachFeedback } from '@/services/feedbackService';

interface FeedbackState {
  // Feedback data
  coachFeedbackList: CoachFeedback[];
  unreadFeedbackCount: number;
  
  // Loading states
  isLoading: boolean;
  
  // Actions
  loadCoachFeedback: (limit?: number, offset?: number) => Promise<void>;
  markFeedbackAsRead: (feedbackId: string) => Promise<void>;
  getUnreadFeedbackCount: () => Promise<void>;
}

export const useFeedbackStore = create<FeedbackState>((set, get) => ({
  coachFeedbackList: [],
  unreadFeedbackCount: 0,
  isLoading: false,
  
  loadCoachFeedback: async (limit?: number, offset?: number) => {
    set({ isLoading: true });
    
    try {
      console.log('📚 FeedbackStore: Loading coach feedback with limit:', limit, 'offset:', offset);
      const feedback = await feedbackService.fetchPublishedFeedback(limit, offset);
      console.log(`✅ FeedbackStore: Loaded ${feedback.length} feedback items`);
      
      set({
        coachFeedbackList: feedback,
        isLoading: false,
      });
    } catch (error) {
      console.error('❌ FeedbackStore: Error loading coach feedback:', error);
      set({ isLoading: false });
      throw error;
    }
  },
  
  markFeedbackAsRead: async (feedbackId: string) => {
    try {
      console.log('📝 FeedbackStore: Marking feedback as read:', feedbackId);
      await feedbackService.markFeedbackAsRead(feedbackId);
      console.log('✅ FeedbackStore: Feedback marked as read');
      
      // Update the local state to reflect the change
      set((state) => ({
        coachFeedbackList: state.coachFeedbackList.map((feedback) => 
          feedback.id === feedbackId
            ? { ...feedback, status: 'read_by_client', read_at: new Date().toISOString() }
            : feedback
        ),
        unreadFeedbackCount: Math.max(0, state.unreadFeedbackCount - 1),
      }));
      
      // Refresh the feedback list to ensure UI is updated
      await get().loadCoachFeedback();
      
      console.log('✅ FeedbackStore: Feedback marked as read successfully');
    } catch (error) {
      console.error('❌ FeedbackStore: Error marking feedback as read:', error);
      throw error;
    }
  },
  
  getUnreadFeedbackCount: async () => {
    try {
      console.log('🔢 FeedbackStore: Getting unread feedback count');
      const count = await feedbackService.getUnreadFeedbackCount();
      console.log(`✅ FeedbackStore: Unread feedback count: ${count}`);

      set({ unreadFeedbackCount: count });
    } catch (error) {
      console.error('❌ FeedbackStore: Error getting unread feedback count:', error);
      throw error;
    }
  },
}));