import { create } from 'zustand';
import { persist, PERSIST_CONFIGS } from '../utils/zustandPersistence';
import { workoutLoggingService, SetLog, WorkoutLogData, ExerciseLogData } from '../services/workoutLoggingService';
import { WorkoutProgram, Workout, WorkoutExercise } from '../services/workoutService';

export interface ActiveWorkoutState {
  // Current workout data
  currentWorkoutPlan?: {
    program: WorkoutProgram;
    workout: Workout & { exercises: WorkoutExercise[] };
  };
  currentExerciseIndex: number;
  sessionStartTime?: Date;
  
  // Logged data for current session
  loggedData: {
    [exerciseId: string]: {
      exercise: WorkoutExercise;
      sets: SetLog[];
      notes?: string;
    };
  };
  
  // Rest timer
  restTimer: {
    isActive: boolean;
    duration: number; // in seconds
    remaining: number; // in seconds
    exerciseId?: string;
  };
  
  // Loading states
  isLogging: boolean;
  isSaving: boolean;
  
  // Actions
  startWorkout: (program: WorkoutProgram, workout: Workout & { exercises: WorkoutExercise[] }) => void;
  logSet: (exerciseId: string, setData: Omit<SetLog, 'set_number'>) => void;
  updateExerciseNotes: (exerciseId: string, notes: string) => void;
  nextExercise: () => void;
  previousExercise: () => void;
  goToExercise: (index: number) => void;
  startRestTimer: (duration: number, exerciseId: string) => void;
  stopRestTimer: () => void;
  updateRestTimer: () => void;
  finishWorkout: (overallRpe?: number, sessionNotes?: string) => Promise<void>;
  resetWorkout: () => void;
  
  // Getters
  getCurrentExercise: () => WorkoutExercise | undefined;
  getExerciseProgress: () => { completed: number; total: number };
  getTotalSetsLogged: () => number;
  getWorkoutDuration: () => number; // in seconds
}

export const useActiveWorkoutStore = create<ActiveWorkoutState>()(
  persist(
    (set, get) => ({
  currentExerciseIndex: 0,
  loggedData: {},
  restTimer: {
    isActive: false,
    duration: 0,
    remaining: 0,
  },
  isLogging: false,
  isSaving: false,

  startWorkout: (program, workout) => {
    console.log('🏋️ ActiveWorkoutStore: Starting workout:', workout.title);
    set({
      currentWorkoutPlan: { program, workout },
      currentExerciseIndex: 0,
      sessionStartTime: new Date(),
      loggedData: {},
      restTimer: {
        isActive: false,
        duration: 0,
        remaining: 0,
      },
      isLogging: false,
      isSaving: false,
    });
  },

  logSet: (exerciseId, setData) => {
    console.log('📝 ActiveWorkoutStore: Logging set for exercise:', exerciseId);
    const state = get();
    const exercise = state.currentWorkoutPlan?.workout.exercises.find(ex => ex.exercise?.id === exerciseId);
    
    if (!exercise) {
      console.error('❌ Exercise not found:', exerciseId);
      return;
    }

    const currentExerciseData = state.loggedData[exerciseId] || {
      exercise,
      sets: [],
      notes: '',
    };

    const setNumber = currentExerciseData.sets.length + 1;
    const newSet: SetLog = {
      set_number: setNumber,
      ...setData,
    };

    set({
      loggedData: {
        ...state.loggedData,
        [exerciseId]: {
          ...currentExerciseData,
          sets: [...currentExerciseData.sets, newSet],
        },
      },
    });

    console.log('✅ Set logged:', newSet);
  },

  updateExerciseNotes: (exerciseId, notes) => {
    const state = get();
    const exercise = state.currentWorkoutPlan?.workout.exercises.find(ex => ex.exercise?.id === exerciseId);
    
    if (!exercise) return;

    const currentExerciseData = state.loggedData[exerciseId] || {
      exercise,
      sets: [],
      notes: '',
    };

    set({
      loggedData: {
        ...state.loggedData,
        [exerciseId]: {
          ...currentExerciseData,
          notes,
        },
      },
    });
  },

  nextExercise: () => {
    const state = get();
    const totalExercises = state.currentWorkoutPlan?.workout.exercises.length || 0;
    
    if (state.currentExerciseIndex < totalExercises - 1) {
      set({ currentExerciseIndex: state.currentExerciseIndex + 1 });
      console.log('➡️ Moved to next exercise:', state.currentExerciseIndex + 1);
    }
  },

  previousExercise: () => {
    const state = get();
    
    if (state.currentExerciseIndex > 0) {
      set({ currentExerciseIndex: state.currentExerciseIndex - 1 });
      console.log('⬅️ Moved to previous exercise:', state.currentExerciseIndex - 1);
    }
  },

  goToExercise: (index) => {
    const state = get();
    const totalExercises = state.currentWorkoutPlan?.workout.exercises.length || 0;
    
    if (index >= 0 && index < totalExercises) {
      set({ currentExerciseIndex: index });
      console.log('🎯 Jumped to exercise:', index);
    }
  },

  startRestTimer: (duration, exerciseId) => {
    console.log('⏱️ Starting rest timer:', duration, 'seconds');
    set({
      restTimer: {
        isActive: true,
        duration,
        remaining: duration,
        exerciseId,
      },
    });
  },

  stopRestTimer: () => {
    console.log('⏹️ Stopping rest timer');
    set({
      restTimer: {
        isActive: false,
        duration: 0,
        remaining: 0,
      },
    });
  },

  updateRestTimer: () => {
    const state = get();
    if (state.restTimer.isActive && state.restTimer.remaining > 0) {
      set({
        restTimer: {
          ...state.restTimer,
          remaining: state.restTimer.remaining - 1,
        },
      });
    } else if (state.restTimer.isActive && state.restTimer.remaining <= 0) {
      // Timer finished
      set({
        restTimer: {
          isActive: false,
          duration: 0,
          remaining: 0,
        },
      });
    }
  },

  finishWorkout: async (overallRpe, sessionNotes) => {
    console.log('🏁 ActiveWorkoutStore: Finishing workout');
    const state = get();
    
    if (!state.currentWorkoutPlan || !state.sessionStartTime) {
      throw new Error('No active workout to finish');
    }

    set({ isSaving: true });

    try {
      const completedAt = new Date();
      const durationSeconds = Math.floor((completedAt.getTime() - state.sessionStartTime.getTime()) / 1000);

      const workoutLogData: WorkoutLogData = {
        workout_program_id: state.currentWorkoutPlan.program.id,
        planned_workout_id: state.currentWorkoutPlan.workout.id,
        workout_name_actual: state.currentWorkoutPlan.workout.title,
        started_at: state.sessionStartTime.toISOString(),
        completed_at: completedAt.toISOString(),
        duration_seconds: durationSeconds,
        overall_session_rpe: overallRpe,
        client_notes_for_session: sessionNotes,
      };

      const exerciseLogsData: ExerciseLogData[] = Object.entries(state.loggedData)
        .filter(([_, data]) => data.sets.length > 0) // Only include exercises with logged sets
        .map(([exerciseId, data], index) => ({
          exercise_id: exerciseId,
          planned_workout_exercise_id: data.exercise.id,
          exercise_order: index + 1,
          sets_logged: data.sets,
          exercise_notes: data.notes || undefined,
        }));

      console.log('💾 Saving workout with', exerciseLogsData.length, 'exercises');
      await workoutLoggingService.saveCompletedWorkout(workoutLogData, exerciseLogsData);
      
      console.log('✅ Workout saved successfully');
      
      // Reset the workout state
      get().resetWorkout();
    } catch (error) {
      console.error('❌ Error finishing workout:', error);
      throw error;
    } finally {
      set({ isSaving: false });
    }
  },

  resetWorkout: () => {
    console.log('🔄 ActiveWorkoutStore: Resetting workout');
    set({
      currentWorkoutPlan: undefined,
      currentExerciseIndex: 0,
      sessionStartTime: undefined,
      loggedData: {},
      restTimer: {
        isActive: false,
        duration: 0,
        remaining: 0,
      },
      isLogging: false,
      isSaving: false,
    });
  },

  // Getters
  getCurrentExercise: () => {
    const state = get();
    return state.currentWorkoutPlan?.workout.exercises[state.currentExerciseIndex];
  },

  getExerciseProgress: () => {
    const state = get();
    const total = state.currentWorkoutPlan?.workout.exercises.length || 0;
    const completed = Object.keys(state.loggedData).filter(
      exerciseId => state.loggedData[exerciseId].sets.length > 0
    ).length;
    
    return { completed, total };
  },

  getTotalSetsLogged: () => {
    const state = get();
    return Object.values(state.loggedData).reduce(
      (total, exerciseData) => total + exerciseData.sets.length,
      0
    );
  },

  getWorkoutDuration: () => {
    const state = get();
    if (!state.sessionStartTime) return 0;
    
    return Math.floor((new Date().getTime() - state.sessionStartTime.getTime()) / 1000);
  },
    }),
    {
      name: 'active-workout-store',
      ...PERSIST_CONFIGS.ACTIVE_WORKOUT,
    }
  )
);