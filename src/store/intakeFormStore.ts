import { create } from 'zustand';
import { profileService, IntakeData } from '../services/profileService';

interface IntakeFormState {
  formData: IntakeData;
  currentStep: number;
  isLoading: boolean;
  isSubmitting: boolean;
  updateFormData: (stepData: Partial<IntakeData>) => void;
  setCurrentStep: (step: number) => void;
  setLoading: (loading: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  resetForm: () => void;
  loadDraft: () => Promise<void>;
  saveDraft: (stepData: Partial<IntakeData>) => Promise<void>;
  submitIntake: () => Promise<void>;
}

const defaultFormData: IntakeData = {
  height_unit: 'cm',
  weight_unit: 'kg',
  has_specific_event: false,
  primary_fitness_goal: [], // Initialize as empty array
};

export const useIntakeFormStore = create<IntakeFormState>((set, get) => ({
  formData: defaultFormData,
  currentStep: 1,
  isLoading: false,
  isSubmitting: false,

  updateFormData: (stepData) => {
    console.log('🔄 IntakeFormStore: updateFormData called with:', stepData);
    const currentFormData = get().formData;
    const updatedFormData = { ...currentFormData, ...stepData };
    console.log('📊 IntakeFormStore: Updated form data:', updatedFormData);
    set((state) => ({
      formData: updatedFormData,
    }));
  },

  setCurrentStep: (step) => set({ currentStep: step }),

  setLoading: (loading) => set({ isLoading: loading }),

  setSubmitting: (submitting) => set({ isSubmitting: submitting }),

  resetForm: () =>
    set({
      formData: defaultFormData,
      currentStep: 1,
      isLoading: false,
      isSubmitting: false,
    }),

  loadDraft: async () => {
    set({ isLoading: true });
    try {
      console.log('📥 IntakeFormStore: Loading draft...');
      const intakeData = await profileService.getIntakeData();
      if (intakeData) {
        console.log('✅ IntakeFormStore: Draft loaded successfully:', intakeData);
        set({ 
          formData: { ...defaultFormData, ...intakeData },
          isLoading: false 
        });
      } else {
        console.log('ℹ️ IntakeFormStore: No draft found, using defaults');
        set({ isLoading: false });
      }
    } catch (error) {
      console.error('❌ IntakeFormStore: Error loading draft:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  saveDraft: async (stepData) => {
    const { formData } = get();
    const updatedData = { ...formData, ...stepData };
    
    console.log('💾 IntakeFormStore: saveDraft called with stepData:', stepData);
    console.log('📋 IntakeFormStore: Current formData:', formData);
    console.log('🔄 IntakeFormStore: Updated data to save:', updatedData);
    
    try {
      console.log('🚀 IntakeFormStore: Calling profileService.updateIntakeData for DRAFT SAVE (no submission flag)...');
      const updatedProfile = await profileService.updateIntakeData(updatedData);
      console.log('✅ IntakeFormStore: Draft saved successfully');
      console.log('📊 IntakeFormStore: Updated profile from database:', updatedProfile);
      
      // CRITICAL FIX: Sync the formData with the complete profile data returned from database
      // This ensures intake_status and other computed fields are up-to-date
      const syncedFormData: IntakeData = {
        intake_gender: updatedProfile.intake_gender,
        age: updatedProfile.age,
        height_cm: updatedProfile.height_cm,
        weight_kg: updatedProfile.weight_kg,
        primary_fitness_goal: updatedProfile.primary_fitness_goal,
        training_experience_level: updatedProfile.training_experience_level,
        goal_timeline_months: updatedProfile.goal_timeline_months,
        equipment_access_type: updatedProfile.equipment_access_type,
        available_equipment: updatedProfile.available_equipment,
        custom_equipment_notes: updatedProfile.custom_equipment_notes,
        training_days_per_week: updatedProfile.training_days_per_week,
        preferred_training_days: updatedProfile.preferred_training_days,
        preferred_session_duration_minutes: updatedProfile.preferred_session_duration_minutes,
        injuries_limitations: updatedProfile.injuries_limitations,
        training_preferences_notes: updatedProfile.training_preferences_notes,
        has_specific_event: updatedProfile.has_specific_event,
        height_unit: updatedProfile.height_unit,
        weight_unit: updatedProfile.weight_unit,
        intake_status: updatedProfile.intake_status, // CRITICAL: Sync the database-computed status
        intake_completed_at: updatedProfile.intake_completed_at,
      };
      
      console.log('🔄 IntakeFormStore: Syncing formData with database state:', syncedFormData);
      set({ formData: syncedFormData });
      
    } catch (error) {
      console.error('❌ IntakeFormStore: Error saving draft:', error);
      throw error;
    }
  },

  submitIntake: async () => {
    const { formData } = get();
    console.log('🎯 IntakeFormStore: submitIntake called (EXPLICIT SUBMISSION)');
    console.log('📋 IntakeFormStore: Final form data to submit:', formData);

    set({ isSubmitting: true });

    try {
      console.log('🚀 IntakeFormStore: Calling profileService.submitIntakeData for EXPLICIT SUBMISSION...');
      const updatedProfile = await profileService.submitIntakeData(formData);
      console.log('✅ IntakeFormStore: Intake submitted successfully');
      console.log('📊 IntakeFormStore: Updated profile after submission:', updatedProfile);
      
      // CRITICAL FIX: Sync the formData with the complete profile data returned from database
      // This ensures the final state reflects what's actually in the database
      const syncedFormData: IntakeData = {
        intake_gender: updatedProfile.intake_gender,
        age: updatedProfile.age,
        height_cm: updatedProfile.height_cm,
        weight_kg: updatedProfile.weight_kg,
        primary_fitness_goal: updatedProfile.primary_fitness_goal,
        training_experience_level: updatedProfile.training_experience_level,
        goal_timeline_months: updatedProfile.goal_timeline_months,
        equipment_access_type: updatedProfile.equipment_access_type,
        available_equipment: updatedProfile.available_equipment,
        custom_equipment_notes: updatedProfile.custom_equipment_notes,
        training_days_per_week: updatedProfile.training_days_per_week,
        preferred_training_days: updatedProfile.preferred_training_days,
        preferred_session_duration_minutes: updatedProfile.preferred_session_duration_minutes,
        injuries_limitations: updatedProfile.injuries_limitations,
        training_preferences_notes: updatedProfile.training_preferences_notes,
        has_specific_event: updatedProfile.has_specific_event,
        height_unit: updatedProfile.height_unit,
        weight_unit: updatedProfile.weight_unit,
        intake_status: updatedProfile.intake_status, // CRITICAL: Sync the database-computed status
        intake_completed_at: updatedProfile.intake_completed_at,
      };
      
      console.log('🔄 IntakeFormStore: Syncing formData with final database state:', syncedFormData);
      set({ formData: syncedFormData });
      
      // Import and update the user store to reflect the new profile state
      const { useUserStore } = await import('./userStore');
      useUserStore.getState().fetchProfile();
      
      set({ isSubmitting: false });
    } catch (error) {
      console.error('❌ IntakeFormStore: Error submitting intake:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        formData: formData
      });
      set({ isSubmitting: false });
      throw error;
    }
  },
}));