import { create } from 'zustand';
import { checkInService, WeeklyCheckIn } from '@/services/checkInService';
import { useUserStore } from '@/store/userStore';
import { supabase } from '@/services/supabaseClient';

interface CheckInState {
  // Current check-in form data
  currentCheckInForm: Partial<WeeklyCheckIn>;
  
  // Check-in history
  checkInHistory: WeeklyCheckIn[];
  
  // Check-in window status
  isCheckInWindowOpen: boolean;
  hasSubmittedCurrentCheckIn: boolean;
  currentCheckInDate: string | null;
  
  // Loading states
  isLoading: boolean;
  isSubmitting: boolean;
  
  // Actions
  updateCheckInForm: (data: Partial<WeeklyCheckIn>) => void;
  resetCheckInForm: () => void;
  submitCheckIn: () => Promise<void>;
  loadCheckInHistory: (limit?: number, offset?: number) => Promise<void>;
  determineCheckInWindowStatus: () => Promise<void>;
}

export const useCheckInStore = create<CheckInState>((set, get) => ({
  currentCheckInForm: {},
  checkInHistory: [],
  isCheckInWindowOpen: false,
  hasSubmittedCurrentCheckIn: false,
  currentCheckInDate: null,
  isLoading: false,
  isSubmitting: false,
  
  updateCheckInForm: (data) => {
    set((state) => ({
      currentCheckInForm: {
        ...state.currentCheckInForm,
        ...data,
      },
    }));
  },
  
  resetCheckInForm: () => {
    set({
      currentCheckInForm: {},
    });
  },
  
  submitCheckIn: async () => {
    const { currentCheckInForm, currentCheckInDate } = get();
    
    if (!currentCheckInDate) {
      throw new Error('No check-in date available');
    }
    
    set({ isSubmitting: true });
    
    try {
      console.log('🚀 CheckInStore: Submitting check-in for date:', currentCheckInDate);
      
      // Prepare check-in data
      const checkInData: Omit<WeeklyCheckIn, 'id' | 'user_id' | 'submitted_at'> = {
        checkin_date: currentCheckInDate,
        ...currentCheckInForm,
      };
      
      console.log('📋 CheckInStore: Check-in data to submit:', checkInData);
      
      // Submit check-in
      await checkInService.submitCheckIn(checkInData);
      
      console.log('✅ CheckInStore: Check-in submitted successfully');
      
      // Update state
      set({
        hasSubmittedCurrentCheckIn: true,
        currentCheckInForm: {},
        isSubmitting: false,
      });
      
      // Refresh check-in history
      await get().loadCheckInHistory();
    } catch (error) {
      console.error('❌ CheckInStore: Error submitting check-in:', error);
      set({ isSubmitting: false });
      throw error;
    }
  },
  
  loadCheckInHistory: async (limit = 10, offset = 0) => {
    set({ isLoading: true });
    
    try {
      console.log('🔍 CheckInStore: Loading check-in history...');
      
      // First check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.log('⚠️ CheckInStore: No authenticated user, skipping history load');
        set({ isLoading: false });
        return;
      }
      
      const history = await checkInService.fetchCheckInHistory(limit, offset);
      console.log('✅ CheckInStore: Loaded', history.length, 'check-ins');
      
      set({
        checkInHistory: history,
        isLoading: false,
      });
    } catch (error) {
      console.error('❌ CheckInStore: Error loading check-in history:', error);
      set({ isLoading: false });
      throw error;
    }
  },
  
  determineCheckInWindowStatus: async () => {
    console.log('🔍 CheckInStore: Determining check-in window status...');
    
    try {
      // First check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        console.log('⚠️ CheckInStore: No authenticated user, setting window closed');
        set({
          isCheckInWindowOpen: false,
          hasSubmittedCurrentCheckIn: false,
          currentCheckInDate: null,
        });
        return;
      }
      
      // Get user profile to check account creation date
      const profile = useUserStore.getState().profile;
      const userCreatedAt = profile?.created_at;
      
      // Check if today is Sunday and time is between 6 AM and 11:59 PM
      const windowStatus = checkInService.isCheckInWindowOpen(userCreatedAt);
      console.log('📊 CheckInStore: Window status:', windowStatus);
      
      if (windowStatus.status === 'Open' && windowStatus.date) {
        // Check if user has already submitted a check-in for today
        try {
          console.log('🔍 CheckInStore: Checking if user has submitted check-in for date:', windowStatus.date);
          const hasSubmitted = await checkInService.hasSubmittedCheckInForDate(windowStatus.date);
          console.log('📊 CheckInStore: Has submitted check-in:', hasSubmitted);
          
          set({
            isCheckInWindowOpen: true,
            hasSubmittedCurrentCheckIn: hasSubmitted,
            currentCheckInDate: windowStatus.date,
          });
        } catch (error) {
          console.error('❌ CheckInStore: Error checking for existing check-in:', error);
          
          // Default to window closed on error
          set({
            isCheckInWindowOpen: false,
            hasSubmittedCurrentCheckIn: false,
            currentCheckInDate: null,
          });
        }
      } else {
        // Window is closed (either due to time window or grace period)
        console.log('📊 CheckInStore: Check-in window is closed. Reason:', windowStatus.reason);
        set({
          isCheckInWindowOpen: false,
          hasSubmittedCurrentCheckIn: false,
          currentCheckInDate: null,
        });
      }
    } catch (error) {
      console.error('❌ CheckInStore: Error determining check-in window status:', error);
      // Default to window closed on error
      set({
        isCheckInWindowOpen: false,
        hasSubmittedCurrentCheckIn: false,
        currentCheckInDate: null,
      });
    }
  },
}));