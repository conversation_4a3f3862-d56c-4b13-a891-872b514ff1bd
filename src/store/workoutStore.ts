import { create } from 'zustand';
import { workoutService, WorkoutProgram, ProgramWeek, Workout, WorkoutExercise } from '../services/workoutService';
import { supabase } from '../services/supabaseClient';

interface WorkoutState {
  programs: WorkoutProgram[];
  currentProgram: WorkoutProgram | null;
  currentProgramDetails: {
    program: WorkoutProgram;
    weeks: (ProgramWeek & {
      workouts: (Workout & {
        exercises: WorkoutExercise[];
      })[];
    })[];
  } | null;
  isLoading: boolean;
  fetchPrograms: () => Promise<void>;
  fetchProgramDetails: (programId: string) => Promise<void>;
  startProgram: (programId: string) => Promise<void>;
  triggerProgramGeneration: () => Promise<{ success: boolean; error?: string }>;
  clearCurrentProgram: () => void;
  subscribeToPrograms: () => () => void;
}

export const useWorkoutStore = create<WorkoutState>((set, get) => ({
  programs: [],
  currentProgram: null,
  currentProgramDetails: null,
  isLoading: false,

  fetchPrograms: async () => {
    set({ isLoading: true });
    try {
      const programs = await workoutService.getWorkoutPrograms();
      set({ programs, isLoading: false });
    } catch (error) {
      console.error('Error fetching programs:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  fetchProgramDetails: async (programId: string) => {
    set({ isLoading: true });
    try {
      const details = await workoutService.getWorkoutProgramDetails(programId);
      set({ 
        currentProgramDetails: details,
        currentProgram: details.program,
        isLoading: false 
      });
    } catch (error) {
      console.error('Error fetching program details:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  startProgram: async (programId: string) => {
    set({ isLoading: true });
    try {
      const updatedProgram = await workoutService.startWorkoutProgram(programId);
      
      // Update the program in our state
      set(state => ({
        programs: state.programs.map(p => 
          p.id === programId ? updatedProgram : p
        ),
        currentProgram: updatedProgram,
        currentProgramDetails: state.currentProgramDetails ? {
          ...state.currentProgramDetails,
          program: updatedProgram,
        } : null,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error starting program:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  triggerProgramGeneration: async () => {
    set({ isLoading: true });
    try {
      const result = await workoutService.triggerProgramGeneration();
      set({ isLoading: false });
      
      if (result.success) {
        // Refresh programs after generation
        await get().fetchPrograms();
      }
      
      return result;
    } catch (error) {
      console.error('Error triggering program generation:', error);
      set({ isLoading: false });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  },

  clearCurrentProgram: () => {
    set({
      currentProgram: null,
      currentProgramDetails: null,
    });
  },

  subscribeToPrograms: () => {
    const { data: { user } } = supabase.auth.getUser();

    user.then(({ user: authenticatedUser }) => {
      if (!authenticatedUser) return;

      const subscription = supabase
        .channel('workout_programs')
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'workout_programs',
            filter: `user_id=eq.${authenticatedUser.id}`,
          },
          (payload) => {
            console.log('🔄 Workout program updated:', payload);

            // Check if status changed to approved
            const updatedProgram = payload.new as WorkoutProgram;
            if (updatedProgram.status === 'coach_approved' || updatedProgram.status === 'auto_approved') {
              console.log('🎉 Program approved - refreshing programs');
              // Refresh programs to show the newly approved program
              get().fetchPrograms();
            }
          }
        )
        .subscribe();

      return () => {
        subscription.unsubscribe();
      };
    });

    return () => {};
  },
}));