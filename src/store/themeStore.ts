import { create } from 'zustand';
import { persist, PERSIST_CONFIGS } from '../utils/zustandPersistence';

export type ThemeType = 'Woman' | 'Man' | 'Neutral';

interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  accent: string;
  // New semantic colors
  success: string;
  warning: string;
  error: string;
  info: string;
  overlayText: string;
  overlayBackground: string;
  contrastText: string; // For text on colored backgrounds
}

interface ThemeState {
  currentTheme: ThemeType;
  colors: ThemeColors;
  isInitialized: boolean;
  setTheme: (theme: ThemeType) => void;
  initializeThemeFromProfile: (profileTheme: ThemeType) => void;
  resetTheme: () => void;
}

const themeConfigs: Record<ThemeType, ThemeColors> = {
  Neutral: {
    primary: '#508C9B',
    secondary: '#D0D0D0',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#ECECEC',
    // New semantic colors
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
  Woman: {
    primary: '#B37399',
    secondary: '#2b3568',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#F0E6F7',
    // New semantic colors
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
  Man: {
    primary: '#353E43',
    secondary: '#FFFFFF',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#F5F5F5',
    // New semantic colors
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
};

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      currentTheme: 'Neutral',
      colors: themeConfigs.Neutral,
      isInitialized: false,

      setTheme: (theme: ThemeType) => {
        console.log('🎨 ThemeStore: Setting theme to:', theme);
        set({
          currentTheme: theme,
          colors: themeConfigs[theme],
          isInitialized: true,
        });
      },

      initializeThemeFromProfile: (profileTheme: ThemeType) => {
        try {
          const currentState = get();

          // Check if colors actually match the expected theme colors
          const expectedColors = themeConfigs[profileTheme];
          const colorsMatch = currentState.colors.primary === expectedColors.primary;

          // Always initialize from profile if:
          // 1. Not initialized yet, OR
          // 2. Current theme is Neutral (regardless of initialization state), OR
          // 3. Profile theme is different from current theme (user changed preference), OR
          // 4. Colors don't match expected colors (corrupted state)
          const shouldInitialize =
            !currentState.isInitialized ||
            currentState.currentTheme === 'Neutral' ||
            currentState.currentTheme !== profileTheme ||
            !colorsMatch;

          if (shouldInitialize) {
            console.log('🎨 ThemeStore: Initializing theme from profile:', profileTheme,
              `(current: ${currentState.currentTheme}, colors match: ${colorsMatch})`);
            set({
              currentTheme: profileTheme,
              colors: themeConfigs[profileTheme],
              isInitialized: true,
            });
          } else {
            console.log('🎨 ThemeStore: Theme already matches profile:', currentState.currentTheme);
          }
        } catch (error) {
          console.error('🎨 ThemeStore: Error during theme initialization:', error);
          // Fallback to neutral theme
          set({
            currentTheme: 'Neutral',
            colors: themeConfigs.Neutral,
            isInitialized: true,
          });
        }
      },

      resetTheme: () => {
        console.log('🎨 ThemeStore: Resetting theme to Neutral');
        set({
          currentTheme: 'Neutral',
          colors: themeConfigs.Neutral,
          isInitialized: false,
        });
      },
    }),
    {
      name: 'theme-store',
      ...PERSIST_CONFIGS.THEME,
    }
  )
);