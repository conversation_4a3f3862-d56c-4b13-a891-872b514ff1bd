import { create } from 'zustand';
import { supabase } from '../services/supabaseClient';

export interface InAppNotification {
  id: string;
  type: 'program_approved' | 'program_rejected' | 'program_generated' | 'general';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
  user_id: string;
}

interface NotificationState {
  notifications: InAppNotification[];
  unreadCount: number;
  isLoading: boolean;
  
  // Actions
  fetchNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  addNotification: (notification: InAppNotification) => void;
  clearNotifications: () => void;
  
  // Real-time subscription
  subscribeToNotifications: () => () => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  isLoading: false,

  fetchNotifications: async () => {
    set({ isLoading: true });
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false });
        return;
      }

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      const notifications = data || [];
      const unreadCount = notifications.filter(n => !n.read).length;

      set({ 
        notifications, 
        unreadCount, 
        isLoading: false 
      });
    } catch (error) {
      console.error('Error fetching notifications:', error);
      set({ isLoading: false });
    }
  },

  markAsRead: async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) throw error;

      // Update local state
      set(state => ({
        notifications: state.notifications.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        ),
        unreadCount: Math.max(0, state.unreadCount - 1)
      }));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  },

  markAllAsRead: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) throw error;

      // Update local state
      set(state => ({
        notifications: state.notifications.map(n => ({ ...n, read: true })),
        unreadCount: 0
      }));
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  },

  addNotification: (notification: InAppNotification) => {
    set(state => ({
      notifications: [notification, ...state.notifications],
      unreadCount: notification.read ? state.unreadCount : state.unreadCount + 1
    }));
  },

  clearNotifications: () => {
    set({ notifications: [], unreadCount: 0 });
  },

  subscribeToNotifications: () => {
    const { data: { user } } = supabase.auth.getUser();
    
    // Only subscribe if user is authenticated
    user.then(({ user: authenticatedUser }) => {
      if (!authenticatedUser) return;

      const subscription = supabase
        .channel('notifications')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${authenticatedUser.id}`,
          },
          (payload) => {
            const newNotification = payload.new as InAppNotification;
            console.log('📱 New notification received:', newNotification);
            
            // Add to store
            get().addNotification(newNotification);
            
            // Handle specific notification types
            if (newNotification.type === 'program_approved') {
              // Trigger workout store refresh
              console.log('🎉 Program approved notification - refreshing workout store');
              // This would ideally trigger a workout store refresh
              // You could emit an event or call the workout store directly
            }
          }
        )
        .subscribe();

      // Return cleanup function
      return () => {
        subscription.unsubscribe();
      };
    });

    // Return empty cleanup function if no user
    return () => {};
  },
}));

// Helper function to create program approval notification
export const createProgramApprovalNotification = async (
  programId: string,
  programName: string
): Promise<void> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: user.id,
        type: 'program_approved',
        title: '🎉 Your Workout Program is Ready!',
        message: `Your personalized program "${programName}" has been approved and is now available.`,
        data: { program_id: programId },
        read: false,
      });

    if (error) throw error;
    console.log('✅ Program approval notification created');
  } catch (error) {
    console.error('❌ Error creating program approval notification:', error);
  }
};
