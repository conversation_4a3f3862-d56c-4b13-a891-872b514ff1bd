import { create } from 'zustand';
import { profileService, Profile, IntakeData } from '../services/profileService';

interface UserState {
  profile: Profile | null;
  isLoading: boolean;
  fetchProfile: () => Promise<void>;
  updateProfile: (updates: Partial<Pick<Profile, 'full_name' | 'gender_preference' | 'role'>>) => Promise<void>;
  updateIntakeData: (intakeData: IntakeData) => Promise<void>;
  clearProfile: () => void;
  isCoach: () => boolean;
  isAdmin: () => boolean;
}

export const useUserStore = create<UserState>((set, get) => ({
  profile: null,
  isLoading: false,

  fetchProfile: async () => {
    set({ isLoading: true });
    try {
      const profile = await profileService.getProfile();
      set({ profile, isLoading: false });
    } catch (error) {
      console.error('Error fetching profile:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  updateProfile: async (updates) => {
    set({ isLoading: true });
    try {
      const updatedProfile = await profileService.updateProfile(updates);
      set({ profile: updatedProfile, isLoading: false });
    } catch (error) {
      console.error('Error updating profile:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  updateIntakeData: async (intakeData) => {
    set({ isLoading: true });
    try {
      const updatedProfile = await profileService.updateIntakeData(intakeData);
      set({ profile: updatedProfile, isLoading: false });
    } catch (error) {
      console.error('Error updating intake data:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  clearProfile: () => {
    set({ profile: null });
  },

  isCoach: () => {
    const { profile } = get();
    return profile?.role === 'coach';
  },

  isAdmin: () => {
    const { profile } = get();
    return profile?.role === 'admin';
  },
}));