import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedCard } from '@/components/ThemedComponents';
import { WorkoutLog } from '@/services/workoutLoggingService';
import { Calendar, Clock, Target, ChevronRight, Dumbbell } from 'lucide-react-native';

interface WorkoutHistoryCardProps {
  workout: WorkoutLog;
  onPress: () => void;
}

export default function WorkoutHistoryCard({ workout, onPress }: WorkoutHistoryCardProps) {
  const { colors } = useThemeStore();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString(undefined, {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  return (
    <ThemedCard style={styles.card} onPress={onPress}>
      <View style={styles.header}>
        <ThemedText style={styles.title}>{workout.workout_name_actual}</ThemedText>
        <ChevronRight size={20} color={colors.text} opacity={0.6} />
      </View>

      <View style={styles.details}>
        <View style={styles.detailItem}>
          <Calendar size={16} color={colors.primary} />
          <ThemedText style={styles.detailText}>
            {formatDate(workout.completed_at)}
          </ThemedText>
        </View>

        <View style={styles.detailItem}>
          <Clock size={16} color={colors.primary} />
          <ThemedText style={styles.detailText}>
            {formatDuration(workout.duration_seconds)}
          </ThemedText>
        </View>

        {workout.overall_session_rpe && (
          <View style={styles.detailItem}>
            <Target size={16} color={colors.primary} />
            <ThemedText style={styles.detailText}>
              RPE: {workout.overall_session_rpe}
            </ThemedText>
          </View>
        )}
      </View>

      {/* Visual indicator for workout type */}
      <View style={[styles.workoutTypeIndicator, { backgroundColor: colors.accent }]}>
        <Dumbbell size={14} color={colors.primary} />
        <ThemedText style={styles.workoutTypeText}>
          {workout.planned_workout_id ? 'Planned Workout' : 'Custom Workout'}
        </ThemedText>
      </View>
    </ThemedCard>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginBottom: 12,
    position: 'relative',
    borderLeftWidth: 3,
    borderLeftColor: '#508C9B', // Will be overridden by ThemedCard
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  details: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontSize: 12,
    opacity: 0.8,
  },
  workoutTypeIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  workoutTypeText: {
    fontSize: 10,
    fontWeight: '500',
  },
});