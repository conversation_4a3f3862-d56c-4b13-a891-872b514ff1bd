import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useThemeStore } from '@/store/themeStore';

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
  stepTitles?: string[];
}

export default function ProgressIndicator({ 
  currentStep, 
  totalSteps, 
  stepTitles = [] 
}: ProgressIndicatorProps) {
  const { colors } = useThemeStore();
  const progress = (currentStep / totalSteps) * 100;

  return (
    <View style={[styles.container, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
      <View style={styles.progressContainer}>
        <View style={[styles.progressBackground, { backgroundColor: colors.accent }]}>
          <View style={[styles.progressFill, { width: `${progress}%`, backgroundColor: colors.primary }]} />
        </View>
        <Text style={[styles.progressText, { color: colors.text }]}>
          Step {currentStep} of {totalSteps}
        </Text>
      </View>
      
      {stepTitles[currentStep - 1] && (
        <Text style={[styles.stepTitle, { color: colors.text }]}>{stepTitles[currentStep - 1]}</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressBackground: {
    height: 4,
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.7,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});