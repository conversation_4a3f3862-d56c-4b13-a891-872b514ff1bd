import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import Slider from '@react-native-community/slider';
import {
  CheckCircle,
  AlertCircle,
  Calendar,
  TrendingUp,
  Heart,
  Zap,
  Target,
  MessageSquare,
  Send,
  X,
  Save,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

interface CheckInQuestion {
  id: string;
  type: 'scale' | 'text' | 'multiple_choice' | 'boolean' | 'number';
  question: string;
  description?: string;
  required: boolean;
  options?: string[]; // For multiple choice
  min?: number; // For scale/number
  max?: number; // For scale/number
  placeholder?: string; // For text
}

interface CheckInTemplate {
  id: string;
  coachId: string;
  title: string;
  description?: string;
  questions: CheckInQuestion[];
  frequency: 'weekly' | 'biweekly' | 'monthly';
  isActive: boolean;
}

interface CheckInResponse {
  questionId: string;
  value: string | number | boolean;
}

interface WeeklyCheckInFormProps {
  templateId?: string;
  weekStartDate: string;
  onSubmit?: (responses: CheckInResponse[]) => void;
  onCancel?: () => void;
  existingResponses?: CheckInResponse[];
  isReadOnly?: boolean;
}

const DEFAULT_QUESTIONS: CheckInQuestion[] = [
  {
    id: 'energy_level',
    type: 'scale',
    question: 'How has your energy level been this week?',
    description: '1 = Very low energy, 10 = Very high energy',
    required: true,
    min: 1,
    max: 10,
  },
  {
    id: 'sleep_quality',
    type: 'scale',
    question: 'How would you rate your sleep quality?',
    description: '1 = Very poor sleep, 10 = Excellent sleep',
    required: true,
    min: 1,
    max: 10,
  },
  {
    id: 'stress_level',
    type: 'scale',
    question: 'What has your stress level been like?',
    description: '1 = Very low stress, 10 = Very high stress',
    required: true,
    min: 1,
    max: 10,
  },
  {
    id: 'motivation',
    type: 'scale',
    question: 'How motivated have you felt to exercise?',
    description: '1 = Not motivated at all, 10 = Extremely motivated',
    required: true,
    min: 1,
    max: 10,
  },
  {
    id: 'workout_difficulty',
    type: 'multiple_choice',
    question: 'How did the workouts feel this week?',
    required: true,
    options: ['Too easy', 'Just right', 'Too challenging', 'Way too hard'],
  },
  {
    id: 'injuries_pain',
    type: 'text',
    question: 'Any injuries, pain, or discomfort to report?',
    description: 'Please describe any physical issues you experienced',
    required: false,
    placeholder: 'Describe any pain, discomfort, or injuries...',
  },
  {
    id: 'nutrition_adherence',
    type: 'scale',
    question: 'How well did you stick to your nutrition plan?',
    description: '1 = Not at all, 10 = Perfectly',
    required: false,
    min: 1,
    max: 10,
  },
  {
    id: 'goals_progress',
    type: 'text',
    question: 'How do you feel about your progress toward your goals?',
    required: false,
    placeholder: 'Share your thoughts on your progress...',
  },
  {
    id: 'additional_notes',
    type: 'text',
    question: 'Anything else you\'d like your coach to know?',
    required: false,
    placeholder: 'Additional comments, questions, or concerns...',
  },
];

export const WeeklyCheckInForm: React.FC<WeeklyCheckInFormProps> = ({
  templateId,
  weekStartDate,
  onSubmit,
  onCancel,
  existingResponses = [],
  isReadOnly = false,
}) => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [template, setTemplate] = useState<CheckInTemplate | null>(null);
  const [responses, setResponses] = useState<CheckInResponse[]>(existingResponses);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  useEffect(() => {
    if (templateId) {
      loadTemplate();
    } else {
      // Use default template
      setTemplate({
        id: 'default',
        coachId: '',
        title: 'Weekly Check-In',
        description: 'Help your coach understand how your week went',
        questions: DEFAULT_QUESTIONS,
        frequency: 'weekly',
        isActive: true,
      });
    }
  }, [templateId]);

  const loadTemplate = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('check_in_templates')
        .select('*')
        .eq('id', templateId)
        .eq('is_active', true)
        .single();

      if (error) throw error;

      setTemplate({
        id: data.id,
        coachId: data.coach_id,
        title: data.title,
        description: data.description,
        questions: data.questions,
        frequency: data.frequency,
        isActive: data.is_active,
      });
    } catch (error) {
      console.error('Failed to load check-in template:', error);
      Alert.alert('Error', 'Failed to load check-in form');
    } finally {
      setIsLoading(false);
    }
  };

  const updateResponse = (questionId: string, value: string | number | boolean) => {
    setResponses(prev => {
      const existing = prev.find(r => r.questionId === questionId);
      if (existing) {
        return prev.map(r => 
          r.questionId === questionId ? { ...r, value } : r
        );
      } else {
        return [...prev, { questionId, value }];
      }
    });

    // Clear validation errors for this question
    setValidationErrors(prev => prev.filter(error => !error.includes(questionId)));
  };

  const getResponseValue = (questionId: string) => {
    const response = responses.find(r => r.questionId === questionId);
    return response?.value;
  };

  const validateForm = (): boolean => {
    if (!template) return false;

    const errors: string[] = [];
    
    template.questions.forEach(question => {
      if (question.required) {
        const response = getResponseValue(question.id);
        if (response === undefined || response === null || response === '') {
          errors.push(`${question.question} is required`);
        }
      }
    });

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please complete all required fields');
      return;
    }

    setShowConfirmModal(true);
  };

  const confirmSubmit = async () => {
    try {
      setIsSubmitting(true);
      setShowConfirmModal(false);

      await executeOperation(
        async () => {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user');

          // Get coach ID from coach-client relationship
          const { data: relationship } = await supabase
            .from('coach_client_relationships')
            .select('coach_id')
            .eq('client_id', user.id)
            .eq('status', 'active')
            .single();

          if (!relationship) throw new Error('No active coach relationship');

          const checkInData = {
            template_id: template?.id || 'default',
            client_id: user.id,
            coach_id: relationship.coach_id,
            responses: responses.reduce((acc, response) => {
              acc[response.questionId] = response.value;
              return acc;
            }, {} as Record<string, any>),
            week_start_date: weekStartDate,
            status: 'submitted',
          };

          const { error } = await supabase
            .from('check_in_responses')
            .upsert(checkInData);

          if (error) throw error;

          Alert.alert(
            'Check-In Submitted!',
            'Your weekly check-in has been sent to your coach.',
            [{ text: 'OK', onPress: () => onSubmit?.(responses) }]
          );
        },
        {
          type: 'CHECK_IN_SUBMIT',
          payload: {
            templateId: template?.id,
            weekStartDate,
            responses,
          },
        }
      );
    } catch (error) {
      console.error('Failed to submit check-in:', error);
      Alert.alert('Error', 'Failed to submit check-in. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderQuestion = (question: CheckInQuestion) => {
    const value = getResponseValue(question.id);
    const hasError = validationErrors.some(error => error.includes(question.id));

    return (
      <View
        key={question.id}
        style={[
          styles.questionContainer,
          {
            backgroundColor: colors.accent,
            borderColor: hasError ? colors.error : 'transparent',
            borderWidth: hasError ? 1 : 0,
          },
        ]}
      >
        <View style={styles.questionHeader}>
          <Text style={[styles.questionText, { color: colors.text }]}>
            {question.question}
            {question.required && (
              <Text style={{ color: colors.error }}> *</Text>
            )}
          </Text>
          {question.description && (
            <Text style={[styles.questionDescription, { color: colors.text + '80' }]}>
              {question.description}
            </Text>
          )}
        </View>

        {question.type === 'scale' && (
          <View style={styles.scaleContainer}>
            <View style={styles.scaleLabels}>
              <Text style={[styles.scaleLabel, { color: colors.text + '80' }]}>
                {question.min}
              </Text>
              <Text style={[styles.scaleLabel, { color: colors.text + '80' }]}>
                {question.max}
              </Text>
            </View>
            <Slider
              style={styles.slider}
              minimumValue={question.min || 1}
              maximumValue={question.max || 10}
              step={1}
              value={typeof value === 'number' ? value : question.min || 1}
              onValueChange={(val) => updateResponse(question.id, val)}
              minimumTrackTintColor={colors.primary}
              maximumTrackTintColor={colors.accent}
              thumbStyle={{ backgroundColor: colors.primary }}
              disabled={isReadOnly}
            />
            <Text style={[styles.scaleValue, { color: colors.primary }]}>
              {typeof value === 'number' ? value : question.min || 1}
            </Text>
          </View>
        )}

        {question.type === 'text' && (
          <TextInput
            style={[
              styles.textInput,
              {
                backgroundColor: colors.background,
                color: colors.text,
                borderColor: colors.primary + '40',
              },
            ]}
            value={typeof value === 'string' ? value : ''}
            onChangeText={(text) => updateResponse(question.id, text)}
            placeholder={question.placeholder}
            placeholderTextColor={colors.text + '60'}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
            editable={!isReadOnly}
          />
        )}

        {question.type === 'multiple_choice' && (
          <View style={styles.multipleChoiceContainer}>
            {question.options?.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.choiceOption,
                  {
                    backgroundColor: value === option ? colors.primary + '20' : colors.background,
                    borderColor: value === option ? colors.primary : colors.accent,
                  },
                ]}
                onPress={() => updateResponse(question.id, option)}
                disabled={isReadOnly}
              >
                <View
                  style={[
                    styles.choiceRadio,
                    {
                      backgroundColor: value === option ? colors.primary : 'transparent',
                      borderColor: colors.primary,
                    },
                  ]}
                >
                  {value === option && (
                    <CheckCircle size={16} color={colors.overlayText} />
                  )}
                </View>
                <Text
                  style={[
                    styles.choiceText,
                    {
                      color: value === option ? colors.primary : colors.text,
                    },
                  ]}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {question.type === 'boolean' && (
          <View style={styles.booleanContainer}>
            <TouchableOpacity
              style={[
                styles.booleanOption,
                {
                  backgroundColor: value === true ? colors.success + '20' : colors.background,
                  borderColor: value === true ? colors.success : colors.accent,
                },
              ]}
              onPress={() => updateResponse(question.id, true)}
              disabled={isReadOnly}
            >
              <Text
                style={[
                  styles.booleanText,
                  { color: value === true ? colors.success : colors.text },
                ]}
              >
                Yes
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.booleanOption,
                {
                  backgroundColor: value === false ? colors.error + '20' : colors.background,
                  borderColor: value === false ? colors.error : colors.accent,
                },
              ]}
              onPress={() => updateResponse(question.id, false)}
              disabled={isReadOnly}
            >
              <Text
                style={[
                  styles.booleanText,
                  { color: value === false ? colors.error : colors.text },
                ]}
              >
                No
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {question.type === 'number' && (
          <TextInput
            style={[
              styles.numberInput,
              {
                backgroundColor: colors.background,
                color: colors.text,
                borderColor: colors.primary + '40',
              },
            ]}
            value={typeof value === 'number' ? value.toString() : ''}
            onChangeText={(text) => {
              const num = parseFloat(text);
              if (!isNaN(num)) {
                updateResponse(question.id, num);
              } else if (text === '') {
                updateResponse(question.id, '');
              }
            }}
            placeholder="Enter number"
            placeholderTextColor={colors.text + '60'}
            keyboardType="numeric"
            editable={!isReadOnly}
          />
        )}
      </View>
    );
  };

  const renderConfirmModal = () => (
    <Modal visible={showConfirmModal} transparent animationType="fade">
      <View style={styles.modalOverlay}>
        <View style={[styles.confirmModal, { backgroundColor: colors.background }]}>
          <View style={styles.confirmHeader}>
            <CheckCircle size={48} color={colors.success} />
            <Text style={[styles.confirmTitle, { color: colors.text }]}>
              Submit Check-In?
            </Text>
            <Text style={[styles.confirmDescription, { color: colors.text + '80' }]}>
              Your responses will be sent to your coach for review.
            </Text>
          </View>

          <View style={styles.confirmActions}>
            <TouchableOpacity
              style={[styles.confirmButton, { backgroundColor: colors.accent }]}
              onPress={() => setShowConfirmModal(false)}
            >
              <Text style={[styles.confirmButtonText, { color: colors.text }]}>
                Review Again
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.confirmButton, { backgroundColor: colors.success }]}
              onPress={confirmSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color={colors.overlayText} />
              ) : (
                <>
                  <Send size={16} color={colors.overlayText} />
                  <Text style={[styles.confirmButtonText, { color: colors.overlayText }]}>
                    Submit
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 20,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 8,
    },
    headerDescription: {
      fontSize: 16,
      color: colors.text + '80',
      lineHeight: 24,
    },
    weekInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 12,
      gap: 8,
    },
    weekText: {
      fontSize: 14,
      color: colors.primary,
      fontWeight: '500',
    },
    content: {
      flex: 1,
      padding: 20,
    },
    questionsContainer: {
      gap: 20,
    },
    questionContainer: {
      padding: 20,
      borderRadius: 16,
    },
    questionHeader: {
      marginBottom: 16,
    },
    questionText: {
      fontSize: 18,
      fontWeight: '600',
      lineHeight: 24,
      marginBottom: 8,
    },
    questionDescription: {
      fontSize: 14,
      lineHeight: 20,
    },
    scaleContainer: {
      gap: 12,
    },
    scaleLabels: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    scaleLabel: {
      fontSize: 14,
      fontWeight: '500',
    },
    slider: {
      height: 40,
    },
    scaleValue: {
      fontSize: 24,
      fontWeight: '700',
      textAlign: 'center',
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      minHeight: 80,
    },
    multipleChoiceContainer: {
      gap: 12,
    },
    choiceOption: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      borderWidth: 2,
      gap: 12,
    },
    choiceRadio: {
      width: 24,
      height: 24,
      borderRadius: 12,
      borderWidth: 2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    choiceText: {
      fontSize: 16,
      flex: 1,
    },
    booleanContainer: {
      flexDirection: 'row',
      gap: 12,
    },
    booleanOption: {
      flex: 1,
      padding: 16,
      borderRadius: 12,
      borderWidth: 2,
      alignItems: 'center',
    },
    booleanText: {
      fontSize: 16,
      fontWeight: '500',
    },
    numberInput: {
      borderWidth: 1,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      textAlign: 'center',
    },
    validationErrors: {
      backgroundColor: colors.error + '10',
      padding: 16,
      borderRadius: 12,
      marginBottom: 20,
    },
    errorTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.error,
      marginBottom: 8,
    },
    errorText: {
      fontSize: 14,
      color: colors.error,
      lineHeight: 20,
    },
    footer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
      borderTopWidth: 1,
      borderTopColor: colors.accent,
    },
    footerButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
    },
    footerButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: colors.text + '80',
      marginTop: 16,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    confirmModal: {
      width: '80%',
      borderRadius: 16,
      padding: 24,
    },
    confirmHeader: {
      alignItems: 'center',
      marginBottom: 24,
    },
    confirmTitle: {
      fontSize: 20,
      fontWeight: '600',
      marginTop: 16,
      marginBottom: 8,
    },
    confirmDescription: {
      fontSize: 16,
      textAlign: 'center',
      lineHeight: 24,
    },
    confirmActions: {
      flexDirection: 'row',
      gap: 12,
    },
    confirmButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      borderRadius: 8,
      gap: 6,
    },
    confirmButtonText: {
      fontSize: 16,
      fontWeight: '500',
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading check-in form...</Text>
      </View>
    );
  }

  if (!template) {
    return (
      <View style={styles.loadingContainer}>
        <AlertCircle size={48} color={colors.error} />
        <Text style={[styles.loadingText, { color: colors.error }]}>
          Failed to load check-in form
        </Text>
      </View>
    );
  }

  const weekEndDate = new Date(weekStartDate);
  weekEndDate.setDate(weekEndDate.getDate() + 6);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{template.title}</Text>
        {template.description && (
          <Text style={styles.headerDescription}>{template.description}</Text>
        )}
        <View style={styles.weekInfo}>
          <Calendar size={16} color={colors.primary} />
          <Text style={styles.weekText}>
            Week of {new Date(weekStartDate).toLocaleDateString()} - {weekEndDate.toLocaleDateString()}
          </Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {validationErrors.length > 0 && (
          <View style={styles.validationErrors}>
            <Text style={styles.errorTitle}>Please complete the following:</Text>
            {validationErrors.map((error, index) => (
              <Text key={index} style={styles.errorText}>
                • {error}
              </Text>
            ))}
          </View>
        )}

        <View style={styles.questionsContainer}>
          {template.questions.map(renderQuestion)}
        </View>
      </ScrollView>

      {!isReadOnly && (
        <View style={styles.footer}>
          {onCancel && (
            <TouchableOpacity
              style={[styles.footerButton, { backgroundColor: colors.accent }]}
              onPress={onCancel}
            >
              <X size={16} color={colors.text} />
              <Text style={[styles.footerButtonText, { color: colors.text }]}>
                Cancel
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.footerButton,
              { backgroundColor: colors.primary },
              onCancel ? {} : { flex: 2 },
            ]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color={colors.overlayText} />
            ) : (
              <>
                <Send size={16} color={colors.overlayText} />
                <Text style={[styles.footerButtonText, { color: colors.overlayText }]}>
                  Submit Check-In
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}

      {renderConfirmModal()}
    </View>
  );
};
