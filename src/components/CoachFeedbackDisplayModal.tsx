import React, { useEffect } from 'react';
import { View, StyleSheet, Modal, ScrollView, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { useFeedbackStore } from '@/store/feedbackStore';
import { CoachFeedback } from '@/services/feedbackService';
import { ThemedView, ThemedText, ThemedButton, ThemedOverlay } from '@/components/ThemedComponents';
import { MessageSquare, X, User, Calendar } from 'lucide-react-native';

interface CoachFeedbackDisplayModalProps {
  isVisible: boolean;
  onClose: () => void;
  feedback: CoachFeedback;
}

export default function CoachFeedbackDisplayModal({
  isVisible,
  onClose,
  feedback,
}: CoachFeedbackDisplayModalProps) {
  const { colors } = useThemeStore();
  const { markFeedbackAsRead } = useFeedbackStore();
  
  // Automatically mark feedback as read when modal is opened
  useEffect(() => {
    const autoMarkAsRead = async () => {
      if (isVisible && feedback.status === 'published') {
        try {
          console.log('📝 CoachFeedbackModal: Auto-marking feedback as read:', feedback.id);
          await markFeedbackAsRead(feedback.id);
          console.log('✅ CoachFeedbackModal: Feedback auto-marked as read successfully');
        } catch (error) {
          console.error('❌ CoachFeedbackModal: Error auto-marking feedback as read:', error);
        }
      }
    };
    
    autoMarkAsRead();
  }, [isVisible, feedback.id, feedback.status, markFeedbackAsRead]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <ThemedOverlay style={styles.overlay}>
        <ThemedView style={styles.modalContainer} variant="card">
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <MessageSquare size={24} color={colors.primary} />
              <ThemedText style={styles.title}>Coach Feedback</ThemedText>
            </View>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <X size={20} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            <View style={styles.metaInfo}>
              <View style={styles.metaItem}>
                <User size={16} color={colors.primary} />
                <ThemedText style={styles.metaText}>From Coach</ThemedText>
              </View>
              <View style={styles.metaItem}>
                <Calendar size={16} color={colors.primary} />
                <ThemedText style={styles.metaText}>
                  {formatDate(feedback.published_at)}
                </ThemedText>
              </View>
            </View>

            <View style={[styles.feedbackContent, { backgroundColor: colors.accent }]}>
              <ThemedText style={styles.feedbackText}>
                {feedback.feedback_content}
              </ThemedText>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            <ThemedButton
              title="Close"
              onPress={onClose}
              style={[styles.button, { backgroundColor: colors.accent }]}
              textStyle={{ color: colors.text }}
            />
          </View>
        </ThemedView>
      </ThemedOverlay>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    width: '100%',
    maxWidth: 500,
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  metaInfo: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metaText: {
    fontSize: 14,
    opacity: 0.8,
  },
  feedbackContent: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feedbackText: {
    fontSize: 16,
    lineHeight: 24,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  button: {
    paddingVertical: 12,
  },
});