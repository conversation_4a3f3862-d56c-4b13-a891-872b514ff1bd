import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import {
  Settings,
  AlertTriangle,
  Calendar,
  Dumbbell,
  Heart,
  Tool,
  MessageSquare,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  X,
  Plus,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

interface ModificationRequest {
  id: string;
  clientId: string;
  coachId: string;
  programId: string;
  requestType: 'exercise_swap' | 'schedule_change' | 'intensity_adjustment' | 'injury_accommodation' | 'equipment_limitation' | 'other';
  title: string;
  description: string;
  currentDetails: any;
  requestedChanges: any;
  reason: string;
  urgency: 'low' | 'medium' | 'high';
  status: 'pending' | 'approved' | 'rejected' | 'implemented';
  coachResponse?: string;
  coachRespondedAt?: string;
  createdAt: string;
  updatedAt: string;
  program?: {
    id: string;
    name: string;
  };
}

interface RequestFormData {
  requestType: ModificationRequest['requestType'];
  title: string;
  description: string;
  reason: string;
  urgency: ModificationRequest['urgency'];
  currentDetails: string;
  requestedChanges: string;
}

const REQUEST_TYPES = [
  {
    key: 'exercise_swap',
    label: 'Exercise Swap',
    description: 'Replace an exercise with an alternative',
    icon: <Dumbbell size={24} />,
    color: '#3B82F6',
  },
  {
    key: 'schedule_change',
    label: 'Schedule Change',
    description: 'Modify workout days or timing',
    icon: <Calendar size={24} />,
    color: '#8B5CF6',
  },
  {
    key: 'intensity_adjustment',
    label: 'Intensity Adjustment',
    description: 'Increase or decrease workout intensity',
    icon: <Settings size={24} />,
    color: '#F59E0B',
  },
  {
    key: 'injury_accommodation',
    label: 'Injury Accommodation',
    description: 'Modify program due to injury or pain',
    icon: <Heart size={24} />,
    color: '#EF4444',
  },
  {
    key: 'equipment_limitation',
    label: 'Equipment Limitation',
    description: 'Adapt exercises for available equipment',
    icon: <Tool size={24} />,
    color: '#10B981',
  },
  {
    key: 'other',
    label: 'Other',
    description: 'Custom modification request',
    icon: <MessageSquare size={24} />,
    color: '#6B7280',
  },
] as const;

const URGENCY_LEVELS = [
  { key: 'low', label: 'Low', description: 'Can wait for next program update', color: '#10B981' },
  { key: 'medium', label: 'Medium', description: 'Should be addressed this week', color: '#F59E0B' },
  { key: 'high', label: 'High', description: 'Needs immediate attention', color: '#EF4444' },
] as const;

const STATUS_COLORS = {
  pending: '#F59E0B',
  approved: '#10B981',
  rejected: '#EF4444',
  implemented: '#3B82F6',
};

const STATUS_ICONS = {
  pending: <Clock size={16} />,
  approved: <CheckCircle size={16} />,
  rejected: <XCircle size={16} />,
  implemented: <CheckCircle size={16} />,
};

interface ProgramModificationRequestProps {
  programId?: string;
  onRequestSubmitted?: (request: ModificationRequest) => void;
  showHistory?: boolean;
}

export const ProgramModificationRequest: React.FC<ProgramModificationRequestProps> = ({
  programId,
  onRequestSubmitted,
  showHistory = true,
}) => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [requests, setRequests] = useState<ModificationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<RequestFormData>({
    requestType: 'exercise_swap',
    title: '',
    description: '',
    reason: '',
    urgency: 'medium',
    currentDetails: '',
    requestedChanges: '',
  });

  useEffect(() => {
    if (showHistory) {
      loadRequests();
    }
  }, [showHistory]);

  const loadRequests = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      let query = supabase
        .from('program_modification_requests')
        .select(`
          *,
          workout_programs!inner(id, name)
        `)
        .eq('client_id', user.id)
        .order('created_at', { ascending: false });

      if (programId) {
        query = query.eq('program_id', programId);
      }

      const { data, error } = await query;
      if (error) throw error;

      const formattedRequests: ModificationRequest[] = data?.map(item => ({
        id: item.id,
        clientId: item.client_id,
        coachId: item.coach_id,
        programId: item.program_id,
        requestType: item.request_type,
        title: item.title,
        description: item.description,
        currentDetails: item.current_details,
        requestedChanges: item.requested_changes,
        reason: item.reason,
        urgency: item.urgency,
        status: item.status,
        coachResponse: item.coach_response,
        coachRespondedAt: item.coach_responded_at,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        program: {
          id: item.workout_programs.id,
          name: item.workout_programs.name,
        },
      })) || [];

      setRequests(formattedRequests);
    } catch (error) {
      console.error('Failed to load modification requests:', error);
      Alert.alert('Error', 'Failed to load modification requests');
    } finally {
      setIsLoading(false);
    }
  };

  const submitRequest = async () => {
    try {
      if (!formData.title.trim() || !formData.description.trim() || !formData.reason.trim()) {
        Alert.alert('Validation Error', 'Please fill in all required fields');
        return;
      }

      setIsSubmitting(true);

      await executeOperation(
        async () => {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user');

          // Get coach ID from relationship
          const { data: relationship } = await supabase
            .from('coach_client_relationships')
            .select('coach_id')
            .eq('client_id', user.id)
            .eq('status', 'active')
            .single();

          if (!relationship) throw new Error('No active coach relationship');

          // Get current program if not specified
          let targetProgramId = programId;
          if (!targetProgramId) {
            const { data: currentProgram } = await supabase
              .from('user_programs')
              .select('program_id')
              .eq('user_id', user.id)
              .eq('is_active', true)
              .single();

            if (!currentProgram) throw new Error('No active program found');
            targetProgramId = currentProgram.program_id;
          }

          const requestData = {
            client_id: user.id,
            coach_id: relationship.coach_id,
            program_id: targetProgramId,
            request_type: formData.requestType,
            title: formData.title.trim(),
            description: formData.description.trim(),
            current_details: { description: formData.currentDetails.trim() },
            requested_changes: { description: formData.requestedChanges.trim() },
            reason: formData.reason.trim(),
            urgency: formData.urgency,
            status: 'pending',
          };

          const { data, error } = await supabase
            .from('program_modification_requests')
            .insert(requestData)
            .select(`
              *,
              workout_programs!inner(id, name)
            `)
            .single();

          if (error) throw error;

          const newRequest: ModificationRequest = {
            id: data.id,
            clientId: data.client_id,
            coachId: data.coach_id,
            programId: data.program_id,
            requestType: data.request_type,
            title: data.title,
            description: data.description,
            currentDetails: data.current_details,
            requestedChanges: data.requested_changes,
            reason: data.reason,
            urgency: data.urgency,
            status: data.status,
            coachResponse: data.coach_response,
            coachRespondedAt: data.coach_responded_at,
            createdAt: data.created_at,
            updatedAt: data.updated_at,
            program: {
              id: data.workout_programs.id,
              name: data.workout_programs.name,
            },
          };

          setRequests(prev => [newRequest, ...prev]);
          setShowRequestForm(false);
          resetForm();
          onRequestSubmitted?.(newRequest);

          Alert.alert(
            'Request Submitted!',
            'Your modification request has been sent to your coach for review.',
            [{ text: 'OK' }]
          );
        },
        {
          type: 'PROGRAM_MODIFICATION_REQUEST',
          payload: formData,
        }
      );
    } catch (error) {
      console.error('Failed to submit modification request:', error);
      Alert.alert('Error', 'Failed to submit modification request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      requestType: 'exercise_swap',
      title: '',
      description: '',
      reason: '',
      urgency: 'medium',
      currentDetails: '',
      requestedChanges: '',
    });
  };

  const renderRequestTypeSelector = () => (
    <View style={styles.requestTypeSelector}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Request Type *
      </Text>
      <View style={styles.requestTypeGrid}>
        {REQUEST_TYPES.map((type) => (
          <TouchableOpacity
            key={type.key}
            style={[
              styles.requestTypeCard,
              {
                backgroundColor: formData.requestType === type.key ? type.color + '20' : colors.accent,
                borderColor: formData.requestType === type.key ? type.color : 'transparent',
                borderWidth: 2,
              },
            ]}
            onPress={() => setFormData({ ...formData, requestType: type.key })}
          >
            <View style={[styles.requestTypeIcon, { backgroundColor: type.color + '20' }]}>
              {React.cloneElement(type.icon as React.ReactElement, { color: type.color })}
            </View>
            <Text
              style={[
                styles.requestTypeLabel,
                {
                  color: formData.requestType === type.key ? type.color : colors.text,
                },
              ]}
            >
              {type.label}
            </Text>
            <Text
              style={[
                styles.requestTypeDescription,
                {
                  color: formData.requestType === type.key ? type.color + 'CC' : colors.text + '80',
                },
              ]}
            >
              {type.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderUrgencySelector = () => (
    <View style={styles.urgencySelector}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Urgency Level *
      </Text>
      <View style={styles.urgencyOptions}>
        {URGENCY_LEVELS.map((level) => (
          <TouchableOpacity
            key={level.key}
            style={[
              styles.urgencyOption,
              {
                backgroundColor: formData.urgency === level.key ? level.color + '20' : colors.accent,
                borderColor: formData.urgency === level.key ? level.color : 'transparent',
                borderWidth: 2,
              },
            ]}
            onPress={() => setFormData({ ...formData, urgency: level.key as any })}
          >
            <Text
              style={[
                styles.urgencyLabel,
                {
                  color: formData.urgency === level.key ? level.color : colors.text,
                },
              ]}
            >
              {level.label}
            </Text>
            <Text
              style={[
                styles.urgencyDescription,
                {
                  color: formData.urgency === level.key ? level.color + 'CC' : colors.text + '80',
                },
              ]}
            >
              {level.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderRequestForm = () => (
    <Modal visible={showRequestForm} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            style={[styles.modalButton, { backgroundColor: colors.error }]}
            onPress={() => {
              setShowRequestForm(false);
              resetForm();
            }}
          >
            <X size={20} color={colors.overlayText} />
          </TouchableOpacity>

          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Request Program Modification
          </Text>

          <TouchableOpacity
            style={[styles.modalButton, { backgroundColor: colors.success }]}
            onPress={submitRequest}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color={colors.overlayText} />
            ) : (
              <Send size={20} color={colors.overlayText} />
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          {renderRequestTypeSelector()}

          <View style={styles.formSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Request Title *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
              placeholder="Brief title for your request"
              placeholderTextColor={colors.text + '60'}
            />
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Description *
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              placeholder="Detailed description of what you'd like to change..."
              placeholderTextColor={colors.text + '60'}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Current Situation
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.currentDetails}
              onChangeText={(text) => setFormData({ ...formData, currentDetails: text })}
              placeholder="Describe the current program aspect you want to change..."
              placeholderTextColor={colors.text + '60'}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Requested Changes
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.requestedChanges}
              onChangeText={(text) => setFormData({ ...formData, requestedChanges: text })}
              placeholder="Describe exactly what you'd like instead..."
              placeholderTextColor={colors.text + '60'}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Reason for Request *
            </Text>
            <TextInput
              style={[
                styles.textArea,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.reason}
              onChangeText={(text) => setFormData({ ...formData, reason: text })}
              placeholder="Why do you need this modification? (injury, equipment, schedule, etc.)"
              placeholderTextColor={colors.text + '60'}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          {renderUrgencySelector()}
        </ScrollView>
      </View>
    </Modal>
  );

  const renderRequestItem = (request: ModificationRequest) => {
    const requestType = REQUEST_TYPES.find(type => type.key === request.requestType);
    const statusColor = STATUS_COLORS[request.status];

    return (
      <View
        key={request.id}
        style={[
          styles.requestItem,
          {
            backgroundColor: colors.accent,
            borderLeftColor: requestType?.color || colors.primary,
            borderLeftWidth: 4,
          },
        ]}
      >
        <View style={styles.requestHeader}>
          <View style={styles.requestTitleSection}>
            <View style={[styles.requestIcon, { backgroundColor: requestType?.color + '20' || colors.primary + '20' }]}>
              {requestType?.icon ?
                React.cloneElement(requestType.icon as React.ReactElement, {
                  color: requestType.color,
                  size: 20
                }) :
                <Settings size={20} color={colors.primary} />
              }
            </View>

            <View style={styles.requestTitleContainer}>
              <Text style={[styles.requestTitle, { color: colors.text }]}>
                {request.title}
              </Text>
              <Text style={[styles.requestProgram, { color: colors.text + '80' }]}>
                {request.program?.name}
              </Text>
            </View>
          </View>

          <View style={styles.requestStatus}>
            <View style={[styles.statusBadge, { backgroundColor: statusColor + '20' }]}>
              {React.cloneElement(STATUS_ICONS[request.status] as React.ReactElement, { color: statusColor })}
              <Text style={[styles.statusText, { color: statusColor }]}>
                {request.status.toUpperCase()}
              </Text>
            </View>
          </View>
        </View>

        <Text style={[styles.requestDescription, { color: colors.text }]}>
          {request.description}
        </Text>

        <View style={styles.requestDetails}>
          <View style={styles.requestDetailItem}>
            <Text style={[styles.requestDetailLabel, { color: colors.text + '80' }]}>
              Reason:
            </Text>
            <Text style={[styles.requestDetailText, { color: colors.text }]}>
              {request.reason}
            </Text>
          </View>

          {request.coachResponse && (
            <View style={[styles.coachResponseSection, { backgroundColor: colors.background }]}>
              <Text style={[styles.coachResponseTitle, { color: colors.primary }]}>
                Coach Response:
              </Text>
              <Text style={[styles.coachResponseText, { color: colors.text }]}>
                {request.coachResponse}
              </Text>
              {request.coachRespondedAt && (
                <Text style={[styles.coachResponseDate, { color: colors.text + '60' }]}>
                  {new Date(request.coachRespondedAt).toLocaleDateString()}
                </Text>
              )}
            </View>
          )}
        </View>

        <View style={styles.requestFooter}>
          <View style={[styles.urgencyBadge, { backgroundColor: URGENCY_LEVELS.find(l => l.key === request.urgency)?.color + '20' }]}>
            <Text style={[styles.urgencyText, { color: URGENCY_LEVELS.find(l => l.key === request.urgency)?.color }]}>
              {request.urgency.toUpperCase()} URGENCY
            </Text>
          </View>

          <Text style={[styles.requestDate, { color: colors.text + '60' }]}>
            {new Date(request.createdAt).toLocaleDateString()}
          </Text>
        </View>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
    },
    newRequestButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
      backgroundColor: colors.primary,
      borderRadius: 8,
      gap: 6,
    },
    newRequestButtonText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.overlayText,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    requestsList: {
      gap: 16,
    },
    requestItem: {
      borderRadius: 16,
      padding: 20,
    },
    requestHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    requestTitleSection: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      gap: 12,
    },
    requestIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    requestTitleContainer: {
      flex: 1,
    },
    requestTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    requestProgram: {
      fontSize: 14,
    },
    requestStatus: {
      alignItems: 'flex-end',
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
      gap: 4,
    },
    statusText: {
      fontSize: 10,
      fontWeight: '600',
    },
    requestDescription: {
      fontSize: 16,
      lineHeight: 24,
      marginBottom: 16,
    },
    requestDetails: {
      gap: 12,
    },
    requestDetailItem: {
      gap: 4,
    },
    requestDetailLabel: {
      fontSize: 14,
      fontWeight: '500',
    },
    requestDetailText: {
      fontSize: 14,
      lineHeight: 20,
    },
    coachResponseSection: {
      padding: 12,
      borderRadius: 8,
      marginTop: 8,
    },
    coachResponseTitle: {
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 8,
    },
    coachResponseText: {
      fontSize: 14,
      lineHeight: 20,
      marginBottom: 8,
    },
    coachResponseDate: {
      fontSize: 12,
    },
    requestFooter: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 16,
    },
    urgencyBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
    },
    urgencyText: {
      fontSize: 10,
      fontWeight: '600',
    },
    requestDate: {
      fontSize: 12,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyStateTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateDescription: {
      fontSize: 16,
      color: colors.text + '80',
      textAlign: 'center',
      lineHeight: 24,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: colors.text + '80',
      marginTop: 16,
    },
    // Modal styles
    modalContainer: {
      flex: 1,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    modalButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    modalContent: {
      flex: 1,
      padding: 20,
    },
    formSection: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 8,
    },
    textInput: {
      height: 48,
      borderRadius: 12,
      borderWidth: 1,
      paddingHorizontal: 16,
      fontSize: 16,
    },
    textArea: {
      borderRadius: 12,
      borderWidth: 1,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      minHeight: 80,
    },
    requestTypeSelector: {
      marginBottom: 20,
    },
    requestTypeGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    requestTypeCard: {
      width: '48%',
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      gap: 8,
    },
    requestTypeIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    requestTypeLabel: {
      fontSize: 14,
      fontWeight: '600',
      textAlign: 'center',
    },
    requestTypeDescription: {
      fontSize: 12,
      textAlign: 'center',
      lineHeight: 16,
    },
    urgencySelector: {
      marginBottom: 20,
    },
    urgencyOptions: {
      gap: 8,
    },
    urgencyOption: {
      padding: 16,
      borderRadius: 12,
    },
    urgencyLabel: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    urgencyDescription: {
      fontSize: 14,
      lineHeight: 20,
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading requests...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Program Modifications</Text>
        <TouchableOpacity
          style={styles.newRequestButton}
          onPress={() => setShowRequestForm(true)}
        >
          <Plus size={16} color={colors.overlayText} />
          <Text style={styles.newRequestButtonText}>New Request</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {requests.length === 0 ? (
          <View style={styles.emptyState}>
            <Settings size={64} color={colors.text + '40'} />
            <Text style={styles.emptyStateTitle}>No Modification Requests</Text>
            <Text style={styles.emptyStateDescription}>
              Request changes to your workout program when you need accommodations for injuries, equipment, or schedule changes.
            </Text>
          </View>
        ) : (
          <View style={styles.requestsList}>
            {requests.map(renderRequestItem)}
          </View>
        )}
      </ScrollView>

      {renderRequestForm()}
    </View>
  );
};
