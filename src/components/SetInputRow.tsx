import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedInput } from '@/components/ThemedComponents';
import { SetLog } from '@/services/workoutLoggingService';
import { Plus, Check } from 'lucide-react-native';
import { profileService } from '@/services/profileService';

interface SetInputRowProps {
  setNumber: number;
  onLogSet: (setData: Omit<SetLog, 'set_number'>) => void;
  onStartRest?: (duration: number) => void;
  restDuration?: number;
  isTimeBased?: boolean;
  disabled?: boolean;
  exerciseId?: string; // Add exerciseId prop to track which exercise this row belongs to
}

export default function SetInputRow({
  setNumber,
  onLogSet,
  onStartRest,
  restDuration = 60,
  isTimeBased = false,
  disabled = false,
  exerciseId,
}: SetInputRowProps) {
  const { colors } = useThemeStore();
  const [reps, setReps] = useState('');
  const [weight, setWeight] = useState('');
  const [duration, setDuration] = useState('');
  const [rpe, setRpe] = useState('');
  const [isLogged, setIsLogged] = useState(false);
  
  // Reset state when exerciseId changes
  useEffect(() => {
    setReps('');
    setWeight('');
    setDuration('');
    setRpe('');
    setIsLogged(false);
  }, [exerciseId]);

  const handleLogSet = () => {
    if (isLogged) return;

    // Convert weight from lbs to kg for storage
    const weightInKg = weight ? profileService.convertWeightToKg(parseFloat(weight)) : undefined;

    const setData: Omit<SetLog, 'set_number'> = {
      reps_actual: isTimeBased ? undefined : (reps ? parseInt(reps, 10) : undefined),
      weight_kg_actual: weightInKg,
      duration_seconds_actual: isTimeBased ? (duration ? parseInt(duration, 10) : undefined) : undefined,
      rpe_actual: rpe ? parseInt(rpe, 10) : undefined,
    };

    // Validate required fields
    if (!isTimeBased && !reps) {
      return; // Need reps for non-time based exercises
    }
    if (isTimeBased && !duration) {
      return; // Need duration for time-based exercises
    }

    onLogSet(setData);
    setIsLogged(true);

    // Auto-start rest timer if provided
    if (onStartRest && restDuration > 0) {
      onStartRest(restDuration);
    }
  };

  const canLogSet = () => {
    if (isLogged || disabled) return false;
    if (isTimeBased) {
      return duration.trim() !== '';
    }
    return reps.trim() !== '';
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.accent }]}>
      <View style={styles.setNumber}>
        <ThemedText style={styles.setNumberText}>Set {setNumber}</ThemedText>
      </View>

      <View style={styles.inputsContainer}>
        {!isTimeBased ? (
          <View style={styles.repsInputGroup}>
            <ThemedText style={styles.inputLabel}>Reps</ThemedText>
            <ThemedInput
              value={reps}
              onChangeText={setReps}
              placeholder="0"
              keyboardType="numeric"
              style={styles.input}
              editable={!isLogged && !disabled}
            />
          </View>
        ) : (
          <View style={styles.repsInputGroup}>
            <ThemedText style={styles.inputLabel}>Duration (s)</ThemedText>
            <ThemedInput
              value={duration}
              onChangeText={setDuration}
              placeholder="0"
              keyboardType="numeric"
              style={styles.input}
              editable={!isLogged && !disabled}
            />
          </View>
        )}

        <View style={styles.weightInputGroup}>
          <ThemedText style={styles.inputLabel}>Weight (lbs)</ThemedText>
          <ThemedInput
            value={weight}
            onChangeText={setWeight}
            placeholder="0"
            keyboardType="numeric"
            style={styles.input}
            editable={!isLogged && !disabled}
          />
        </View>

        <View style={styles.rpeInputGroup}>
          <ThemedText style={styles.inputLabel}>RPE</ThemedText>
          <ThemedInput
            value={rpe}
            onChangeText={setRpe}
            placeholder="1-10"
            keyboardType="numeric"
            style={styles.input}
            editable={!isLogged && !disabled}
          />
        </View>
      </View>

      <TouchableOpacity
        style={[
          styles.logButton,
          { 
            backgroundColor: isLogged ? colors.success : colors.background,
            borderWidth: 1,
            borderColor: isLogged ? colors.success : colors.accent
          },
          !canLogSet() && styles.logButtonDisabled,
        ]}
        onPress={handleLogSet}
        disabled={!canLogSet()}
      >
        {isLogged ? (
          <Check size={20} color={colors.contrastText} />
        ) : (
          <Plus size={20} color={canLogSet() ? colors.primary : colors.text} />
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 8,
  },
  setNumber: {
    width: 45,
    alignItems: 'center',
  },
  setNumberText: {
    fontSize: 15,
    fontWeight: '700',
  },
  inputsContainer: {
    flex: 1,
    flexDirection: 'row',
    gap: 6,
  },
  repsInputGroup: {
    flex: 0.8,
  },
  weightInputGroup: {
    flex: 1,
  },
  rpeInputGroup: {
    flex: 0.7,
  },
  inputLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
    opacity: 0.8,
  },
  input: {
    fontSize: 14,
    textAlign: 'center',
    paddingVertical: 6,
    paddingHorizontal: 2,
    minHeight: 36,
  },
  logButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  logButtonDisabled: {
    opacity: 0.5,
  },
});