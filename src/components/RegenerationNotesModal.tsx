import React, { useState } from 'react';
import { View, StyleSheet, Modal, ScrollView } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedView, ThemedButton, ThemedInput, ThemedOverlay } from '@/components/ThemedComponents';
import { X, RotateCcw, AlertCircle } from 'lucide-react-native';

interface RegenerationNotesModalProps {
  isVisible: boolean;
  onClose: () => void;
  onRegenerate: (notes: string) => void;
  isRegenerating?: boolean;
  programName: string;
}

export default function RegenerationNotesModal({
  isVisible,
  onClose,
  onRegenerate,
  isRegenerating = false,
  programName,
}: RegenerationNotesModalProps) {
  const { colors } = useThemeStore();
  const [notes, setNotes] = useState('');

  const handleRegenerate = () => {
    if (notes.trim()) {
      onRegenerate(notes.trim());
      setNotes('');
    }
  };

  const handleClose = () => {
    if (!isRegenerating) {
      setNotes('');
      onClose();
    }
  };

  const exampleNotes = [
    "Focus more on compound movements",
    "Reduce training volume for week 1",
    "Add more core exercises",
    "Include more unilateral exercises",
    "Adjust for shoulder mobility issues",
  ];

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <ThemedOverlay style={styles.overlay}>
        <ThemedView style={styles.modalContainer} variant="card">
          <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerLeft}>
                <View style={[styles.iconContainer, { backgroundColor: colors.warning + '20' }]}>
                  <RotateCcw size={24} color={colors.warning} />
                </View>
                <View style={styles.headerText}>
                  <ThemedText style={styles.title}>Regenerate Program</ThemedText>
                  <ThemedText style={styles.subtitle}>{programName}</ThemedText>
                </View>
              </View>
              
              {!isRegenerating && (
                <ThemedButton
                  title=""
                  onPress={handleClose}
                  variant="outline"
                  style={styles.closeButton}
                >
                  <X size={20} color={colors.text} />
                </ThemedButton>
              )}
            </View>

            {/* Warning Notice */}
            <View style={[styles.warningContainer, { backgroundColor: colors.warning + '15', borderColor: colors.warning + '30' }]}>
              <AlertCircle size={20} color={colors.warning} />
              <View style={styles.warningText}>
                <ThemedText style={[styles.warningTitle, { color: colors.warning }]}>
                  Program Regeneration
                </ThemedText>
                <ThemedText style={styles.warningDescription}>
                  This will create a completely new program based on your feedback. The current program will be archived.
                </ThemedText>
              </View>
            </View>

            {/* Instructions */}
            <View style={styles.instructionsContainer}>
              <ThemedText style={styles.instructionsTitle}>Provide Specific Feedback</ThemedText>
              <ThemedText style={styles.instructionsText}>
                Help the AI understand what changes you'd like to see in the new program. Be specific about:
              </ThemedText>
              
              <View style={styles.instructionsList}>
                <ThemedText style={styles.instructionItem}>• Exercise selection preferences</ThemedText>
                <ThemedText style={styles.instructionItem}>• Training volume adjustments</ThemedText>
                <ThemedText style={styles.instructionItem}>• Specific muscle group focus</ThemedText>
                <ThemedText style={styles.instructionItem}>• Progression modifications</ThemedText>
                <ThemedText style={styles.instructionItem}>• Any other specific requirements</ThemedText>
              </View>
            </View>

            {/* Notes Input */}
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Regeneration Notes *</ThemedText>
              <ThemedInput
                value={notes}
                onChangeText={setNotes}
                placeholder="Describe the changes you'd like to see in the regenerated program..."
                multiline
                numberOfLines={6}
                textAlignVertical="top"
                style={styles.notesInput}
              />
              <ThemedText style={styles.inputHelper}>
                Minimum 10 characters required. Be specific to get better results.
              </ThemedText>
            </View>

            {/* Example Notes */}
            <View style={styles.examplesContainer}>
              <ThemedText style={styles.examplesTitle}>Example Feedback:</ThemedText>
              <View style={styles.examplesList}>
                {exampleNotes.map((example, index) => (
                  <ThemedButton
                    key={index}
                    title={example}
                    variant="outline"
                    style={styles.exampleButton}
                    textStyle={styles.exampleButtonText}
                    onPress={() => {
                      if (notes.trim()) {
                        setNotes(notes + '\n• ' + example);
                      } else {
                        setNotes('• ' + example);
                      }
                    }}
                    disabled={isRegenerating}
                  />
                ))}
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionsContainer}>
              <ThemedButton
                title="Cancel"
                variant="outline"
                onPress={handleClose}
                style={styles.actionButton}
                disabled={isRegenerating}
              />
              
              <ThemedButton
                title={isRegenerating ? "Regenerating..." : "Regenerate Program"}
                onPress={handleRegenerate}
                style={[styles.actionButton, { backgroundColor: colors.warning }]}
                disabled={isRegenerating || notes.trim().length < 10}
              />
            </View>
          </ScrollView>
        </ThemedView>
      </ThemedOverlay>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
    maxWidth: 500,
    width: '100%',
    maxHeight: '90%',
  },
  scrollContainer: {
    maxHeight: 600,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    padding: 24,
    paddingBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    padding: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    marginHorizontal: 24,
    marginBottom: 20,
    borderRadius: 8,
    borderWidth: 1,
  },
  warningText: {
    flex: 1,
    marginLeft: 12,
  },
  warningTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  warningDescription: {
    fontSize: 12,
    lineHeight: 16,
    opacity: 0.8,
  },
  instructionsContainer: {
    paddingHorizontal: 24,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
    opacity: 0.8,
  },
  instructionsList: {
    gap: 4,
  },
  instructionItem: {
    fontSize: 13,
    lineHeight: 18,
    opacity: 0.7,
  },
  inputContainer: {
    paddingHorizontal: 24,
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  notesInput: {
    minHeight: 120,
    textAlignVertical: 'top',
  },
  inputHelper: {
    fontSize: 12,
    marginTop: 6,
    opacity: 0.6,
  },
  examplesContainer: {
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  examplesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  examplesList: {
    gap: 8,
  },
  exampleButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignSelf: 'flex-start',
  },
  exampleButtonText: {
    fontSize: 12,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 14,
  },
});