import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText } from '@/components/ThemedComponents';

interface WorkoutProgressBarProps {
  completed: number;
  total: number;
}

export default function WorkoutProgressBar({ completed, total }: WorkoutProgressBarProps) {
  const { colors } = useThemeStore();
  const progress = total > 0 ? (completed / total) * 100 : 0;

  return (
    <View style={styles.container}>
      <View style={styles.progressHeader}>
        <ThemedText style={styles.progressTitle}>Workout Progress</ThemedText>
        <ThemedText style={styles.progressText}>
          {completed} of {total} exercises
        </ThemedText>
      </View>
      
      <View style={[styles.progressBar, { backgroundColor: colors.accent }]}>
        <View 
          style={[
            styles.progressFill, 
            { 
              width: `${progress}%`, 
              backgroundColor: progress === 100 ? colors.success : colors.primary 
            }
          ]} 
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  progressText: {
    fontSize: 14,
    opacity: 0.7,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
});