import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  Dimensions,
} from 'react-native';
import { LineChart } from 'react-native-chart-kit';
import {
  Plus,
  TrendingUp,
  TrendingDown,
  Calendar,
  Ruler,
  Weight,
  Target,
  Edit3,
  Save,
  X,
  BarChart3,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

const { width } = Dimensions.get('window');

interface BodyMeasurement {
  id: string;
  measurementDate: string;
  weightKg?: number;
  heightCm?: number;
  bodyFatPercentage?: number;
  muscleMassKg?: number;
  chestCm?: number;
  waistCm?: number;
  hipsCm?: number;
  bicepLeftCm?: number;
  bicepRightCm?: number;
  thighLeftCm?: number;
  thighRightCm?: number;
  neckCm?: number;
  forearmLeftCm?: number;
  forearmRightCm?: number;
  calfLeftCm?: number;
  calfRightCm?: number;
  restingHeartRate?: number;
  bloodPressureSystolic?: number;
  bloodPressureDiastolic?: number;
  notes?: string;
}

interface MeasurementField {
  key: keyof BodyMeasurement;
  label: string;
  unit: string;
  category: 'body_composition' | 'circumference' | 'health';
  icon: React.ReactNode;
}

const MEASUREMENT_FIELDS: MeasurementField[] = [
  { key: 'weightKg', label: 'Weight', unit: 'kg', category: 'body_composition', icon: <Weight size={20} /> },
  { key: 'heightCm', label: 'Height', unit: 'cm', category: 'body_composition', icon: <Ruler size={20} /> },
  { key: 'bodyFatPercentage', label: 'Body Fat', unit: '%', category: 'body_composition', icon: <Target size={20} /> },
  { key: 'muscleMassKg', label: 'Muscle Mass', unit: 'kg', category: 'body_composition', icon: <TrendingUp size={20} /> },
  { key: 'chestCm', label: 'Chest', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'waistCm', label: 'Waist', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'hipsCm', label: 'Hips', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'bicepLeftCm', label: 'Left Bicep', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'bicepRightCm', label: 'Right Bicep', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'thighLeftCm', label: 'Left Thigh', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'thighRightCm', label: 'Right Thigh', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'neckCm', label: 'Neck', unit: 'cm', category: 'circumference', icon: <Ruler size={20} /> },
  { key: 'restingHeartRate', label: 'Resting HR', unit: 'bpm', category: 'health', icon: <TrendingUp size={20} /> },
];

export const MeasurementsTracker: React.FC = () => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [measurements, setMeasurements] = useState<BodyMeasurement[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showChartModal, setShowChartModal] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState<keyof BodyMeasurement>('weightKg');
  const [isLoading, setIsLoading] = useState(false);
  const [activeCategory, setActiveCategory] = useState<'body_composition' | 'circumference' | 'health'>('body_composition');
  const [newMeasurement, setNewMeasurement] = useState<Partial<BodyMeasurement>>({
    measurementDate: new Date().toISOString().split('T')[0],
  });

  useEffect(() => {
    loadMeasurements();
  }, []);

  const loadMeasurements = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('body_measurements')
        .select('*')
        .eq('user_id', user.id)
        .order('measurement_date', { ascending: false });

      if (error) throw error;

      const formattedMeasurements: BodyMeasurement[] = data?.map(item => ({
        id: item.id,
        measurementDate: item.measurement_date,
        weightKg: item.weight_kg,
        heightCm: item.height_cm,
        bodyFatPercentage: item.body_fat_percentage,
        muscleMassKg: item.muscle_mass_kg,
        chestCm: item.chest_cm,
        waistCm: item.waist_cm,
        hipsCm: item.hips_cm,
        bicepLeftCm: item.bicep_left_cm,
        bicepRightCm: item.bicep_right_cm,
        thighLeftCm: item.thigh_left_cm,
        thighRightCm: item.thigh_right_cm,
        neckCm: item.neck_cm,
        forearmLeftCm: item.forearm_left_cm,
        forearmRightCm: item.forearm_right_cm,
        calfLeftCm: item.calf_left_cm,
        calfRightCm: item.calf_right_cm,
        restingHeartRate: item.resting_heart_rate,
        bloodPressureSystolic: item.blood_pressure_systolic,
        bloodPressureDiastolic: item.blood_pressure_diastolic,
        notes: item.notes,
      })) || [];

      setMeasurements(formattedMeasurements);
    } catch (error) {
      console.error('Failed to load measurements:', error);
      Alert.alert('Error', 'Failed to load measurements');
    } finally {
      setIsLoading(false);
    }
  };

  const saveMeasurement = async () => {
    try {
      if (!newMeasurement.measurementDate) {
        Alert.alert('Error', 'Please select a measurement date');
        return;
      }

      await executeOperation(
        async () => {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user');

          const measurementData = {
            user_id: user.id,
            measurement_date: newMeasurement.measurementDate,
            weight_kg: newMeasurement.weightKg,
            height_cm: newMeasurement.heightCm,
            body_fat_percentage: newMeasurement.bodyFatPercentage,
            muscle_mass_kg: newMeasurement.muscleMassKg,
            chest_cm: newMeasurement.chestCm,
            waist_cm: newMeasurement.waistCm,
            hips_cm: newMeasurement.hipsCm,
            bicep_left_cm: newMeasurement.bicepLeftCm,
            bicep_right_cm: newMeasurement.bicepRightCm,
            thigh_left_cm: newMeasurement.thighLeftCm,
            thigh_right_cm: newMeasurement.thighRightCm,
            neck_cm: newMeasurement.neckCm,
            forearm_left_cm: newMeasurement.forearmLeftCm,
            forearm_right_cm: newMeasurement.forearmRightCm,
            calf_left_cm: newMeasurement.calfLeftCm,
            calf_right_cm: newMeasurement.calfRightCm,
            resting_heart_rate: newMeasurement.restingHeartRate,
            blood_pressure_systolic: newMeasurement.bloodPressureSystolic,
            blood_pressure_diastolic: newMeasurement.bloodPressureDiastolic,
            notes: newMeasurement.notes,
          };

          const { data, error } = await supabase
            .from('body_measurements')
            .upsert(measurementData)
            .select()
            .single();

          if (error) throw error;

          const formattedMeasurement: BodyMeasurement = {
            id: data.id,
            measurementDate: data.measurement_date,
            weightKg: data.weight_kg,
            heightCm: data.height_cm,
            bodyFatPercentage: data.body_fat_percentage,
            muscleMassKg: data.muscle_mass_kg,
            chestCm: data.chest_cm,
            waistCm: data.waist_cm,
            hipsCm: data.hips_cm,
            bicepLeftCm: data.bicep_left_cm,
            bicepRightCm: data.bicep_right_cm,
            thighLeftCm: data.thigh_left_cm,
            thighRightCm: data.thigh_right_cm,
            neckCm: data.neck_cm,
            forearmLeftCm: data.forearm_left_cm,
            forearmRightCm: data.forearm_right_cm,
            calfLeftCm: data.calf_left_cm,
            calfRightCm: data.calf_right_cm,
            restingHeartRate: data.resting_heart_rate,
            bloodPressureSystolic: data.blood_pressure_systolic,
            bloodPressureDiastolic: data.blood_pressure_diastolic,
            notes: data.notes,
          };

          setMeasurements(prev => {
            const filtered = prev.filter(m => m.measurementDate !== formattedMeasurement.measurementDate);
            return [formattedMeasurement, ...filtered].sort((a, b) => 
              new Date(b.measurementDate).getTime() - new Date(a.measurementDate).getTime()
            );
          });

          setShowAddModal(false);
          setNewMeasurement({ measurementDate: new Date().toISOString().split('T')[0] });
          Alert.alert('Success', 'Measurements saved successfully!');
        },
        {
          type: 'MEASUREMENT_SAVE',
          payload: newMeasurement,
        }
      );
    } catch (error) {
      console.error('Failed to save measurement:', error);
      Alert.alert('Error', 'Failed to save measurements');
    }
  };

  const getLatestMeasurement = () => {
    return measurements.length > 0 ? measurements[0] : null;
  };

  const getPreviousMeasurement = () => {
    return measurements.length > 1 ? measurements[1] : null;
  };

  const calculateChange = (current?: number, previous?: number) => {
    if (!current || !previous) return null;
    const change = current - previous;
    const percentage = (change / previous) * 100;
    return { absolute: change, percentage };
  };

  const getChartData = (metric: keyof BodyMeasurement) => {
    const data = measurements
      .filter(m => m[metric] !== undefined && m[metric] !== null)
      .reverse()
      .slice(-10); // Last 10 measurements

    if (data.length === 0) return null;

    return {
      labels: data.map(m => new Date(m.measurementDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
      datasets: [{
        data: data.map(m => Number(m[metric]) || 0),
        color: (opacity = 1) => colors.primary + Math.round(opacity * 255).toString(16),
        strokeWidth: 2,
      }],
    };
  };

  const renderCategoryTabs = () => (
    <View style={styles.categoryTabs}>
      {(['body_composition', 'circumference', 'health'] as const).map((category) => (
        <TouchableOpacity
          key={category}
          style={[
            styles.categoryTab,
            {
              backgroundColor: activeCategory === category ? colors.primary : colors.accent,
            },
          ]}
          onPress={() => setActiveCategory(category)}
        >
          <Text
            style={[
              styles.categoryTabText,
              {
                color: activeCategory === category ? colors.overlayText : colors.text,
              },
            ]}
          >
            {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderMeasurementCard = (field: MeasurementField) => {
    const latest = getLatestMeasurement();
    const previous = getPreviousMeasurement();
    const currentValue = latest?.[field.key] as number;
    const previousValue = previous?.[field.key] as number;
    const change = calculateChange(currentValue, previousValue);

    return (
      <TouchableOpacity
        key={field.key}
        style={[styles.measurementCard, { backgroundColor: colors.accent }]}
        onPress={() => {
          setSelectedMetric(field.key);
          setShowChartModal(true);
        }}
      >
        <View style={styles.measurementHeader}>
          <View style={[styles.measurementIcon, { backgroundColor: colors.primary + '20' }]}>
            {React.cloneElement(field.icon as React.ReactElement, { color: colors.primary })}
          </View>
          <Text style={[styles.measurementLabel, { color: colors.text }]}>
            {field.label}
          </Text>
        </View>

        <View style={styles.measurementValue}>
          <Text style={[styles.measurementNumber, { color: colors.text }]}>
            {currentValue ? `${currentValue.toFixed(1)}` : '--'}
          </Text>
          <Text style={[styles.measurementUnit, { color: colors.text + '80' }]}>
            {field.unit}
          </Text>
        </View>

        {change && (
          <View style={styles.measurementChange}>
            {change.absolute > 0 ? (
              <TrendingUp size={16} color={colors.success} />
            ) : (
              <TrendingDown size={16} color={colors.error} />
            )}
            <Text
              style={[
                styles.changeText,
                { color: change.absolute > 0 ? colors.success : colors.error },
              ]}
            >
              {change.absolute > 0 ? '+' : ''}{change.absolute.toFixed(1)} ({change.percentage.toFixed(1)}%)
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderAddMeasurementModal = () => (
    <Modal visible={showAddModal} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            style={[styles.modalButton, { backgroundColor: colors.error }]}
            onPress={() => setShowAddModal(false)}
          >
            <X size={20} color={colors.overlayText} />
          </TouchableOpacity>

          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Add Measurements
          </Text>

          <TouchableOpacity
            style={[styles.modalButton, { backgroundColor: colors.success }]}
            onPress={saveMeasurement}
          >
            <Save size={20} color={colors.overlayText} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          <View style={styles.dateSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Measurement Date
            </Text>
            <TextInput
              style={[
                styles.dateInput,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={newMeasurement.measurementDate}
              onChangeText={(text) => setNewMeasurement({ ...newMeasurement, measurementDate: text })}
              placeholder="YYYY-MM-DD"
              placeholderTextColor={colors.text + '60'}
            />
          </View>

          {MEASUREMENT_FIELDS.map((field) => (
            <View key={field.key} style={styles.inputSection}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                {field.label} ({field.unit})
              </Text>
              <TextInput
                style={[
                  styles.measurementInput,
                  {
                    backgroundColor: colors.accent,
                    color: colors.text,
                    borderColor: colors.primary + '40',
                  },
                ]}
                value={newMeasurement[field.key]?.toString() || ''}
                onChangeText={(text) => {
                  const value = parseFloat(text) || undefined;
                  setNewMeasurement({ ...newMeasurement, [field.key]: value });
                }}
                keyboardType="numeric"
                placeholder={`Enter ${field.label.toLowerCase()}`}
                placeholderTextColor={colors.text + '60'}
              />
            </View>
          ))}

          <View style={styles.inputSection}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>
              Notes (Optional)
            </Text>
            <TextInput
              style={[
                styles.notesInput,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={newMeasurement.notes || ''}
              onChangeText={(text) => setNewMeasurement({ ...newMeasurement, notes: text })}
              placeholder="Any notes about these measurements..."
              placeholderTextColor={colors.text + '60'}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
