import React from 'react';
import { View, StyleSheet, Modal, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedView, ThemedText, ThemedOverlay } from '@/components/ThemedComponents';
import EmbeddedVideoPlayer from '@/components/EmbeddedVideoPlayer';
import { X } from 'lucide-react-native';

interface ExerciseVideoModalProps {
  isVisible: boolean;
  onClose: () => void;
  exerciseName: string;
  videoUrl?: string;
}

export default function ExerciseVideoModal({
  isVisible,
  onClose,
  exerciseName,
  videoUrl,
}: ExerciseVideoModalProps) {
  const { colors } = useThemeStore();

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <ThemedOverlay style={styles.overlay}>
        <ThemedView style={styles.modalContainer} variant="card">
          <View style={styles.header}>
            <ThemedText style={styles.title}>{exerciseName}</ThemedText>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <X size={20} color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.videoContainer}>
            <EmbeddedVideoPlayer videoUrl={videoUrl} style={styles.video} />
          </View>
        </ThemedView>
      </ThemedOverlay>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
    maxWidth: 600,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  videoContainer: {
    padding: 20,
    paddingTop: 0,
  },
  video: {
    height: 300,
  },
});