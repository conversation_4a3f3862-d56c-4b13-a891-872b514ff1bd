import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Video, ResizeMode } from 'expo-av';
import { 
  X, 
  Play, 
  Pause, 
  RotateCcw, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Info,
  AlertTriangle,
  Target,
  Dumbbell
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { Exercise } from '../services/workoutService';

const { width, height } = Dimensions.get('window');

interface ExerciseInstructionModalProps {
  visible: boolean;
  exercise: Exercise | null;
  onClose: () => void;
  onStartExercise?: () => void;
}

export const ExerciseInstructionModal: React.FC<ExerciseInstructionModalProps> = ({
  visible,
  exercise,
  onClose,
  onStartExercise,
}) => {
  const { colors } = useThemeStore();
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [videoStatus, setVideoStatus] = useState<any>({});
  const [activeTab, setActiveTab] = useState<'instructions' | 'tips' | 'muscles'>('instructions');
  const videoRef = useRef<Video>(null);

  if (!exercise) return null;

  const handleVideoPlayPause = async () => {
    if (videoRef.current) {
      if (isVideoPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
      setIsVideoPlaying(!isVideoPlaying);
    }
  };

  const handleVideoRestart = async () => {
    if (videoRef.current) {
      await videoRef.current.replayAsync();
      setIsVideoPlaying(true);
    }
  };

  const handleMuteToggle = async () => {
    if (videoRef.current) {
      await videoRef.current.setIsMutedAsync(!isMuted);
      setIsMuted(!isMuted);
    }
  };

  const renderVideoPlayer = () => {
    if (!exercise.video_url && !exercise.demo_gif_url) {
      return (
        <View style={[styles.videoPlaceholder, { backgroundColor: colors.accent }]}>
          <Dumbbell size={48} color={colors.text + '40'} />
          <Text style={[styles.placeholderText, { color: colors.text + '60' }]}>
            No video available
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.videoContainer}>
        {exercise.video_url ? (
          <Video
            ref={videoRef}
            style={styles.video}
            source={{ uri: exercise.video_url }}
            useNativeControls={false}
            resizeMode={ResizeMode.CONTAIN}
            isLooping
            onPlaybackStatusUpdate={(status) => setVideoStatus(status)}
            onLoad={() => console.log('Video loaded')}
            onError={(error) => console.error('Video error:', error)}
          />
        ) : exercise.demo_gif_url ? (
          <Image
            source={{ uri: exercise.demo_gif_url }}
            style={styles.video}
            resizeMode="contain"
          />
        ) : null}

        {exercise.video_url && (
          <View style={styles.videoControls}>
            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: colors.primary }]}
              onPress={handleVideoPlayPause}
            >
              {isVideoPlaying ? (
                <Pause size={20} color={colors.overlayText} />
              ) : (
                <Play size={20} color={colors.overlayText} />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: colors.accent }]}
              onPress={handleVideoRestart}
            >
              <RotateCcw size={18} color={colors.text} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: colors.accent }]}
              onPress={handleMuteToggle}
            >
              {isMuted ? (
                <VolumeX size={18} color={colors.text} />
              ) : (
                <Volume2 size={18} color={colors.text} />
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'instructions':
        return (
          <View style={styles.tabContent}>
            <Text style={[styles.contentTitle, { color: colors.text }]}>
              How to Perform
            </Text>
            <Text style={[styles.contentText, { color: colors.text }]}>
              {exercise.instructions || 'No instructions available for this exercise.'}
            </Text>

            {exercise.form_cues && exercise.form_cues.length > 0 && (
              <>
                <Text style={[styles.contentTitle, { color: colors.text }]}>
                  Form Cues
                </Text>
                {exercise.form_cues.map((cue, index) => (
                  <View key={index} style={styles.cueItem}>
                    <View style={[styles.cueNumber, { backgroundColor: colors.primary }]}>
                      <Text style={[styles.cueNumberText, { color: colors.overlayText }]}>
                        {index + 1}
                      </Text>
                    </View>
                    <Text style={[styles.cueText, { color: colors.text }]}>
                      {cue}
                    </Text>
                  </View>
                ))}
              </>
            )}
          </View>
        );

      case 'tips':
        return (
          <View style={styles.tabContent}>
            <View style={styles.tipSection}>
              <View style={styles.tipHeader}>
                <Target size={20} color={colors.success} />
                <Text style={[styles.tipTitle, { color: colors.text }]}>
                  Pro Tips
                </Text>
              </View>
              <Text style={[styles.contentText, { color: colors.text }]}>
                {exercise.tips || 'Focus on proper form over heavy weight. Control the movement throughout the entire range of motion.'}
              </Text>
            </View>

            {exercise.common_mistakes && (
              <View style={styles.tipSection}>
                <View style={styles.tipHeader}>
                  <AlertTriangle size={20} color={colors.warning} />
                  <Text style={[styles.tipTitle, { color: colors.text }]}>
                    Common Mistakes
                  </Text>
                </View>
                <Text style={[styles.contentText, { color: colors.text }]}>
                  {exercise.common_mistakes}
                </Text>
              </View>
            )}

            <View style={styles.tipSection}>
              <View style={styles.tipHeader}>
                <Info size={20} color={colors.primary} />
                <Text style={[styles.tipTitle, { color: colors.text }]}>
                  Equipment
                </Text>
              </View>
              <Text style={[styles.contentText, { color: colors.text }]}>
                {exercise.equipment || 'No specific equipment required'}
              </Text>
            </View>
          </View>
        );

      case 'muscles':
        return (
          <View style={styles.tabContent}>
            <Text style={[styles.contentTitle, { color: colors.text }]}>
              Primary Muscles
            </Text>
            <View style={styles.muscleGroup}>
              {exercise.primary_muscles?.map((muscle, index) => (
                <View key={index} style={[styles.muscleTag, { backgroundColor: colors.primary + '20' }]}>
                  <Text style={[styles.muscleTagText, { color: colors.primary }]}>
                    {muscle}
                  </Text>
                </View>
              )) || (
                <Text style={[styles.contentText, { color: colors.text + '80' }]}>
                  No muscle information available
                </Text>
              )}
            </View>

            {exercise.secondary_muscles && exercise.secondary_muscles.length > 0 && (
              <>
                <Text style={[styles.contentTitle, { color: colors.text }]}>
                  Secondary Muscles
                </Text>
                <View style={styles.muscleGroup}>
                  {exercise.secondary_muscles.map((muscle, index) => (
                    <View key={index} style={[styles.muscleTag, { backgroundColor: colors.accent }]}>
                      <Text style={[styles.muscleTagText, { color: colors.text }]}>
                        {muscle}
                      </Text>
                    </View>
                  ))}
                </View>
              </>
            )}

            <Text style={[styles.contentTitle, { color: colors.text }]}>
              Exercise Category
            </Text>
            <Text style={[styles.contentText, { color: colors.text }]}>
              {exercise.category || 'General Fitness'}
            </Text>

            <Text style={[styles.contentTitle, { color: colors.text }]}>
              Difficulty Level
            </Text>
            <View style={styles.difficultyContainer}>
              <Text style={[styles.contentText, { color: colors.text }]}>
                {exercise.difficulty_level || 'Intermediate'}
              </Text>
              <View style={styles.difficultyDots}>
                {[1, 2, 3, 4, 5].map((level) => (
                  <View
                    key={level}
                    style={[
                      styles.difficultyDot,
                      {
                        backgroundColor: level <= (exercise.difficulty_level === 'Beginner' ? 2 : 
                          exercise.difficulty_level === 'Intermediate' ? 3 : 4) 
                          ? colors.primary 
                          : colors.accent
                      }
                    ]}
                  />
                ))}
              </View>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      flex: 1,
      marginRight: 16,
    },
    closeButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.accent,
      justifyContent: 'center',
      alignItems: 'center',
    },
    videoContainer: {
      position: 'relative',
      height: 250,
      backgroundColor: colors.background,
    },
    video: {
      width: '100%',
      height: '100%',
    },
    videoPlaceholder: {
      height: 250,
      justifyContent: 'center',
      alignItems: 'center',
      gap: 12,
    },
    placeholderText: {
      fontSize: 16,
    },
    videoControls: {
      position: 'absolute',
      bottom: 16,
      right: 16,
      flexDirection: 'row',
      gap: 8,
    },
    controlButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    tabContainer: {
      flexDirection: 'row',
      backgroundColor: colors.accent,
      margin: 20,
      borderRadius: 12,
      padding: 4,
    },
    tab: {
      flex: 1,
      paddingVertical: 12,
      alignItems: 'center',
      borderRadius: 8,
    },
    activeTab: {
      backgroundColor: colors.primary,
    },
    tabText: {
      fontSize: 14,
      fontWeight: '500',
    },
    activeTabText: {
      color: colors.overlayText,
    },
    content: {
      flex: 1,
    },
    tabContent: {
      padding: 20,
    },
    contentTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 12,
      marginTop: 20,
    },
    contentText: {
      fontSize: 16,
      lineHeight: 24,
      marginBottom: 16,
    },
    cueItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 12,
    },
    cueNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
      marginTop: 2,
    },
    cueNumberText: {
      fontSize: 12,
      fontWeight: '600',
    },
    cueText: {
      flex: 1,
      fontSize: 16,
      lineHeight: 24,
    },
    tipSection: {
      marginBottom: 24,
    },
    tipHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
      gap: 8,
    },
    tipTitle: {
      fontSize: 16,
      fontWeight: '600',
    },
    muscleGroup: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    muscleTag: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    muscleTagText: {
      fontSize: 14,
      fontWeight: '500',
    },
    difficultyContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    difficultyDots: {
      flexDirection: 'row',
      gap: 4,
    },
    difficultyDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    footer: {
      padding: 20,
      borderTopWidth: 1,
      borderTopColor: colors.accent,
    },
    startButton: {
      backgroundColor: colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    startButtonText: {
      color: colors.overlayText,
      fontSize: 18,
      fontWeight: '600',
    },
  });

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {exercise.name}
          </Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={20} color={colors.text} />
          </TouchableOpacity>
        </View>

        {renderVideoPlayer()}

        <View style={styles.tabContainer}>
          {(['instructions', 'tips', 'muscles'] as const).map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tab, activeTab === tab && styles.activeTab]}
              onPress={() => setActiveTab(tab)}
            >
              <Text
                style={[
                  styles.tabText,
                  { color: colors.text },
                  activeTab === tab && styles.activeTabText,
                ]}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderTabContent()}
        </ScrollView>

        {onStartExercise && (
          <View style={styles.footer}>
            <TouchableOpacity style={styles.startButton} onPress={onStartExercise}>
              <Text style={styles.startButtonText}>Start Exercise</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};
