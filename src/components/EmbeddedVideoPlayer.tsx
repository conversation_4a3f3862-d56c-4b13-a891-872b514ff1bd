import React, { useState } from 'react';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import { WebView } from 'react-native-webview';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText } from '@/components/ThemedComponents';
import { Play, CircleAlert as AlertCircle } from 'lucide-react-native';

interface EmbeddedVideoPlayerProps {
  videoUrl?: string;
  style?: any;
}

export default function EmbeddedVideoPlayer({ videoUrl, style }: EmbeddedVideoPlayerProps) {
  const { colors } = useThemeStore();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  if (!videoUrl) {
    return (
      <View style={[styles.container, styles.noVideoContainer, { backgroundColor: colors.accent }, style]}>
        <Play size={32} color={colors.text} opacity={0.5} />
        <ThemedText style={styles.noVideoText}>No video available</ThemedText>
      </View>
    );
  }

  // Convert YouTube URLs to embed format with specified parameters
  const getEmbedUrl = (url: string) => {
    const videoParams = 'playsinline=1&autoplay=1&rel=0&modestbranding=1&enablejsapi=1';
    
    if (url.includes('youtube.com/watch?v=')) {
      const videoId = url.split('v=')[1]?.split('&')[0];
      return `https://www.youtube.com/embed/${videoId}?${videoParams}`;
    }
    if (url.includes('youtu.be/')) {
      const videoId = url.split('youtu.be/')[1]?.split('?')[0];
      return `https://www.youtube.com/embed/${videoId}?${videoParams}`;
    }
    if (url.includes('vimeo.com/')) {
      const videoId = url.split('vimeo.com/')[1]?.split('?')[0];
      return `https://player.vimeo.com/video/${videoId}`;
    }
    return url;
  };

  const embedUrl = getEmbedUrl(videoUrl);

  if (hasError) {
    return (
      <View style={[styles.container, styles.errorContainer, { backgroundColor: colors.accent }, style]}>
        <AlertCircle size={32} color={colors.error} />
        <ThemedText style={styles.errorText}>Unable to load video</ThemedText>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {isLoading && (
        <View style={[styles.loadingContainer, { backgroundColor: colors.accent }]}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading video...</ThemedText>
        </View>
      )}
      <WebView
        source={{ uri: embedUrl }}
        style={styles.webview}
        onLoadStart={() => setIsLoading(true)}
        onLoadEnd={() => setIsLoading(false)}
        onError={() => {
          setHasError(true);
          setIsLoading(false);
        }}
        allowsFullscreenVideo
        mediaPlaybackRequiresUserAction={false}
        javaScriptEnabled
        domStorageEnabled
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  webview: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  noVideoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  noVideoText: {
    marginTop: 8,
    fontSize: 14,
    opacity: 0.7,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    marginTop: 8,
    fontSize: 14,
    color: '#DC3545',
  },
});