import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  ActivityIndicator,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import {
  Send,
  Paperclip,
  Camera,
  Image as ImageIcon,
  Smile,
  MoreVertical,
  Check,
  CheckCheck,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  messageType: 'text' | 'image' | 'file' | 'system';
  replyToId?: string;
  isEdited: boolean;
  editedAt?: string;
  isDeleted: boolean;
  deletedAt?: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
  attachments?: MessageAttachment[];
  reactions?: MessageReaction[];
  readStatus?: MessageReadStatus[];
  sender?: {
    id: string;
    name: string;
    avatar?: string;
    role: 'coach' | 'client';
  };
}

interface MessageAttachment {
  id: string;
  messageId: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize?: number;
  mimeType?: string;
  thumbnailUrl?: string;
}

interface MessageReaction {
  id: string;
  messageId: string;
  userId: string;
  emoji: string;
  createdAt: string;
}

interface MessageReadStatus {
  id: string;
  messageId: string;
  userId: string;
  readAt: string;
}

interface Conversation {
  id: string;
  coachId: string;
  clientId: string;
  title?: string;
  lastMessageAt: string;
  lastMessagePreview?: string;
  isArchived: boolean;
  participants: {
    coach: { id: string; name: string; avatar?: string };
    client: { id: string; name: string; avatar?: string };
  };
}

interface MessagingInterfaceProps {
  conversationId: string;
  currentUserId: string;
  userRole: 'coach' | 'client';
  onBack?: () => void;
}

export const MessagingInterface: React.FC<MessagingInterfaceProps> = ({
  conversationId,
  currentUserId,
  userRole,
  onBack,
}) => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messageText, setMessageText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);
  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    loadConversation();
    loadMessages();
    setupRealtimeSubscription();
    markMessagesAsRead();

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [conversationId]);

  const loadConversation = async () => {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          coach:coach_id(id, first_name, last_name, avatar_url),
          client:client_id(id, first_name, last_name, avatar_url)
        `)
        .eq('id', conversationId)
        .single();

      if (error) throw error;

      setConversation({
        id: data.id,
        coachId: data.coach_id,
        clientId: data.client_id,
        title: data.title,
        lastMessageAt: data.last_message_at,
        lastMessagePreview: data.last_message_preview,
        isArchived: data.is_archived,
        participants: {
          coach: {
            id: data.coach.id,
            name: `${data.coach.first_name} ${data.coach.last_name}`,
            avatar: data.coach.avatar_url,
          },
          client: {
            id: data.client.id,
            name: `${data.client.first_name} ${data.client.last_name}`,
            avatar: data.client.avatar_url,
          },
        },
      });
    } catch (error) {
      console.error('Failed to load conversation:', error);
      Alert.alert('Error', 'Failed to load conversation');
    }
  };

  const loadMessages = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          message_attachments(*),
          message_reactions(*),
          message_read_status(*),
          sender:sender_id(id, first_name, last_name, avatar_url, role)
        `)
        .eq('conversation_id', conversationId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const formattedMessages: Message[] = data?.map(msg => ({
        id: msg.id,
        conversationId: msg.conversation_id,
        senderId: msg.sender_id,
        content: msg.content,
        messageType: msg.message_type,
        replyToId: msg.reply_to_id,
        isEdited: msg.is_edited,
        editedAt: msg.edited_at,
        isDeleted: msg.is_deleted,
        deletedAt: msg.deleted_at,
        metadata: msg.metadata,
        createdAt: msg.created_at,
        updatedAt: msg.updated_at,
        attachments: msg.message_attachments,
        reactions: msg.message_reactions,
        readStatus: msg.message_read_status,
        sender: {
          id: msg.sender.id,
          name: `${msg.sender.first_name} ${msg.sender.last_name}`,
          avatar: msg.sender.avatar_url,
          role: msg.sender.role,
        },
      })) || [];

      setMessages(formattedMessages);
    } catch (error) {
      console.error('Failed to load messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  };

  const setupRealtimeSubscription = () => {
    const subscription = supabase
      .channel(`conversation:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          const newMessage = payload.new as any;
          // Add the new message to the list
          loadMessages(); // Reload to get full message data with relations
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          // Handle message updates (edits, reactions, etc.)
          loadMessages();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  };

  const markMessagesAsRead = async () => {
    try {
      const unreadMessages = messages.filter(
        msg => 
          msg.senderId !== currentUserId && 
          !msg.readStatus?.some(status => status.userId === currentUserId)
      );

      if (unreadMessages.length === 0) return;

      const readStatusInserts = unreadMessages.map(msg => ({
        message_id: msg.id,
        user_id: currentUserId,
        read_at: new Date().toISOString(),
      }));

      const { error } = await supabase
        .from('message_read_status')
        .upsert(readStatusInserts);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
    }
  };

  const sendMessage = async () => {
    if (!messageText.trim() && !replyToMessage) return;

    try {
      setIsSending(true);

      await executeOperation(
        async () => {
          const messageData = {
            conversation_id: conversationId,
            sender_id: currentUserId,
            content: messageText.trim(),
            message_type: 'text',
            reply_to_id: replyToMessage?.id || null,
          };

          const { data, error } = await supabase
            .from('messages')
            .insert(messageData)
            .select()
            .single();

          if (error) throw error;

          // Update conversation last message
          await supabase
            .from('conversations')
            .update({
              last_message_at: new Date().toISOString(),
              last_message_preview: messageText.trim().substring(0, 100),
            })
            .eq('id', conversationId);

          setMessageText('');
          setReplyToMessage(null);
          
          // Scroll to bottom
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: true });
          }, 100);
        },
        {
          type: 'MESSAGE_SEND',
          payload: {
            conversationId,
            content: messageText.trim(),
            replyToId: replyToMessage?.id,
          },
        }
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setIsSending(false);
    }
  };

  const handleImagePicker = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await sendImageMessage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Failed to pick image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const handleCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await sendImageMessage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Failed to take photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const sendImageMessage = async (imageUri: string) => {
    try {
      setIsSending(true);

      await executeOperation(
        async () => {
          // Upload image to Supabase Storage
          const fileName = `messages/${conversationId}/${Date.now()}_image.jpg`;
          const response = await fetch(imageUri);
          const blob = await response.blob();

          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('message-attachments')
            .upload(fileName, blob, {
              contentType: 'image/jpeg',
              upsert: false,
            });

          if (uploadError) throw uploadError;

          // Get public URL
          const { data: { publicUrl } } = supabase.storage
            .from('message-attachments')
            .getPublicUrl(fileName);

          // Create message
          const messageData = {
            conversation_id: conversationId,
            sender_id: currentUserId,
            content: 'Image',
            message_type: 'image',
          };

          const { data: messageResult, error: messageError } = await supabase
            .from('messages')
            .insert(messageData)
            .select()
            .single();

          if (messageError) throw messageError;

          // Create attachment
          const attachmentData = {
            message_id: messageResult.id,
            file_name: 'image.jpg',
            file_url: publicUrl,
            file_type: 'image',
            mime_type: 'image/jpeg',
          };

          const { error: attachmentError } = await supabase
            .from('message_attachments')
            .insert(attachmentData);

          if (attachmentError) throw attachmentError;

          // Update conversation
          await supabase
            .from('conversations')
            .update({
              last_message_at: new Date().toISOString(),
              last_message_preview: '📷 Image',
            })
            .eq('id', conversationId);
        },
        {
          type: 'MESSAGE_SEND_IMAGE',
          payload: {
            conversationId,
            imageUri,
          },
        }
      );
    } catch (error) {
      console.error('Failed to send image:', error);
      Alert.alert('Error', 'Failed to send image');
    } finally {
      setIsSending(false);
    }
  };

  const renderMessage = ({ item: message }: { item: Message }) => {
    const isOwnMessage = message.senderId === currentUserId;
    const isRead = message.readStatus?.some(status => status.userId !== message.senderId);

    return (
      <View
        style={[
          styles.messageContainer,
          isOwnMessage ? styles.ownMessage : styles.otherMessage,
        ]}
      >
        {!isOwnMessage && (
          <View style={styles.senderInfo}>
            <Text style={[styles.senderName, { color: colors.text + '80' }]}>
              {message.sender?.name}
            </Text>
          </View>
        )}

        {message.replyToId && (
          <View style={[styles.replyContainer, { backgroundColor: colors.accent }]}>
            <Text style={[styles.replyText, { color: colors.text + '80' }]}>
              Replying to previous message
            </Text>
          </View>
        )}

        <View
          style={[
            styles.messageBubble,
            {
              backgroundColor: isOwnMessage ? colors.primary : colors.accent,
            },
          ]}
        >
          {message.messageType === 'image' && message.attachments?.[0] && (
            <Image
              source={{ uri: message.attachments[0].fileUrl }}
              style={styles.messageImage}
              resizeMode="cover"
            />
          )}

          <Text
            style={[
              styles.messageText,
              {
                color: isOwnMessage ? colors.overlayText : colors.text,
              },
            ]}
          >
            {message.content}
          </Text>

          {message.isEdited && (
            <Text
              style={[
                styles.editedText,
                {
                  color: isOwnMessage ? colors.overlayText + '80' : colors.text + '80',
                },
              ]}
            >
              (edited)
            </Text>
          )}
        </View>

        <View style={styles.messageFooter}>
          <Text
            style={[
              styles.messageTime,
              {
                color: colors.text + '60',
                textAlign: isOwnMessage ? 'right' : 'left',
              },
            ]}
          >
            {new Date(message.createdAt).toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>

          {isOwnMessage && (
            <View style={styles.readStatus}>
              {isRead ? (
                <CheckCheck size={14} color={colors.success} />
              ) : (
                <Check size={14} color={colors.text + '60'} />
              )}
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderReplyPreview = () => {
    if (!replyToMessage) return null;

    return (
      <View style={[styles.replyPreview, { backgroundColor: colors.accent }]}>
        <View style={styles.replyPreviewContent}>
          <Text style={[styles.replyPreviewTitle, { color: colors.text }]}>
            Replying to {replyToMessage.sender?.name}
          </Text>
          <Text
            style={[styles.replyPreviewText, { color: colors.text + '80' }]}
            numberOfLines={1}
          >
            {replyToMessage.content}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.replyPreviewClose}
          onPress={() => setReplyToMessage(null)}
        >
          <Text style={[styles.replyPreviewCloseText, { color: colors.error }]}>
            ✕
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
      backgroundColor: colors.background,
    },
    backButton: {
      marginRight: 16,
    },
    headerContent: {
      flex: 1,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    headerSubtitle: {
      fontSize: 14,
      color: colors.text + '80',
      marginTop: 2,
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
    },
    headerButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.accent,
      justifyContent: 'center',
      alignItems: 'center',
    },
    messagesList: {
      flex: 1,
      paddingHorizontal: 16,
    },
    messageContainer: {
      marginVertical: 4,
      maxWidth: '80%',
    },
    ownMessage: {
      alignSelf: 'flex-end',
    },
    otherMessage: {
      alignSelf: 'flex-start',
    },
    senderInfo: {
      marginBottom: 4,
    },
    senderName: {
      fontSize: 12,
      fontWeight: '500',
    },
    replyContainer: {
      padding: 8,
      borderRadius: 8,
      marginBottom: 4,
      borderLeftWidth: 3,
      borderLeftColor: colors.primary,
    },
    replyText: {
      fontSize: 12,
      fontStyle: 'italic',
    },
    messageBubble: {
      padding: 12,
      borderRadius: 16,
      minWidth: 60,
    },
    messageImage: {
      width: 200,
      height: 150,
      borderRadius: 8,
      marginBottom: 8,
    },
    messageText: {
      fontSize: 16,
      lineHeight: 20,
    },
    editedText: {
      fontSize: 12,
      fontStyle: 'italic',
      marginTop: 4,
    },
    messageFooter: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginTop: 4,
    },
    messageTime: {
      fontSize: 12,
    },
    readStatus: {
      marginLeft: 4,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'flex-end',
      padding: 16,
      paddingBottom: Platform.OS === 'ios' ? 34 : 16,
      borderTopWidth: 1,
      borderTopColor: colors.accent,
      backgroundColor: colors.background,
    },
    attachmentButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.accent,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 8,
    },
    textInputContainer: {
      flex: 1,
      maxHeight: 100,
      borderRadius: 20,
      backgroundColor: colors.accent,
      paddingHorizontal: 16,
      paddingVertical: 8,
      marginRight: 8,
    },
    textInput: {
      fontSize: 16,
      color: colors.text,
      maxHeight: 80,
    },
    sendButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    sendButtonDisabled: {
      backgroundColor: colors.accent,
    },
    replyPreview: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      marginHorizontal: 16,
      marginBottom: 8,
      borderRadius: 8,
      borderLeftWidth: 3,
      borderLeftColor: colors.primary,
    },
    replyPreviewContent: {
      flex: 1,
    },
    replyPreviewTitle: {
      fontSize: 12,
      fontWeight: '600',
      marginBottom: 2,
    },
    replyPreviewText: {
      fontSize: 14,
    },
    replyPreviewClose: {
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    replyPreviewCloseText: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyText: {
      fontSize: 16,
      color: colors.text + '80',
      textAlign: 'center',
      marginTop: 16,
    },
    typingIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    typingText: {
      fontSize: 14,
      color: colors.text + '80',
      fontStyle: 'italic',
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.emptyText, { marginTop: 16 }]}>
          Loading messages...
        </Text>
      </View>
    );
  }

  const otherParticipant = conversation?.participants[userRole === 'coach' ? 'client' : 'coach'];

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <View style={styles.header}>
        {onBack && (
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Text style={[styles.headerTitle, { color: colors.primary }]}>
              ← Back
            </Text>
          </TouchableOpacity>
        )}

        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>
            {otherParticipant?.name || 'Chat'}
          </Text>
          <Text style={styles.headerSubtitle}>
            {userRole === 'coach' ? 'Client' : 'Coach'}
          </Text>
        </View>

        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton}>
            <MoreVertical size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {messages.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            No messages yet. Start the conversation!
          </Text>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          style={styles.messagesList}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: false })}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: false })}
        />
      )}

      {isTyping && (
        <View style={styles.typingIndicator}>
          <Text style={styles.typingText}>
            {otherParticipant?.name} is typing...
          </Text>
        </View>
      )}

      {renderReplyPreview()}

      <View style={styles.inputContainer}>
        <TouchableOpacity
          style={styles.attachmentButton}
          onPress={() => {
            Alert.alert(
              'Add Attachment',
              'Choose an option',
              [
                { text: 'Camera', onPress: handleCamera },
                { text: 'Gallery', onPress: handleImagePicker },
                { text: 'Cancel', style: 'cancel' },
              ]
            );
          }}
        >
          <Paperclip size={20} color={colors.text} />
        </TouchableOpacity>

        <View style={styles.textInputContainer}>
          <TextInput
            style={styles.textInput}
            value={messageText}
            onChangeText={setMessageText}
            placeholder="Type a message..."
            placeholderTextColor={colors.text + '60'}
            multiline
            maxLength={1000}
          />
        </View>

        <TouchableOpacity
          style={[
            styles.sendButton,
            (!messageText.trim() || isSending) && styles.sendButtonDisabled,
          ]}
          onPress={sendMessage}
          disabled={!messageText.trim() || isSending}
        >
          {isSending ? (
            <ActivityIndicator size="small" color={colors.overlayText} />
          ) : (
            <Send size={20} color={messageText.trim() ? colors.overlayText : colors.text + '60'} />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};
