import React, { useState } from 'react';
import { View, StyleSheet, Modal, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { feedbackService, CoachFeedback } from '@/services/feedbackService';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedOverlay } from '@/components/ThemedComponents';
import { MessageSquare, X, User, Calendar, Send, LocationEdit as Edit3 } from 'lucide-react-native';

interface CoachFeedbackEditModalProps {
  isVisible: boolean;
  onClose: () => void;
  feedback: CoachFeedback;
  onSave: () => void;
}

export default function CoachFeedbackEditModal({
  isVisible,
  onClose,
  feedback,
  onSave,
}: CoachFeedbackEditModalProps) {
  const { colors } = useThemeStore();
  const [feedbackContent, setFeedbackContent] = useState(feedback.feedback_content);
  // Always start in review mode, regardless of feedback status
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSaveDraft = async () => {
    if (!feedbackContent.trim()) {
      setError('Feedback content cannot be empty');
      return;
    }

    setError(null);
    setIsSaving(true);
    try {
      await feedbackService.updateFeedbackDraft(feedback.id, feedbackContent);
      // After saving, return to review mode but don't close the modal
      setIsEditing(false);
      // Don't call onSave() here to keep the modal open
    } catch (error) {
      console.error('Error saving feedback draft:', error);
      setError('Failed to save feedback. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublishFeedback = async () => {
    if (!feedbackContent.trim()) {
      setError('Feedback content cannot be empty');
      return;
    }

    setError(null);
    setIsPublishing(true);
    try {
      await feedbackService.publishFeedback(feedback.id, feedbackContent);
      // Only call onSave when publishing to close the modal
      onSave();
    } catch (error) {
      console.error('Error publishing feedback:', error);
      setError('Failed to publish feedback. Please try again.');
    } finally {
      setIsPublishing(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <ThemedOverlay style={styles.overlay}>
        <ThemedView style={styles.modalContainer} variant="card">
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <MessageSquare size={24} color={colors.primary} />
              <ThemedText style={styles.title}>
                {isEditing ? 'Edit Feedback' : 'Review Feedback'}
              </ThemedText>
            </View>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              disabled={isSaving || isPublishing}
            >
              <X size={20} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            <View style={styles.metaInfo}>
              <View style={styles.metaItem}>
                <User size={16} color={colors.primary} />
                <ThemedText style={styles.metaText}>
                  For: {feedback.user_id.substring(0, 8)}...
                </ThemedText>
              </View>
              <View style={styles.metaItem}>
                <Calendar size={16} color={colors.primary} />
                <ThemedText style={styles.metaText}>
                  Created: {formatDate(feedback.created_at)}
                </ThemedText>
              </View>
            </View>

            {error && (
              <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
                <ThemedText style={[styles.errorText, { color: colors.error }]}>
                  {error}
                </ThemedText>
              </View>
            )}

            {isEditing ? (
              <ThemedInput
                value={feedbackContent}
                onChangeText={setFeedbackContent}
                placeholder="Enter feedback for the client..."
                multiline
                numberOfLines={10}
                textAlignVertical="top"
                style={styles.feedbackInput}
              />
            ) : (
              <View style={[styles.feedbackContent, { backgroundColor: colors.accent }]}>
                <ThemedText style={styles.feedbackText}>
                  {feedbackContent}
                </ThemedText>
              </View>
            )}
          </ScrollView>

          <View style={styles.footer}>
            {isEditing ? (
              <View style={styles.editActions}>
                <ThemedButton
                  title="Cancel"
                  onPress={() => {
                    setFeedbackContent(feedback.feedback_content);
                    setIsEditing(false);
                  }}
                  variant="outline"
                  style={styles.footerButton}
                  disabled={isSaving || isPublishing}
                />
                <ThemedButton
                  title={isSaving ? "Saving..." : "Save Draft"}
                  onPress={handleSaveDraft}
                  style={styles.footerButton}
                  disabled={isSaving || isPublishing}
                />
                <ThemedButton
                  title={isPublishing ? "Publishing..." : "Publish"}
                  onPress={handlePublishFeedback}
                  style={[styles.footerButton, { backgroundColor: colors.success }]}
                  disabled={isSaving || isPublishing}
                />
              </View>
            ) : (
              <View style={styles.viewActions}>
                <ThemedButton
                  title="Edit"
                  onPress={() => setIsEditing(true)}
                  variant="outline"
                  style={styles.footerButton}
                  disabled={isSaving || isPublishing}
                >
                  <View style={styles.buttonContent}>
                    <Edit3 size={16} color={colors.primary} />
                    <ThemedText style={[styles.buttonText, { color: colors.primary }]}>
                      Edit
                    </ThemedText>
                  </View>
                </ThemedButton>
                <ThemedButton
                  title={isPublishing ? "Publishing..." : "Publish Feedback"}
                  onPress={handlePublishFeedback}
                  style={[styles.footerButton, { backgroundColor: colors.success }]}
                  disabled={isSaving || isPublishing}
                >
                  <View style={styles.buttonContent}>
                    <Send size={16} color={colors.contrastText} />
                    <ThemedText style={[styles.buttonText, { color: colors.contrastText }]}>
                      {isPublishing ? "Publishing..." : "Publish Feedback"}
                    </ThemedText>
                  </View>
                </ThemedButton>
              </View>
            )}
          </View>

          {(isSaving || isPublishing) && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          )}
        </ThemedView>
      </ThemedOverlay>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    width: '100%',
    maxWidth: 600,
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 20,
    maxHeight: 500,
  },
  metaInfo: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metaText: {
    fontSize: 14,
    opacity: 0.8,
  },
  errorContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  feedbackInput: {
    minHeight: 200,
    textAlignVertical: 'top',
  },
  feedbackContent: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feedbackText: {
    fontSize: 16,
    lineHeight: 24,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
  },
  viewActions: {
    flexDirection: 'row',
    gap: 12,
  },
  footerButton: {
    flex: 1,
    paddingVertical: 14,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
  },
});