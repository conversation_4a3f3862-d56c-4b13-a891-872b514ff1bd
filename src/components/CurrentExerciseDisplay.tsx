import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedCard } from '@/components/ThemedComponents';
import EmbeddedVideoPlayer from '@/components/EmbeddedVideoPlayer';
import { WorkoutExercise } from '@/services/workoutService';
import { Play, Target, Dumbbell, Clock, Timer, RotateCcw } from 'lucide-react-native';

interface CurrentExerciseDisplayProps {
  exercise: WorkoutExercise;
  exerciseNumber: number;
  totalExercises: number;
  onShowVideo?: () => void;
}

export default function CurrentExerciseDisplay({
  exercise,
  exerciseNumber,
  totalExercises,
  onShowVideo,
}: CurrentExerciseDisplayProps) {
  const { colors } = useThemeStore();

  const formatReps = () => {
    if (exercise.prescribed_reps_min && exercise.prescribed_reps_max) {
      if (exercise.prescribed_reps_min === exercise.prescribed_reps_max) {
        return `${exercise.prescribed_reps_min} reps`;
      }
      return `${exercise.prescribed_reps_min}-${exercise.prescribed_reps_max} reps`;
    }
    if (exercise.prescribed_reps_min) {
      return `${exercise.prescribed_reps_min}+ reps`;
    }
    if (exercise.prescribed_duration_seconds) {
      const minutes = Math.floor(exercise.prescribed_duration_seconds / 60);
      const seconds = exercise.prescribed_duration_seconds % 60;
      if (minutes > 0) {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
      }
      return `${exercise.prescribed_duration_seconds}s`;
    }
    return 'As prescribed';
  };

  const formatRestTime = (seconds: number) => {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      if (remainingSeconds === 0) {
        return `${minutes}min`;
      }
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${seconds}s`;
  };

  return (
    <ThemedCard style={styles.container}>
      {/* Exercise Header */}
      <View style={styles.header}>
        <View style={styles.exerciseInfo}>
          <ThemedText style={styles.exerciseNumber}>
            Exercise {exerciseNumber} of {totalExercises}
          </ThemedText>
          <ThemedText style={styles.exerciseName}>
            {exercise.exercise?.name || 'Unknown Exercise'}
          </ThemedText>
        </View>
        
        {exercise.exercise?.video_url && onShowVideo && (
          <TouchableOpacity
            style={[styles.videoButton, { backgroundColor: colors.primary }]}
            onPress={onShowVideo}
          >
            <Play size={16} color={colors.contrastText} />
            <ThemedText style={[styles.videoButtonText, { color: colors.contrastText }]}>
              Video
            </ThemedText>
          </TouchableOpacity>
        )}
      </View>

      {/* Target Muscles */}
      {exercise.exercise?.target_muscles_primary && exercise.exercise.target_muscles_primary.length > 0 && (
        <View style={styles.musclesSection}>
          <View style={styles.musclesHeader}>
            <Target size={16} color={colors.primary} />
            <ThemedText style={styles.musclesLabel}>Target Muscles:</ThemedText>
          </View>
          <ThemedText style={styles.musclesText}>
            {exercise.exercise.target_muscles_primary.join(', ')}
          </ThemedText>
        </View>
      )}

      {/* Prescription Details */}
      <View style={styles.prescriptionSection}>
        <ThemedText style={styles.prescriptionTitle}>Prescription</ThemedText>
        
        <View style={styles.prescriptionGrid}>
          <View style={styles.prescriptionItem}>
            <Dumbbell size={16} color={colors.primary} />
            <ThemedText style={styles.prescriptionLabel}>Sets:</ThemedText>
            <ThemedText style={styles.prescriptionValue}>
              {exercise.prescribed_sets}
            </ThemedText>
          </View>

          <View style={styles.prescriptionItem}>
            <Target size={16} color={colors.primary} />
            <ThemedText style={styles.prescriptionLabel}>Reps:</ThemedText>
            <ThemedText style={styles.prescriptionValue}>
              {formatReps()}
            </ThemedText>
          </View>

          <View style={styles.prescriptionItem}>
            <Timer size={16} color={colors.primary} />
            <ThemedText style={styles.prescriptionLabel}>Rest:</ThemedText>
            <ThemedText style={styles.prescriptionValue}>
              {formatRestTime(exercise.rest_period_seconds_after_set)}
            </ThemedText>
          </View>

          {exercise.prescribed_rir && (
            <View style={styles.prescriptionItem}>
              <Clock size={16} color={colors.primary} />
              <ThemedText style={styles.prescriptionLabel}>RIR:</ThemedText>
              <ThemedText style={styles.prescriptionValue}>
                {exercise.prescribed_rir}
              </ThemedText>
            </View>
          )}

          {exercise.prescribed_rpe && (
            <View style={styles.prescriptionItem}>
              <Target size={16} color={colors.primary} />
              <ThemedText style={styles.prescriptionLabel}>RPE:</ThemedText>
              <ThemedText style={styles.prescriptionValue}>
                {exercise.prescribed_rpe}
              </ThemedText>
            </View>
          )}

          {exercise.prescribed_tempo && (
            <View style={styles.prescriptionItem}>
              <RotateCcw size={16} color={colors.primary} />
              <ThemedText style={styles.prescriptionLabel}>Tempo:</ThemedText>
              <ThemedText style={styles.prescriptionValue}>
                {exercise.prescribed_tempo}
              </ThemedText>
            </View>
          )}
        </View>
      </View>

      {/* Exercise Notes */}
      {exercise.notes && (
        <View style={[styles.notesSection, { backgroundColor: colors.accent }]}>
          <ThemedText style={styles.notesTitle}>Notes:</ThemedText>
          <ThemedText style={styles.notesText}>{exercise.notes}</ThemedText>
        </View>
      )}
    </ThemedCard>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  exerciseInfo: {
    flex: 1,
    marginRight: 12,
  },
  exerciseNumber: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  exerciseName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  videoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  videoButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  musclesSection: {
    marginBottom: 16,
  },
  musclesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 4,
  },
  musclesLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  musclesText: {
    fontSize: 14,
    opacity: 0.8,
  },
  prescriptionSection: {
    marginBottom: 16,
  },
  prescriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  prescriptionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  prescriptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    minWidth: '45%',
  },
  prescriptionLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  prescriptionValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  notesSection: {
    padding: 12,
    borderRadius: 8,
  },
  notesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 18,
  },
});