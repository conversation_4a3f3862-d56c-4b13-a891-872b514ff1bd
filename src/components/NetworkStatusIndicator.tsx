import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Wifi, WifiOff, Smartphone, AlertTriangle } from 'lucide-react-native';
import { useNetworkStatus, useConnectionQuality } from '../hooks/useNetworkStatus';
import { useThemeStore } from '../store/themeStore';

interface NetworkStatusIndicatorProps {
  showDetails?: boolean;
  onPress?: () => void;
  style?: any;
}

export const NetworkStatusIndicator: React.FC<NetworkStatusIndicatorProps> = ({
  showDetails = false,
  onPress,
  style,
}) => {
  const { colors } = useThemeStore();
  const { isOnline, type, isInitialized } = useNetworkStatus();
  const connectionQuality = useConnectionQuality();

  if (!isInitialized) {
    return null;
  }

  const getStatusColor = () => {
    if (!isOnline) return colors.error;
    
    switch (connectionQuality.quality) {
      case 'excellent':
        return colors.success;
      case 'good':
        return colors.success;
      case 'fair':
        return colors.warning;
      case 'poor':
        return colors.error;
      default:
        return colors.text;
    }
  };

  const getStatusIcon = () => {
    const iconColor = getStatusColor();
    const iconSize = 16;

    if (!isOnline) {
      return <WifiOff size={iconSize} color={iconColor} />;
    }

    switch (type) {
      case 'wifi':
        return <Wifi size={iconSize} color={iconColor} />;
      case 'cellular':
        return <Smartphone size={iconSize} color={iconColor} />;
      default:
        return <Wifi size={iconSize} color={iconColor} />;
    }
  };

  const getStatusText = () => {
    if (!isOnline) return 'Offline';
    
    if (showDetails) {
      const typeText = type === 'wifi' ? 'WiFi' : type === 'cellular' ? 'Mobile' : 'Connected';
      const qualityText = connectionQuality.quality.charAt(0).toUpperCase() + connectionQuality.quality.slice(1);
      return `${typeText} • ${qualityText}`;
    }
    
    return isOnline ? 'Online' : 'Offline';
  };

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      backgroundColor: isOnline ? colors.success + '20' : colors.error + '20',
    },
    icon: {
      marginRight: 4,
    },
    text: {
      fontSize: 12,
      fontWeight: '500',
      color: getStatusColor(),
    },
    touchable: {
      borderRadius: 12,
    },
  });

  const content = (
    <View style={[styles.container, style]}>
      <View style={styles.icon}>
        {getStatusIcon()}
      </View>
      <Text style={styles.text}>
        {getStatusText()}
      </Text>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity style={styles.touchable} onPress={onPress}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

/**
 * Compact network status indicator for headers/toolbars
 */
export const CompactNetworkIndicator: React.FC<{ onPress?: () => void }> = ({ onPress }) => {
  const { colors } = useThemeStore();
  const { isOnline, isInitialized } = useNetworkStatus();

  if (!isInitialized) {
    return null;
  }

  const styles = StyleSheet.create({
    container: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: isOnline ? colors.success : colors.error,
    },
    touchable: {
      padding: 8,
      borderRadius: 16,
    },
  });

  const indicator = <View style={styles.container} />;

  if (onPress) {
    return (
      <TouchableOpacity style={styles.touchable} onPress={onPress}>
        {indicator}
      </TouchableOpacity>
    );
  }

  return indicator;
};

/**
 * Offline banner that appears when connection is lost
 */
export const OfflineBanner: React.FC = () => {
  const { colors } = useThemeStore();
  const { isOnline, isInitialized } = useNetworkStatus();

  if (!isInitialized || isOnline) {
    return null;
  }

  const styles = StyleSheet.create({
    banner: {
      backgroundColor: colors.error,
      paddingVertical: 8,
      paddingHorizontal: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    icon: {
      marginRight: 8,
    },
    text: {
      color: colors.overlayText,
      fontSize: 14,
      fontWeight: '500',
    },
  });

  return (
    <View style={styles.banner}>
      <View style={styles.icon}>
        <WifiOff size={16} color={colors.overlayText} />
      </View>
      <Text style={styles.text}>
        No internet connection
      </Text>
    </View>
  );
};

/**
 * Network status modal/sheet content
 */
export const NetworkStatusDetails: React.FC = () => {
  const { colors } = useThemeStore();
  const { 
    isOnline, 
    type, 
    isExpensive, 
    connectionStats,
    refreshNetworkStatus 
  } = useNetworkStatus();
  const connectionQuality = useConnectionQuality();

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    return `${Math.round(ms / 60000)}m`;
  };

  const styles = StyleSheet.create({
    container: {
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
    },
    headerIcon: {
      marginRight: 12,
    },
    headerText: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    section: {
      marginBottom: 16,
    },
    sectionTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 4,
    },
    label: {
      fontSize: 14,
      color: colors.text,
    },
    value: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    refreshButton: {
      backgroundColor: colors.primary,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      alignItems: 'center',
      marginTop: 16,
    },
    refreshButtonText: {
      color: colors.overlayText,
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerIcon}>
          {isOnline ? (
            <Wifi size={24} color={colors.success} />
          ) : (
            <WifiOff size={24} color={colors.error} />
          )}
        </View>
        <Text style={styles.headerText}>
          Network Status
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Connection</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Status</Text>
          <Text style={styles.value}>{isOnline ? 'Online' : 'Offline'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Type</Text>
          <Text style={styles.value}>{type || 'Unknown'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Quality</Text>
          <Text style={styles.value}>{connectionQuality.quality}</Text>
        </View>
        {isExpensive && (
          <View style={styles.row}>
            <Text style={styles.label}>Data Usage</Text>
            <Text style={[styles.value, { color: colors.warning }]}>Expensive</Text>
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Statistics</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Connections</Text>
          <Text style={styles.value}>{connectionStats.totalConnections}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Disconnections</Text>
          <Text style={styles.value}>{connectionStats.totalDisconnections}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Avg Duration</Text>
          <Text style={styles.value}>
            {formatDuration(connectionStats.averageConnectionDuration)}
          </Text>
        </View>
        {isOnline && (
          <View style={styles.row}>
            <Text style={styles.label}>Current Session</Text>
            <Text style={styles.value}>
              {formatDuration(connectionStats.currentConnectionDuration)}
            </Text>
          </View>
        )}
      </View>

      <TouchableOpacity style={styles.refreshButton} onPress={refreshNetworkStatus}>
        <Text style={styles.refreshButtonText}>Refresh Status</Text>
      </TouchableOpacity>
    </View>
  );
};
