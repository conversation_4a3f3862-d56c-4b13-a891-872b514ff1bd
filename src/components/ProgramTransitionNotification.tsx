import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Calendar,
  TrendingUp,
  Target,
  Award,
  ArrowRight,
  Clock,
  CheckCircle,
  Star,
  Zap,
  X,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useWorkoutStore } from '../store/workoutStore';
import { notificationService } from '../services/notificationService';

const { width } = Dimensions.get('window');

interface ProgramTransitionData {
  currentProgram: {
    id: string;
    name: string;
    cycle: number;
    completionRate: number;
    startDate: string;
    endDate: string;
  };
  nextProgram?: {
    id: string;
    name: string;
    cycle: number;
    estimatedStartDate: string;
    changes: string[];
  };
  stats: {
    workoutsCompleted: number;
    totalWorkouts: number;
    averageRPE: number;
    strengthGains: number;
    consistencyScore: number;
  };
  achievements: string[];
}

interface ProgramTransitionNotificationProps {
  visible: boolean;
  transitionData: ProgramTransitionData | null;
  onContinue: () => void;
  onRequestNewProgram: () => void;
  onClose: () => void;
}

export const ProgramTransitionNotification: React.FC<ProgramTransitionNotificationProps> = ({
  visible,
  transitionData,
  onContinue,
  onRequestNewProgram,
  onClose,
}) => {
  const { colors } = useThemeStore();
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    if (visible && transitionData) {
      setCurrentStep(0);
      // Schedule local notification for program transition
      scheduleTransitionReminder();
    }
  }, [visible, transitionData]);

  const scheduleTransitionReminder = async () => {
    if (!transitionData?.nextProgram) return;

    try {
      const transitionDate = new Date(transitionData.nextProgram.estimatedStartDate);
      const reminderDate = new Date(transitionDate.getTime() - 24 * 60 * 60 * 1000); // 1 day before

      await notificationService.scheduleLocalNotification(
        'New Program Starting Tomorrow!',
        `Your new ${transitionData.nextProgram.name} program starts tomorrow. Get ready to level up!`,
        { date: reminderDate },
        {
          type: 'program_transition',
          programId: transitionData.nextProgram.id,
          cycle: transitionData.nextProgram.cycle,
        }
      );
    } catch (error) {
      console.warn('Failed to schedule transition reminder:', error);
    }
  };

  if (!transitionData) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getCompletionColor = (rate: number) => {
    if (rate >= 90) return colors.success;
    if (rate >= 75) return colors.warning;
    return colors.error;
  };

  const renderProgressSummary = () => (
    <View style={styles.stepContent}>
      <View style={styles.header}>
        <Award size={48} color={colors.warning} />
        <Text style={[styles.title, { color: colors.text }]}>
          Cycle {transitionData.currentProgram.cycle} Complete!
        </Text>
        <Text style={[styles.subtitle, { color: colors.text + '80' }]}>
          {transitionData.currentProgram.name}
        </Text>
      </View>

      <View style={styles.completionCard}>
        <View style={styles.completionHeader}>
          <Text style={[styles.completionRate, { color: getCompletionColor(transitionData.currentProgram.completionRate) }]}>
            {transitionData.currentProgram.completionRate}%
          </Text>
          <Text style={[styles.completionLabel, { color: colors.text }]}>
            Completion Rate
          </Text>
        </View>
        
        <View style={[styles.progressBar, { backgroundColor: colors.accent }]}>
          <LinearGradient
            colors={[getCompletionColor(transitionData.currentProgram.completionRate), getCompletionColor(transitionData.currentProgram.completionRate) + '80']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[
              styles.progressFill,
              { width: `${transitionData.currentProgram.completionRate}%` }
            ]}
          />
        </View>
      </View>

      <View style={styles.dateRange}>
        <View style={styles.dateItem}>
          <Calendar size={16} color={colors.primary} />
          <Text style={[styles.dateText, { color: colors.text }]}>
            Started: {formatDate(transitionData.currentProgram.startDate)}
          </Text>
        </View>
        <View style={styles.dateItem}>
          <CheckCircle size={16} color={colors.success} />
          <Text style={[styles.dateText, { color: colors.text }]}>
            Completed: {formatDate(transitionData.currentProgram.endDate)}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderStats = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Your Progress Stats
      </Text>
      <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
        Here's how you performed over the past 4 weeks
      </Text>

      <View style={styles.statsGrid}>
        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <Target size={24} color={colors.primary} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {transitionData.stats.workoutsCompleted}/{transitionData.stats.totalWorkouts}
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
            Workouts Completed
          </Text>
        </View>

        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <TrendingUp size={24} color={colors.success} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {transitionData.stats.averageRPE.toFixed(1)}
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
            Average RPE
          </Text>
        </View>

        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <Zap size={24} color={colors.warning} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            +{transitionData.stats.strengthGains}%
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
            Strength Gains
          </Text>
        </View>

        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <Star size={24} color={colors.primary} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {transitionData.stats.consistencyScore}%
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
            Consistency
          </Text>
        </View>
      </View>
    </View>
  );

  const renderAchievements = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Achievements Unlocked!
      </Text>
      <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
        Celebrate what you accomplished this cycle
      </Text>

      <View style={styles.achievementsList}>
        {transitionData.achievements.map((achievement, index) => (
          <View key={index} style={[styles.achievementItem, { backgroundColor: colors.success + '10' }]}>
            <Award size={20} color={colors.success} />
            <Text style={[styles.achievementText, { color: colors.text }]}>
              {achievement}
            </Text>
          </View>
        ))}
      </View>

      {transitionData.achievements.length === 0 && (
        <View style={styles.noAchievements}>
          <Text style={[styles.noAchievementsText, { color: colors.text + '60' }]}>
            Keep pushing to unlock achievements in your next cycle!
          </Text>
        </View>
      )}
    </View>
  );

  const renderNextProgram = () => {
    if (!transitionData.nextProgram) {
      return (
        <View style={styles.stepContent}>
          <Text style={[styles.stepTitle, { color: colors.text }]}>
            Ready for Your Next Challenge?
          </Text>
          <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
            Your coach is preparing your next program based on your progress
          </Text>

          <View style={styles.waitingCard}>
            <Clock size={48} color={colors.primary} />
            <Text style={[styles.waitingTitle, { color: colors.text }]}>
              New Program Coming Soon
            </Text>
            <Text style={[styles.waitingDescription, { color: colors.text + '80' }]}>
              Your coach will review your progress and create a new program tailored to your improvements.
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.stepContent}>
        <Text style={[styles.stepTitle, { color: colors.text }]}>
          Your Next Program
        </Text>
        <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
          Ready to take it to the next level?
        </Text>

        <View style={[styles.nextProgramCard, { backgroundColor: colors.primary + '10' }]}>
          <View style={styles.nextProgramHeader}>
            <Text style={[styles.nextProgramName, { color: colors.text }]}>
              {transitionData.nextProgram.name}
            </Text>
            <Text style={[styles.nextProgramCycle, { color: colors.primary }]}>
              Cycle {transitionData.nextProgram.cycle}
            </Text>
          </View>

          <View style={styles.startDate}>
            <Calendar size={16} color={colors.primary} />
            <Text style={[styles.startDateText, { color: colors.text }]}>
              Starts: {formatDate(transitionData.nextProgram.estimatedStartDate)}
            </Text>
          </View>

          <View style={styles.changes}>
            <Text style={[styles.changesTitle, { color: colors.text }]}>
              What's New:
            </Text>
            {transitionData.nextProgram.changes.map((change, index) => (
              <View key={index} style={styles.changeItem}>
                <ArrowRight size={16} color={colors.primary} />
                <Text style={[styles.changeText, { color: colors.text }]}>
                  {change}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </View>
    );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderProgressSummary();
      case 1:
        return renderStats();
      case 2:
        return renderAchievements();
      case 3:
        return renderNextProgram();
      default:
        return renderProgressSummary();
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      if (transitionData.nextProgram) {
        onContinue();
      } else {
        onRequestNewProgram();
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const steps = ['Progress', 'Stats', 'Achievements', 'Next Program'];

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    closeButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.accent,
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressIndicator: {
      flexDirection: 'row',
      justifyContent: 'center',
      padding: 20,
      gap: 8,
    },
    progressDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    content: {
      flex: 1,
    },
    stepContent: {
      flex: 1,
      padding: 20,
    },
    header: {
      alignItems: 'center',
      marginBottom: 32,
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      marginTop: 16,
      marginBottom: 8,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 18,
      textAlign: 'center',
    },
    completionCard: {
      alignItems: 'center',
      marginBottom: 24,
    },
    completionHeader: {
      alignItems: 'center',
      marginBottom: 16,
    },
    completionRate: {
      fontSize: 48,
      fontWeight: '700',
    },
    completionLabel: {
      fontSize: 16,
      fontWeight: '500',
    },
    progressBar: {
      width: width - 80,
      height: 8,
      borderRadius: 4,
    },
    progressFill: {
      height: '100%',
      borderRadius: 4,
    },
    dateRange: {
      gap: 12,
    },
    dateItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    dateText: {
      fontSize: 16,
    },
    stepTitle: {
      fontSize: 24,
      fontWeight: '700',
      marginBottom: 8,
      textAlign: 'center',
    },
    stepDescription: {
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 32,
      lineHeight: 24,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    statCard: {
      width: (width - 60) / 2,
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      gap: 8,
    },
    statValue: {
      fontSize: 24,
      fontWeight: '700',
    },
    statLabel: {
      fontSize: 12,
      textAlign: 'center',
    },
    achievementsList: {
      gap: 12,
    },
    achievementItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      gap: 12,
    },
    achievementText: {
      fontSize: 16,
      flex: 1,
    },
    noAchievements: {
      alignItems: 'center',
      padding: 40,
    },
    noAchievementsText: {
      fontSize: 16,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    waitingCard: {
      alignItems: 'center',
      padding: 32,
      backgroundColor: colors.accent,
      borderRadius: 16,
      gap: 16,
    },
    waitingTitle: {
      fontSize: 20,
      fontWeight: '600',
    },
    waitingDescription: {
      fontSize: 16,
      textAlign: 'center',
      lineHeight: 24,
    },
    nextProgramCard: {
      padding: 20,
      borderRadius: 16,
    },
    nextProgramHeader: {
      alignItems: 'center',
      marginBottom: 16,
    },
    nextProgramName: {
      fontSize: 22,
      fontWeight: '600',
      marginBottom: 4,
    },
    nextProgramCycle: {
      fontSize: 16,
      fontWeight: '500',
    },
    startDate: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      marginBottom: 20,
    },
    startDateText: {
      fontSize: 16,
      fontWeight: '500',
    },
    changes: {
      gap: 8,
    },
    changesTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
    },
    changeItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    changeText: {
      fontSize: 14,
      flex: 1,
    },
    footer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
      borderTopWidth: 1,
      borderTopColor: colors.accent,
    },
    footerButton: {
      flex: 1,
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    footerButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <View style={{ width: 40 }} />
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={20} color={colors.text} />
          </TouchableOpacity>
        </View>

        <View style={styles.progressIndicator}>
          {steps.map((_, index) => (
            <View
              key={index}
              style={[
                styles.progressDot,
                {
                  backgroundColor: index <= currentStep ? colors.primary : colors.accent,
                },
              ]}
            />
          ))}
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderCurrentStep()}
        </ScrollView>

        <View style={styles.footer}>
          {currentStep > 0 && (
            <TouchableOpacity
              style={[styles.footerButton, { backgroundColor: colors.accent }]}
              onPress={handlePrevious}
            >
              <Text style={[styles.footerButtonText, { color: colors.text }]}>
                Previous
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.footerButton,
              { backgroundColor: colors.primary },
              currentStep === 0 && { flex: 2 },
            ]}
            onPress={handleNext}
          >
            <Text style={[styles.footerButtonText, { color: colors.overlayText }]}>
              {currentStep === 3 
                ? (transitionData.nextProgram ? 'Start Next Program' : 'Request New Program')
                : 'Next'
              }
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
