import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText } from '@/components/ThemedComponents';

interface CompletionRateChartProps {
  data: {
    weekStartDate: string;
    completedWorkouts: number;
    totalPlannedWorkouts: number;
    completionRate: number;
  }[];
}

export default function CompletionRateChart({ data }: CompletionRateChartProps) {
  const { colors } = useThemeStore();

  // Format date to show month and day
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  return (
    <View style={styles.container}>
      <ThemedText style={styles.title}>Weekly Completion</ThemedText>
      
      <View style={styles.chartContainer}>
        {data.map((week, index) => (
          <View key={index} style={styles.weekColumn}>
            <View style={styles.barContainer}>
              <View 
                style={[
                  styles.barFill, 
                  { 
                    height: `${week.completionRate}%`,
                    backgroundColor: getBarColor(week.completionRate, colors)
                  }
                ]} 
              />
            </View>
            <ThemedText style={styles.weekLabel}>{formatDate(week.weekStartDate)}</ThemedText>
            <ThemedText style={styles.completionText}>{week.completionRate}%</ThemedText>
          </View>
        ))}
      </View>
      
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: colors.success }]} />
          <ThemedText style={styles.legendText}>80-100%</ThemedText>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: colors.warning }]} />
          <ThemedText style={styles.legendText}>50-79%</ThemedText>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: colors.error }]} />
          <ThemedText style={styles.legendText}>0-49%</ThemedText>
        </View>
      </View>
    </View>
  );
}

// Helper function to determine bar color based on completion rate
function getBarColor(rate: number, colors: any) {
  if (rate >= 80) return colors.success;
  if (rate >= 50) return colors.warning;
  return colors.error;
}

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 150,
    marginBottom: 16,
  },
  weekColumn: {
    flex: 1,
    alignItems: 'center',
  },
  barContainer: {
    width: 20,
    height: 100,
    backgroundColor: '#E5E5E5',
    borderRadius: 4,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  barFill: {
    width: '100%',
    borderRadius: 4,
  },
  weekLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  completionText: {
    fontSize: 10,
    opacity: 0.7,
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 2,
  },
  legendText: {
    fontSize: 10,
    opacity: 0.7,
  },
});