import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  Bell,
  MessageSquare,
  TrendingUp,
  Calendar,
  Award,
  Mail,
  Moon,
  Clock,
  Settings,
  Save,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { coachNotificationService, NotificationPreferences } from '../services/coachNotificationService';

interface NotificationPreferencesProps {
  onSave?: (preferences: NotificationPreferences) => void;
}

const NOTIFICATION_CATEGORIES = [
  {
    key: 'messageNotifications',
    title: 'Messages',
    description: 'New messages from your coach',
    icon: <MessageSquare size={24} />,
    color: '#3B82F6',
  },
  {
    key: 'feedbackNotifications',
    title: 'Feedback',
    description: 'Coach feedback on workouts and check-ins',
    icon: <TrendingUp size={24} />,
    color: '#10B981',
  },
  {
    key: 'checkInReminders',
    title: 'Check-in Reminders',
    description: 'Weekly check-in reminders',
    icon: <Calendar size={24} />,
    color: '#F59E0B',
  },
  {
    key: 'programUpdates',
    title: 'Program Updates',
    description: 'Program modifications and updates',
    icon: <Settings size={24} />,
    color: '#8B5CF6',
  },
  {
    key: 'achievementNotifications',
    title: 'Achievements',
    description: 'Goal completions and milestones',
    icon: <Award size={24} />,
    color: '#EF4444',
  },
  {
    key: 'marketingNotifications',
    title: 'Marketing',
    description: 'Tips, updates, and promotional content',
    icon: <Mail size={24} />,
    color: '#6B7280',
  },
] as const;

const TIMEZONES = [
  { label: 'Eastern Time (ET)', value: 'America/New_York' },
  { label: 'Central Time (CT)', value: 'America/Chicago' },
  { label: 'Mountain Time (MT)', value: 'America/Denver' },
  { label: 'Pacific Time (PT)', value: 'America/Los_Angeles' },
  { label: 'UTC', value: 'UTC' },
];

export const NotificationPreferencesComponent: React.FC<NotificationPreferencesProps> = ({
  onSave,
}) => {
  const { colors } = useThemeStore();
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    messageNotifications: true,
    feedbackNotifications: true,
    checkInReminders: true,
    programUpdates: true,
    achievementNotifications: true,
    marketingNotifications: false,
    timezone: 'UTC',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [showTimezoneSelector, setShowTimezoneSelector] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      setIsLoading(true);
      const userPreferences = await coachNotificationService.getNotificationPreferences();
      setPreferences(userPreferences);
    } catch (error) {
      console.error('Failed to load notification preferences:', error);
      Alert.alert('Error', 'Failed to load notification preferences');
    } finally {
      setIsLoading(false);
    }
  };

  const savePreferences = async () => {
    try {
      setIsSaving(true);
      await coachNotificationService.updateNotificationPreferences(preferences);
      onSave?.(preferences);
      Alert.alert('Success', 'Notification preferences saved successfully');
    } catch (error) {
      console.error('Failed to save notification preferences:', error);
      Alert.alert('Error', 'Failed to save notification preferences');
    } finally {
      setIsSaving(false);
    }
  };

  const updatePreference = (key: keyof NotificationPreferences, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return 'Not set';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const handleTimeChange = (event: any, selectedTime?: Date, type: 'start' | 'end') => {
    if (type === 'start') {
      setShowStartTimePicker(false);
    } else {
      setShowEndTimePicker(false);
    }

    if (selectedTime) {
      const timeString = selectedTime.toTimeString().slice(0, 5); // HH:MM format
      if (type === 'start') {
        updatePreference('quietHoursStart', timeString);
      } else {
        updatePreference('quietHoursEnd', timeString);
      }
    }
  };

  const getTimeFromString = (timeString?: string): Date => {
    const now = new Date();
    if (!timeString) return now;
    
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  const renderNotificationCategory = (category: typeof NOTIFICATION_CATEGORIES[0]) => (
    <View
      key={category.key}
      style={[styles.categoryItem, { backgroundColor: colors.accent }]}
    >
      <View style={styles.categoryHeader}>
        <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
          {React.cloneElement(category.icon as React.ReactElement, { color: category.color })}
        </View>
        
        <View style={styles.categoryInfo}>
          <Text style={[styles.categoryTitle, { color: colors.text }]}>
            {category.title}
          </Text>
          <Text style={[styles.categoryDescription, { color: colors.text + '80' }]}>
            {category.description}
          </Text>
        </View>

        <Switch
          value={preferences[category.key as keyof NotificationPreferences] as boolean}
          onValueChange={(value) => updatePreference(category.key as keyof NotificationPreferences, value)}
          trackColor={{ false: colors.background, true: category.color + '40' }}
          thumbColor={preferences[category.key as keyof NotificationPreferences] ? category.color : colors.text + '60'}
        />
      </View>
    </View>
  );

  const renderQuietHours = () => (
    <View style={[styles.section, { backgroundColor: colors.accent }]}>
      <View style={styles.sectionHeader}>
        <View style={[styles.sectionIcon, { backgroundColor: colors.primary + '20' }]}>
          <Moon size={24} color={colors.primary} />
        </View>
        <View style={styles.sectionInfo}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Quiet Hours
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.text + '80' }]}>
            Disable notifications during specific hours
          </Text>
        </View>
      </View>

      <View style={styles.quietHoursControls}>
        <View style={styles.timeSelector}>
          <Text style={[styles.timeLabel, { color: colors.text }]}>Start Time</Text>
          <TouchableOpacity
            style={[styles.timeButton, { backgroundColor: colors.background }]}
            onPress={() => setShowStartTimePicker(true)}
          >
            <Clock size={16} color={colors.primary} />
            <Text style={[styles.timeText, { color: colors.text }]}>
              {formatTime(preferences.quietHoursStart)}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.timeSelector}>
          <Text style={[styles.timeLabel, { color: colors.text }]}>End Time</Text>
          <TouchableOpacity
            style={[styles.timeButton, { backgroundColor: colors.background }]}
            onPress={() => setShowEndTimePicker(true)}
          >
            <Clock size={16} color={colors.primary} />
            <Text style={[styles.timeText, { color: colors.text }]}>
              {formatTime(preferences.quietHoursEnd)}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {preferences.quietHoursStart && preferences.quietHoursEnd && (
        <View style={[styles.quietHoursPreview, { backgroundColor: colors.background }]}>
          <Text style={[styles.quietHoursPreviewText, { color: colors.text + '80' }]}>
            Notifications will be silenced from {formatTime(preferences.quietHoursStart)} to {formatTime(preferences.quietHoursEnd)}
          </Text>
        </View>
      )}
    </View>
  );

  const renderTimezoneSelector = () => (
    <View style={[styles.section, { backgroundColor: colors.accent }]}>
      <View style={styles.sectionHeader}>
        <View style={[styles.sectionIcon, { backgroundColor: colors.warning + '20' }]}>
          <Clock size={24} color={colors.warning} />
        </View>
        <View style={styles.sectionInfo}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Timezone
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.text + '80' }]}>
            Your local timezone for scheduling notifications
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.timezoneButton, { backgroundColor: colors.background }]}
        onPress={() => setShowTimezoneSelector(!showTimezoneSelector)}
      >
        <Text style={[styles.timezoneText, { color: colors.text }]}>
          {TIMEZONES.find(tz => tz.value === preferences.timezone)?.label || preferences.timezone}
        </Text>
      </TouchableOpacity>

      {showTimezoneSelector && (
        <View style={styles.timezoneOptions}>
          {TIMEZONES.map((timezone) => (
            <TouchableOpacity
              key={timezone.value}
              style={[
                styles.timezoneOption,
                {
                  backgroundColor: preferences.timezone === timezone.value ? colors.primary + '20' : colors.background,
                },
              ]}
              onPress={() => {
                updatePreference('timezone', timezone.value);
                setShowTimezoneSelector(false);
              }}
            >
              <Text
                style={[
                  styles.timezoneOptionText,
                  {
                    color: preferences.timezone === timezone.value ? colors.primary : colors.text,
                  },
                ]}
              >
                {timezone.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    headerDescription: {
      fontSize: 16,
      color: colors.text + '80',
      lineHeight: 24,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    categoriesSection: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
    },
    categoryItem: {
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
    },
    categoryHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    categoryIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    categoryInfo: {
      flex: 1,
    },
    categoryTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    categoryDescription: {
      fontSize: 14,
      lineHeight: 20,
    },
    section: {
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
    },
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
      marginBottom: 16,
    },
    sectionIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    sectionInfo: {
      flex: 1,
    },
    sectionDescription: {
      fontSize: 14,
      lineHeight: 20,
    },
    quietHoursControls: {
      flexDirection: 'row',
      gap: 16,
    },
    timeSelector: {
      flex: 1,
    },
    timeLabel: {
      fontSize: 14,
      fontWeight: '500',
      marginBottom: 8,
    },
    timeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderRadius: 8,
      gap: 8,
    },
    timeText: {
      fontSize: 16,
    },
    quietHoursPreview: {
      padding: 12,
      borderRadius: 8,
      marginTop: 12,
    },
    quietHoursPreviewText: {
      fontSize: 14,
      textAlign: 'center',
    },
    timezoneButton: {
      padding: 12,
      borderRadius: 8,
    },
    timezoneText: {
      fontSize: 16,
    },
    timezoneOptions: {
      marginTop: 8,
      gap: 4,
    },
    timezoneOption: {
      padding: 12,
      borderRadius: 8,
    },
    timezoneOptionText: {
      fontSize: 14,
    },
    saveButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.primary,
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
      margin: 20,
    },
    saveButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.overlayText,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: colors.text + '80',
      marginTop: 16,
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading preferences...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Notification Preferences</Text>
        <Text style={styles.headerDescription}>
          Customize when and how you receive notifications from your coach
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Notification Types</Text>
          {NOTIFICATION_CATEGORIES.map(renderNotificationCategory)}
        </View>

        {renderQuietHours()}
        {renderTimezoneSelector()}
      </ScrollView>

      <TouchableOpacity
        style={styles.saveButton}
        onPress={savePreferences}
        disabled={isSaving}
      >
        {isSaving ? (
          <ActivityIndicator size="small" color={colors.overlayText} />
        ) : (
          <>
            <Save size={16} color={colors.overlayText} />
            <Text style={styles.saveButtonText}>Save Preferences</Text>
          </>
        )}
      </TouchableOpacity>

      {showStartTimePicker && (
        <DateTimePicker
          value={getTimeFromString(preferences.quietHoursStart)}
          mode="time"
          is24Hour={false}
          onChange={(event, time) => handleTimeChange(event, time, 'start')}
        />
      )}

      {showEndTimePicker && (
        <DateTimePicker
          value={getTimeFromString(preferences.quietHoursEnd)}
          mode="time"
          is24Hour={false}
          onChange={(event, time) => handleTimeChange(event, time, 'end')}
        />
      )}
    </View>
  );
};
