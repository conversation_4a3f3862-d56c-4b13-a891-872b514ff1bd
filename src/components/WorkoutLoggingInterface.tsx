import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import {
  Plus,
  Minus,
  Check,
  X,
  Timer,
  Weight,
  RotateCcw,
  Target,
  TrendingUp,
  Edit3,
  Save,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useActiveWorkoutStore } from '../store/activeWorkoutStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { WorkoutExercise } from '../services/workoutService';

interface SetData {
  id: string;
  setNumber: number;
  targetReps: number;
  actualReps: number;
  targetWeight: number;
  actualWeight: number;
  rpe?: number;
  isCompleted: boolean;
  notes?: string;
}

interface ExerciseLogData {
  exerciseId: string;
  sets: SetData[];
  notes?: string;
  isCompleted: boolean;
}

interface WorkoutLoggingInterfaceProps {
  exercise: WorkoutExercise;
  onComplete: (logData: ExerciseLogData) => void;
  onSkip: () => void;
  previousPerformance?: {
    lastWeight: number;
    lastReps: number;
    bestWeight: number;
    bestReps: number;
  };
}

export const WorkoutLoggingInterface: React.FC<WorkoutLoggingInterfaceProps> = ({
  exercise,
  onComplete,
  onSkip,
  previousPerformance,
}) => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [sets, setSets] = useState<SetData[]>([]);
  const [exerciseNotes, setExerciseNotes] = useState('');
  const [showRPEModal, setShowRPEModal] = useState(false);
  const [activeSetIndex, setActiveSetIndex] = useState<number | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    initializeSets();
  }, [exercise]);

  const initializeSets = () => {
    const initialSets: SetData[] = [];
    const targetSets = exercise.sets || 3;
    const targetReps = exercise.reps || 10;
    const targetWeight = exercise.weight || previousPerformance?.lastWeight || 0;

    for (let i = 0; i < targetSets; i++) {
      initialSets.push({
        id: `set-${i + 1}`,
        setNumber: i + 1,
        targetReps,
        actualReps: targetReps,
        targetWeight,
        actualWeight: targetWeight,
        isCompleted: false,
      });
    }

    setSets(initialSets);
  };

  const updateSet = (index: number, field: keyof SetData, value: any) => {
    const updatedSets = [...sets];
    updatedSets[index] = { ...updatedSets[index], [field]: value };
    setSets(updatedSets);
  };

  const completeSet = (index: number) => {
    const updatedSets = [...sets];
    updatedSets[index].isCompleted = true;
    setSets(updatedSets);

    // Auto-populate next set with same values
    if (index < sets.length - 1 && !updatedSets[index + 1].isCompleted) {
      updatedSets[index + 1].actualWeight = updatedSets[index].actualWeight;
      updatedSets[index + 1].actualReps = updatedSets[index].actualReps;
    }

    setSets(updatedSets);
  };

  const addSet = () => {
    const lastSet = sets[sets.length - 1];
    const newSet: SetData = {
      id: `set-${sets.length + 1}`,
      setNumber: sets.length + 1,
      targetReps: lastSet?.targetReps || exercise.reps || 10,
      actualReps: lastSet?.actualReps || exercise.reps || 10,
      targetWeight: lastSet?.targetWeight || exercise.weight || 0,
      actualWeight: lastSet?.actualWeight || exercise.weight || 0,
      isCompleted: false,
    };

    setSets([...sets, newSet]);
  };

  const removeSet = (index: number) => {
    if (sets.length <= 1) return;
    
    const updatedSets = sets.filter((_, i) => i !== index);
    // Renumber sets
    updatedSets.forEach((set, i) => {
      set.setNumber = i + 1;
      set.id = `set-${i + 1}`;
    });
    
    setSets(updatedSets);
  };

  const handleRPESubmit = (rpe: number) => {
    if (activeSetIndex !== null) {
      updateSet(activeSetIndex, 'rpe', rpe);
    }
    setShowRPEModal(false);
    setActiveSetIndex(null);
  };

  const handleCompleteExercise = async () => {
    const completedSets = sets.filter(set => set.isCompleted);
    
    if (completedSets.length === 0) {
      Alert.alert('No Sets Completed', 'Please complete at least one set before finishing this exercise.');
      return;
    }

    const logData: ExerciseLogData = {
      exerciseId: exercise.id,
      sets,
      notes: exerciseNotes,
      isCompleted: true,
    };

    try {
      await executeOperation(
        async () => {
          // Save exercise log data
          // This would typically call a service to save to the database
          console.log('Saving exercise log:', logData);
        },
        {
          type: 'EXERCISE_LOG_CREATE',
          payload: logData,
        }
      );

      onComplete(logData);
    } catch (error) {
      console.error('Failed to save exercise log:', error);
      Alert.alert('Error', 'Failed to save workout data. Please try again.');
    }
  };

  const renderPreviousPerformance = () => {
    if (!previousPerformance) return null;

    return (
      <View style={[styles.previousPerformance, { backgroundColor: colors.accent }]}>
        <Text style={[styles.previousTitle, { color: colors.text }]}>
          Previous Performance
        </Text>
        <View style={styles.previousStats}>
          <View style={styles.previousStat}>
            <Text style={[styles.previousLabel, { color: colors.text + '80' }]}>
              Last
            </Text>
            <Text style={[styles.previousValue, { color: colors.text }]}>
              {previousPerformance.lastWeight}kg × {previousPerformance.lastReps}
            </Text>
          </View>
          <View style={styles.previousStat}>
            <Text style={[styles.previousLabel, { color: colors.text + '80' }]}>
              Best
            </Text>
            <Text style={[styles.previousValue, { color: colors.primary }]}>
              {previousPerformance.bestWeight}kg × {previousPerformance.bestReps}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderSetRow = (set: SetData, index: number) => {
    const isCompleted = set.isCompleted;
    
    return (
      <View
        key={set.id}
        style={[
          styles.setRow,
          {
            backgroundColor: isCompleted ? colors.success + '10' : colors.accent,
            borderColor: isCompleted ? colors.success : 'transparent',
            borderWidth: isCompleted ? 1 : 0,
          },
        ]}
      >
        <View style={styles.setNumber}>
          <Text style={[styles.setNumberText, { color: colors.text }]}>
            {set.setNumber}
          </Text>
        </View>

        <View style={styles.setInputs}>
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text + '80' }]}>
              Weight (kg)
            </Text>
            <View style={styles.inputContainer}>
              <TouchableOpacity
                style={[styles.adjustButton, { backgroundColor: colors.primary }]}
                onPress={() => updateSet(index, 'actualWeight', Math.max(0, set.actualWeight - 2.5))}
                disabled={isCompleted}
              >
                <Minus size={16} color={colors.overlayText} />
              </TouchableOpacity>
              
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.primary + '40',
                  },
                ]}
                value={set.actualWeight.toString()}
                onChangeText={(text) => {
                  const value = parseFloat(text) || 0;
                  updateSet(index, 'actualWeight', value);
                }}
                keyboardType="numeric"
                editable={!isCompleted}
              />
              
              <TouchableOpacity
                style={[styles.adjustButton, { backgroundColor: colors.primary }]}
                onPress={() => updateSet(index, 'actualWeight', set.actualWeight + 2.5)}
                disabled={isCompleted}
              >
                <Plus size={16} color={colors.overlayText} />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text + '80' }]}>
              Reps
            </Text>
            <View style={styles.inputContainer}>
              <TouchableOpacity
                style={[styles.adjustButton, { backgroundColor: colors.primary }]}
                onPress={() => updateSet(index, 'actualReps', Math.max(0, set.actualReps - 1))}
                disabled={isCompleted}
              >
                <Minus size={16} color={colors.overlayText} />
              </TouchableOpacity>
              
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.primary + '40',
                  },
                ]}
                value={set.actualReps.toString()}
                onChangeText={(text) => {
                  const value = parseInt(text) || 0;
                  updateSet(index, 'actualReps', value);
                }}
                keyboardType="numeric"
                editable={!isCompleted}
              />
              
              <TouchableOpacity
                style={[styles.adjustButton, { backgroundColor: colors.primary }]}
                onPress={() => updateSet(index, 'actualReps', set.actualReps + 1)}
                disabled={isCompleted}
              >
                <Plus size={16} color={colors.overlayText} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.setActions}>
          {!isCompleted ? (
            <>
              <TouchableOpacity
                style={[styles.rpeButton, { backgroundColor: colors.warning }]}
                onPress={() => {
                  setActiveSetIndex(index);
                  setShowRPEModal(true);
                }}
              >
                <Text style={[styles.rpeButtonText, { color: colors.overlayText }]}>
                  RPE {set.rpe || '?'}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.completeButton, { backgroundColor: colors.success }]}
                onPress={() => completeSet(index)}
              >
                <Check size={20} color={colors.overlayText} />
              </TouchableOpacity>
            </>
          ) : (
            <View style={styles.completedIndicator}>
              <Check size={20} color={colors.success} />
              <Text style={[styles.completedText, { color: colors.success }]}>
                Done
              </Text>
            </View>
          )}

          <TouchableOpacity
            style={[styles.removeButton, { backgroundColor: colors.error }]}
            onPress={() => removeSet(index)}
            disabled={sets.length <= 1}
          >
            <X size={16} color={colors.overlayText} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderRPEModal = () => (
    <Modal
      visible={showRPEModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowRPEModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.rpeModal, { backgroundColor: colors.background }]}>
          <Text style={[styles.rpeModalTitle, { color: colors.text }]}>
            Rate Perceived Exertion (RPE)
          </Text>
          <Text style={[styles.rpeModalDescription, { color: colors.text + '80' }]}>
            How hard did that set feel?
          </Text>

          <View style={styles.rpeOptions}>
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rpe) => (
              <TouchableOpacity
                key={rpe}
                style={[styles.rpeOption, { backgroundColor: colors.accent }]}
                onPress={() => handleRPESubmit(rpe)}
              >
                <Text style={[styles.rpeOptionText, { color: colors.text }]}>
                  {rpe}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <TouchableOpacity
            style={[styles.rpeModalClose, { backgroundColor: colors.error }]}
            onPress={() => setShowRPEModal(false)}
          >
            <Text style={[styles.rpeModalCloseText, { color: colors.overlayText }]}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const completedSets = sets.filter(set => set.isCompleted).length;
  const totalSets = sets.length;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    exerciseName: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    exerciseInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
    },
    exerciseInfoItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    exerciseInfoText: {
      fontSize: 14,
      color: colors.text + '80',
    },
    previousPerformance: {
      margin: 20,
      padding: 16,
      borderRadius: 12,
    },
    previousTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 12,
    },
    previousStats: {
      flexDirection: 'row',
      gap: 20,
    },
    previousStat: {
      flex: 1,
    },
    previousLabel: {
      fontSize: 12,
      marginBottom: 4,
    },
    previousValue: {
      fontSize: 16,
      fontWeight: '600',
    },
    content: {
      flex: 1,
      padding: 20,
    },
    progressHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    progressText: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.text,
    },
    addSetButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      backgroundColor: colors.primary,
      borderRadius: 8,
      gap: 4,
    },
    addSetText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.overlayText,
    },
    setsList: {
      gap: 12,
      marginBottom: 20,
    },
    setRow: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      gap: 12,
    },
    setNumber: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    setNumberText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.overlayText,
    },
    setInputs: {
      flex: 1,
      flexDirection: 'row',
      gap: 16,
    },
    inputGroup: {
      flex: 1,
    },
    inputLabel: {
      fontSize: 12,
      marginBottom: 4,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    adjustButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    input: {
      flex: 1,
      height: 40,
      borderRadius: 8,
      borderWidth: 1,
      paddingHorizontal: 12,
      textAlign: 'center',
      fontSize: 16,
      fontWeight: '500',
    },
    setActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    rpeButton: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
    },
    rpeButtonText: {
      fontSize: 12,
      fontWeight: '500',
    },
    completeButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
    },
    completedIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    completedText: {
      fontSize: 12,
      fontWeight: '500',
    },
    removeButton: {
      width: 28,
      height: 28,
      borderRadius: 14,
      justifyContent: 'center',
      alignItems: 'center',
    },
    notesSection: {
      marginBottom: 20,
    },
    notesLabel: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.text,
      marginBottom: 8,
    },
    notesInput: {
      backgroundColor: colors.accent,
      borderRadius: 12,
      padding: 12,
      fontSize: 16,
      color: colors.text,
      minHeight: 80,
      textAlignVertical: 'top',
    },
    footer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
      borderTopWidth: 1,
      borderTopColor: colors.accent,
    },
    footerButton: {
      flex: 1,
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    footerButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    rpeModal: {
      width: '80%',
      borderRadius: 16,
      padding: 20,
    },
    rpeModalTitle: {
      fontSize: 18,
      fontWeight: '600',
      textAlign: 'center',
      marginBottom: 8,
    },
    rpeModalDescription: {
      fontSize: 14,
      textAlign: 'center',
      marginBottom: 20,
    },
    rpeOptions: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      justifyContent: 'center',
      marginBottom: 20,
    },
    rpeOption: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
    },
    rpeOptionText: {
      fontSize: 18,
      fontWeight: '600',
    },
    rpeModalClose: {
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    rpeModalCloseText: {
      fontSize: 16,
      fontWeight: '500',
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.exerciseName}>
          {exercise.exercise?.name || 'Unknown Exercise'}
        </Text>
        <View style={styles.exerciseInfo}>
          <View style={styles.exerciseInfoItem}>
            <Target size={16} color={colors.primary} />
            <Text style={styles.exerciseInfoText}>
              {exercise.sets} sets × {exercise.reps} reps
            </Text>
          </View>
          {exercise.weight && (
            <View style={styles.exerciseInfoItem}>
              <Weight size={16} color={colors.primary} />
              <Text style={styles.exerciseInfoText}>
                {exercise.weight}kg
              </Text>
            </View>
          )}
        </View>
      </View>

      {renderPreviousPerformance()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressText}>
            Sets: {completedSets}/{totalSets}
          </Text>
          <TouchableOpacity style={styles.addSetButton} onPress={addSet}>
            <Plus size={16} color={colors.overlayText} />
            <Text style={styles.addSetText}>Add Set</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.setsList}>
          {sets.map((set, index) => renderSetRow(set, index))}
        </View>

        <View style={styles.notesSection}>
          <Text style={styles.notesLabel}>Exercise Notes (Optional)</Text>
          <TextInput
            style={styles.notesInput}
            placeholder="How did this exercise feel? Any form notes or observations..."
            placeholderTextColor={colors.text + '60'}
            multiline
            value={exerciseNotes}
            onChangeText={setExerciseNotes}
          />
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.footerButton, { backgroundColor: colors.accent }]}
          onPress={onSkip}
        >
          <Text style={[styles.footerButtonText, { color: colors.text }]}>
            Skip Exercise
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.footerButton,
            {
              backgroundColor: completedSets > 0 ? colors.success : colors.primary,
            },
          ]}
          onPress={handleCompleteExercise}
        >
          <Text style={[styles.footerButtonText, { color: colors.overlayText }]}>
            Complete Exercise
          </Text>
        </TouchableOpacity>
      </View>

      {renderRPEModal()}
    </View>
  );
};
