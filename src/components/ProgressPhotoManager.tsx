import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Modal,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Camera, CameraType, FlashMode } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import {
  Camera as CameraIcon,
  Image as ImageIcon,
  FlashOn,
  FlashOff,
  RotateCcw,
  Check,
  X,
  Calendar,
  Compare,
  Download,
  Share2,
  Trash2,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

const { width, height } = Dimensions.get('window');

interface ProgressPhoto {
  id: string;
  photoUrl: string;
  photoType: 'front' | 'side' | 'back' | 'custom';
  caption?: string;
  weightKg?: number;
  bodyFatPercentage?: number;
  takenAt: string;
  isPublic: boolean;
  tags?: string[];
}

interface ProgressPhotoManagerProps {
  onPhotoSaved?: (photo: ProgressPhoto) => void;
  initialPhotoType?: 'front' | 'side' | 'back' | 'custom';
}

export const ProgressPhotoManager: React.FC<ProgressPhotoManagerProps> = ({
  onPhotoSaved,
  initialPhotoType = 'front',
}) => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [showCamera, setShowCamera] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [photos, setPhotos] = useState<ProgressPhoto[]>([]);
  const [selectedPhotos, setSelectedPhotos] = useState<ProgressPhoto[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [cameraType, setCameraType] = useState(CameraType.back);
  const [flashMode, setFlashMode] = useState(FlashMode.off);
  const [currentPhotoType, setCurrentPhotoType] = useState(initialPhotoType);

  const cameraRef = useRef<Camera>(null);

  useEffect(() => {
    loadProgressPhotos();
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();
    const { status: mediaStatus } = await MediaLibrary.requestPermissionsAsync();

    if (cameraStatus !== 'granted' || mediaStatus !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'Camera and media library permissions are required to take and save progress photos.',
        [{ text: 'OK' }]
      );
    }
  };

  const loadProgressPhotos = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('progress_photos')
        .select('*')
        .eq('user_id', user.id)
        .order('taken_at', { ascending: false });

      if (error) throw error;

      setPhotos(data || []);
    } catch (error) {
      console.error('Failed to load progress photos:', error);
      Alert.alert('Error', 'Failed to load progress photos');
    } finally {
      setIsLoading(false);
    }
  };

  const takePicture = async () => {
    if (!cameraRef.current) return;

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        exif: false,
      });

      setShowCamera(false);
      await savePhoto(photo.uri);
    } catch (error) {
      console.error('Failed to take picture:', error);
      Alert.alert('Error', 'Failed to take picture');
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 4],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await savePhoto(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Failed to pick image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const savePhoto = async (photoUri: string) => {
    try {
      setIsLoading(true);

      await executeOperation(
        async () => {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user');

          // Upload photo to Supabase Storage
          const fileName = `progress_photos/${user.id}/${Date.now()}_${currentPhotoType}.jpg`;

          const response = await fetch(photoUri);
          const blob = await response.blob();

          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('progress-photos')
            .upload(fileName, blob, {
              contentType: 'image/jpeg',
              upsert: false,
            });

          if (uploadError) throw uploadError;

          // Get public URL
          const { data: { publicUrl } } = supabase.storage
            .from('progress-photos')
            .getPublicUrl(fileName);

          // Save photo record to database
          const photoData = {
            user_id: user.id,
            photo_url: publicUrl,
            photo_type: currentPhotoType,
            taken_at: new Date().toISOString(),
            is_public: false,
          };

          const { data, error } = await supabase
            .from('progress_photos')
            .insert(photoData)
            .select()
            .single();

          if (error) throw error;

          const newPhoto: ProgressPhoto = {
            id: data.id,
            photoUrl: data.photo_url,
            photoType: data.photo_type,
            caption: data.caption,
            weightKg: data.weight_kg,
            bodyFatPercentage: data.body_fat_percentage,
            takenAt: data.taken_at,
            isPublic: data.is_public,
            tags: data.tags,
          };

          setPhotos(prev => [newPhoto, ...prev]);
          onPhotoSaved?.(newPhoto);

          Alert.alert('Success', 'Progress photo saved successfully!');
        },
        {
          type: 'PROGRESS_PHOTO_UPLOAD',
          payload: {
            photoUri,
            photoType: currentPhotoType,
            fileName: `progress_${currentPhotoType}_${Date.now()}.jpg`,
          },
        }
      );
    } catch (error) {
      console.error('Failed to save photo:', error);
      Alert.alert('Error', 'Failed to save progress photo');
    } finally {
      setIsLoading(false);
    }
  };

  const deletePhoto = async (photoId: string) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this progress photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('progress_photos')
                .delete()
                .eq('id', photoId);

              if (error) throw error;

              setPhotos(prev => prev.filter(photo => photo.id !== photoId));
              Alert.alert('Success', 'Photo deleted successfully');
            } catch (error) {
              console.error('Failed to delete photo:', error);
              Alert.alert('Error', 'Failed to delete photo');
            }
          },
        },
      ]
    );
  };

  const renderCameraInterface = () => (
    <Modal visible={showCamera} animationType="slide">
      <View style={styles.cameraContainer}>
        <Camera
          ref={cameraRef}
          style={styles.camera}
          type={cameraType}
          flashMode={flashMode}
        >
          <View style={styles.cameraOverlay}>
            <View style={styles.cameraHeader}>
              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: colors.error }]}
                onPress={() => setShowCamera(false)}
              >
                <X size={24} color={colors.overlayText} />
              </TouchableOpacity>

              <Text style={[styles.cameraTitle, { color: colors.overlayText }]}>
                {currentPhotoType.charAt(0).toUpperCase() + currentPhotoType.slice(1)} Photo
              </Text>

              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: colors.accent }]}
                onPress={() => setFlashMode(
                  flashMode === FlashMode.off ? FlashMode.on : FlashMode.off
                )}
              >
                {flashMode === FlashMode.off ? (
                  <FlashOff size={24} color={colors.text} />
                ) : (
                  <FlashOn size={24} color={colors.text} />
                )}
              </TouchableOpacity>
            </View>

            <View style={styles.cameraGuide}>
              <View style={[styles.guideFrame, { borderColor: colors.primary }]} />
              <Text style={[styles.guideText, { color: colors.overlayText }]}>
                Position yourself within the frame
              </Text>
            </View>

            <View style={styles.cameraControls}>
              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: colors.accent }]}
                onPress={() => setCameraType(
                  cameraType === CameraType.back ? CameraType.front : CameraType.back
                )}
              >
                <RotateCcw size={24} color={colors.text} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.captureButton, { borderColor: colors.primary }]}
                onPress={takePicture}
              >
                <View style={[styles.captureButtonInner, { backgroundColor: colors.primary }]} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.cameraButton, { backgroundColor: colors.accent }]}
                onPress={() => {
                  setShowCamera(false);
                  pickImage();
                }}
              >
                <ImageIcon size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
          </View>
        </Camera>
      </View>
    </Modal>
  );

  const renderPhotoTypeSelector = () => (
    <View style={styles.photoTypeSelector}>
      {(['front', 'side', 'back', 'custom'] as const).map((type) => (
        <TouchableOpacity
          key={type}
          style={[
            styles.photoTypeButton,
            {
              backgroundColor: currentPhotoType === type ? colors.primary : colors.accent,
            },
          ]}
          onPress={() => setCurrentPhotoType(type)}
        >
          <Text
            style={[
              styles.photoTypeText,
              {
                color: currentPhotoType === type ? colors.overlayText : colors.text,
              },
            ]}
          >
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderPhotoGrid = () => (
    <ScrollView style={styles.photoGrid} showsVerticalScrollIndicator={false}>
      <View style={styles.photoGridContainer}>
        {photos.map((photo) => (
          <TouchableOpacity
            key={photo.id}
            style={styles.photoItem}
            onPress={() => {
              if (selectedPhotos.length > 0) {
                // Toggle selection
                setSelectedPhotos(prev =>
                  prev.find(p => p.id === photo.id)
                    ? prev.filter(p => p.id !== photo.id)
                    : [...prev, photo]
                );
              } else {
                // View photo details
                // TODO: Implement photo detail view
              }
            }}
            onLongPress={() => {
              setSelectedPhotos([photo]);
            }}
          >
            <Image source={{ uri: photo.photoUrl }} style={styles.photoImage} />

            {selectedPhotos.find(p => p.id === photo.id) && (
              <View style={[styles.photoSelected, { backgroundColor: colors.primary + '80' }]}>
                <Check size={24} color={colors.overlayText} />
              </View>
            )}

            <View style={[styles.photoInfo, { backgroundColor: colors.background + 'E0' }]}>
              <Text style={[styles.photoType, { color: colors.text }]}>
                {photo.photoType}
              </Text>
              <Text style={[styles.photoDate, { color: colors.text + '80' }]}>
                {new Date(photo.takenAt).toLocaleDateString()}
              </Text>
            </View>

            <TouchableOpacity
              style={[styles.photoDeleteButton, { backgroundColor: colors.error }]}
              onPress={() => deletePhoto(photo.id)}
            >
              <Trash2 size={16} color={colors.overlayText} />
            </TouchableOpacity>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );

  const renderComparisonView = () => (
    <Modal visible={showComparison} animationType="slide">
      <View style={[styles.comparisonContainer, { backgroundColor: colors.background }]}>
        <View style={styles.comparisonHeader}>
          <TouchableOpacity
            style={[styles.comparisonButton, { backgroundColor: colors.error }]}
            onPress={() => setShowComparison(false)}
          >
            <X size={24} color={colors.overlayText} />
          </TouchableOpacity>

          <Text style={[styles.comparisonTitle, { color: colors.text }]}>
            Progress Comparison
          </Text>

          <TouchableOpacity
            style={[styles.comparisonButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              // TODO: Implement share functionality
            }}
          >
            <Share2 size={24} color={colors.overlayText} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.comparisonContent}>
          <View style={styles.comparisonPhotos}>
            {selectedPhotos.map((photo, index) => (
              <View key={photo.id} style={styles.comparisonPhotoContainer}>
                <Image source={{ uri: photo.photoUrl }} style={styles.comparisonPhoto} />
                <View style={[styles.comparisonPhotoInfo, { backgroundColor: colors.accent }]}>
                  <Text style={[styles.comparisonPhotoDate, { color: colors.text }]}>
                    {new Date(photo.takenAt).toLocaleDateString()}
                  </Text>
                  <Text style={[styles.comparisonPhotoType, { color: colors.text + '80' }]}>
                    {photo.photoType}
                  </Text>
                  {photo.weightKg && (
                    <Text style={[styles.comparisonPhotoWeight, { color: colors.text + '80' }]}>
                      {photo.weightKg}kg
                    </Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
    },
    headerActions: {
      flexDirection: 'row',
      gap: 12,
    },
    headerButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    photoTypeSelector: {
      flexDirection: 'row',
      padding: 20,
      gap: 8,
    },
    photoTypeButton: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    photoTypeText: {
      fontSize: 14,
      fontWeight: '500',
    },
    actionButtons: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
    },
    actionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
    },
    actionButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    photoGrid: {
      flex: 1,
      padding: 20,
    },
    photoGridContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    photoItem: {
      width: (width - 56) / 2,
      aspectRatio: 3 / 4,
      borderRadius: 12,
      overflow: 'hidden',
      position: 'relative',
    },
    photoImage: {
      width: '100%',
      height: '100%',
    },
    photoSelected: {
      position: 'absolute',
      top: 8,
      right: 8,
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    photoInfo: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      padding: 8,
    },
    photoType: {
      fontSize: 12,
      fontWeight: '600',
      textTransform: 'capitalize',
    },
    photoDate: {
      fontSize: 10,
    },
    photoDeleteButton: {
      position: 'absolute',
      top: 8,
      left: 8,
      width: 28,
      height: 28,
      borderRadius: 14,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyStateIcon: {
      marginBottom: 16,
    },
    emptyStateTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    emptyStateDescription: {
      fontSize: 16,
      color: colors.text + '80',
      textAlign: 'center',
      lineHeight: 24,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    // Camera styles
    cameraContainer: {
      flex: 1,
    },
    camera: {
      flex: 1,
    },
    cameraOverlay: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    cameraHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    cameraButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    cameraTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    cameraGuide: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40,
    },
    guideFrame: {
      width: width * 0.7,
      height: height * 0.5,
      borderWidth: 2,
      borderRadius: 12,
      borderStyle: 'dashed',
    },
    guideText: {
      fontSize: 16,
      textAlign: 'center',
      marginTop: 20,
    },
    cameraControls: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 40,
      paddingBottom: 40,
    },
    captureButton: {
      width: 80,
      height: 80,
      borderRadius: 40,
      borderWidth: 4,
      justifyContent: 'center',
      alignItems: 'center',
    },
    captureButtonInner: {
      width: 60,
      height: 60,
      borderRadius: 30,
    },
    // Comparison styles
    comparisonContainer: {
      flex: 1,
    },
    comparisonHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    comparisonButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    comparisonTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    comparisonContent: {
      flex: 1,
      padding: 20,
    },
    comparisonPhotos: {
      flexDirection: 'row',
      gap: 16,
    },
    comparisonPhotoContainer: {
      flex: 1,
    },
    comparisonPhoto: {
      width: '100%',
      aspectRatio: 3 / 4,
      borderRadius: 12,
    },
    comparisonPhotoInfo: {
      padding: 12,
      borderRadius: 8,
      marginTop: 8,
    },
    comparisonPhotoDate: {
      fontSize: 14,
      fontWeight: '600',
      marginBottom: 4,
    },
    comparisonPhotoType: {
      fontSize: 12,
      textTransform: 'capitalize',
    },
    comparisonPhotoWeight: {
      fontSize: 12,
      marginTop: 2,
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.emptyStateDescription, { marginTop: 16 }]}>
          Loading progress photos...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Progress Photos</Text>
        <View style={styles.headerActions}>
          {selectedPhotos.length > 1 && (
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: colors.primary }]}
              onPress={() => setShowComparison(true)}
            >
              <Compare size={20} color={colors.overlayText} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {renderPhotoTypeSelector()}

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.primary }]}
          onPress={() => setShowCamera(true)}
        >
          <CameraIcon size={20} color={colors.overlayText} />
          <Text style={[styles.actionButtonText, { color: colors.overlayText }]}>
            Take Photo
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.accent }]}
          onPress={pickImage}
        >
          <ImageIcon size={20} color={colors.text} />
          <Text style={[styles.actionButtonText, { color: colors.text }]}>
            From Gallery
          </Text>
        </TouchableOpacity>
      </View>

      {photos.length === 0 ? (
        <View style={styles.emptyState}>
          <CameraIcon size={64} color={colors.text + '40'} style={styles.emptyStateIcon} />
          <Text style={styles.emptyStateTitle}>No Progress Photos Yet</Text>
          <Text style={styles.emptyStateDescription}>
            Take your first progress photo to start tracking your fitness journey visually.
          </Text>
        </View>
      ) : (
        renderPhotoGrid()
      )}

      {renderCameraInterface()}
      {renderComparisonView()}
    </View>
  );
};