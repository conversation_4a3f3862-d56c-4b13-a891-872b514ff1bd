import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ActivityIndicator, ScrollView } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { workoutLoggingService } from '@/services/workoutLoggingService';
import { ThemedText, ThemedCard } from '@/components/ThemedComponents';
import CompletionRateChart from '@/components/CompletionRateChart';
import IntensityTrendChart from '@/components/IntensityTrendChart';
import { Award, Calendar, Clock, Target, ChartBar as BarChart } from 'lucide-react-native';

interface DetailedProgressViewProps {
  refreshTrigger?: number;
}

export default function DetailedProgressView({ refreshTrigger }: DetailedProgressViewProps) {
  const { colors } = useThemeStore();
  const [stats, setStats] = useState({
    totalWorkouts: 0,
    totalDuration: 0,
    averageRpe: 0,
    lastWorkoutDate: undefined as string | undefined,
    currentStreak: 0
  });
  const [completionRates, setCompletionRates] = useState<any[]>([]);
  const [intensityTrends, setIntensityTrends] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAllProgressData();
  }, [refreshTrigger]);

  const fetchAllProgressData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch all data in parallel
      const [workoutStats, completionData, intensityData, streak] = await Promise.all([
        workoutLoggingService.getWorkoutStats(),
        workoutLoggingService.getWeeklyCompletionRates(4),
        workoutLoggingService.getIntensityTrends(4),
        workoutLoggingService.calculateWorkoutStreak()
      ]);
      
      setStats({
        ...workoutStats,
        currentStreak: streak
      });
      setCompletionRates(completionData);
      setIntensityTrends(intensityData);
    } catch (error) {
      console.error('Error fetching detailed progress data:', error);
      setError('Failed to load progress data');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  if (isLoading) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading progress data...</ThemedText>
        </View>
      </ThemedCard>
    );
  }

  if (error) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.errorContainer}>
          <ThemedText style={[styles.errorText, { color: colors.error }]}>{error}</ThemedText>
        </View>
      </ThemedCard>
    );
  }

  // If no workouts logged yet
  if (stats.totalWorkouts === 0) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.emptyContainer}>
          <BarChart size={32} color={colors.primary} opacity={0.5} />
          <ThemedText style={styles.emptyTitle}>No Workout Data Yet</ThemedText>
          <ThemedText style={styles.emptyText}>
            Complete your first workout to start tracking your progress!
          </ThemedText>
        </View>
      </ThemedCard>
    );
  }

  return (
    <ThemedCard style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <BarChart size={20} color={colors.primary} />
          <ThemedText style={styles.title}>Detailed Progress</ThemedText>
        </View>

        <View style={styles.statsGrid}>
          <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
            <Calendar size={20} color={colors.primary} />
            <ThemedText style={styles.statValue}>{stats.totalWorkouts}</ThemedText>
            <ThemedText style={styles.statLabel}>Workouts</ThemedText>
          </View>
          
          <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
            <Clock size={20} color={colors.primary} />
            <ThemedText style={styles.statValue}>
              {formatDuration(stats.totalDuration)}
            </ThemedText>
            <ThemedText style={styles.statLabel}>Total Time</ThemedText>
          </View>
          
          <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
            <Target size={20} color={colors.primary} />
            <ThemedText style={styles.statValue}>
              {stats.averageRpe > 0 ? stats.averageRpe.toFixed(1) : '-'}
            </ThemedText>
            <ThemedText style={styles.statLabel}>Avg. RPE</ThemedText>
          </View>
        </View>

        {stats.currentStreak > 0 && (
          <View style={[styles.streakContainer, { backgroundColor: colors.success + '15' }]}>
            <Award size={20} color={colors.success} />
            <ThemedText style={[styles.streakText, { color: colors.success }]}>
              {stats.currentStreak} day streak! Keep it up!
            </ThemedText>
          </View>
        )}

        {/* Weekly Completion Chart */}
        <View style={styles.chartSection}>
          <CompletionRateChart data={completionRates} />
        </View>

        {/* Intensity Trend Chart */}
        <View style={styles.chartSection}>
          <IntensityTrendChart data={intensityTrends} />
        </View>

        {stats.lastWorkoutDate && (
          <View style={styles.lastWorkout}>
            <ThemedText style={styles.lastWorkoutLabel}>Last workout:</ThemedText>
            <ThemedText style={styles.lastWorkoutValue}>
              {new Date(stats.lastWorkoutDate).toLocaleDateString(undefined, {
                weekday: 'long',
                month: 'short',
                day: 'numeric'
              })}
            </ThemedText>
          </View>
        )}
      </ScrollView>
    </ThemedCard>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    opacity: 0.7,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 20,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 16,
  },
  statItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    gap: 8,
  },
  streakText: {
    fontSize: 14,
    fontWeight: '600',
  },
  chartSection: {
    marginBottom: 24,
  },
  lastWorkout: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  lastWorkoutLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  lastWorkoutValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});