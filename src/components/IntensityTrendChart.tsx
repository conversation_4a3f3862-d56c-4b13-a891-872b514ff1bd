import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText } from '@/components/ThemedComponents';
import { Target } from 'lucide-react-native';

interface IntensityTrendChartProps {
  data: {
    weekStartDate: string;
    averageRpe: number;
  }[];
}

export default function IntensityTrendChart({ data }: IntensityTrendChartProps) {
  const { colors } = useThemeStore();

  // Format date to show month and day
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  // Find max RPE value for scaling (default to 10 if no data)
  const maxRpe = Math.max(...data.map(week => week.averageRpe), 10);

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Target size={16} color={colors.primary} />
        <ThemedText style={styles.title}>Intensity Trend (RPE)</ThemedText>
      </View>
      
      <View style={styles.chartContainer}>
        {/* Y-axis labels */}
        <View style={styles.yAxis}>
          <ThemedText style={styles.yAxisLabel}>10</ThemedText>
          <ThemedText style={styles.yAxisLabel}>5</ThemedText>
          <ThemedText style={styles.yAxisLabel}>0</ThemedText>
        </View>
        
        {/* Chart content */}
        <View style={styles.chartContent}>
          {/* Horizontal grid lines */}
          <View style={[styles.gridLine, { top: 0 }]} />
          <View style={[styles.gridLine, { top: '50%' }]} />
          <View style={[styles.gridLine, { top: '100%' }]} />
          
          {/* Data points and connecting lines */}
          <View style={styles.dataPointsContainer}>
            {data.map((week, index) => {
              const heightPercentage = (week.averageRpe / 10) * 100;
              
              return (
                <View key={index} style={styles.dataColumn}>
                  {/* Data point */}
                  <View 
                    style={[
                      styles.dataPoint, 
                      { 
                        bottom: `${heightPercentage}%`,
                        backgroundColor: colors.primary,
                      }
                    ]}
                  >
                    <View style={styles.tooltipContainer}>
                      <ThemedText style={styles.tooltipText}>{week.averageRpe}</ThemedText>
                    </View>
                  </View>
                  
                  {/* X-axis label */}
                  <ThemedText style={styles.xAxisLabel}>
                    {formatDate(week.weekStartDate)}
                  </ThemedText>
                </View>
              );
            })}
          </View>
          
          {/* Connecting line (simplified) */}
          <View style={styles.connectingLinesContainer}>
            {data.map((week, index) => {
              if (index === 0) return null;
              
              const prevHeightPercentage = (data[index - 1].averageRpe / 10) * 100;
              const currHeightPercentage = (week.averageRpe / 10) * 100;
              
              // Calculate line position and angle
              const x1 = (index - 1) * (100 / (data.length - 1));
              const y1 = 100 - prevHeightPercentage;
              const x2 = index * (100 / (data.length - 1));
              const y2 = 100 - currHeightPercentage;
              
              return (
                <View 
                  key={index}
                  style={[
                    styles.connectingLine,
                    {
                      left: `${x1}%`,
                      top: `${y1}%`,
                      width: `${x2 - x1}%`,
                      height: Math.abs(y2 - y1),
                      transform: [
                        { translateY: y2 < y1 ? 0 : -Math.abs(y2 - y1) },
                        { rotate: y2 < y1 ? '-45deg' : '45deg' },
                        { translateX: 10 }
                      ],
                      backgroundColor: colors.primary,
                    }
                  ]}
                />
              );
            })}
          </View>
        </View>
      </View>
      
      <ThemedText style={styles.chartDescription}>
        Average Rate of Perceived Exertion (RPE) per week
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    marginBottom: 16,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  chartContainer: {
    flexDirection: 'row',
    height: 150,
    marginBottom: 8,
  },
  yAxis: {
    width: 20,
    height: '100%',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 4,
  },
  yAxisLabel: {
    fontSize: 10,
    opacity: 0.7,
  },
  chartContent: {
    flex: 1,
    height: '100%',
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#E5E5E5',
  },
  dataPointsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: '100%',
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    paddingBottom: 20, // Space for x-axis labels
  },
  dataColumn: {
    alignItems: 'center',
    flex: 1,
    height: '100%',
    position: 'relative',
  },
  dataPoint: {
    width: 8,
    height: 8,
    borderRadius: 4,
    position: 'absolute',
  },
  tooltipContainer: {
    position: 'absolute',
    top: -18,
    left: -8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  tooltipText: {
    color: 'white',
    fontSize: 8,
  },
  xAxisLabel: {
    position: 'absolute',
    bottom: -20,
    fontSize: 10,
    opacity: 0.7,
  },
  connectingLinesContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  connectingLine: {
    position: 'absolute',
    height: 2,
  },
  chartDescription: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.7,
    marginTop: 24,
  },
});