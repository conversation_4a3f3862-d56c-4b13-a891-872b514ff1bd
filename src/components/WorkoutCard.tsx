import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedCard, ThemedStatusIndicator } from '@/components/ThemedComponents';
import { Calendar, Clock, Target, User } from 'lucide-react-native';

interface WorkoutCardProps {
  program: {
    id: string;
    name: string;
    description: string;
    duration_weeks: number;
    status: string;
    created_at: string;
    coach_notes_for_client?: string;
    generated_by_ai_at?: string;
    coach_reviewed_at?: string;
  };
  onPress: () => void;
}

export default function WorkoutCard({ program, onPress }: WorkoutCardProps) {
  const { colors } = useThemeStore();

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'ai_generated_pending_review':
        return {
          icon: <Clock size={16} color={colors.warning} />,
          text: 'Being Personalized',
          statusType: 'warning' as const,
        };
      case 'coach_approved':
        return {
          icon: <Target size={16} color={colors.success} />,
          text: 'Ready to Start',
          statusType: 'success' as const,
        };
      case 'active_by_client':
        return {
          icon: <Target size={16} color={colors.primary} />,
          text: 'Active',
          statusType: 'primary' as const,
        };
      case 'completed_by_client':
        return {
          icon: <Target size={16} color={colors.success} />,
          text: 'Completed',
          statusType: 'success' as const,
        };
      default:
        return {
          icon: <Clock size={16} color={colors.text} />,
          text: 'Unknown',
          statusType: 'info' as const,
        };
    }
  };

  const statusInfo = getStatusInfo(program.status);

  return (
    <ThemedCard style={styles.card} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <ThemedText style={styles.title}>{program.name}</ThemedText>
          <ThemedStatusIndicator
            status={statusInfo.statusType}
            text={statusInfo.text}
            icon={statusInfo.icon}
            style={styles.statusBadge}
            textStyle={styles.statusText}
          />
        </View>
      </View>

      <ThemedText style={styles.description} numberOfLines={2}>
        {program.description}
      </ThemedText>

      <View style={styles.details}>
        <View style={styles.detailItem}>
          <Calendar size={14} color={colors.text} />
          <ThemedText style={styles.detailText}>
            {program.duration_weeks} weeks
          </ThemedText>
        </View>
        <View style={styles.detailItem}>
          <Clock size={14} color={colors.text} />
          <ThemedText style={styles.detailText}>
            Created {new Date(program.created_at).toLocaleDateString()}
          </ThemedText>
        </View>
      </View>

      {program.coach_notes_for_client && (
        <View style={[styles.coachNotes, { backgroundColor: colors.accent }]}>
          <View style={styles.coachNotesHeader}>
            <User size={12} color={colors.primary} />
            <ThemedText style={styles.coachNotesTitle}>Coach Notes:</ThemedText>
          </View>
          <ThemedText style={styles.coachNotesText} numberOfLines={2}>
            {program.coach_notes_for_client}
          </ThemedText>
        </View>
      )}
    </ThemedCard>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginBottom: 12,
  },
  header: {
    marginBottom: 8,
  },
  titleContainer: {
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 11,
    fontWeight: '600',
    marginLeft: 4,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
    opacity: 0.8,
  },
  details: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: 12,
    opacity: 0.7,
  },
  coachNotes: {
    padding: 10,
    borderRadius: 6,
  },
  coachNotesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 4,
  },
  coachNotesTitle: {
    fontSize: 12,
    fontWeight: '600',
  },
  coachNotesText: {
    fontSize: 12,
    lineHeight: 16,
  },
});