import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { workoutLoggingService } from '@/services/workoutLoggingService';
import { ThemedText, ThemedCard } from '@/components/ThemedComponents';
import { Calendar, Clock, Target, Award, ChartBar as BarChart } from 'lucide-react-native';

interface ProgressOverviewProps {
  refreshTrigger?: number; // Optional prop to trigger refresh
}

export default function ProgressOverview({ refreshTrigger }: ProgressOverviewProps) {
  const { colors } = useThemeStore();
  const [stats, setStats] = useState({
    totalWorkouts: 0,
    totalDuration: 0,
    averageRpe: 0,
    lastWorkoutDate: undefined as string | undefined,
    currentStreak: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProgressData();
  }, [refreshTrigger]);

  const fetchProgressData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Fetch workout statistics
      const workoutStats = await workoutLoggingService.getWorkoutStats();
      
      // Calculate streak (this would ideally be done server-side)
      const streak = calculateStreak(workoutStats.lastWorkoutDate);
      
      setStats({
        ...workoutStats,
        currentStreak: streak
      });
    } catch (error) {
      console.error('Error fetching progress data:', error);
      setError('Failed to load progress data');
    } finally {
      setIsLoading(false);
    }
  };

  // Simple streak calculation based on last workout date
  // In a real implementation, this would be more sophisticated and server-side
  const calculateStreak = (lastWorkoutDate?: string): number => {
    if (!lastWorkoutDate) return 0;
    
    const lastWorkout = new Date(lastWorkoutDate);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // If last workout was today or yesterday, consider streak active
    if (lastWorkout.toDateString() === today.toDateString() || 
        lastWorkout.toDateString() === yesterday.toDateString()) {
      // For demo purposes, return a number based on total workouts
      // In a real app, this would be calculated from consecutive workout days
      return Math.min(Math.max(1, Math.floor(stats.totalWorkouts / 2)), 30);
    }
    
    return 0;
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  if (isLoading) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading progress data...</ThemedText>
        </View>
      </ThemedCard>
    );
  }

  if (error) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.errorContainer}>
          <ThemedText style={[styles.errorText, { color: colors.error }]}>{error}</ThemedText>
        </View>
      </ThemedCard>
    );
  }

  // If no workouts logged yet
  if (stats.totalWorkouts === 0) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.emptyContainer}>
          <BarChart size={32} color={colors.primary} opacity={0.5} />
          <ThemedText style={styles.emptyTitle}>No Workout Data Yet</ThemedText>
          <ThemedText style={styles.emptyText}>
            Complete your first workout to start tracking your progress!
          </ThemedText>
        </View>
      </ThemedCard>
    );
  }

  return (
    <ThemedCard style={styles.container}>
      <View style={styles.header}>
        <BarChart size={20} color={colors.primary} />
        <ThemedText style={styles.title}>Progress Overview</ThemedText>
      </View>
      
      <ThemedText style={styles.description}>
        These metrics show you your lifetime training metrics and average RPE to gauge your consistency and intensity over time.
      </ThemedText>

      <View style={styles.statsGrid}>
        <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
          <Calendar size={20} color={colors.primary} />
          <ThemedText style={styles.statValue}>{stats.totalWorkouts}</ThemedText>
          <ThemedText style={styles.statLabel}>Total workouts</ThemedText>
        </View>
        
        <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
          <Clock size={20} color={colors.primary} />
          <ThemedText style={styles.statValue}>
            {formatDuration(stats.totalDuration)}
          </ThemedText>
          <ThemedText style={styles.statLabel}>Total training time</ThemedText>
        </View>
        
        <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
          <Target size={20} color={colors.primary} />
          <ThemedText style={styles.statValue}>
            {stats.averageRpe > 0 ? stats.averageRpe.toFixed(1) : '-'}
          </ThemedText>
          <ThemedText style={styles.statLabel}>Avg. RPE</ThemedText>
        </View>
      </View>

      {stats.currentStreak > 0 && (
        <View style={[styles.streakContainer, { backgroundColor: colors.success + '15' }]}>
          <Award size={20} color={colors.success} />
          <ThemedText style={[styles.streakText, { color: colors.success }]}>
            {stats.currentStreak} day streak! Keep it up!
          </ThemedText>
        </View>
      )}

      {stats.lastWorkoutDate && (
        <View style={styles.lastWorkout}>
          <ThemedText style={styles.lastWorkoutLabel}>Last workout:</ThemedText>
          <ThemedText style={styles.lastWorkoutValue}>
            {new Date(stats.lastWorkoutDate).toLocaleDateString(undefined, {
              weekday: 'long',
              month: 'short',
              day: 'numeric'
            })}
          </ThemedText>
        </View>
      )}
    </ThemedCard>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 16,
    lineHeight: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    opacity: 0.7,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 20,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 16,
  },
  statItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
  },
  streakText: {
    fontSize: 14,
    fontWeight: '600',
  },
  lastWorkout: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastWorkoutLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  lastWorkoutValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});