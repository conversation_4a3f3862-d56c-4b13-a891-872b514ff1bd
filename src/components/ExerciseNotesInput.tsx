import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedInput, ThemedButton } from '@/components/ThemedComponents';
import { MessageSquare, Save } from 'lucide-react-native';

interface ExerciseNotesInputProps {
  initialNotes?: string;
  onSaveNotes: (notes: string) => void;
  disabled?: boolean;
}

export default function ExerciseNotesInput({
  initialNotes = '',
  onSaveNotes,
  disabled = false,
}: ExerciseNotesInputProps) {
  const { colors } = useThemeStore();
  const [notes, setNotes] = useState(initialNotes);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSaveNotes(notes);
    setIsEditing(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <MessageSquare size={16} color={colors.primary} />
          <ThemedText style={styles.title}>Exercise Notes</ThemedText>
        </View>
        
        {!isEditing && (
          <ThemedButton
            title="Add Notes"
            onPress={() => setIsEditing(true)}
            variant="outline"
            style={styles.editButton}
            disabled={disabled}
          />
        )}
      </View>

      {isEditing ? (
        <View style={styles.editContainer}>
          <ThemedInput
            value={notes}
            onChangeText={setNotes}
            placeholder="Add notes about this exercise (technique cues, how it felt, etc.)"
            multiline
            numberOfLines={3}
            textAlignVertical="top"
            style={styles.input}
          />
          <ThemedButton
            title="Save Notes"
            onPress={handleSave}
            style={styles.saveButton}
          />
        </View>
      ) : (
        <ThemedText style={styles.notesText}>
          {notes || 'No notes added for this exercise.'}
        </ThemedText>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  editButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  editContainer: {
    gap: 8,
  },
  input: {
    minHeight: 80,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
});