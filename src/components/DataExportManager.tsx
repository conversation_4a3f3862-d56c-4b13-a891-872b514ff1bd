import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Share,
  ActivityIndicator,
} from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import {
  Download,
  FileText,
  BarChart3,
  Calendar,
  Target,
  Camera,
  Share2,
  Mail,
  CheckCircle,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

interface ExportOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  format: 'csv' | 'json' | 'pdf';
  dataType: 'workouts' | 'measurements' | 'goals' | 'photos' | 'all';
}

const EXPORT_OPTIONS: ExportOption[] = [
  {
    id: 'workouts_csv',
    title: 'Workout History (CSV)',
    description: 'Export all workout logs with sets, reps, and weights',
    icon: <BarChart3 size={24} />,
    format: 'csv',
    dataType: 'workouts',
  },
  {
    id: 'measurements_csv',
    title: 'Body Measurements (CSV)',
    description: 'Export body measurements and progress tracking data',
    icon: <Target size={24} />,
    format: 'csv',
    dataType: 'measurements',
  },
  {
    id: 'goals_csv',
    title: 'Goals & Progress (CSV)',
    description: 'Export fitness goals and achievement data',
    icon: <CheckCircle size={24} />,
    format: 'csv',
    dataType: 'goals',
  },
  {
    id: 'complete_json',
    title: 'Complete Data (JSON)',
    description: 'Export all data in structured JSON format',
    icon: <FileText size={24} />,
    format: 'json',
    dataType: 'all',
  },
  {
    id: 'progress_report_pdf',
    title: 'Progress Report (PDF)',
    description: 'Generate a comprehensive progress report',
    icon: <Calendar size={24} />,
    format: 'pdf',
    dataType: 'all',
  },
];

export const DataExportManager: React.FC = () => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [isExporting, setIsExporting] = useState<string | null>(null);

  const exportWorkoutData = async (): Promise<string> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    // Get workout logs with related data
    const { data: workoutLogs, error } = await supabase
      .from('workout_logs')
      .select(`
        *,
        workout_programs!inner(name),
        exercise_logs!inner(
          *,
          exercises!inner(name),
          set_logs!inner(*)
        )
      `)
      .eq('user_id', user.id)
      .order('completed_at', { ascending: false });

    if (error) throw error;

    // Convert to CSV format
    const csvHeaders = [
      'Date',
      'Program',
      'Duration (min)',
      'Exercise',
      'Set',
      'Reps',
      'Weight (kg)',
      'RPE',
      'Notes'
    ];

    const csvRows = [csvHeaders.join(',')];

    workoutLogs?.forEach(workout => {
      workout.exercise_logs?.forEach(exerciseLog => {
        exerciseLog.set_logs?.forEach((setLog, setIndex) => {
          const row = [
            new Date(workout.completed_at || workout.started_at).toLocaleDateString(),
            `"${workout.workout_programs?.name || 'Unknown'}"`,
            Math.round((workout.duration_seconds || 0) / 60),
            `"${exerciseLog.exercises?.name || 'Unknown'}"`,
            setIndex + 1,
            setLog.actual_reps || 0,
            setLog.actual_weight_kg || 0,
            setLog.rpe || '',
            `"${setLog.notes || ''}"`,
          ];
          csvRows.push(row.join(','));
        });
      });
    });

    return csvRows.join('\n');
  };

  const exportMeasurementData = async (): Promise<string> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    const { data: measurements, error } = await supabase
      .from('body_measurements')
      .select('*')
      .eq('user_id', user.id)
      .order('measurement_date', { ascending: false });

    if (error) throw error;

    const csvHeaders = [
      'Date',
      'Weight (kg)',
      'Height (cm)',
      'Body Fat (%)',
      'Muscle Mass (kg)',
      'Chest (cm)',
      'Waist (cm)',
      'Hips (cm)',
      'Bicep Left (cm)',
      'Bicep Right (cm)',
      'Thigh Left (cm)',
      'Thigh Right (cm)',
      'Neck (cm)',
      'Resting HR (bpm)',
      'Notes'
    ];

    const csvRows = [csvHeaders.join(',')];

    measurements?.forEach(measurement => {
      const row = [
        measurement.measurement_date,
        measurement.weight_kg || '',
        measurement.height_cm || '',
        measurement.body_fat_percentage || '',
        measurement.muscle_mass_kg || '',
        measurement.chest_cm || '',
        measurement.waist_cm || '',
        measurement.hips_cm || '',
        measurement.bicep_left_cm || '',
        measurement.bicep_right_cm || '',
        measurement.thigh_left_cm || '',
        measurement.thigh_right_cm || '',
        measurement.neck_cm || '',
        measurement.resting_heart_rate || '',
        `"${measurement.notes || ''}"`,
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  };

  const exportGoalsData = async (): Promise<string> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    const { data: goals, error } = await supabase
      .from('fitness_goals')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    const csvHeaders = [
      'Title',
      'Type',
      'Description',
      'Current Value',
      'Target Value',
      'Unit',
      'Progress (%)',
      'Priority',
      'Status',
      'Target Date',
      'Created Date',
      'Notes'
    ];

    const csvRows = [csvHeaders.join(',')];

    goals?.forEach(goal => {
      const row = [
        `"${goal.title}"`,
        goal.goal_type,
        `"${goal.description || ''}"`,
        goal.current_value || 0,
        goal.target_value || 0,
        goal.unit || '',
        goal.progress_percentage || 0,
        goal.priority,
        goal.status,
        goal.target_date || '',
        new Date(goal.created_at).toLocaleDateString(),
        `"${goal.notes || ''}"`,
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  };

  const exportAllDataAsJSON = async (): Promise<string> => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    // Get all data types
    const [workoutLogs, measurements, goals, progressPhotos] = await Promise.all([
      supabase
        .from('workout_logs')
        .select(`
          *,
          workout_programs!inner(name),
          exercise_logs!inner(
            *,
            exercises!inner(name),
            set_logs!inner(*)
          )
        `)
        .eq('user_id', user.id),
      supabase
        .from('body_measurements')
        .select('*')
        .eq('user_id', user.id),
      supabase
        .from('fitness_goals')
        .select('*')
        .eq('user_id', user.id),
      supabase
        .from('progress_photos')
        .select('*')
        .eq('user_id', user.id),
    ]);

    const exportData = {
      exportDate: new Date().toISOString(),
      userId: user.id,
      data: {
        workoutLogs: workoutLogs.data || [],
        measurements: measurements.data || [],
        goals: goals.data || [],
        progressPhotos: progressPhotos.data || [],
      },
      summary: {
        totalWorkouts: workoutLogs.data?.length || 0,
        totalMeasurements: measurements.data?.length || 0,
        totalGoals: goals.data?.length || 0,
        totalPhotos: progressPhotos.data?.length || 0,
      },
    };

    return JSON.stringify(exportData, null, 2);
  };

  const generateProgressReport = async (): Promise<string> => {
    // This would generate a PDF report - for now, we'll create a detailed text report
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    const reportData = await exportAllDataAsJSON();
    const data = JSON.parse(reportData);

    const report = `
FITNESS PROGRESS REPORT
Generated: ${new Date().toLocaleDateString()}

SUMMARY
=======
Total Workouts: ${data.summary.totalWorkouts}
Total Measurements: ${data.summary.totalMeasurements}
Active Goals: ${data.data.goals.filter((g: any) => g.status === 'active').length}
Completed Goals: ${data.data.goals.filter((g: any) => g.status === 'completed').length}
Progress Photos: ${data.summary.totalPhotos}

RECENT ACTIVITY
===============
${data.data.workoutLogs.slice(0, 5).map((workout: any) => 
  `- ${new Date(workout.completed_at || workout.started_at).toLocaleDateString()}: ${workout.workout_programs?.name || 'Workout'} (${Math.round((workout.duration_seconds || 0) / 60)} min)`
).join('\n')}

CURRENT GOALS
=============
${data.data.goals.filter((g: any) => g.status === 'active').slice(0, 5).map((goal: any) => 
  `- ${goal.title}: ${goal.current_value}/${goal.target_value} ${goal.unit} (${goal.progress_percentage.toFixed(0)}%)`
).join('\n')}

This report was generated by your fitness tracking app.
    `.trim();

    return report;
  };

  const handleExport = async (option: ExportOption) => {
    try {
      setIsExporting(option.id);

      await executeOperation(
        async () => {
          let content: string;
          let filename: string;
          let mimeType: string;

          switch (option.dataType) {
            case 'workouts':
              content = await exportWorkoutData();
              filename = `workout_history_${new Date().toISOString().split('T')[0]}.csv`;
              mimeType = 'text/csv';
              break;
            case 'measurements':
              content = await exportMeasurementData();
              filename = `body_measurements_${new Date().toISOString().split('T')[0]}.csv`;
              mimeType = 'text/csv';
              break;
            case 'goals':
              content = await exportGoalsData();
              filename = `fitness_goals_${new Date().toISOString().split('T')[0]}.csv`;
              mimeType = 'text/csv';
              break;
            case 'all':
              if (option.format === 'json') {
                content = await exportAllDataAsJSON();
                filename = `complete_fitness_data_${new Date().toISOString().split('T')[0]}.json`;
                mimeType = 'application/json';
              } else {
                content = await generateProgressReport();
                filename = `progress_report_${new Date().toISOString().split('T')[0]}.txt`;
                mimeType = 'text/plain';
              }
              break;
            default:
              throw new Error('Unknown export type');
          }

          // Save file to device
          const fileUri = FileSystem.documentDirectory + filename;
          await FileSystem.writeAsStringAsync(fileUri, content);

          // Share the file
          if (await Sharing.isAvailableAsync()) {
            await Sharing.shareAsync(fileUri, {
              mimeType,
              dialogTitle: `Share ${option.title}`,
            });
          } else {
            // Fallback to native share
            await Share.share({
              message: content,
              title: option.title,
            });
          }

          Alert.alert('Success', `${option.title} exported successfully!`);
        },
        {
          type: 'DATA_EXPORT',
          payload: {
            exportType: option.dataType,
            format: option.format,
            timestamp: new Date().toISOString(),
          },
        }
      );
    } catch (error) {
      console.error('Export failed:', error);
      Alert.alert('Export Failed', 'Failed to export data. Please try again.');
    } finally {
      setIsExporting(null);
    }
  };

  const renderExportOption = (option: ExportOption) => (
    <TouchableOpacity
      key={option.id}
      style={[styles.exportOption, { backgroundColor: colors.accent }]}
      onPress={() => handleExport(option)}
      disabled={isExporting === option.id}
    >
      <View style={[styles.optionIcon, { backgroundColor: colors.primary + '20' }]}>
        {React.cloneElement(option.icon as React.ReactElement, { color: colors.primary })}
      </View>
      
      <View style={styles.optionContent}>
        <Text style={[styles.optionTitle, { color: colors.text }]}>
          {option.title}
        </Text>
        <Text style={[styles.optionDescription, { color: colors.text + '80' }]}>
          {option.description}
        </Text>
      </View>

      <View style={styles.optionAction}>
        {isExporting === option.id ? (
          <ActivityIndicator size="small" color={colors.primary} />
        ) : (
          <Download size={20} color={colors.primary} />
        )}
      </View>
    </TouchableOpacity>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    headerDescription: {
      fontSize: 16,
      color: colors.text + '80',
      lineHeight: 24,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    exportOptions: {
      gap: 16,
    },
    exportOption: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 20,
      borderRadius: 16,
      gap: 16,
    },
    optionIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    optionContent: {
      flex: 1,
    },
    optionTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    optionDescription: {
      fontSize: 14,
      lineHeight: 20,
    },
    optionAction: {
      width: 32,
      height: 32,
      justifyContent: 'center',
      alignItems: 'center',
    },
    infoSection: {
      backgroundColor: colors.primary + '10',
      padding: 16,
      borderRadius: 12,
      marginBottom: 24,
    },
    infoTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.primary,
      marginBottom: 8,
    },
    infoText: {
      fontSize: 14,
      color: colors.text + '80',
      lineHeight: 20,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Export Data</Text>
        <Text style={styles.headerDescription}>
          Export your fitness data in various formats for backup or analysis
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>📊 Data Privacy</Text>
          <Text style={styles.infoText}>
            Your exported data is processed locally on your device. We recommend reviewing the content before sharing with third parties.
          </Text>
        </View>

        <View style={styles.exportOptions}>
          {EXPORT_OPTIONS.map(renderExportOption)}
        </View>
      </ScrollView>
    </View>
  );
};
