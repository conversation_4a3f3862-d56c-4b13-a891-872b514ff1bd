import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useCheckInStore } from '@/store/checkInStore';
import { useUserStore } from '@/store/userStore';
import { ThemedText, ThemedCard, ThemedStatusIndicator } from '@/components/ThemedComponents';
import { ClipboardCheck, ArrowRight, Calendar, Clock } from 'lucide-react-native';

interface WeeklyCheckInPromptProps {
  onPress?: () => void;
}

export default function WeeklyCheckInPrompt({ onPress }: WeeklyCheckInPromptProps) {
  const { colors } = useThemeStore();
  const { profile } = useUserStore();
  const { isCheckInWindowOpen, hasSubmittedCurrentCheckIn, currentCheckInDate, determineCheckInWindowStatus } = useCheckInStore();
  const [isLoading, setIsLoading] = useState(true);
  const [nextCheckInDate, setNextCheckInDate] = useState<string>('');
  const [isInGracePeriod, setIsInGracePeriod] = useState(false);

  useEffect(() => {
    const checkStatus = async () => {
      setIsLoading(true);
      try {
        // Check if window is open and if user has already submitted
        await determineCheckInWindowStatus();

        // Calculate next check-in date if window is closed
        if (!isCheckInWindowOpen) {
          calculateNextCheckInDate();
        }

        // Check if user is in grace period (account created less than 3 days ago)
        if (profile?.created_at) {
          const createdAtDate = new Date(profile.created_at);
          const now = new Date();
          const gracePeriodEndDate = new Date(createdAtDate);
          gracePeriodEndDate.setDate(createdAtDate.getDate() + 3); // 3-day grace period
          
          setIsInGracePeriod(now < gracePeriodEndDate);
        }
      } catch (error) {
        console.error('Error checking check-in status:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkStatus();
  }, [determineCheckInWindowStatus, isCheckInWindowOpen, profile?.created_at]);

  const calculateNextCheckInDate = () => {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysUntilNextSunday = dayOfWeek === 0 ? 7 : 7 - dayOfWeek;
    
    const nextSunday = new Date(now);
    nextSunday.setDate(now.getDate() + daysUntilNextSunday);
    
    // Format the date
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric' 
    };
    setNextCheckInDate(nextSunday.toLocaleDateString(undefined, options));
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push('/checkin/form');
    }
  };

  if (isLoading) {
    return null; // Don't show anything while loading
  }

  // If user is in grace period, show special message
  if (isInGracePeriod) {
    // Calculate when the grace period ends
    const createdAtDate = new Date(profile?.created_at || new Date());
    const gracePeriodEndDate = new Date(createdAtDate);
    gracePeriodEndDate.setDate(createdAtDate.getDate() + 3);
    
    const formattedGracePeriodEndDate = gracePeriodEndDate.toLocaleDateString(undefined, {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });

    return (
      <ThemedCard style={styles.container}>
        <View style={styles.content}>
          <View style={[styles.iconContainer, { backgroundColor: colors.info + '20' }]}>
            <Clock size={24} color={colors.info} />
          </View>
          <View style={styles.textContainer}>
            <ThemedText style={styles.title}>Check-ins Coming Soon</ThemedText>
            <ThemedText style={styles.message}>
              Weekly check-ins will be available next week. This gives you time to start and build momentum with your fitness program first.
            </ThemedText>
          </View>
        </View>
      </ThemedCard>
    );
  }

  if (!isCheckInWindowOpen) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.content}>
          <View style={[styles.iconContainer, { backgroundColor: colors.accent }]}>
            <Calendar size={24} color={colors.primary} />
          </View>
          <View style={styles.textContainer}>
            <ThemedText style={styles.title}>Next Check-in</ThemedText>
            <ThemedText style={styles.message}>
              Your next weekly check-in will be available on {nextCheckInDate} at 6 AM.
            </ThemedText>
          </View>
        </View>
      </ThemedCard>
    );
  }

  if (hasSubmittedCurrentCheckIn) {
    return (
      <ThemedCard style={styles.container}>
        <View style={styles.content}>
          <View style={[styles.iconContainer, { backgroundColor: colors.success + '20' }]}>
            <ClipboardCheck size={24} color={colors.success} />
          </View>
          <View style={styles.textContainer}>
            <ThemedText style={styles.title}>Weekly Check-in Complete</ThemedText>
            <ThemedText style={styles.message}>
              Thanks for submitting your weekly check-in! Your coach will review it soon.
            </ThemedText>
          </View>
        </View>
        <ThemedStatusIndicator
          status="success"
          text="Completed"
          icon={<ClipboardCheck size={14} color={colors.success} />}
          style={[styles.statusBadge, { backgroundColor: colors.success + '15' }]}
          textStyle={styles.statusText}
        />
      </ThemedCard>
    );
  }

  return (
    <ThemedCard style={styles.container}>
      <View style={styles.content}>
        <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
          <ClipboardCheck size={24} color={colors.primary} />
        </View>
        <View style={styles.textContainer}>
          <ThemedText style={styles.title}>Weekly Check-in Available</ThemedText>
          <ThemedText style={styles.message}>
            It's Sunday! Take a moment to reflect on your week and share your progress with your coach.
          </ThemedText>
        </View>
      </View>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: colors.primary }]}
        onPress={handlePress}
      >
        <ThemedText style={[styles.buttonText, { color: colors.contrastText }]}>
          Complete Check-in
        </ThemedText>
        <ArrowRight size={16} color={colors.contrastText} />
      </TouchableOpacity>
    </ThemedCard>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    opacity: 0.8,
    lineHeight: 20,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    marginLeft: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
});