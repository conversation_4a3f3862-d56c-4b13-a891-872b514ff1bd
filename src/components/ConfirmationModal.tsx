import React from 'react';
import { View, StyleSheet, Modal, ActivityIndicator } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedView, ThemedButton, ThemedOverlay } from '@/components/ThemedComponents';

interface ConfirmationModalProps {
  isVisible: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  isConfirming?: boolean;
  confirmButtonVariant?: 'primary' | 'secondary' | 'outline';
}

export default function ConfirmationModal({
  isVisible,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  isConfirming = false,
  confirmButtonVariant = 'primary',
}: ConfirmationModalProps) {
  const { colors } = useThemeStore();

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <ThemedOverlay style={styles.overlay}>
        <ThemedView style={styles.modalContainer} variant="card">
          <View style={styles.modalContent}>
            <ThemedText style={styles.title}>{title}</ThemedText>
            <ThemedText style={styles.message}>{message}</ThemedText>

            <View style={styles.buttonContainer}>
              <ThemedButton
                title={cancelText}
                onPress={onCancel}
                variant="outline"
                style={styles.button}
                disabled={isConfirming}
              />

              <ThemedButton
                title={isConfirming ? '' : confirmText}
                onPress={onConfirm}
                variant={confirmButtonVariant}
                style={styles.button}
                disabled={isConfirming}
              />

              {isConfirming && (
                <View style={styles.loadingOverlay}>
                  <ActivityIndicator color={colors.contrastText} size="small" />
                </View>
              )}
            </View>
          </View>
        </ThemedView>
      </ThemedOverlay>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
    maxWidth: 400,
    width: '100%',
  },
  modalContent: {
    padding: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.8,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    position: 'relative',
  },
  button: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});