import React, { useEffect } from 'react';
import { View, StyleSheet, Modal, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { useActiveWorkoutStore } from '@/store/activeWorkoutStore';
import { ThemedText, ThemedView, ThemedButton, ThemedOverlay } from '@/components/ThemedComponents';
import { Timer, Play, Pause, SkipForward, X } from 'lucide-react-native';

interface RestTimerModalProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function RestTimerModal({ isVisible, onClose }: RestTimerModalProps) {
  const { colors } = useThemeStore();
  const { restTimer, stopRestTimer, updateRestTimer } = useActiveWorkoutStore();

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (restTimer.isActive && restTimer.remaining > 0) {
      interval = setInterval(() => {
        updateRestTimer();
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [restTimer.isActive, restTimer.remaining, updateRestTimer]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    if (restTimer.duration === 0) return 0;
    return ((restTimer.duration - restTimer.remaining) / restTimer.duration) * 100;
  };

  const handleSkip = () => {
    stopRestTimer();
    onClose();
  };

  const handleClose = () => {
    stopRestTimer();
    onClose();
  };

  if (!restTimer.isActive && restTimer.remaining <= 0) {
    return null;
  }

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <ThemedOverlay style={styles.overlay}>
        <ThemedView style={styles.modalContainer} variant="card">
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Timer size={24} color={colors.primary} />
              <ThemedText style={styles.title}>Rest Timer</ThemedText>
            </View>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleClose}
            >
              <X size={20} color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.timerContainer}>
            {/* Circular Progress */}
            <View style={styles.circularProgress}>
              <View
                style={[
                  styles.progressRing,
                  { borderColor: colors.accent },
                ]}
              >
                <View
                  style={[
                    styles.progressFill,
                    {
                      borderColor: colors.primary,
                      transform: [{ rotate: `${(getProgressPercentage() * 3.6)}deg` }],
                    },
                  ]}
                />
              </View>
              <View style={styles.timerTextContainer}>
                <ThemedText style={styles.timerText}>
                  {formatTime(restTimer.remaining)}
                </ThemedText>
                <ThemedText style={styles.timerSubtext}>
                  of {formatTime(restTimer.duration)}
                </ThemedText>
              </View>
            </View>

            {/* Status */}
            {restTimer.remaining <= 0 ? (
              <View style={[styles.statusContainer, { backgroundColor: colors.success + '20' }]}>
                <ThemedText style={[styles.statusText, { color: colors.success }]}>
                  Rest Complete! 🎉
                </ThemedText>
              </View>
            ) : (
              <View style={[styles.statusContainer, { backgroundColor: colors.primary + '20' }]}>
                <ThemedText style={[styles.statusText, { color: colors.primary }]}>
                  Take your rest
                </ThemedText>
              </View>
            )}
          </View>

          <View style={styles.actions}>
            <ThemedButton
              title="Skip Rest"
              onPress={handleSkip}
              variant="outline"
              style={styles.actionButton}
            />
            
            {restTimer.remaining <= 0 && (
              <ThemedButton
                title="Continue"
                onPress={onClose}
                style={[styles.actionButton, { backgroundColor: colors.success }]}
              />
            )}
          </View>
        </ThemedView>
      </ThemedOverlay>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
    maxWidth: 400,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  timerContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  circularProgress: {
    position: 'relative',
    marginBottom: 20,
  },
  progressRing: {
    width: 160,
    height: 160,
    borderRadius: 80,
    borderWidth: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressFill: {
    position: 'absolute',
    width: 160,
    height: 160,
    borderRadius: 80,
    borderWidth: 8,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
    transformOrigin: 'center',
  },
  timerTextContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerText: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  timerSubtext: {
    fontSize: 14,
    opacity: 0.7,
  },
  statusContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 0,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});