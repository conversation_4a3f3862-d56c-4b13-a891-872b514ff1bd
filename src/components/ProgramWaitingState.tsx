import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Clock, CheckCircle, AlertCircle, MessageCircle, RefreshCw } from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';

interface ProgramWaitingStateProps {
  programId?: string;
  submittedDate?: string;
  estimatedReviewTime?: string;
  onContactSupport?: () => void;
  onRefresh?: () => void;
}

export const ProgramWaitingState: React.FC<ProgramWaitingStateProps> = ({
  programId,
  submittedDate,
  estimatedReviewTime = '24-48 hours',
  onContactSupport,
  onRefresh,
}) => {
  const { colors } = useThemeStore();

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Recently';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Recently';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <LinearGradient
        colors={[colors.primary + '15', colors.accent]}
        style={styles.waitingCard}
      >
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
            <Clock size={24} color={colors.primary} />
          </View>
          <Text style={[styles.title, { color: colors.text }]}>
            Your Program is Almost Ready
          </Text>
        </View>

        <Text style={[styles.description, { color: colors.text + 'CC' }]}>
          Your coach is putting the finishing touches on your personalized program, ensuring it perfectly matches your goals and preferences.
        </Text>

        <View style={styles.statusContainer}>
          <View style={styles.statusItem}>
            <CheckCircle size={16} color={colors.success} />
            <Text style={[styles.statusText, { color: colors.text + 'DD' }]}>
              Program Created
            </Text>
          </View>

          <View style={styles.statusItem}>
            <Clock size={16} color={colors.warning} />
            <Text style={[styles.statusText, { color: colors.text + 'DD' }]}>
              Coach Personalizing
            </Text>
          </View>

          <View style={[styles.statusItem, { opacity: 0.5 }]}>
            <AlertCircle size={16} color={colors.text + '60'} />
            <Text style={[styles.statusText, { color: colors.text + '60' }]}>
              Ready to Start
            </Text>
          </View>
        </View>

        <View style={styles.timelineContainer}>
          <View style={styles.timelineItem}>
            <Text style={[styles.timelineLabel, { color: colors.text + '80' }]}>
              Submitted
            </Text>
            <Text style={[styles.timelineValue, { color: colors.text }]}>
              {formatDate(submittedDate)}
            </Text>
          </View>
          
          <View style={styles.timelineItem}>
            <Text style={[styles.timelineLabel, { color: colors.text + '80' }]}>
              Estimated Review Time
            </Text>
            <Text style={[styles.timelineValue, { color: colors.primary }]}>
              {estimatedReviewTime}
            </Text>
          </View>
        </View>

        <View style={styles.actionContainer}>
          <Text style={[styles.nextStepsTitle, { color: colors.text }]}>
            What happens next?
          </Text>
          <Text style={[styles.nextStepsText, { color: colors.text + 'CC' }]}>
            • Your coach is adding personal touches to ensure it's perfect for you{'\n'}
            • You'll be notified as soon as it's ready{'\n'}
            • Your personalized program will then be available in the Workouts tab
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          {onRefresh && (
            <TouchableOpacity
              style={[styles.secondaryButton, { borderColor: colors.primary }]}
              onPress={onRefresh}
            >
              <RefreshCw size={16} color={colors.primary} />
              <Text style={[styles.secondaryButtonText, { color: colors.primary }]}>
                Refresh
              </Text>
            </TouchableOpacity>
          )}
          
          {onContactSupport && (
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: colors.primary }]}
              onPress={onContactSupport}
            >
              <MessageCircle size={16} color={colors.overlayText} />
              <Text style={[styles.primaryButtonText, { color: colors.overlayText }]}>
                Contact Support
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  waitingCard: {
    borderRadius: 16,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 24,
  },
  statusContainer: {
    marginBottom: 24,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    marginLeft: 8,
  },
  timelineContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    paddingHorizontal: 8,
  },
  timelineItem: {
    flex: 1,
    alignItems: 'center',
  },
  timelineLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  timelineValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionContainer: {
    marginBottom: 24,
  },
  nextStepsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  nextStepsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  primaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
