import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Vibration,
  AppState,
  AppStateStatus,
} from 'react-native';
import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';
import {
  Play,
  Pause,
  RotateCcw,
  Plus,
  Minus,
  Volume2,
  VolumeX,
  Timer,
  SkipForward,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';

interface RestTimerProps {
  initialTime?: number; // in seconds
  onComplete?: () => void;
  onSkip?: () => void;
  onTimeChange?: (time: number) => void;
  autoStart?: boolean;
  showControls?: boolean;
  exerciseName?: string;
  nextExerciseName?: string;
}

export const RestTimer: React.FC<RestTimerProps> = ({
  initialTime = 60,
  onComplete,
  onSkip,
  onTimeChange,
  autoStart = false,
  showControls = true,
  exerciseName,
  nextExerciseName,
}) => {
  const { colors } = useThemeStore();
  const [timeRemaining, setTimeRemaining] = useState(initialTime);
  const [isRunning, setIsRunning] = useState(autoStart);
  const [isMuted, setIsMuted] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [appState, setAppState] = useState(AppState.currentState);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const backgroundTimeRef = useRef<number>(Date.now());
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    loadSound();
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
      if (sound) {
        sound.unloadAsync();
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isRunning) {
      startTimer();
    } else {
      stopTimer();
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning]);

  useEffect(() => {
    onTimeChange?.(timeRemaining);
    
    if (timeRemaining <= 0 && isRunning) {
      handleTimerComplete();
    } else if (timeRemaining <= 10 && isRunning) {
      // Start pulsing animation for last 10 seconds
      startPulseAnimation();
    }
  }, [timeRemaining, isRunning]);

  const loadSound = async () => {
    try {
      const { sound: newSound } = await Audio.Sound.createAsync(
        // You would replace this with an actual sound file
        { uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav' },
        { shouldPlay: false }
      );
      setSound(newSound);
    } catch (error) {
      console.warn('Failed to load timer sound:', error);
    }
  };

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      // App came back to foreground
      if (isRunning) {
        const timeInBackground = Math.floor((Date.now() - backgroundTimeRef.current) / 1000);
        const newTime = Math.max(0, timeRemaining - timeInBackground);
        setTimeRemaining(newTime);
        
        if (newTime <= 0) {
          handleTimerComplete();
        }
      }
    } else if (nextAppState.match(/inactive|background/)) {
      // App went to background
      backgroundTimeRef.current = Date.now();
    }
    
    setAppState(nextAppState);
  };

  const startTimer = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 1) {
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const stopTimer = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  const handleTimerComplete = () => {
    setIsRunning(false);
    playCompletionAlert();
    onComplete?.();
  };

  const playCompletionAlert = async () => {
    if (!isMuted) {
      // Play sound
      try {
        if (sound) {
          await sound.replayAsync();
        }
      } catch (error) {
        console.warn('Failed to play completion sound:', error);
      }
      
      // Haptic feedback
      try {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } catch (error) {
        console.warn('Failed to trigger haptic feedback:', error);
      }
      
      // Vibration pattern
      Vibration.vibrate([0, 500, 200, 500]);
    }
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const handlePlayPause = () => {
    setIsRunning(!isRunning);
    
    // Scale animation for button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleReset = () => {
    setIsRunning(false);
    setTimeRemaining(initialTime);
    pulseAnim.setValue(1);
  };

  const handleAddTime = (seconds: number) => {
    setTimeRemaining((prev) => Math.max(0, prev + seconds));
  };

  const handleSkip = () => {
    setIsRunning(false);
    setTimeRemaining(0);
    onSkip?.();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    if (timeRemaining <= 10) return colors.error;
    if (timeRemaining <= 30) return colors.warning;
    return colors.primary;
  };

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      padding: 20,
    },
    timerContainer: {
      alignItems: 'center',
      marginBottom: 24,
    },
    exerciseInfo: {
      alignItems: 'center',
      marginBottom: 16,
    },
    currentExercise: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.text + '80',
      marginBottom: 4,
    },
    exerciseName: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'center',
    },
    nextExercise: {
      fontSize: 14,
      color: colors.text + '60',
      marginTop: 8,
    },
    timerCircle: {
      width: 200,
      height: 200,
      borderRadius: 100,
      borderWidth: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    timerText: {
      fontSize: 48,
      fontWeight: '700',
      fontFamily: 'monospace',
    },
    timerLabel: {
      fontSize: 16,
      fontWeight: '500',
      marginTop: 4,
    },
    controls: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 16,
      marginBottom: 20,
    },
    controlButton: {
      width: 56,
      height: 56,
      borderRadius: 28,
      justifyContent: 'center',
      alignItems: 'center',
    },
    playPauseButton: {
      width: 72,
      height: 72,
      borderRadius: 36,
    },
    timeAdjustments: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
      marginBottom: 20,
    },
    adjustButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      backgroundColor: colors.accent,
      gap: 4,
    },
    adjustButtonText: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    actionButtons: {
      flexDirection: 'row',
      gap: 12,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      gap: 6,
    },
    skipButton: {
      backgroundColor: colors.primary,
    },
    muteButton: {
      backgroundColor: colors.accent,
    },
    actionButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    completedContainer: {
      alignItems: 'center',
      padding: 20,
    },
    completedText: {
      fontSize: 24,
      fontWeight: '700',
      color: colors.success,
      marginBottom: 8,
    },
    completedSubtext: {
      fontSize: 16,
      color: colors.text + '80',
      textAlign: 'center',
    },
  });

  if (timeRemaining <= 0 && !isRunning) {
    return (
      <View style={styles.completedContainer}>
        <Timer size={64} color={colors.success} />
        <Text style={styles.completedText}>Rest Complete!</Text>
        <Text style={styles.completedSubtext}>
          {nextExerciseName ? `Ready for ${nextExerciseName}` : 'Ready for next exercise'}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {(exerciseName || nextExerciseName) && (
        <View style={styles.exerciseInfo}>
          {exerciseName && (
            <>
              <Text style={styles.currentExercise}>Just completed</Text>
              <Text style={styles.exerciseName}>{exerciseName}</Text>
            </>
          )}
          {nextExerciseName && (
            <Text style={styles.nextExercise}>Next: {nextExerciseName}</Text>
          )}
        </View>
      )}

      <View style={styles.timerContainer}>
        <Animated.View
          style={[
            styles.timerCircle,
            {
              borderColor: getTimerColor(),
              transform: [{ scale: pulseAnim }],
            },
          ]}
        >
          <Text style={[styles.timerText, { color: getTimerColor() }]}>
            {formatTime(timeRemaining)}
          </Text>
          <Text style={[styles.timerLabel, { color: colors.text + '80' }]}>
            REST
          </Text>
        </Animated.View>
      </View>

      {showControls && (
        <>
          <View style={styles.controls}>
            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: colors.accent }]}
              onPress={handleReset}
            >
              <RotateCcw size={24} color={colors.text} />
            </TouchableOpacity>

            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
              <TouchableOpacity
                style={[
                  styles.controlButton,
                  styles.playPauseButton,
                  { backgroundColor: colors.primary },
                ]}
                onPress={handlePlayPause}
              >
                {isRunning ? (
                  <Pause size={32} color={colors.overlayText} />
                ) : (
                  <Play size={32} color={colors.overlayText} />
                )}
              </TouchableOpacity>
            </Animated.View>

            <TouchableOpacity
              style={[styles.controlButton, { backgroundColor: colors.accent }]}
              onPress={() => setIsMuted(!isMuted)}
            >
              {isMuted ? (
                <VolumeX size={24} color={colors.text} />
              ) : (
                <Volume2 size={24} color={colors.text} />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.timeAdjustments}>
            <TouchableOpacity
              style={styles.adjustButton}
              onPress={() => handleAddTime(-15)}
            >
              <Minus size={16} color={colors.text} />
              <Text style={styles.adjustButtonText}>15s</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.adjustButton}
              onPress={() => handleAddTime(15)}
            >
              <Plus size={16} color={colors.text} />
              <Text style={styles.adjustButtonText}>15s</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.adjustButton}
              onPress={() => handleAddTime(30)}
            >
              <Plus size={16} color={colors.text} />
              <Text style={styles.adjustButtonText}>30s</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.skipButton]}
              onPress={handleSkip}
            >
              <SkipForward size={20} color={colors.overlayText} />
              <Text style={[styles.actionButtonText, { color: colors.overlayText }]}>
                Skip Rest
              </Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );
};
