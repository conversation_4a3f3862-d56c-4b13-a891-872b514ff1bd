import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {
  MessageSquare,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Target,
  Dumbbell,
  Star,
  Clock,
  Filter,
  Eye,
  EyeOff,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

interface CoachFeedback {
  id: string;
  coachId: string;
  clientId: string;
  feedbackType: 'workout' | 'progress' | 'general' | 'program_adjustment';
  relatedId?: string;
  title: string;
  content: string;
  actionItems?: ActionItem[];
  priority: 'low' | 'medium' | 'high';
  isRead: boolean;
  readAt?: string;
  createdAt: string;
  updatedAt: string;
  coach: {
    id: string;
    name: string;
    avatar?: string;
  };
  relatedData?: {
    workoutName?: string;
    programName?: string;
    checkInWeek?: string;
  };
}

interface ActionItem {
  id: string;
  description: string;
  isCompleted: boolean;
  dueDate?: string;
  priority: 'low' | 'medium' | 'high';
}

interface CheckInFeedback {
  id: string;
  checkInResponseId: string;
  coachId: string;
  feedbackText: string;
  recommendations?: Recommendation[];
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  updatedAt: string;
  coach: {
    id: string;
    name: string;
    avatar?: string;
  };
  checkInResponse: {
    weekStartDate: string;
    responses: Record<string, any>;
  };
}

interface Recommendation {
  category: 'nutrition' | 'training' | 'recovery' | 'lifestyle';
  description: string;
  priority: 'low' | 'medium' | 'high';
}

interface CoachFeedbackDisplayProps {
  feedbackType?: 'all' | 'workout' | 'progress' | 'general' | 'program_adjustment' | 'check_in';
  showUnreadOnly?: boolean;
  onFeedbackRead?: (feedbackId: string) => void;
}

const FEEDBACK_TYPE_ICONS = {
  workout: <Dumbbell size={20} />,
  progress: <TrendingUp size={20} />,
  general: <MessageSquare size={20} />,
  program_adjustment: <Target size={20} />,
  check_in: <CheckCircle size={20} />,
};

const PRIORITY_COLORS = {
  low: '#10B981',
  medium: '#F59E0B',
  high: '#EF4444',
};

export const CoachFeedbackDisplay: React.FC<CoachFeedbackDisplayProps> = ({
  feedbackType = 'all',
  showUnreadOnly = false,
  onFeedbackRead,
}) => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [feedback, setFeedback] = useState<CoachFeedback[]>([]);
  const [checkInFeedback, setCheckInFeedback] = useState<CheckInFeedback[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeFilter, setActiveFilter] = useState<'all' | 'unread' | 'high_priority'>('all');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadFeedback();
  }, [feedbackType, showUnreadOnly]);

  const loadFeedback = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load general coach feedback
      let feedbackQuery = supabase
        .from('coach_feedback')
        .select(`
          *,
          coach:coach_id(id, first_name, last_name, avatar_url)
        `)
        .eq('client_id', user.id)
        .order('created_at', { ascending: false });

      if (feedbackType !== 'all' && feedbackType !== 'check_in') {
        feedbackQuery = feedbackQuery.eq('feedback_type', feedbackType);
      }

      if (showUnreadOnly) {
        feedbackQuery = feedbackQuery.eq('is_read', false);
      }

      const { data: feedbackData, error: feedbackError } = await feedbackQuery;
      if (feedbackError) throw feedbackError;

      // Load check-in feedback
      let checkInQuery = supabase
        .from('check_in_feedback')
        .select(`
          *,
          coach:coach_id(id, first_name, last_name, avatar_url),
          check_in_responses!inner(week_start_date, responses)
        `)
        .eq('check_in_responses.client_id', user.id)
        .order('created_at', { ascending: false });

      const { data: checkInData, error: checkInError } = await checkInQuery;
      if (checkInError) throw checkInError;

      // Format feedback data
      const formattedFeedback: CoachFeedback[] = feedbackData?.map(item => ({
        id: item.id,
        coachId: item.coach_id,
        clientId: item.client_id,
        feedbackType: item.feedback_type,
        relatedId: item.related_id,
        title: item.title,
        content: item.content,
        actionItems: item.action_items,
        priority: item.priority,
        isRead: item.is_read,
        readAt: item.read_at,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        coach: {
          id: item.coach.id,
          name: `${item.coach.first_name} ${item.coach.last_name}`,
          avatar: item.coach.avatar_url,
        },
      })) || [];

      // Format check-in feedback data
      const formattedCheckInFeedback: CheckInFeedback[] = checkInData?.map(item => ({
        id: item.id,
        checkInResponseId: item.check_in_response_id,
        coachId: item.coach_id,
        feedbackText: item.feedback_text,
        recommendations: item.recommendations,
        priority: item.priority,
        createdAt: item.created_at,
        updatedAt: item.updated_at,
        coach: {
          id: item.coach.id,
          name: `${item.coach.first_name} ${item.coach.last_name}`,
          avatar: item.coach.avatar_url,
        },
        checkInResponse: {
          weekStartDate: item.check_in_responses.week_start_date,
          responses: item.check_in_responses.responses,
        },
      })) || [];

      setFeedback(formattedFeedback);
      setCheckInFeedback(formattedCheckInFeedback);
    } catch (error) {
      console.error('Failed to load feedback:', error);
      Alert.alert('Error', 'Failed to load coach feedback');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadFeedback();
    setIsRefreshing(false);
  };

  const markAsRead = async (feedbackId: string) => {
    try {
      await executeOperation(
        async () => {
          const { error } = await supabase
            .from('coach_feedback')
            .update({
              is_read: true,
              read_at: new Date().toISOString(),
            })
            .eq('id', feedbackId);

          if (error) throw error;

          setFeedback(prev => prev.map(item => 
            item.id === feedbackId 
              ? { ...item, isRead: true, readAt: new Date().toISOString() }
              : item
          ));

          onFeedbackRead?.(feedbackId);
        },
        {
          type: 'FEEDBACK_MARK_READ',
          payload: { feedbackId },
        }
      );
    } catch (error) {
      console.error('Failed to mark feedback as read:', error);
    }
  };

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const getFilteredFeedback = () => {
    let filtered = feedback;

    switch (activeFilter) {
      case 'unread':
        filtered = feedback.filter(item => !item.isRead);
        break;
      case 'high_priority':
        filtered = feedback.filter(item => item.priority === 'high');
        break;
      default:
        break;
    }

    return filtered;
  };

  const renderFilterTabs = () => (
    <View style={styles.filterTabs}>
      {[
        { key: 'all', label: 'All' },
        { key: 'unread', label: 'Unread' },
        { key: 'high_priority', label: 'High Priority' },
      ].map((filter) => (
        <TouchableOpacity
          key={filter.key}
          style={[
            styles.filterTab,
            {
              backgroundColor: activeFilter === filter.key ? colors.primary : colors.accent,
            },
          ]}
          onPress={() => setActiveFilter(filter.key as any)}
        >
          <Text
            style={[
              styles.filterTabText,
              {
                color: activeFilter === filter.key ? colors.overlayText : colors.text,
              },
            ]}
          >
            {filter.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderFeedbackItem = (item: CoachFeedback) => {
    const isExpanded = expandedItems.has(item.id);
    const priorityColor = PRIORITY_COLORS[item.priority];

    return (
      <TouchableOpacity
        key={item.id}
        style={[
          styles.feedbackItem,
          {
            backgroundColor: colors.accent,
            borderLeftColor: priorityColor,
            borderLeftWidth: 4,
          },
        ]}
        onPress={() => {
          if (!item.isRead) {
            markAsRead(item.id);
          }
          toggleExpanded(item.id);
        }}
      >
        <View style={styles.feedbackHeader}>
          <View style={styles.feedbackTitleSection}>
            <View style={[styles.feedbackIcon, { backgroundColor: priorityColor + '20' }]}>
              {React.cloneElement(
                FEEDBACK_TYPE_ICONS[item.feedbackType] as React.ReactElement,
                { color: priorityColor }
              )}
            </View>
            
            <View style={styles.feedbackTitleContainer}>
              <Text style={[styles.feedbackTitle, { color: colors.text }]}>
                {item.title}
              </Text>
              <View style={styles.feedbackMeta}>
                <Text style={[styles.feedbackCoach, { color: colors.text + '80' }]}>
                  From {item.coach.name}
                </Text>
                <Text style={[styles.feedbackDate, { color: colors.text + '60' }]}>
                  {new Date(item.createdAt).toLocaleDateString()}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.feedbackActions}>
            {!item.isRead && (
              <View style={[styles.unreadIndicator, { backgroundColor: colors.primary }]} />
            )}
            
            <View style={styles.expandIcon}>
              {isExpanded ? (
                <EyeOff size={16} color={colors.text + '60'} />
              ) : (
                <Eye size={16} color={colors.text + '60'} />
              )}
            </View>
          </View>
        </View>

        {isExpanded && (
          <View style={styles.feedbackContent}>
            <Text style={[styles.feedbackText, { color: colors.text }]}>
              {item.content}
            </Text>

            {item.actionItems && item.actionItems.length > 0 && (
              <View style={styles.actionItemsSection}>
                <Text style={[styles.actionItemsTitle, { color: colors.text }]}>
                  Action Items:
                </Text>
                {item.actionItems.map((actionItem, index) => (
                  <View key={index} style={styles.actionItem}>
                    <View
                      style={[
                        styles.actionItemBullet,
                        { backgroundColor: PRIORITY_COLORS[actionItem.priority] },
                      ]}
                    />
                    <Text style={[styles.actionItemText, { color: colors.text }]}>
                      {actionItem.description}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            <View style={styles.feedbackFooter}>
              <View style={[styles.priorityBadge, { backgroundColor: priorityColor + '20' }]}>
                <Text style={[styles.priorityText, { color: priorityColor }]}>
                  {item.priority.toUpperCase()} PRIORITY
                </Text>
              </View>
              
              <Text style={[styles.feedbackTime, { color: colors.text + '60' }]}>
                {new Date(item.createdAt).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderCheckInFeedbackItem = (item: CheckInFeedback) => {
    const isExpanded = expandedItems.has(`checkin-${item.id}`);
    const priorityColor = PRIORITY_COLORS[item.priority];

    return (
      <TouchableOpacity
        key={`checkin-${item.id}`}
        style={[
          styles.feedbackItem,
          {
            backgroundColor: colors.accent,
            borderLeftColor: priorityColor,
            borderLeftWidth: 4,
          },
        ]}
        onPress={() => toggleExpanded(`checkin-${item.id}`)}
      >
        <View style={styles.feedbackHeader}>
          <View style={styles.feedbackTitleSection}>
            <View style={[styles.feedbackIcon, { backgroundColor: priorityColor + '20' }]}>
              <CheckCircle size={20} color={priorityColor} />
            </View>
            
            <View style={styles.feedbackTitleContainer}>
              <Text style={[styles.feedbackTitle, { color: colors.text }]}>
                Check-In Feedback
              </Text>
              <View style={styles.feedbackMeta}>
                <Text style={[styles.feedbackCoach, { color: colors.text + '80' }]}>
                  From {item.coach.name}
                </Text>
                <Text style={[styles.feedbackDate, { color: colors.text + '60' }]}>
                  Week of {new Date(item.checkInResponse.weekStartDate).toLocaleDateString()}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.feedbackActions}>
            <View style={styles.expandIcon}>
              {isExpanded ? (
                <EyeOff size={16} color={colors.text + '60'} />
              ) : (
                <Eye size={16} color={colors.text + '60'} />
              )}
            </View>
          </View>
        </View>

        {isExpanded && (
          <View style={styles.feedbackContent}>
            <Text style={[styles.feedbackText, { color: colors.text }]}>
              {item.feedbackText}
            </Text>

            {item.recommendations && item.recommendations.length > 0 && (
              <View style={styles.recommendationsSection}>
                <Text style={[styles.recommendationsTitle, { color: colors.text }]}>
                  Recommendations:
                </Text>
                {item.recommendations.map((rec, index) => (
                  <View key={index} style={styles.recommendation}>
                    <View
                      style={[
                        styles.recommendationBullet,
                        { backgroundColor: PRIORITY_COLORS[rec.priority] },
                      ]}
                    />
                    <View style={styles.recommendationContent}>
                      <Text style={[styles.recommendationCategory, { color: colors.primary }]}>
                        {rec.category.toUpperCase()}
                      </Text>
                      <Text style={[styles.recommendationText, { color: colors.text }]}>
                        {rec.description}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>
            )}

            <View style={styles.feedbackFooter}>
              <View style={[styles.priorityBadge, { backgroundColor: priorityColor + '20' }]}>
                <Text style={[styles.priorityText, { color: priorityColor }]}>
                  {item.priority.toUpperCase()} PRIORITY
                </Text>
              </View>
              
              <Text style={[styles.feedbackTime, { color: colors.text + '60' }]}>
                {new Date(item.createdAt).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    headerDescription: {
      fontSize: 16,
      color: colors.text + '80',
      lineHeight: 24,
    },
    filterTabs: {
      flexDirection: 'row',
      padding: 20,
      gap: 8,
    },
    filterTab: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    filterTabText: {
      fontSize: 14,
      fontWeight: '500',
    },
    content: {
      flex: 1,
      padding: 20,
    },
    feedbackList: {
      gap: 16,
    },
    feedbackItem: {
      borderRadius: 16,
      overflow: 'hidden',
    },
    feedbackHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
    },
    feedbackTitleSection: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      gap: 12,
    },
    feedbackIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    feedbackTitleContainer: {
      flex: 1,
    },
    feedbackTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    feedbackMeta: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    feedbackCoach: {
      fontSize: 14,
    },
    feedbackDate: {
      fontSize: 12,
    },
    feedbackActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    unreadIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    expandIcon: {
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    feedbackContent: {
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    feedbackText: {
      fontSize: 16,
      lineHeight: 24,
      marginBottom: 16,
    },
    actionItemsSection: {
      marginBottom: 16,
    },
    actionItemsTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 12,
    },
    actionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 8,
      gap: 8,
    },
    actionItemBullet: {
      width: 6,
      height: 6,
      borderRadius: 3,
      marginTop: 8,
    },
    actionItemText: {
      fontSize: 14,
      lineHeight: 20,
      flex: 1,
    },
    recommendationsSection: {
      marginBottom: 16,
    },
    recommendationsTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 12,
    },
    recommendation: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 12,
      gap: 8,
    },
    recommendationBullet: {
      width: 6,
      height: 6,
      borderRadius: 3,
      marginTop: 8,
    },
    recommendationContent: {
      flex: 1,
    },
    recommendationCategory: {
      fontSize: 12,
      fontWeight: '600',
      marginBottom: 4,
    },
    recommendationText: {
      fontSize: 14,
      lineHeight: 20,
    },
    feedbackFooter: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    priorityBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
    },
    priorityText: {
      fontSize: 10,
      fontWeight: '600',
    },
    feedbackTime: {
      fontSize: 12,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyStateTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateDescription: {
      fontSize: 16,
      color: colors.text + '80',
      textAlign: 'center',
      lineHeight: 24,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: colors.text + '80',
      marginTop: 16,
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading feedback...</Text>
      </View>
    );
  }

  const filteredFeedback = getFilteredFeedback();
  const allFeedbackItems = [
    ...filteredFeedback,
    ...(feedbackType === 'all' || feedbackType === 'check_in' ? checkInFeedback : []),
  ].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Coach Feedback</Text>
        <Text style={styles.headerDescription}>
          View feedback and recommendations from your coach
        </Text>
      </View>

      {renderFilterTabs()}

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {allFeedbackItems.length === 0 ? (
          <View style={styles.emptyState}>
            <MessageSquare size={64} color={colors.text + '40'} />
            <Text style={styles.emptyStateTitle}>No Feedback Yet</Text>
            <Text style={styles.emptyStateDescription}>
              Your coach hasn't provided any feedback yet. Complete some workouts and check-ins to receive personalized guidance.
            </Text>
          </View>
        ) : (
          <View style={styles.feedbackList}>
            {filteredFeedback.map(renderFeedbackItem)}
            {(feedbackType === 'all' || feedbackType === 'check_in') &&
              checkInFeedback.map(renderCheckInFeedbackItem)}
          </View>
        )}
      </ScrollView>
    </View>
  );
};
