import React from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useThemeStore } from '@/store/themeStore';

interface ThemedViewProps {
  style?: ViewStyle;
  children: React.ReactNode;
  variant?: 'background' | 'card' | 'accent';
}

export function ThemedView({ style, children, variant = 'background' }: ThemedViewProps) {
  const { colors } = useThemeStore();
  
  const getBackgroundColor = () => {
    switch (variant) {
      case 'card':
        return colors.background;
      case 'accent':
        return colors.accent;
      default:
        return colors.background;
    }
  };

  return (
    <View style={[{ backgroundColor: getBackgroundColor() }, style]}>
      {children}
    </View>
  );
}

interface ThemedTextProps {
  style?: TextStyle;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent';
}

export function ThemedText({ style, children, variant = 'primary' }: ThemedTextProps) {
  const { colors } = useThemeStore();
  
  const getTextColor = () => {
    switch (variant) {
      case 'secondary':
        return colors.secondary;
      case 'accent':
        return colors.primary;
      default:
        return colors.text;
    }
  };

  return (
    <Text style={[{ color: getTextColor() }, style]}>
      {children}
    </Text>
  );
}

interface ThemedButtonProps {
  onPress: () => void;
  title: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  variant?: 'primary' | 'secondary' | 'outline';
  disabled?: boolean;
}

export function ThemedButton({ 
  onPress, 
  title, 
  style, 
  textStyle, 
  variant = 'primary',
  disabled = false 
}: ThemedButtonProps) {
  const { colors } = useThemeStore();
  
  const getButtonStyle = () => {
    const baseStyle = {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    };

    switch (variant) {
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: colors.secondary,
        };
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.primary,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: colors.primary,
        };
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case 'outline':
        return colors.primary;
      case 'secondary':
        return colors.text;
      default:
        return colors.contrastText;
    }
  };

  return (
    <TouchableOpacity
      style={[
        getButtonStyle(),
        disabled && { opacity: 0.6 },
        style
      ]}
      onPress={onPress}
      disabled={disabled}
    >
      <Text style={[{ color: getTextColor(), fontWeight: '600' }, textStyle]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
}

interface ThemedInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  style?: ViewStyle;
  multiline?: boolean;
  numberOfLines?: number;
  keyboardType?: 'default' | 'numeric' | 'email-address';
  secureTextEntry?: boolean;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  textAlignVertical?: 'auto' | 'top' | 'bottom' | 'center';
  returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send';
  onSubmitEditing?: () => void;
  blurOnSubmit?: boolean;
  ref?: React.RefObject<TextInput>;
  autoFocus?: boolean;
}

export const ThemedInput = React.forwardRef<TextInput, ThemedInputProps>(({
  value,
  onChangeText,
  placeholder,
  style,
  multiline = false,
  numberOfLines = 1,
  keyboardType = 'default',
  secureTextEntry = false,
  autoCapitalize = 'sentences',
  textAlignVertical = 'auto',
  returnKeyType = 'done',
  onSubmitEditing,
  blurOnSubmit = true,
  autoFocus = false,
}, ref) => {
  const { colors } = useThemeStore();

  return (
    <TextInput
      ref={ref}
      style={[
        {
          backgroundColor: colors.background,
          borderWidth: 1,
          borderColor: colors.accent,
          borderRadius: 8,
          padding: 12,
          fontSize: 16,
          color: colors.text,
          minHeight: multiline ? 80 : 44,
        },
        style
      ]}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor={`${colors.text}80`} // 50% opacity
      multiline={multiline}
      numberOfLines={numberOfLines}
      keyboardType={keyboardType}
      secureTextEntry={secureTextEntry}
      autoCapitalize={autoCapitalize}
      textAlignVertical={textAlignVertical}
      returnKeyType={returnKeyType}
      onSubmitEditing={onSubmitEditing}
      blurOnSubmit={blurOnSubmit}
      autoFocus={autoFocus}
    />
  );
});

interface ThemedCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
}

export function ThemedCard({ children, style, onPress }: ThemedCardProps) {
  const { colors } = useThemeStore();

  const cardStyle = {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
    borderColor: colors.accent,
  };

  if (onPress) {
    return (
      <TouchableOpacity style={[cardStyle, style]} onPress={onPress}>
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[cardStyle, style]}>
      {children}
    </View>
  );
}

// New themed components for common patterns

interface ThemedErrorTextProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export function ThemedErrorText({ children, style }: ThemedErrorTextProps) {
  const { colors } = useThemeStore();

  return (
    <Text style={[{ color: colors.error }, style]}>
      {children}
    </Text>
  );
}

interface ThemedStatusIndicatorProps {
  status: 'success' | 'warning' | 'error' | 'info' | 'primary';
  text: string;
  icon?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export function ThemedStatusIndicator({
  status,
  text,
  icon,
  style,
  textStyle
}: ThemedStatusIndicatorProps) {
  const { colors } = useThemeStore();

  const getStatusColor = () => {
    switch (status) {
      case 'success': return colors.success;
      case 'warning': return colors.warning;
      case 'error': return colors.error;
      case 'info': return colors.info;
      case 'primary': return colors.primary;
      default: return colors.primary;
    }
  };

  return (
    <View style={[{ flexDirection: 'row', alignItems: 'center' }, style]}>
      {icon && <View style={{ marginRight: 8 }}>{icon}</View>}
      <Text style={[{ color: getStatusColor() }, textStyle]}>{text}</Text>
    </View>
  );
}

interface ThemedOverlayProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function ThemedOverlay({ children, style }: ThemedOverlayProps) {
  const { colors } = useThemeStore();

  return (
    <View style={[{ backgroundColor: colors.overlayBackground }, style]}>
      {children}
    </View>
  );
}

interface ThemedSelectionOptionProps {
  selected: boolean;
  onPress: () => void;
  children: React.ReactNode;
  style?: ViewStyle;
}

export function ThemedSelectionOption({
  selected,
  onPress,
  children,
  style
}: ThemedSelectionOptionProps) {
  const { colors } = useThemeStore();

  return (
    <TouchableOpacity
      style={[
        {
          borderWidth: 1,
          borderRadius: 8,
          padding: 12,
          backgroundColor: colors.background,
          borderColor: colors.accent
        },
        selected && {
          backgroundColor: colors.primary,
          borderColor: colors.primary
        },
        style
      ]}
      onPress={onPress}
    >
      {children}
    </TouchableOpacity>
  );
}