import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  SkipForward,
  Check,
  Info,
  Timer,
  Dumbbell,
  Target,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useActiveWorkoutStore } from '../store/activeWorkoutStore';
import { Workout, WorkoutExercise } from '../services/workoutService';
import { ExerciseInstructionModal } from './ExerciseInstructionModal';

const { width } = Dimensions.get('window');

interface WorkoutFlowNavigationProps {
  workout: Workout;
  onComplete: () => void;
  onExit: () => void;
}

interface ExerciseProgress {
  exerciseId: string;
  currentSet: number;
  totalSets: number;
  isCompleted: boolean;
  restTimeRemaining?: number;
}

export const WorkoutFlowNavigation: React.FC<WorkoutFlowNavigationProps> = ({
  workout,
  onComplete,
  onExit,
}) => {
  const { colors } = useThemeStore();
  const { 
    currentWorkoutPlan,
    loggedData,
    updateSetLog,
    completeExercise,
    sessionStartTime 
  } = useActiveWorkoutStore();

  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [exerciseProgress, setExerciseProgress] = useState<Record<string, ExerciseProgress>>({});
  const [showInstructionModal, setShowInstructionModal] = useState(false);
  const [isResting, setIsResting] = useState(false);
  const [restTimeRemaining, setRestTimeRemaining] = useState(0);

  const exercises = workout.workout_exercises || [];
  const currentExercise = exercises[currentExerciseIndex];

  useEffect(() => {
    // Initialize exercise progress
    const initialProgress: Record<string, ExerciseProgress> = {};
    exercises.forEach((exercise) => {
      initialProgress[exercise.id] = {
        exerciseId: exercise.id,
        currentSet: 1,
        totalSets: exercise.sets || 3,
        isCompleted: false,
      };
    });
    setExerciseProgress(initialProgress);
  }, [exercises]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isResting && restTimeRemaining > 0) {
      interval = setInterval(() => {
        setRestTimeRemaining((prev) => {
          if (prev <= 1) {
            setIsResting(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isResting, restTimeRemaining]);

  const getCurrentProgress = () => {
    if (!currentExercise) return null;
    return exerciseProgress[currentExercise.id];
  };

  const handleSetComplete = (reps: number, weight?: number) => {
    if (!currentExercise) return;

    const progress = getCurrentProgress();
    if (!progress) return;

    // Log the set
    updateSetLog(currentExercise.id, progress.currentSet - 1, {
      reps,
      weight: weight || 0,
      completed: true,
    });

    // Update progress
    const newProgress = { ...exerciseProgress };
    if (progress.currentSet < progress.totalSets) {
      // Move to next set
      newProgress[currentExercise.id] = {
        ...progress,
        currentSet: progress.currentSet + 1,
      };
      
      // Start rest timer if not the last set
      const restTime = currentExercise.rest_seconds || 60;
      setRestTimeRemaining(restTime);
      setIsResting(true);
    } else {
      // Exercise completed
      newProgress[currentExercise.id] = {
        ...progress,
        isCompleted: true,
      };
      completeExercise(currentExercise.id);
      
      // Auto-advance to next exercise
      setTimeout(() => {
        handleNextExercise();
      }, 1000);
    }
    
    setExerciseProgress(newProgress);
  };

  const handlePreviousExercise = () => {
    if (currentExerciseIndex > 0) {
      setCurrentExerciseIndex(currentExerciseIndex - 1);
      setIsResting(false);
      setRestTimeRemaining(0);
    }
  };

  const handleNextExercise = () => {
    if (currentExerciseIndex < exercises.length - 1) {
      setCurrentExerciseIndex(currentExerciseIndex + 1);
      setIsResting(false);
      setRestTimeRemaining(0);
    } else {
      // Workout completed
      handleWorkoutComplete();
    }
  };

  const handleSkipRest = () => {
    setIsResting(false);
    setRestTimeRemaining(0);
  };

  const handleWorkoutComplete = () => {
    Alert.alert(
      'Workout Complete!',
      'Congratulations on completing your workout!',
      [
        {
          text: 'Finish',
          onPress: onComplete,
        },
      ]
    );
  };

  const handleExitWorkout = () => {
    Alert.alert(
      'Exit Workout?',
      'Your progress will be saved. You can continue this workout later.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Exit',
          onPress: onExit,
        },
      ]
    );
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getWorkoutProgress = () => {
    const completedExercises = Object.values(exerciseProgress).filter(p => p.isCompleted).length;
    return Math.round((completedExercises / exercises.length) * 100);
  };

  const renderProgressBar = () => {
    const progress = getWorkoutProgress();
    const progressWidth = (progress / 100) * (width - 40);

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={[styles.progressText, { color: colors.text }]}>
            Exercise {currentExerciseIndex + 1} of {exercises.length}
          </Text>
          <Text style={[styles.progressPercentage, { color: colors.primary }]}>
            {progress}%
          </Text>
        </View>
        <View style={[styles.progressTrack, { backgroundColor: colors.accent }]}>
          <LinearGradient
            colors={[colors.primary, colors.primary + '80']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[styles.progressFill, { width: progressWidth }]}
          />
        </View>
      </View>
    );
  };

  const renderCurrentExercise = () => {
    if (!currentExercise) return null;

    const progress = getCurrentProgress();
    if (!progress) return null;

    return (
      <View style={[styles.exerciseCard, { backgroundColor: colors.accent }]}>
        <View style={styles.exerciseHeader}>
          <View style={styles.exerciseInfo}>
            <Text style={[styles.exerciseName, { color: colors.text }]}>
              {currentExercise.exercise?.name || 'Unknown Exercise'}
            </Text>
            <Text style={[styles.exerciseDetails, { color: colors.text + '80' }]}>
              Set {progress.currentSet} of {progress.totalSets}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.infoButton, { backgroundColor: colors.primary }]}
            onPress={() => setShowInstructionModal(true)}
          >
            <Info size={20} color={colors.overlayText} />
          </TouchableOpacity>
        </View>

        <View style={styles.exerciseSpecs}>
          <View style={styles.specItem}>
            <Target size={16} color={colors.primary} />
            <Text style={[styles.specText, { color: colors.text }]}>
              {currentExercise.reps} reps
            </Text>
          </View>
          <View style={styles.specItem}>
            <Dumbbell size={16} color={colors.primary} />
            <Text style={[styles.specText, { color: colors.text }]}>
              {currentExercise.weight || 'Bodyweight'}
            </Text>
          </View>
          {currentExercise.rest_seconds && (
            <View style={styles.specItem}>
              <Timer size={16} color={colors.primary} />
              <Text style={[styles.specText, { color: colors.text }]}>
                {currentExercise.rest_seconds}s rest
              </Text>
            </View>
          )}
        </View>

        {!progress.isCompleted && (
          <View style={styles.setActions}>
            <TouchableOpacity
              style={[styles.completeSetButton, { backgroundColor: colors.success }]}
              onPress={() => handleSetComplete(currentExercise.reps || 10)}
            >
              <Check size={20} color={colors.overlayText} />
              <Text style={[styles.completeSetText, { color: colors.overlayText }]}>
                Complete Set
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {progress.isCompleted && (
          <View style={[styles.completedIndicator, { backgroundColor: colors.success + '20' }]}>
            <Check size={24} color={colors.success} />
            <Text style={[styles.completedText, { color: colors.success }]}>
              Exercise Completed!
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderRestTimer = () => {
    if (!isResting) return null;

    return (
      <View style={[styles.restTimer, { backgroundColor: colors.warning + '20' }]}>
        <View style={styles.restHeader}>
          <Timer size={24} color={colors.warning} />
          <Text style={[styles.restTitle, { color: colors.text }]}>
            Rest Time
          </Text>
        </View>
        <Text style={[styles.restTime, { color: colors.warning }]}>
          {formatTime(restTimeRemaining)}
        </Text>
        <TouchableOpacity
          style={[styles.skipRestButton, { backgroundColor: colors.primary }]}
          onPress={handleSkipRest}
        >
          <Text style={[styles.skipRestText, { color: colors.overlayText }]}>
            Skip Rest
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderNavigation = () => {
    return (
      <View style={styles.navigation}>
        <TouchableOpacity
          style={[
            styles.navButton,
            { backgroundColor: colors.accent },
            currentExerciseIndex === 0 && { opacity: 0.5 }
          ]}
          onPress={handlePreviousExercise}
          disabled={currentExerciseIndex === 0}
        >
          <ChevronLeft size={20} color={colors.text} />
          <Text style={[styles.navButtonText, { color: colors.text }]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.exitButton, { backgroundColor: colors.error }]}
          onPress={handleExitWorkout}
        >
          <Text style={[styles.exitButtonText, { color: colors.overlayText }]}>
            Exit
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, { backgroundColor: colors.primary }]}
          onPress={handleNextExercise}
        >
          <Text style={[styles.navButtonText, { color: colors.overlayText }]}>
            {currentExerciseIndex === exercises.length - 1 ? 'Finish' : 'Next'}
          </Text>
          <ChevronRight size={20} color={colors.overlayText} />
        </TouchableOpacity>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    progressContainer: {
      padding: 20,
      paddingBottom: 10,
    },
    progressHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    progressText: {
      fontSize: 16,
      fontWeight: '500',
    },
    progressPercentage: {
      fontSize: 18,
      fontWeight: '700',
    },
    progressTrack: {
      height: 8,
      borderRadius: 4,
    },
    progressFill: {
      height: '100%',
      borderRadius: 4,
    },
    exerciseCard: {
      margin: 20,
      padding: 20,
      borderRadius: 16,
    },
    exerciseHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 16,
    },
    exerciseInfo: {
      flex: 1,
    },
    exerciseName: {
      fontSize: 20,
      fontWeight: '600',
      marginBottom: 4,
    },
    exerciseDetails: {
      fontSize: 16,
    },
    infoButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    exerciseSpecs: {
      flexDirection: 'row',
      gap: 16,
      marginBottom: 20,
    },
    specItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
    },
    specText: {
      fontSize: 14,
      fontWeight: '500',
    },
    setActions: {
      marginTop: 16,
    },
    completeSetButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
    },
    completeSetText: {
      fontSize: 16,
      fontWeight: '600',
    },
    completedIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      borderRadius: 12,
      gap: 8,
    },
    completedText: {
      fontSize: 16,
      fontWeight: '600',
    },
    restTimer: {
      margin: 20,
      padding: 24,
      borderRadius: 16,
      alignItems: 'center',
    },
    restHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginBottom: 16,
    },
    restTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    restTime: {
      fontSize: 48,
      fontWeight: '700',
      marginBottom: 20,
    },
    skipRestButton: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
    skipRestText: {
      fontSize: 16,
      fontWeight: '600',
    },
    navigation: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
      alignItems: 'center',
    },
    navButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 14,
      borderRadius: 12,
      gap: 6,
    },
    navButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    exitButton: {
      paddingHorizontal: 20,
      paddingVertical: 14,
      borderRadius: 12,
    },
    exitButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <View style={styles.container}>
      {renderProgressBar()}
      
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderCurrentExercise()}
        {renderRestTimer()}
      </ScrollView>

      {renderNavigation()}

      <ExerciseInstructionModal
        visible={showInstructionModal}
        exercise={currentExercise?.exercise || null}
        onClose={() => setShowInstructionModal(false)}
        onStartExercise={() => setShowInstructionModal(false)}
      />
    </View>
  );
};
