import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Trophy,
  Clock,
  Target,
  TrendingUp,
  Star,
  MessageSquare,
  Camera,
  Share2,
  CheckCircle,
  X,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useActiveWorkoutStore } from '../store/activeWorkoutStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { workoutLoggingService } from '../services/workoutLoggingService';

const { width } = Dimensions.get('window');

interface WorkoutCompletionFlowProps {
  visible: boolean;
  workoutData: {
    workoutId: string;
    workoutName: string;
    duration: number;
    exercisesCompleted: number;
    totalExercises: number;
    estimatedCalories?: number;
  };
  onComplete: (data: WorkoutCompletionData) => void;
  onClose: () => void;
}

interface WorkoutCompletionData {
  rpe: number;
  notes: string;
  mood: 'great' | 'good' | 'okay' | 'tired' | 'struggled';
  achievements: string[];
  shareToSocial: boolean;
  takeProgressPhoto: boolean;
}

const RPE_DESCRIPTIONS = {
  1: 'Very Easy',
  2: 'Easy',
  3: 'Moderate',
  4: 'Somewhat Hard',
  5: 'Hard',
  6: 'Very Hard',
  7: 'Extremely Hard',
  8: 'Maximum Effort',
  9: 'Near Maximum',
  10: 'Maximum',
};

const MOOD_OPTIONS = [
  { key: 'great', label: 'Great!', emoji: '🔥', color: '#4CAF50' },
  { key: 'good', label: 'Good', emoji: '💪', color: '#8BC34A' },
  { key: 'okay', label: 'Okay', emoji: '👍', color: '#FFC107' },
  { key: 'tired', label: 'Tired', emoji: '😴', color: '#FF9800' },
  { key: 'struggled', label: 'Struggled', emoji: '😅', color: '#F44336' },
];

export const WorkoutCompletionFlow: React.FC<WorkoutCompletionFlowProps> = ({
  visible,
  workoutData,
  onComplete,
  onClose,
}) => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [currentStep, setCurrentStep] = useState(0);
  const [completionData, setCompletionData] = useState<WorkoutCompletionData>({
    rpe: 5,
    notes: '',
    mood: 'good',
    achievements: [],
    shareToSocial: false,
    takeProgressPhoto: false,
  });

  const steps = [
    'Summary',
    'Rate Effort',
    'How You Felt',
    'Notes',
    'Achievements',
    'Share & Photo',
  ];

  useEffect(() => {
    if (visible) {
      setCurrentStep(0);
      // Reset completion data when modal opens
      setCompletionData({
        rpe: 5,
        notes: '',
        mood: 'good',
        achievements: [],
        shareToSocial: false,
        takeProgressPhoto: false,
      });
    }
  }, [visible]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    try {
      await executeOperation(
        async () => {
          // Save workout completion data
          await workoutLoggingService.completeWorkout(workoutData.workoutId, {
            rpe: completionData.rpe,
            notes: completionData.notes,
            mood: completionData.mood,
            duration: workoutData.duration,
          });
        },
        {
          type: 'WORKOUT_COMPLETE',
          payload: {
            workoutId: workoutData.workoutId,
            completionData,
            workoutData,
          },
        }
      );

      onComplete(completionData);
    } catch (error) {
      console.error('Failed to save workout completion:', error);
      Alert.alert('Error', 'Failed to save workout data. Please try again.');
    }
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const renderProgressBar = () => {
    const progress = ((currentStep + 1) / steps.length) * 100;
    const progressWidth = (progress / 100) * (width - 40);

    return (
      <View style={styles.progressContainer}>
        <Text style={[styles.stepText, { color: colors.text }]}>
          Step {currentStep + 1} of {steps.length}: {steps[currentStep]}
        </Text>
        <View style={[styles.progressTrack, { backgroundColor: colors.accent }]}>
          <LinearGradient
            colors={[colors.primary, colors.primary + '80']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[styles.progressFill, { width: progressWidth }]}
          />
        </View>
      </View>
    );
  };

  const renderSummaryStep = () => (
    <View style={styles.stepContent}>
      <View style={styles.celebrationHeader}>
        <Trophy size={64} color={colors.warning} />
        <Text style={[styles.celebrationTitle, { color: colors.text }]}>
          Workout Complete!
        </Text>
        <Text style={[styles.celebrationSubtitle, { color: colors.text + '80' }]}>
          Great job finishing your workout
        </Text>
      </View>

      <View style={styles.summaryStats}>
        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <Clock size={24} color={colors.primary} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {formatDuration(workoutData.duration)}
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
            Duration
          </Text>
        </View>

        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <Target size={24} color={colors.success} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {workoutData.exercisesCompleted}/{workoutData.totalExercises}
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
            Exercises
          </Text>
        </View>

        {workoutData.estimatedCalories && (
          <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
            <TrendingUp size={24} color={colors.error} />
            <Text style={[styles.statValue, { color: colors.text }]}>
              {workoutData.estimatedCalories}
            </Text>
            <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
              Calories
            </Text>
          </View>
        )}
      </View>

      <Text style={[styles.workoutName, { color: colors.text }]}>
        {workoutData.workoutName}
      </Text>
    </View>
  );

  const renderRPEStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Rate Your Effort
      </Text>
      <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
        How hard did this workout feel? (RPE Scale 1-10)
      </Text>

      <View style={styles.rpeContainer}>
        <Text style={[styles.rpeValue, { color: colors.primary }]}>
          {completionData.rpe}
        </Text>
        <Text style={[styles.rpeDescription, { color: colors.text }]}>
          {RPE_DESCRIPTIONS[completionData.rpe as keyof typeof RPE_DESCRIPTIONS]}
        </Text>
      </View>

      <View style={styles.rpeSlider}>
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
          <TouchableOpacity
            key={value}
            style={[
              styles.rpeButton,
              {
                backgroundColor: completionData.rpe === value ? colors.primary : colors.accent,
              },
            ]}
            onPress={() => setCompletionData({ ...completionData, rpe: value })}
          >
            <Text
              style={[
                styles.rpeButtonText,
                {
                  color: completionData.rpe === value ? colors.overlayText : colors.text,
                },
              ]}
            >
              {value}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderMoodStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        How Did You Feel?
      </Text>
      <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
        Select your overall mood during the workout
      </Text>

      <View style={styles.moodOptions}>
        {MOOD_OPTIONS.map((mood) => (
          <TouchableOpacity
            key={mood.key}
            style={[
              styles.moodButton,
              {
                backgroundColor: completionData.mood === mood.key ? mood.color + '20' : colors.accent,
                borderColor: completionData.mood === mood.key ? mood.color : 'transparent',
                borderWidth: 2,
              },
            ]}
            onPress={() => setCompletionData({ ...completionData, mood: mood.key as any })}
          >
            <Text style={styles.moodEmoji}>{mood.emoji}</Text>
            <Text
              style={[
                styles.moodLabel,
                {
                  color: completionData.mood === mood.key ? mood.color : colors.text,
                  fontWeight: completionData.mood === mood.key ? '600' : '400',
                },
              ]}
            >
              {mood.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderNotesStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Add Notes (Optional)
      </Text>
      <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
        Any thoughts about today's workout?
      </Text>

      <TextInput
        style={[
          styles.notesInput,
          {
            backgroundColor: colors.accent,
            color: colors.text,
            borderColor: colors.primary + '40',
          },
        ]}
        placeholder="How did the workout feel? Any exercises that were particularly challenging or easy?"
        placeholderTextColor={colors.text + '60'}
        multiline
        numberOfLines={6}
        value={completionData.notes}
        onChangeText={(text) => setCompletionData({ ...completionData, notes: text })}
        textAlignVertical="top"
      />
    </View>
  );

  const renderAchievementsStep = () => {
    const achievements = [
      'First workout this week',
      'Personal best on an exercise',
      'Completed all exercises',
      'Improved from last time',
      'Pushed through fatigue',
      'Perfect form focus',
    ];

    return (
      <View style={styles.stepContent}>
        <Text style={[styles.stepTitle, { color: colors.text }]}>
          Celebrate Your Wins!
        </Text>
        <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
          What did you accomplish today?
        </Text>

        <View style={styles.achievementsList}>
          {achievements.map((achievement) => (
            <TouchableOpacity
              key={achievement}
              style={[
                styles.achievementButton,
                {
                  backgroundColor: completionData.achievements.includes(achievement)
                    ? colors.success + '20'
                    : colors.accent,
                  borderColor: completionData.achievements.includes(achievement)
                    ? colors.success
                    : 'transparent',
                  borderWidth: 1,
                },
              ]}
              onPress={() => {
                const newAchievements = completionData.achievements.includes(achievement)
                  ? completionData.achievements.filter((a) => a !== achievement)
                  : [...completionData.achievements, achievement];
                setCompletionData({ ...completionData, achievements: newAchievements });
              }}
            >
              {completionData.achievements.includes(achievement) && (
                <CheckCircle size={20} color={colors.success} />
              )}
              <Text
                style={[
                  styles.achievementText,
                  {
                    color: completionData.achievements.includes(achievement)
                      ? colors.success
                      : colors.text,
                  },
                ]}
              >
                {achievement}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderShareStep = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: colors.text }]}>
        Share Your Success
      </Text>
      <Text style={[styles.stepDescription, { color: colors.text + '80' }]}>
        Optional: Share your workout or take a progress photo
      </Text>

      <View style={styles.shareOptions}>
        <TouchableOpacity
          style={[
            styles.shareOption,
            {
              backgroundColor: completionData.takeProgressPhoto ? colors.primary + '20' : colors.accent,
              borderColor: completionData.takeProgressPhoto ? colors.primary : 'transparent',
              borderWidth: 2,
            },
          ]}
          onPress={() =>
            setCompletionData({ ...completionData, takeProgressPhoto: !completionData.takeProgressPhoto })
          }
        >
          <Camera size={32} color={completionData.takeProgressPhoto ? colors.primary : colors.text} />
          <Text
            style={[
              styles.shareOptionText,
              {
                color: completionData.takeProgressPhoto ? colors.primary : colors.text,
              },
            ]}
          >
            Take Progress Photo
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.shareOption,
            {
              backgroundColor: completionData.shareToSocial ? colors.primary + '20' : colors.accent,
              borderColor: completionData.shareToSocial ? colors.primary : 'transparent',
              borderWidth: 2,
            },
          ]}
          onPress={() =>
            setCompletionData({ ...completionData, shareToSocial: !completionData.shareToSocial })
          }
        >
          <Share2 size={32} color={completionData.shareToSocial ? colors.primary : colors.text} />
          <Text
            style={[
              styles.shareOptionText,
              {
                color: completionData.shareToSocial ? colors.primary : colors.text,
              },
            ]}
          >
            Share to Social
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderSummaryStep();
      case 1:
        return renderRPEStep();
      case 2:
        return renderMoodStep();
      case 3:
        return renderNotesStep();
      case 4:
        return renderAchievementsStep();
      case 5:
        return renderShareStep();
      default:
        return renderSummaryStep();
    }
  };

  const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    closeButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.accent,
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressContainer: {
      padding: 20,
      paddingBottom: 10,
    },
    stepText: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 8,
    },
    progressTrack: {
      height: 6,
      borderRadius: 3,
    },
    progressFill: {
      height: '100%',
      borderRadius: 3,
    },
    content: {
      flex: 1,
    },
    stepContent: {
      flex: 1,
      padding: 20,
    },
    celebrationHeader: {
      alignItems: 'center',
      marginBottom: 32,
    },
    celebrationTitle: {
      fontSize: 28,
      fontWeight: '700',
      marginTop: 16,
      marginBottom: 8,
    },
    celebrationSubtitle: {
      fontSize: 16,
      textAlign: 'center',
    },
    summaryStats: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 24,
    },
    statCard: {
      flex: 1,
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      gap: 8,
    },
    statValue: {
      fontSize: 20,
      fontWeight: '700',
    },
    statLabel: {
      fontSize: 12,
      textAlign: 'center',
    },
    workoutName: {
      fontSize: 18,
      fontWeight: '600',
      textAlign: 'center',
    },
    stepTitle: {
      fontSize: 24,
      fontWeight: '700',
      marginBottom: 8,
      textAlign: 'center',
    },
    stepDescription: {
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 32,
      lineHeight: 24,
    },
    rpeContainer: {
      alignItems: 'center',
      marginBottom: 32,
    },
    rpeValue: {
      fontSize: 64,
      fontWeight: '700',
    },
    rpeDescription: {
      fontSize: 18,
      fontWeight: '500',
      marginTop: 8,
    },
    rpeSlider: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      justifyContent: 'center',
    },
    rpeButton: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
    },
    rpeButtonText: {
      fontSize: 18,
      fontWeight: '600',
    },
    moodOptions: {
      gap: 12,
    },
    moodButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      gap: 12,
    },
    moodEmoji: {
      fontSize: 24,
    },
    moodLabel: {
      fontSize: 18,
    },
    notesInput: {
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      borderWidth: 1,
      minHeight: 120,
    },
    achievementsList: {
      gap: 12,
    },
    achievementButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      gap: 12,
    },
    achievementText: {
      fontSize: 16,
      flex: 1,
    },
    shareOptions: {
      flexDirection: 'row',
      gap: 16,
    },
    shareOption: {
      flex: 1,
      alignItems: 'center',
      padding: 20,
      borderRadius: 12,
      gap: 12,
    },
    shareOptionText: {
      fontSize: 16,
      fontWeight: '500',
      textAlign: 'center',
    },
    footer: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
      borderTopWidth: 1,
      borderTopColor: colors.accent,
    },
    footerButton: {
      flex: 1,
      paddingVertical: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    footerButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.header}>
          <View style={{ width: 40 }} />
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={20} color={colors.text} />
          </TouchableOpacity>
        </View>

        {renderProgressBar()}

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderCurrentStep()}
        </ScrollView>

        <View style={styles.footer}>
          {currentStep > 0 && (
            <TouchableOpacity
              style={[styles.footerButton, { backgroundColor: colors.accent }]}
              onPress={handlePrevious}
            >
              <Text style={[styles.footerButtonText, { color: colors.text }]}>
                Previous
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.footerButton,
              { backgroundColor: colors.primary },
              currentStep === 0 && { flex: 2 },
            ]}
            onPress={handleNext}
          >
            <Text style={[styles.footerButtonText, { color: colors.overlayText }]}>
              {currentStep === steps.length - 1 ? 'Complete Workout' : 'Next'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
