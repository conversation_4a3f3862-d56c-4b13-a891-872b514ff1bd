import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { CheckCircle, X, ExternalLink, Bell, MessageSquare, Dumbbell, AlertCircle } from 'lucide-react-native';
import { router } from 'expo-router';
import { useThemeStore } from '../store/themeStore';
import { useNotificationStore } from '../store/notificationStore';
import { useNotificationBannerStore } from '../services/notificationBannerService';

interface NotificationBannerProps {
  onNavigateToProgram?: (programId: string) => void;
}

// Icon mapping for different notification types
const NOTIFICATION_ICONS = {
  program_approved: CheckCircle,
  feedback: CheckCircle,
  check_in_response: CheckCircle,
  workout_reminder: Dumbbell,
  program_update: Bell,
  message: MessageSquare,
  default: Bell,
};

// Color mapping for different notification types
const NOTIFICATION_COLORS = {
  program_approved: 'success',
  feedback: 'primary',
  check_in_response: 'primary',
  workout_reminder: 'accent',
  program_update: 'primary',
  message: 'primary',
  default: 'primary',
};

export const NotificationBanner: React.FC<NotificationBannerProps> = ({
  onNavigateToProgram,
}) => {
  const { colors } = useThemeStore();
  const { notifications, markAsRead } = useNotificationStore();
  const { currentNotification, dismissCurrentNotification } = useNotificationBannerStore();
  const [visibleNotification, setVisibleNotification] = useState<any>(null);
  const [slideAnim] = useState(new Animated.Value(-100));

  // Handle banner notifications from the banner service
  useEffect(() => {
    if (currentNotification && !visibleNotification) {
      setVisibleNotification(currentNotification);
      showBanner();

      // Auto-hide if enabled
      if (currentNotification.autoHide !== false) {
        const duration = currentNotification.duration || 5000;
        setTimeout(() => {
          if (visibleNotification?.id === currentNotification.id) {
            handleDismiss();
          }
        }, duration);
      }
    }
  }, [currentNotification, visibleNotification]);

  // Also handle legacy notifications from notification store (for backward compatibility)
  useEffect(() => {
    if (!currentNotification) { // Only if no banner service notification is active
      const priorityOrder = ['program_approved', 'feedback', 'check_in_response', 'program_update', 'workout_reminder', 'message'];

      let selectedNotification = null;
      for (const type of priorityOrder) {
        const notification = notifications.find(n => n.type === type && !n.read);
        if (notification) {
          selectedNotification = notification;
          break;
        }
      }

      if (selectedNotification && !visibleNotification) {
        setVisibleNotification(selectedNotification);
        showBanner();

        // Auto-hide after 5 seconds for non-critical notifications
        if (selectedNotification.type !== 'program_approved') {
          setTimeout(() => {
            if (visibleNotification?.id === selectedNotification.id) {
              handleDismiss();
            }
          }, 5000);
        }
      }
    }
  }, [notifications, visibleNotification, currentNotification]);

  const showBanner = () => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const hideBanner = () => {
    Animated.timing(slideAnim, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setVisibleNotification(null);
    });
  };

  const handleDismiss = async () => {
    if (visibleNotification) {
      // Handle banner service notifications
      if (currentNotification?.id === visibleNotification.id) {
        dismissCurrentNotification();
      } else {
        // Handle legacy notifications
        await markAsRead(visibleNotification.id);
      }
      hideBanner();
    }
  };

  const handleViewProgram = async () => {
    if (visibleNotification?.data?.program_id) {
      await markAsRead(visibleNotification.id);
      onNavigateToProgram?.(visibleNotification.data.program_id);
      hideBanner();
    }
  };

  const handleNotificationTap = async () => {
    if (!visibleNotification) return;

    await markAsRead(visibleNotification.id);

    try {
      switch (visibleNotification.type) {
        case 'program_approved':
          if (visibleNotification.data?.program_id) {
            onNavigateToProgram?.(visibleNotification.data.program_id);
          } else {
            router.push('/(tabs)/workouts');
          }
          break;
        case 'feedback':
        case 'check_in_response':
          if (visibleNotification.data?.feedbackId) {
            router.push(`/(tabs)/checkin/${visibleNotification.data.feedbackId}`);
          } else if (visibleNotification.data?.checkInId) {
            router.push(`/(tabs)/checkin/${visibleNotification.data.checkInId}`);
          } else {
            router.push('/(tabs)/checkin');
          }
          break;
        case 'workout_reminder':
          router.push('/(tabs)/workouts');
          break;
        case 'program_update':
          if (visibleNotification.data?.programId) {
            router.push(`/(tabs)/workouts/${visibleNotification.data.programId}`);
          } else {
            router.push('/(tabs)/workouts');
          }
          break;
        case 'message':
          router.push('/(tabs)/messages');
          break;
        default:
          console.log('🔔 NotificationBanner: Unknown notification type for navigation');
      }
    } catch (error) {
      console.error('❌ NotificationBanner: Navigation failed:', error);
    }

    hideBanner();
  };

  if (!visibleNotification) {
    return null;
  }

  const IconComponent = NOTIFICATION_ICONS[visibleNotification.type] || NOTIFICATION_ICONS.default;
  const colorKey = NOTIFICATION_COLORS[visibleNotification.type] || NOTIFICATION_COLORS.default;
  const notificationColor = colors[colorKey] || colors.primary;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: notificationColor + '20',
          borderColor: notificationColor,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.content}
        onPress={handleNotificationTap}
        activeOpacity={0.8}
      >
        <View style={styles.iconContainer}>
          <IconComponent size={24} color={notificationColor} />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={[styles.title, { color: colors.text }]} numberOfLines={1}>
            {visibleNotification.title}
          </Text>
          <Text style={[styles.message, { color: colors.text + 'DD' }]} numberOfLines={2}>
            {visibleNotification.message || visibleNotification.body}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.dismissButton}
          onPress={handleDismiss}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <X size={20} color={colors.text + '80'} />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50, // Below status bar
    left: 16,
    right: 16,
    zIndex: 9999,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    lineHeight: 18,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  dismissButton: {
    padding: 4,
  },
});
