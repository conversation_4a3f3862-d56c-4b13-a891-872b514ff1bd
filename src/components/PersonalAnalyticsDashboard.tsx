import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  Target,
  Award,
  Dumbbell,
  Clock,
  Zap,
  BarChart3,
  Activity,
  Filter,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { supabase } from '../services/supabaseClient';

const { width } = Dimensions.get('window');

interface AnalyticsData {
  workoutStats: {
    totalWorkouts: number;
    completedWorkouts: number;
    completionRate: number;
    totalDurationHours: number;
    averageSessionDuration: number;
    averageRPE: number;
    workoutStreak: number;
  };
  strengthProgression: {
    exerciseId: string;
    exerciseName: string;
    data: Array<{
      date: string;
      maxWeight: number;
      totalVolume: number;
    }>;
  }[];
  weeklyTrends: {
    week: string;
    workouts: number;
    totalVolume: number;
    averageRPE: number;
  }[];
  bodyComposition: {
    date: string;
    weight: number;
    bodyFat?: number;
    muscleMass?: number;
  }[];
  personalRecords: {
    exerciseName: string;
    recordType: string;
    value: number;
    unit: string;
    achievedAt: string;
    improvement: number;
  }[];
}

interface TimeFilter {
  label: string;
  value: '7d' | '30d' | '90d' | '1y';
  days: number;
}

const TIME_FILTERS: TimeFilter[] = [
  { label: '7 Days', value: '7d', days: 7 },
  { label: '30 Days', value: '30d', days: 30 },
  { label: '90 Days', value: '90d', days: 90 },
  { label: '1 Year', value: '1y', days: 365 },
];

export const PersonalAnalyticsDashboard: React.FC = () => {
  const { colors } = useThemeStore();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeFilter, setSelectedTimeFilter] = useState<TimeFilter>(TIME_FILTERS[1]); // 30 days default
  const [selectedChart, setSelectedChart] = useState<'strength' | 'volume' | 'rpe' | 'body'>('strength');

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedTimeFilter]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - selectedTimeFilter.days);

      // Load workout stats
      const { data: workoutStats } = await supabase.rpc('get_user_workout_stats', {
        p_user_id: user.id,
        p_start_date: startDate.toISOString().split('T')[0],
        p_end_date: endDate.toISOString().split('T')[0],
      });

      // Load strength progression for top exercises
      const { data: topExercises } = await supabase
        .from('exercise_logs')
        .select(`
          exercise_id,
          exercises!inner(name),
          set_logs!inner(actual_weight_kg, actual_reps)
        `)
        .eq('user_id', user.id)
        .gte('created_at', startDate.toISOString())
        .limit(5);

      // Load weekly trends
      const { data: weeklyData } = await supabase
        .from('workout_logs')
        .select('completed_at, total_weight_kg, session_rpe')
        .eq('user_id', user.id)
        .eq('is_completed', true)
        .gte('completed_at', startDate.toISOString())
        .order('completed_at');

      // Load body composition data
      const { data: bodyData } = await supabase
        .from('body_measurements')
        .select('measurement_date, weight_kg, body_fat_percentage, muscle_mass_kg')
        .eq('user_id', user.id)
        .gte('measurement_date', startDate.toISOString().split('T')[0])
        .order('measurement_date');

      // Load personal records
      const { data: recordsData } = await supabase
        .from('personal_records')
        .select(`
          exercises!inner(name),
          record_type,
          value,
          unit,
          achieved_at,
          improvement_percentage
        `)
        .eq('user_id', user.id)
        .gte('achieved_at', startDate.toISOString())
        .order('achieved_at', { ascending: false });

      // Process and format data
      const analytics: AnalyticsData = {
        workoutStats: workoutStats?.[0] || {
          totalWorkouts: 0,
          completedWorkouts: 0,
          completionRate: 0,
          totalDurationHours: 0,
          averageSessionDuration: 0,
          averageRPE: 0,
          workoutStreak: 0,
        },
        strengthProgression: processStrengthProgression(topExercises || []),
        weeklyTrends: processWeeklyTrends(weeklyData || []),
        bodyComposition: processBodyComposition(bodyData || []),
        personalRecords: processPersonalRecords(recordsData || []),
      };

      setAnalyticsData(analytics);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const processStrengthProgression = (data: any[]) => {
    // Group by exercise and calculate progression
    const exerciseMap = new Map();
    
    data.forEach(item => {
      const exerciseId = item.exercise_id;
      const exerciseName = item.exercises.name;
      
      if (!exerciseMap.has(exerciseId)) {
        exerciseMap.set(exerciseId, {
          exerciseId,
          exerciseName,
          data: [],
        });
      }
    });

    return Array.from(exerciseMap.values()).slice(0, 3); // Top 3 exercises
  };

  const processWeeklyTrends = (data: any[]) => {
    const weeklyMap = new Map();
    
    data.forEach(workout => {
      const date = new Date(workout.completed_at);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekKey = weekStart.toISOString().split('T')[0];
      
      if (!weeklyMap.has(weekKey)) {
        weeklyMap.set(weekKey, {
          week: weekKey,
          workouts: 0,
          totalVolume: 0,
          totalRPE: 0,
          rpeCount: 0,
        });
      }
      
      const week = weeklyMap.get(weekKey);
      week.workouts++;
      week.totalVolume += workout.total_weight_kg || 0;
      if (workout.session_rpe) {
        week.totalRPE += workout.session_rpe;
        week.rpeCount++;
      }
    });

    return Array.from(weeklyMap.values())
      .map(week => ({
        week: week.week,
        workouts: week.workouts,
        totalVolume: week.totalVolume,
        averageRPE: week.rpeCount > 0 ? week.totalRPE / week.rpeCount : 0,
      }))
      .sort((a, b) => a.week.localeCompare(b.week));
  };

  const processBodyComposition = (data: any[]) => {
    return data.map(item => ({
      date: item.measurement_date,
      weight: item.weight_kg || 0,
      bodyFat: item.body_fat_percentage,
      muscleMass: item.muscle_mass_kg,
    }));
  };

  const processPersonalRecords = (data: any[]) => {
    return data.map(record => ({
      exerciseName: record.exercises.name,
      recordType: record.record_type,
      value: record.value,
      unit: record.unit,
      achievedAt: record.achieved_at,
      improvement: record.improvement_percentage || 0,
    }));
  };

  const renderTimeFilter = () => (
    <View style={styles.timeFilter}>
      {TIME_FILTERS.map((filter) => (
        <TouchableOpacity
          key={filter.value}
          style={[
            styles.timeFilterButton,
            {
              backgroundColor: selectedTimeFilter.value === filter.value ? colors.primary : colors.accent,
            },
          ]}
          onPress={() => setSelectedTimeFilter(filter)}
        >
          <Text
            style={[
              styles.timeFilterText,
              {
                color: selectedTimeFilter.value === filter.value ? colors.overlayText : colors.text,
              },
            ]}
          >
            {filter.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverviewCards = () => {
    if (!analyticsData) return null;

    const { workoutStats } = analyticsData;

    return (
      <View style={styles.overviewCards}>
        <View style={[styles.overviewCard, { backgroundColor: colors.accent }]}>
          <View style={[styles.cardIcon, { backgroundColor: colors.primary + '20' }]}>
            <Dumbbell size={20} color={colors.primary} />
          </View>
          <Text style={[styles.cardValue, { color: colors.text }]}>
            {workoutStats.completedWorkouts}
          </Text>
          <Text style={[styles.cardLabel, { color: colors.text + '80' }]}>
            Workouts
          </Text>
        </View>

        <View style={[styles.overviewCard, { backgroundColor: colors.accent }]}>
          <View style={[styles.cardIcon, { backgroundColor: colors.success + '20' }]}>
            <Target size={20} color={colors.success} />
          </View>
          <Text style={[styles.cardValue, { color: colors.text }]}>
            {workoutStats.completionRate.toFixed(0)}%
          </Text>
          <Text style={[styles.cardLabel, { color: colors.text + '80' }]}>
            Completion
          </Text>
        </View>

        <View style={[styles.overviewCard, { backgroundColor: colors.accent }]}>
          <View style={[styles.cardIcon, { backgroundColor: colors.warning + '20' }]}>
            <Clock size={20} color={colors.warning} />
          </View>
          <Text style={[styles.cardValue, { color: colors.text }]}>
            {workoutStats.totalDurationHours.toFixed(1)}h
          </Text>
          <Text style={[styles.cardLabel, { color: colors.text + '80' }]}>
            Total Time
          </Text>
        </View>

        <View style={[styles.overviewCard, { backgroundColor: colors.accent }]}>
          <View style={[styles.cardIcon, { backgroundColor: colors.error + '20' }]}>
            <Zap size={20} color={colors.error} />
          </View>
          <Text style={[styles.cardValue, { color: colors.text }]}>
            {workoutStats.averageRPE.toFixed(1)}
          </Text>
          <Text style={[styles.cardLabel, { color: colors.text + '80' }]}>
            Avg RPE
          </Text>
        </View>
      </View>
    );
  };

  const renderChartSelector = () => (
    <View style={styles.chartSelector}>
      {[
        { key: 'strength', label: 'Strength', icon: <TrendingUp size={16} /> },
        { key: 'volume', label: 'Volume', icon: <BarChart3 size={16} /> },
        { key: 'rpe', label: 'Intensity', icon: <Activity size={16} /> },
        { key: 'body', label: 'Body', icon: <Target size={16} /> },
      ].map((chart) => (
        <TouchableOpacity
          key={chart.key}
          style={[
            styles.chartSelectorButton,
            {
              backgroundColor: selectedChart === chart.key ? colors.primary : colors.accent,
            },
          ]}
          onPress={() => setSelectedChart(chart.key as any)}
        >
          {React.cloneElement(chart.icon as React.ReactElement, {
            color: selectedChart === chart.key ? colors.overlayText : colors.text,
          })}
          <Text
            style={[
              styles.chartSelectorText,
              {
                color: selectedChart === chart.key ? colors.overlayText : colors.text,
              },
            ]}
          >
            {chart.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderChart = () => {
    if (!analyticsData) return null;

    const chartConfig = {
      backgroundColor: colors.background,
      backgroundGradientFrom: colors.background,
      backgroundGradientTo: colors.background,
      decimalPlaces: 1,
      color: (opacity = 1) => colors.primary + Math.round(opacity * 255).toString(16),
      labelColor: (opacity = 1) => colors.text + Math.round(opacity * 255).toString(16),
      style: {
        borderRadius: 16,
      },
      propsForDots: {
        r: '4',
        strokeWidth: '2',
        stroke: colors.primary,
      },
    };

    switch (selectedChart) {
      case 'strength':
        if (analyticsData.strengthProgression.length === 0) {
          return renderNoDataMessage('No strength data available');
        }
        // Render strength progression chart
        return (
          <View style={styles.chartContainer}>
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              Strength Progression
            </Text>
            {/* Chart implementation would go here */}
            <View style={styles.placeholderChart}>
              <TrendingUp size={48} color={colors.text + '40'} />
              <Text style={[styles.placeholderText, { color: colors.text + '60' }]}>
                Strength progression chart
              </Text>
            </View>
          </View>
        );

      case 'volume':
        if (analyticsData.weeklyTrends.length === 0) {
          return renderNoDataMessage('No volume data available');
        }
        
        const volumeData = {
          labels: analyticsData.weeklyTrends.map(week => 
            new Date(week.week).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
          ),
          datasets: [{
            data: analyticsData.weeklyTrends.map(week => week.totalVolume),
          }],
        };

        return (
          <View style={styles.chartContainer}>
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              Weekly Training Volume
            </Text>
            <BarChart
              data={volumeData}
              width={width - 40}
              height={220}
              chartConfig={chartConfig}
              style={styles.chart}
            />
          </View>
        );

      case 'rpe':
        if (analyticsData.weeklyTrends.length === 0) {
          return renderNoDataMessage('No intensity data available');
        }
        
        const rpeData = {
          labels: analyticsData.weeklyTrends.map(week => 
            new Date(week.week).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
          ),
          datasets: [{
            data: analyticsData.weeklyTrends.map(week => week.averageRPE || 0),
          }],
        };

        return (
          <View style={styles.chartContainer}>
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              Average Training Intensity (RPE)
            </Text>
            <LineChart
              data={rpeData}
              width={width - 40}
              height={220}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
            />
          </View>
        );

      case 'body':
        if (analyticsData.bodyComposition.length === 0) {
          return renderNoDataMessage('No body composition data available');
        }
        
        const bodyData = {
          labels: analyticsData.bodyComposition.map(data => 
            new Date(data.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
          ),
          datasets: [{
            data: analyticsData.bodyComposition.map(data => data.weight),
          }],
        };

        return (
          <View style={styles.chartContainer}>
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              Body Weight Trend
            </Text>
            <LineChart
              data={bodyData}
              width={width - 40}
              height={220}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
            />
          </View>
        );

      default:
        return null;
    }
  };

  const renderNoDataMessage = (message: string) => (
    <View style={styles.noDataContainer}>
      <BarChart3 size={48} color={colors.text + '40'} />
      <Text style={[styles.noDataText, { color: colors.text }]}>
        {message}
      </Text>
      <Text style={[styles.noDataSubtext, { color: colors.text + '80' }]}>
        Complete more workouts to see analytics
      </Text>
    </View>
  );

  const renderPersonalRecords = () => {
    if (!analyticsData || analyticsData.personalRecords.length === 0) {
      return (
        <View style={[styles.section, { backgroundColor: colors.accent }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Recent Personal Records
          </Text>
          <View style={styles.noRecordsContainer}>
            <Award size={32} color={colors.text + '40'} />
            <Text style={[styles.noRecordsText, { color: colors.text + '60' }]}>
              No personal records yet
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={[styles.section, { backgroundColor: colors.accent }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Recent Personal Records
        </Text>
        <View style={styles.recordsList}>
          {analyticsData.personalRecords.slice(0, 3).map((record, index) => (
            <View key={index} style={[styles.recordItem, { backgroundColor: colors.background }]}>
              <View style={styles.recordInfo}>
                <Text style={[styles.recordExercise, { color: colors.text }]}>
                  {record.exerciseName}
                </Text>
                <Text style={[styles.recordType, { color: colors.text + '80' }]}>
                  {record.recordType.replace('_', ' ').toUpperCase()}
                </Text>
              </View>
              <View style={styles.recordValue}>
                <Text style={[styles.recordNumber, { color: colors.primary }]}>
                  {record.value} {record.unit}
                </Text>
                {record.improvement > 0 && (
                  <View style={styles.recordImprovement}>
                    <TrendingUp size={12} color={colors.success} />
                    <Text style={[styles.improvementText, { color: colors.success }]}>
                      +{record.improvement.toFixed(1)}%
                    </Text>
                  </View>
                )}
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderWorkoutStreak = () => {
    if (!analyticsData) return null;

    const { workoutStreak } = analyticsData.workoutStats;
    const streakDays = Math.floor(workoutStreak);
    const streakWeeks = Math.floor(streakDays / 7);

    return (
      <View style={[styles.streakCard, { backgroundColor: colors.primary + '10' }]}>
        <View style={styles.streakHeader}>
          <View style={[styles.streakIcon, { backgroundColor: colors.primary }]}>
            <Calendar size={24} color={colors.overlayText} />
          </View>
          <View style={styles.streakInfo}>
            <Text style={[styles.streakTitle, { color: colors.text }]}>
              Current Streak
            </Text>
            <Text style={[styles.streakSubtitle, { color: colors.text + '80' }]}>
              Keep it going!
            </Text>
          </View>
        </View>
        <View style={styles.streakValue}>
          <Text style={[styles.streakNumber, { color: colors.primary }]}>
            {streakDays}
          </Text>
          <Text style={[styles.streakUnit, { color: colors.primary }]}>
            {streakDays === 1 ? 'day' : 'days'}
          </Text>
        </View>
        {streakWeeks > 0 && (
          <Text style={[styles.streakWeeks, { color: colors.text + '80' }]}>
            {streakWeeks} {streakWeeks === 1 ? 'week' : 'weeks'}
          </Text>
        )}
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
    },
    filterButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.accent,
      justifyContent: 'center',
      alignItems: 'center',
    },
    timeFilter: {
      flexDirection: 'row',
      padding: 20,
      gap: 8,
    },
    timeFilterButton: {
      flex: 1,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    timeFilterText: {
      fontSize: 12,
      fontWeight: '500',
    },
    content: {
      flex: 1,
    },
    overviewCards: {
      flexDirection: 'row',
      padding: 20,
      gap: 12,
    },
    overviewCard: {
      flex: 1,
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      gap: 8,
    },
    cardIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    cardValue: {
      fontSize: 20,
      fontWeight: '700',
    },
    cardLabel: {
      fontSize: 12,
      textAlign: 'center',
    },
    chartSelector: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      paddingBottom: 20,
      gap: 8,
    },
    chartSelectorButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
      gap: 4,
    },
    chartSelectorText: {
      fontSize: 12,
      fontWeight: '500',
    },
    chartContainer: {
      padding: 20,
    },
    chartTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 16,
      textAlign: 'center',
    },
    chart: {
      marginVertical: 8,
      borderRadius: 16,
    },
    placeholderChart: {
      height: 220,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.accent,
      borderRadius: 16,
      gap: 12,
    },
    placeholderText: {
      fontSize: 16,
    },
    noDataContainer: {
      height: 220,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.accent,
      borderRadius: 16,
      margin: 20,
      gap: 12,
    },
    noDataText: {
      fontSize: 16,
      fontWeight: '500',
    },
    noDataSubtext: {
      fontSize: 14,
      textAlign: 'center',
    },
    section: {
      margin: 20,
      padding: 20,
      borderRadius: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 16,
    },
    recordsList: {
      gap: 12,
    },
    recordItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      borderRadius: 12,
    },
    recordInfo: {
      flex: 1,
    },
    recordExercise: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 4,
    },
    recordType: {
      fontSize: 12,
    },
    recordValue: {
      alignItems: 'flex-end',
    },
    recordNumber: {
      fontSize: 18,
      fontWeight: '700',
      marginBottom: 4,
    },
    recordImprovement: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    improvementText: {
      fontSize: 12,
      fontWeight: '500',
    },
    noRecordsContainer: {
      alignItems: 'center',
      padding: 20,
      gap: 8,
    },
    noRecordsText: {
      fontSize: 14,
    },
    streakCard: {
      margin: 20,
      padding: 20,
      borderRadius: 16,
    },
    streakHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
      gap: 12,
    },
    streakIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    streakInfo: {
      flex: 1,
    },
    streakTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 4,
    },
    streakSubtitle: {
      fontSize: 14,
    },
    streakValue: {
      flexDirection: 'row',
      alignItems: 'baseline',
      justifyContent: 'center',
      gap: 8,
    },
    streakNumber: {
      fontSize: 48,
      fontWeight: '700',
    },
    streakUnit: {
      fontSize: 18,
      fontWeight: '500',
    },
    streakWeeks: {
      fontSize: 14,
      textAlign: 'center',
      marginTop: 8,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: 16,
      color: colors.text + '80',
      marginTop: 16,
    },
  });

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading analytics...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Analytics</Text>
        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderTimeFilter()}
        {renderOverviewCards()}
        {renderWorkoutStreak()}
        {renderChartSelector()}
        {renderChart()}
        {renderPersonalRecords()}
      </ScrollView>
    </View>
  );
};

  const renderPersonalRecords = () => {
    if (!analyticsData || analyticsData.personalRecords.length === 0) {
      return (
        <View style={[styles.section, { backgroundColor: colors.accent }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Recent Personal Records
          </Text>
          <View style={styles.noRecordsContainer}>
            <Award size={32} color={colors.text + '40'} />
            <Text style={[styles.noRecordsText, { color: colors.text + '60' }]}>
              No personal records yet
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={[styles.section, { backgroundColor: colors.accent }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Recent Personal Records
        </Text>
        <View style={styles.recordsList}>
          {analyticsData.personalRecords.slice(0, 3).map((record, index) => (
            <View key={index} style={[styles.recordItem, { backgroundColor: colors.background }]}>
              <View style={styles.recordInfo}>
                <Text style={[styles.recordExercise, { color: colors.text }]}>
                  {record.exerciseName}
                </Text>
                <Text style={[styles.recordType, { color: colors.text + '80' }]}>
                  {record.recordType.replace('_', ' ').toUpperCase()}
                </Text>
              </View>
              <View style={styles.recordValue}>
                <Text style={[styles.recordNumber, { color: colors.primary }]}>
                  {record.value} {record.unit}
                </Text>
                {record.improvement > 0 && (
                  <View style={styles.recordImprovement}>
                    <TrendingUp size={12} color={colors.success} />
                    <Text style={[styles.improvementText, { color: colors.success }]}>
                      +{record.improvement.toFixed(1)}%
                    </Text>
                  </View>
                )}
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderWorkoutStreak = () => {
    if (!analyticsData) return null;

    const { workoutStreak } = analyticsData.workoutStats;
    const streakDays = Math.floor(workoutStreak);
    const streakWeeks = Math.floor(streakDays / 7);

    return (
      <View style={[styles.streakCard, { backgroundColor: colors.primary + '10' }]}>
        <View style={styles.streakHeader}>
          <View style={[styles.streakIcon, { backgroundColor: colors.primary }]}>
            <Calendar size={24} color={colors.overlayText} />
          </View>
          <View style={styles.streakInfo}>
            <Text style={[styles.streakTitle, { color: colors.text }]}>
              Current Streak
            </Text>
            <Text style={[styles.streakSubtitle, { color: colors.text + '80' }]}>
              Keep it going!
            </Text>
          </View>
        </View>
        <View style={styles.streakValue}>
          <Text style={[styles.streakNumber, { color: colors.primary }]}>
            {streakDays}
          </Text>
          <Text style={[styles.streakUnit, { color: colors.primary }]}>
            {streakDays === 1 ? 'day' : 'days'}
          </Text>
        </View>
        {streakWeeks > 0 && (
          <Text style={[styles.streakWeeks, { color: colors.text + '80' }]}>
            {streakWeeks} {streakWeeks === 1 ? 'week' : 'weeks'}
          </Text>
        )}
      </View>
    );
  };
