import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
  Dimensions,
} from 'react-native';
import { ProgressChart } from 'react-native-chart-kit';
import {
  Target,
  Plus,
  Calendar,
  TrendingUp,
  Award,
  Edit3,
  Trash2,
  CheckCircle,
  Clock,
  Flag,
  X,
  Save,
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useOfflineOperation } from '../hooks/useOfflineSync';
import { supabase } from '../services/supabaseClient';

const { width } = Dimensions.get('window');

interface FitnessGoal {
  id: string;
  goalType: 'weight_loss' | 'weight_gain' | 'muscle_gain' | 'strength' | 'endurance' | 'body_fat' | 'measurement' | 'performance' | 'habit';
  title: string;
  description?: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  targetDate?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  progressPercentage: number;
  milestoneValues?: number[];
  milestoneDates?: string[];
  achievedMilestones: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface GoalFormData {
  goalType: FitnessGoal['goalType'];
  title: string;
  description: string;
  targetValue: string;
  currentValue: string;
  unit: string;
  targetDate: string;
  priority: FitnessGoal['priority'];
}

const GOAL_TYPES = [
  { key: 'weight_loss', label: 'Weight Loss', icon: '⬇️', unit: 'kg' },
  { key: 'weight_gain', label: 'Weight Gain', icon: '⬆️', unit: 'kg' },
  { key: 'muscle_gain', label: 'Muscle Gain', icon: '💪', unit: 'kg' },
  { key: 'strength', label: 'Strength', icon: '🏋️', unit: 'kg' },
  { key: 'endurance', label: 'Endurance', icon: '🏃', unit: 'min' },
  { key: 'body_fat', label: 'Body Fat', icon: '📊', unit: '%' },
  { key: 'measurement', label: 'Measurement', icon: '📏', unit: 'cm' },
  { key: 'performance', label: 'Performance', icon: '⚡', unit: 'reps' },
  { key: 'habit', label: 'Habit', icon: '✅', unit: 'days' },
] as const;

const PRIORITY_COLORS = {
  low: '#10B981',
  medium: '#F59E0B',
  high: '#EF4444',
};

export const GoalTracker: React.FC = () => {
  const { colors } = useThemeStore();
  const { executeOperation } = useOfflineOperation();
  const [goals, setGoals] = useState<FitnessGoal[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<FitnessGoal | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'completed'>('active');
  const [formData, setFormData] = useState<GoalFormData>({
    goalType: 'weight_loss',
    title: '',
    description: '',
    targetValue: '',
    currentValue: '',
    unit: 'kg',
    targetDate: '',
    priority: 'medium',
  });

  useEffect(() => {
    loadGoals();
  }, []);

  const loadGoals = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('fitness_goals')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedGoals: FitnessGoal[] = data?.map(goal => ({
        id: goal.id,
        goalType: goal.goal_type,
        title: goal.title,
        description: goal.description,
        targetValue: goal.target_value,
        currentValue: goal.current_value,
        unit: goal.unit,
        targetDate: goal.target_date,
        priority: goal.priority,
        status: goal.status,
        progressPercentage: goal.progress_percentage,
        milestoneValues: goal.milestone_values,
        milestoneDates: goal.milestone_dates,
        achievedMilestones: goal.achieved_milestones,
        notes: goal.notes,
        createdAt: goal.created_at,
        updatedAt: goal.updated_at,
      })) || [];

      setGoals(formattedGoals);
    } catch (error) {
      console.error('Failed to load goals:', error);
      Alert.alert('Error', 'Failed to load goals');
    } finally {
      setIsLoading(false);
    }
  };

  const saveGoal = async () => {
    try {
      if (!formData.title.trim()) {
        Alert.alert('Error', 'Please enter a goal title');
        return;
      }

      if (!formData.targetValue || parseFloat(formData.targetValue) <= 0) {
        Alert.alert('Error', 'Please enter a valid target value');
        return;
      }

      await executeOperation(
        async () => {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) throw new Error('No authenticated user');

          const goalData = {
            user_id: user.id,
            goal_type: formData.goalType,
            title: formData.title.trim(),
            description: formData.description.trim() || null,
            target_value: parseFloat(formData.targetValue),
            current_value: parseFloat(formData.currentValue) || 0,
            unit: formData.unit,
            target_date: formData.targetDate || null,
            priority: formData.priority,
            status: 'active' as const,
            progress_percentage: 0,
            achieved_milestones: 0,
          };

          const { data, error } = await supabase
            .from('fitness_goals')
            .insert(goalData)
            .select()
            .single();

          if (error) throw error;

          const newGoal: FitnessGoal = {
            id: data.id,
            goalType: data.goal_type,
            title: data.title,
            description: data.description,
            targetValue: data.target_value,
            currentValue: data.current_value,
            unit: data.unit,
            targetDate: data.target_date,
            priority: data.priority,
            status: data.status,
            progressPercentage: data.progress_percentage,
            milestoneValues: data.milestone_values,
            milestoneDates: data.milestone_dates,
            achievedMilestones: data.achieved_milestones,
            notes: data.notes,
            createdAt: data.created_at,
            updatedAt: data.updated_at,
          };

          setGoals(prev => [newGoal, ...prev]);
          setShowAddModal(false);
          resetForm();
          Alert.alert('Success', 'Goal created successfully!');
        },
        {
          type: 'GOAL_CREATE',
          payload: formData,
        }
      );
    } catch (error) {
      console.error('Failed to save goal:', error);
      Alert.alert('Error', 'Failed to save goal');
    }
  };

  const updateGoalProgress = async (goalId: string, newValue: number) => {
    try {
      const goal = goals.find(g => g.id === goalId);
      if (!goal) return;

      const progressPercentage = Math.min((newValue / goal.targetValue) * 100, 100);
      const isCompleted = progressPercentage >= 100;

      await executeOperation(
        async () => {
          const { error } = await supabase
            .from('fitness_goals')
            .update({
              current_value: newValue,
              progress_percentage: progressPercentage,
              status: isCompleted ? 'completed' : 'active',
              updated_at: new Date().toISOString(),
            })
            .eq('id', goalId);

          if (error) throw error;

          setGoals(prev => prev.map(g => 
            g.id === goalId 
              ? { 
                  ...g, 
                  currentValue: newValue, 
                  progressPercentage,
                  status: isCompleted ? 'completed' : 'active',
                  updatedAt: new Date().toISOString(),
                }
              : g
          ));

          if (isCompleted) {
            Alert.alert('🎉 Goal Completed!', `Congratulations! You've achieved your goal: ${goal.title}`);
          }
        },
        {
          type: 'GOAL_UPDATE_PROGRESS',
          payload: { goalId, newValue, progressPercentage },
        }
      );
    } catch (error) {
      console.error('Failed to update goal progress:', error);
      Alert.alert('Error', 'Failed to update goal progress');
    }
  };

  const deleteGoal = async (goalId: string) => {
    Alert.alert(
      'Delete Goal',
      'Are you sure you want to delete this goal?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('fitness_goals')
                .delete()
                .eq('id', goalId);

              if (error) throw error;

              setGoals(prev => prev.filter(g => g.id !== goalId));
              Alert.alert('Success', 'Goal deleted successfully');
            } catch (error) {
              console.error('Failed to delete goal:', error);
              Alert.alert('Error', 'Failed to delete goal');
            }
          },
        },
      ]
    );

  const renderGoalCard = (goal: FitnessGoal) => {
    const goalType = GOAL_TYPES.find(type => type.key === goal.goalType);
    const daysUntilTarget = getDaysUntilTarget(goal.targetDate);
    const isOverdue = daysUntilTarget !== null && daysUntilTarget < 0;
    const isCompleted = goal.status === 'completed';

    return (
      <View
        key={goal.id}
        style={[
          styles.goalCard,
          {
            backgroundColor: colors.accent,
            borderLeftColor: PRIORITY_COLORS[goal.priority],
            borderLeftWidth: 4,
          },
        ]}
      >
        <View style={styles.goalHeader}>
          <View style={styles.goalTitleSection}>
            <Text style={styles.goalTypeEmoji}>{goalType?.icon}</Text>
            <View style={styles.goalTitleContainer}>
              <Text style={[styles.goalTitle, { color: colors.text }]}>
                {goal.title}
              </Text>
              <Text style={[styles.goalType, { color: colors.text + '80' }]}>
                {goalType?.label}
              </Text>
            </View>
          </View>

          <View style={styles.goalActions}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary + '20' }]}
              onPress={() => {
                setSelectedGoal(goal);
                setShowEditModal(true);
              }}
            >
              <Edit3 size={16} color={colors.primary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.error + '20' }]}
              onPress={() => deleteGoal(goal.id)}
            >
              <Trash2 size={16} color={colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        {goal.description && (
          <Text style={[styles.goalDescription, { color: colors.text + '80' }]}>
            {goal.description}
          </Text>
        )}

        <View style={styles.goalProgress}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressText, { color: colors.text }]}>
              {goal.currentValue} / {goal.targetValue} {goal.unit}
            </Text>
            <Text style={[styles.progressPercentage, { color: colors.primary }]}>
              {goal.progressPercentage.toFixed(0)}%
            </Text>
          </View>

          <View style={[styles.progressBar, { backgroundColor: colors.background }]}>
            <View
              style={[
                styles.progressFill,
                {
                  backgroundColor: isCompleted ? colors.success : colors.primary,
                  width: `${Math.min(goal.progressPercentage, 100)}%`,
                },
              ]}
            />
          </View>
        </View>

        {goal.targetDate && (
          <View style={styles.goalDeadline}>
            <Calendar size={14} color={isOverdue ? colors.error : colors.text + '80'} />
            <Text
              style={[
                styles.deadlineText,
                { color: isOverdue ? colors.error : colors.text + '80' },
              ]}
            >
              {isOverdue
                ? `Overdue by ${Math.abs(daysUntilTarget!)} days`
                : daysUntilTarget === 0
                ? 'Due today'
                : daysUntilTarget === 1
                ? 'Due tomorrow'
                : `${daysUntilTarget} days left`
              }
            </Text>
          </View>
        )}

        {isCompleted && (
          <View style={[styles.completedBadge, { backgroundColor: colors.success + '20' }]}>
            <CheckCircle size={16} color={colors.success} />
            <Text style={[styles.completedText, { color: colors.success }]}>
              Completed!
            </Text>
          </View>
        )}

        {!isCompleted && (
          <TouchableOpacity
            style={[styles.updateButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              Alert.prompt(
                'Update Progress',
                `Current: ${goal.currentValue} ${goal.unit}\nEnter new value:`,
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Update',
                    onPress: (value) => {
                      const numValue = parseFloat(value || '0');
                      if (!isNaN(numValue) && numValue >= 0) {
                        updateGoalProgress(goal.id, numValue);
                      }
                    },
                  },
                ],
                'plain-text',
                goal.currentValue.toString()
              );
            }}
          >
            <TrendingUp size={16} color={colors.overlayText} />
            <Text style={[styles.updateButtonText, { color: colors.overlayText }]}>
              Update Progress
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderAddGoalModal = () => (
    <Modal visible={showAddModal} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            style={[styles.modalButton, { backgroundColor: colors.error }]}
            onPress={() => {
              setShowAddModal(false);
              resetForm();
            }}
          >
            <X size={20} color={colors.overlayText} />
          </TouchableOpacity>

          <Text style={[styles.modalTitle, { color: colors.text }]}>
            Create New Goal
          </Text>

          <TouchableOpacity
            style={[styles.modalButton, { backgroundColor: colors.success }]}
            onPress={saveGoal}
          >
            <Save size={20} color={colors.overlayText} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          <View style={styles.formSection}>
            <Text style={[styles.formLabel, { color: colors.text }]}>
              Goal Type
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.goalTypeSelector}>
              {GOAL_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.key}
                  style={[
                    styles.goalTypeButton,
                    {
                      backgroundColor: formData.goalType === type.key ? colors.primary : colors.accent,
                    },
                  ]}
                  onPress={() => {
                    setFormData({ ...formData, goalType: type.key, unit: type.unit });
                  }}
                >
                  <Text style={styles.goalTypeEmoji}>{type.icon}</Text>
                  <Text
                    style={[
                      styles.goalTypeLabel,
                      {
                        color: formData.goalType === type.key ? colors.overlayText : colors.text,
                      },
                    ]}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.formLabel, { color: colors.text }]}>
              Goal Title *
            </Text>
            <TextInput
              style={[
                styles.formInput,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
              placeholder="e.g., Lose 10kg for summer"
              placeholderTextColor={colors.text + '60'}
            />
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.formLabel, { color: colors.text }]}>
              Description (Optional)
            </Text>
            <TextInput
              style={[
                styles.formTextArea,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              placeholder="Add more details about your goal..."
              placeholderTextColor={colors.text + '60'}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formSection, { flex: 1 }]}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Current Value
              </Text>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: colors.accent,
                    color: colors.text,
                    borderColor: colors.primary + '40',
                  },
                ]}
                value={formData.currentValue}
                onChangeText={(text) => setFormData({ ...formData, currentValue: text })}
                placeholder="0"
                placeholderTextColor={colors.text + '60'}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.formSection, { flex: 1 }]}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Target Value *
              </Text>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: colors.accent,
                    color: colors.text,
                    borderColor: colors.primary + '40',
                  },
                ]}
                value={formData.targetValue}
                onChangeText={(text) => setFormData({ ...formData, targetValue: text })}
                placeholder="100"
                placeholderTextColor={colors.text + '60'}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.formSection, { flex: 0.5 }]}>
              <Text style={[styles.formLabel, { color: colors.text }]}>
                Unit
              </Text>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: colors.accent,
                    color: colors.text,
                    borderColor: colors.primary + '40',
                  },
                ]}
                value={formData.unit}
                onChangeText={(text) => setFormData({ ...formData, unit: text })}
                placeholder="kg"
                placeholderTextColor={colors.text + '60'}
              />
            </View>
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.formLabel, { color: colors.text }]}>
              Target Date (Optional)
            </Text>
            <TextInput
              style={[
                styles.formInput,
                {
                  backgroundColor: colors.accent,
                  color: colors.text,
                  borderColor: colors.primary + '40',
                },
              ]}
              value={formData.targetDate}
              onChangeText={(text) => setFormData({ ...formData, targetDate: text })}
              placeholder="YYYY-MM-DD"
              placeholderTextColor={colors.text + '60'}
            />
          </View>

          <View style={styles.formSection}>
            <Text style={[styles.formLabel, { color: colors.text }]}>
              Priority
            </Text>
            <View style={styles.prioritySelector}>
              {(['low', 'medium', 'high'] as const).map((priority) => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.priorityButton,
                    {
                      backgroundColor: formData.priority === priority
                        ? PRIORITY_COLORS[priority] + '20'
                        : colors.accent,
                      borderColor: formData.priority === priority
                        ? PRIORITY_COLORS[priority]
                        : 'transparent',
                      borderWidth: 2,
                    },
                  ]}
                  onPress={() => setFormData({ ...formData, priority })}
                >
                  <Flag size={16} color={PRIORITY_COLORS[priority]} />
                  <Text
                    style={[
                      styles.priorityText,
                      {
                        color: formData.priority === priority
                          ? PRIORITY_COLORS[priority]
                          : colors.text,
                      },
                    ]}
                  >
                    {priority.charAt(0).toUpperCase() + priority.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
    },
    addButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    filterTabs: {
      flexDirection: 'row',
      padding: 20,
      gap: 8,
    },
    filterTab: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    filterTabText: {
      fontSize: 14,
      fontWeight: '500',
    },
    content: {
      flex: 1,
      padding: 20,
    },
    goalsList: {
      gap: 16,
    },
    goalCard: {
      padding: 20,
      borderRadius: 16,
    },
    goalHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    goalTitleSection: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      gap: 12,
    },
    goalTypeEmoji: {
      fontSize: 24,
    },
    goalTitleContainer: {
      flex: 1,
    },
    goalTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 4,
    },
    goalType: {
      fontSize: 14,
    },
    goalActions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    goalDescription: {
      fontSize: 14,
      lineHeight: 20,
      marginBottom: 16,
    },
    goalProgress: {
      marginBottom: 12,
    },
    progressHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    progressText: {
      fontSize: 16,
      fontWeight: '500',
    },
    progressPercentage: {
      fontSize: 16,
      fontWeight: '700',
    },
    progressBar: {
      height: 8,
      borderRadius: 4,
    },
    progressFill: {
      height: '100%',
      borderRadius: 4,
    },
    goalDeadline: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      marginBottom: 12,
    },
    deadlineText: {
      fontSize: 14,
    },
    completedBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
      padding: 8,
      borderRadius: 8,
      marginBottom: 12,
    },
    completedText: {
      fontSize: 14,
      fontWeight: '500',
    },
    updateButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      borderRadius: 8,
      gap: 6,
    },
    updateButtonText: {
      fontSize: 14,
      fontWeight: '500',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyStateTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateDescription: {
      fontSize: 16,
      color: colors.text + '80',
      textAlign: 'center',
      lineHeight: 24,
    },
    // Modal styles
    modalContainer: {
      flex: 1,
    },
    modalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 20,
      paddingTop: 60,
      borderBottomWidth: 1,
      borderBottomColor: colors.accent,
    },
    modalButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    modalContent: {
      flex: 1,
      padding: 20,
    },
    formSection: {
      marginBottom: 20,
    },
    formLabel: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 8,
    },
    formInput: {
      height: 48,
      borderRadius: 12,
      borderWidth: 1,
      paddingHorizontal: 16,
      fontSize: 16,
    },
    formTextArea: {
      height: 80,
      borderRadius: 12,
      borderWidth: 1,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
    },
    formRow: {
      flexDirection: 'row',
      gap: 12,
    },
    goalTypeSelector: {
      flexDirection: 'row',
    },
    goalTypeButton: {
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      marginRight: 8,
      minWidth: 80,
    },
    goalTypeLabel: {
      fontSize: 12,
      fontWeight: '500',
      marginTop: 4,
    },
    prioritySelector: {
      flexDirection: 'row',
      gap: 12,
    },
    priorityButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      borderRadius: 8,
      gap: 6,
    },
    priorityText: {
      fontSize: 14,
      fontWeight: '500',
    },
  });

  const filteredGoals = getFilteredGoals();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Goals</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Plus size={20} color={colors.overlayText} />
        </TouchableOpacity>
      </View>

      {renderFilterTabs()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredGoals.length === 0 ? (
          <View style={styles.emptyState}>
            <Target size={64} color={colors.text + '40'} />
            <Text style={styles.emptyStateTitle}>
              {activeFilter === 'completed' ? 'No Completed Goals' : 'No Goals Yet'}
            </Text>
            <Text style={styles.emptyStateDescription}>
              {activeFilter === 'completed'
                ? 'Complete some goals to see them here!'
                : 'Set your first fitness goal to start tracking your progress.'
              }
            </Text>
          </View>
        ) : (
          <View style={styles.goalsList}>
            {filteredGoals.map(renderGoalCard)}
          </View>
        )}
      </ScrollView>

      {renderAddGoalModal()}
    </View>
  );
};
  };

  const resetForm = () => {
    setFormData({
      goalType: 'weight_loss',
      title: '',
      description: '',
      targetValue: '',
      currentValue: '',
      unit: 'kg',
      targetDate: '',
      priority: 'medium',
    });
  };

  const getFilteredGoals = () => {
    switch (activeFilter) {
      case 'active':
        return goals.filter(goal => goal.status === 'active');
      case 'completed':
        return goals.filter(goal => goal.status === 'completed');
      default:
        return goals;
    }
  };

  const getDaysUntilTarget = (targetDate?: string) => {
    if (!targetDate) return null;
    const target = new Date(targetDate);
    const today = new Date();
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const renderFilterTabs = () => (
    <View style={styles.filterTabs}>
      {(['active', 'completed', 'all'] as const).map((filter) => (
        <TouchableOpacity
          key={filter}
          style={[
            styles.filterTab,
            {
              backgroundColor: activeFilter === filter ? colors.primary : colors.accent,
            },
          ]}
          onPress={() => setActiveFilter(filter)}
        >
          <Text
            style={[
              styles.filterTabText,
              {
                color: activeFilter === filter ? colors.overlayText : colors.text,
              },
            ]}
          >
            {filter.charAt(0).toUpperCase() + filter.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
