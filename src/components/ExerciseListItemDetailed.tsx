import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useThemeStore } from '@/store/themeStore';
import { ThemedText } from '@/components/ThemedComponents';
import { ChevronRight, Timer, RotateCcw } from 'lucide-react-native';

interface ExerciseListItemDetailedProps {
  exercise: {
    id: string;
    order_in_workout: number;
    prescribed_sets: number;
    prescribed_reps_min?: number;
    prescribed_reps_max?: number;
    prescribed_duration_seconds?: number;
    prescribed_rir?: number;
    prescribed_rpe?: number;
    prescribed_tempo?: string;
    rest_period_seconds_after_set: number;
    notes?: string;
    exercise?: {
      id: string;
      name: string;
      description?: string;
      target_muscles_primary: string[];
      equipment_required: string[];
    };
  };
  onPress: () => void;
}

export default function ExerciseListItemDetailed({ exercise, onPress }: ExerciseListItemDetailedProps) {
  const { colors } = useThemeStore();

  const formatReps = () => {
    if (exercise.prescribed_reps_min && exercise.prescribed_reps_max) {
      if (exercise.prescribed_reps_min === exercise.prescribed_reps_max) {
        return `${exercise.prescribed_reps_min} reps`;
      }
      return `${exercise.prescribed_reps_min}-${exercise.prescribed_reps_max} reps`;
    }
    if (exercise.prescribed_reps_min) {
      return `${exercise.prescribed_reps_min}+ reps`;
    }
    if (exercise.prescribed_duration_seconds) {
      const minutes = Math.floor(exercise.prescribed_duration_seconds / 60);
      const seconds = exercise.prescribed_duration_seconds % 60;
      if (minutes > 0) {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
      }
      return `${exercise.prescribed_duration_seconds}s`;
    }
    return 'As prescribed';
  };

  const formatRestTime = (seconds: number) => {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      if (remainingSeconds === 0) {
        return `${minutes}min`;
      }
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${seconds}s`;
  };

  return (
    <TouchableOpacity
      style={[styles.container, { borderLeftColor: colors.primary, backgroundColor: colors.background }]}
      onPress={onPress}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <ThemedText style={styles.exerciseName}>
            {exercise.order_in_workout}. {exercise.exercise?.name || 'Unknown Exercise'}
          </ThemedText>
          <ChevronRight size={16} color={colors.text} opacity={0.6} />
        </View>

        {exercise.exercise?.target_muscles_primary && exercise.exercise.target_muscles_primary.length > 0 && (
          <ThemedText style={styles.targetMuscles}>
            {exercise.exercise.target_muscles_primary.join(', ')}
          </ThemedText>
        )}

        <View style={styles.prescriptionContainer}>
          <View style={styles.prescriptionRow}>
            <View style={styles.prescriptionItem}>
              <ThemedText style={styles.prescriptionLabel}>Sets:</ThemedText>
              <ThemedText style={styles.prescriptionValue}>
                {exercise.prescribed_sets}
              </ThemedText>
            </View>

            <View style={styles.prescriptionItem}>
              <ThemedText style={styles.prescriptionLabel}>Reps:</ThemedText>
              <ThemedText style={styles.prescriptionValue}>
                {formatReps()}
              </ThemedText>
            </View>

            <View style={styles.prescriptionItem}>
              <Timer size={12} color={colors.text} />
              <ThemedText style={styles.prescriptionValue}>
                {formatRestTime(exercise.rest_period_seconds_after_set)}
              </ThemedText>
            </View>
          </View>

          {/* Additional prescription details */}
          {(exercise.prescribed_rir || exercise.prescribed_rpe || exercise.prescribed_tempo) && (
            <View style={styles.prescriptionRow}>
              {exercise.prescribed_rir && (
                <View style={styles.prescriptionItem}>
                  <ThemedText style={styles.prescriptionLabel}>RIR:</ThemedText>
                  <ThemedText style={styles.prescriptionValue}>
                    {exercise.prescribed_rir}
                  </ThemedText>
                </View>
              )}

              {exercise.prescribed_rpe && (
                <View style={styles.prescriptionItem}>
                  <ThemedText style={styles.prescriptionLabel}>RPE:</ThemedText>
                  <ThemedText style={styles.prescriptionValue}>
                    {exercise.prescribed_rpe}
                  </ThemedText>
                </View>
              )}

              {exercise.prescribed_tempo && (
                <View style={styles.prescriptionItem}>
                  <RotateCcw size={12} color={colors.text} />
                  <ThemedText style={styles.prescriptionValue}>
                    {exercise.prescribed_tempo}
                  </ThemedText>
                </View>
              )}
            </View>
          )}
        </View>

        {exercise.notes && (
          <View style={[styles.notes, { backgroundColor: colors.accent }]}>
            <ThemedText style={styles.notesText}>
              {exercise.notes}
            </ThemedText>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderLeftWidth: 3,
    paddingLeft: 12,
    paddingVertical: 12,
    paddingRight: 16,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  exerciseName: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
  },
  targetMuscles: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  prescriptionContainer: {
    gap: 6,
  },
  prescriptionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  prescriptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  prescriptionLabel: {
    fontSize: 11,
    opacity: 0.7,
    fontWeight: '500',
  },
  prescriptionValue: {
    fontSize: 11,
    fontWeight: '600',
  },
  notes: {
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
  },
  notesText: {
    fontSize: 11,
    fontStyle: 'italic',
    opacity: 0.8,
  },
});