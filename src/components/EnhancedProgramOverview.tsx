import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Calendar,
  Clock,
  Target,
  Dumbbell,
  TrendingUp,
  Award,
  ChevronRight,
  Play,
  Pause,
  RotateCcw,
  AlertCircle,
  CheckCircle
} from 'lucide-react-native';
import { useThemeStore } from '../store/themeStore';
import { useWorkoutStore } from '../store/workoutStore';
import { useUserStore } from '../store/userStore';
import { WorkoutProgram } from '../services/workoutService';
import { ProgramWaitingState } from './ProgramWaitingState';
import { Profile } from '../services/profileService';
import { workoutStatsService, WorkoutStatistics } from '../services/workoutStatsService';
import { cycleCalculationService, CycleProgress } from '../services/cycleCalculationService';
import { useActiveWorkoutStore } from '../store/activeWorkoutStore';

const { width } = Dimensions.get('window');

interface ProgramCycleInfo {
  currentCycle: number;
  totalCycles: number;
  cycleProgress: number;
  daysInCycle: number;
  daysCompleted: number;
  nextTransitionDate?: string;
}

interface EnhancedProgramOverviewProps {
  onViewProgram?: (programId: string) => void;
  onRequestNewProgram?: () => void;
}

export const EnhancedProgramOverview: React.FC<EnhancedProgramOverviewProps> = ({
  onViewProgram,
  onRequestNewProgram,
}) => {
  const { colors } = useThemeStore();
  const { programs, currentProgram, isLoading } = useWorkoutStore();
  const { profile } = useUserStore();
  const { currentWorkoutPlan } = useActiveWorkoutStore();
  const [cycleInfo, setCycleInfo] = useState<ProgramCycleInfo | null>(null);

  // New state variables for real data
  const [workoutStats, setWorkoutStats] = useState<WorkoutStatistics | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [realCycleInfo, setRealCycleInfo] = useState<CycleProgress | null>(null);
  const [statsError, setStatsError] = useState<string | null>(null);
  const [lastStatsUpdate, setLastStatsUpdate] = useState<number>(0);

  // Determine current program status based on intake and program state
  const getProgramStatus = () => {
    if (!profile) return 'loading';

    const intakeStatus = profile.intake_status;
    const hasPrograms = programs && programs.length > 0;
    const pendingProgram = programs?.find(p => p.status === 'ai_generated_pending_review');
    const approvedPrograms = programs?.filter(p =>
      p.status === 'coach_approved' ||
      p.status === 'auto_approved' ||
      p.status === 'active_by_client' ||
      p.status === 'completed_by_client'
    );

    // If intake not completed, show intake prompt
    if (intakeStatus !== 'completed') {
      return 'intake_needed';
    }

    // If intake completed but no programs or only pending programs, show generation loading
    if (!hasPrograms || (hasPrograms && !approvedPrograms?.length && pendingProgram)) {
      return 'program_generating';
    }

    // If has approved programs, show normal program view
    if (approvedPrograms && approvedPrograms.length > 0) {
      return 'programs_available';
    }

    // Fallback - should not happen but handle gracefully
    return 'intake_needed';
  };

  useEffect(() => {
    if (currentProgram) {
      calculateCycleInfo(currentProgram);
    }
  }, [currentProgram]);

  // Data fetching effects
  useEffect(() => {
    if (profile?.id) {
      fetchWorkoutStatistics();
    }
  }, [profile?.id]);

  useEffect(() => {
    if (currentProgram) {
      calculateRealCycleInfo(currentProgram);
    }
  }, [currentProgram]);

  // Optional: Add refresh interval for real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (profile?.id && !isLoadingStats) {
        fetchWorkoutStatistics();
      }
    }, 60000); // Refresh every minute

    return () => clearInterval(interval);
  }, [profile?.id, isLoadingStats]);

  // Data fetching functions
  const STATS_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

  const fetchWorkoutStatistics = async () => {
    if (!profile?.id) {
      console.log('No profile ID available for fetching workout stats');
      return;
    }

    // Check cache
    if (workoutStats && Date.now() - lastStatsUpdate < STATS_CACHE_DURATION) {
      console.log('Using cached workout stats');
      return;
    }

    console.log('Fetching workout statistics for user:', profile.id);
    setIsLoadingStats(true);
    setStatsError(null);

    try {
      const stats = await workoutStatsService.getDetailedWorkoutStats(profile.id);
      console.log('Successfully fetched workout stats:', stats);
      setWorkoutStats(stats);
      setLastStatsUpdate(Date.now());
    } catch (error) {
      console.error('Error fetching workout statistics:', error);
      setStatsError('Failed to load workout statistics');

      // Set fallback stats to prevent complete UI failure
      setWorkoutStats({
        totalWorkouts: 0,
        completionRate: 0,
        achievements: 0,
        currentStreak: 0,
        weeklyAverage: 0,
        lastWorkoutDate: undefined
      });
    } finally {
      setIsLoadingStats(false);
    }
  };

  const calculateRealCycleInfo = (program: WorkoutProgram) => {
    try {
      const realCycle = cycleCalculationService.calculateRealCycleInfo(program);
      setRealCycleInfo(realCycle);

      // Update existing cycleInfo for backward compatibility
      setCycleInfo({
        currentCycle: realCycle.currentCycle,
        totalCycles: realCycle.totalCycles,
        cycleProgress: realCycle.cycleProgress,
        daysInCycle: realCycle.daysInCycle,
        daysCompleted: realCycle.daysCompleted,
        nextTransitionDate: realCycle.nextTransitionDate,
      });
    } catch (error) {
      console.error('Error calculating cycle info:', error);
      // Fallback to basic info
      setRealCycleInfo({
        currentCycle: 1,
        totalCycles: Math.ceil((program.duration_weeks || 12) / 4),
        cycleProgress: 0,
        daysInCycle: 28,
        daysCompleted: 0,
        nextTransitionDate: new Date().toISOString().split('T')[0],
        weekProgress: 1,
        isActive: program.status === 'active_by_client'
      });
    }
  };

  const refreshAllData = async () => {
    await Promise.all([
      fetchWorkoutStatistics(),
      currentProgram ? Promise.resolve(calculateRealCycleInfo(currentProgram)) : Promise.resolve()
    ]);
  };

  const calculateCycleInfo = (program: WorkoutProgram) => {
    // Use real calculation instead of mock data
    calculateRealCycleInfo(program);
  };

  const getActiveProgram = () => {
    return programs.find(p => p.status === 'active_by_client') || currentProgram;
  };

  const getPendingPrograms = () => {
    const isCoachOrAdmin = profile?.role === 'coach' || profile?.role === 'admin';

    // Coaches and admins can see all pending programs
    if (isCoachOrAdmin) {
      return programs.filter(p =>
        p.status === 'coach_approved' ||
        p.status === 'ai_generated_pending_review'
      );
    }

    // Regular users can only see approved programs (including auto_approved)
    return programs.filter(p => p.status === 'coach_approved' || p.status === 'auto_approved');
  };

  const getProgramsUnderReview = () => {
    const isCoachOrAdmin = profile?.role === 'coach' || profile?.role === 'admin';

    // Only regular users should see the waiting state for programs under review
    if (isCoachOrAdmin) {
      return [];
    }

    return programs.filter(p => p.status === 'ai_generated_pending_review');
  };

  const renderCycleProgress = () => {
    if (!realCycleInfo && !cycleInfo) return null;

    const cycleData = realCycleInfo || cycleInfo;
    const progressWidth = (cycleData.cycleProgress / 100) * (width - 60);

    // Calculate days until next program
    const calculateDaysUntilNext = () => {
      if (!cycleData.nextTransitionDate) return null;

      const nextDate = new Date(cycleData.nextTransitionDate);
      const today = new Date();
      const diffTime = nextDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diffDays > 0 ? diffDays : 0;
    };

    const daysUntilNext = calculateDaysUntilNext();

    return (
      <View style={[styles.cycleCard, { backgroundColor: colors.accent }]}>
        <View style={styles.cycleHeader}>
          <View style={styles.cycleInfo}>
            <Text style={[styles.cycleTitle, { color: colors.text }]}>
              Phase of Current Plan
            </Text>
            <Text style={[styles.cycleSubtitle, { color: colors.text + '80' }]}>
              {realCycleInfo
                ? `Week ${realCycleInfo.weekProgress} of 4`
                : `Day ${cycleData.daysCompleted} of ${cycleData.daysInCycle}`
              }
            </Text>
          </View>
          <View style={styles.cycleStats}>
            <Text style={[styles.progressPercentage, { color: colors.primary }]}>
              {cycleData.cycleProgress}%
            </Text>
          </View>
        </View>

        <View style={[styles.progressTrack, { backgroundColor: colors.background }]}>
          <LinearGradient
            colors={[
              realCycleInfo?.isActive ? colors.primary : colors.text + '40',
              realCycleInfo?.isActive ? colors.primary + '80' : colors.text + '20'
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[styles.progressFill, { width: progressWidth }]}
          />
        </View>

        {daysUntilNext !== null && (
          <View style={styles.transitionInfo}>
            <Calendar size={14} color={colors.text + '60'} />
            <Text style={[styles.transitionText, { color: colors.text + '60' }]}>
              Next workout program: In {daysUntilNext} day{daysUntilNext !== 1 ? 's' : ''}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderActiveProgram = () => {
    const activeProgram = getActiveProgram();
    if (!activeProgram) return null;

    return (
      <View style={[styles.activeProgramCard, { backgroundColor: colors.primary + '10' }]}>
        <LinearGradient
          colors={[colors.primary + '20', colors.primary + '05']}
          style={styles.activeProgramGradient}
        >
          <View style={styles.activeProgramHeader}>
            <View style={styles.activeProgramInfo}>
              <Text style={[styles.activeProgramTitle, { color: colors.text }]}>
                {activeProgram.name}
              </Text>
              <Text style={[styles.activeProgramDescription, { color: colors.text + '80' }]}>
                {activeProgram.description}
              </Text>
            </View>
            <View style={[styles.activeIndicator, { backgroundColor: colors.success }]}>
              <Dumbbell size={16} color={colors.overlayText} />
            </View>
          </View>

          <View style={styles.programActions}>
            <TouchableOpacity
              style={[styles.primaryAction, { backgroundColor: colors.primary }]}
              onPress={() => onViewProgram?.(activeProgram.id)}
            >
              <Text style={[styles.primaryActionText, { color: colors.overlayText }]}>
                View Program
              </Text>
              <ChevronRight size={16} color={colors.overlayText} />
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </View>
    );
  };

  const renderPendingPrograms = () => {
    const pendingPrograms = getPendingPrograms();
    if (pendingPrograms.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Pending Programs
        </Text>
        {pendingPrograms.map((program) => (
          <TouchableOpacity
            key={program.id}
            style={[styles.pendingProgramCard, { backgroundColor: colors.accent }]}
            onPress={() => onViewProgram?.(program.id)}
          >
            <View style={styles.pendingProgramInfo}>
              <Text style={[styles.pendingProgramTitle, { color: colors.text }]}>
                {program.name}
              </Text>
              <Text style={[styles.pendingProgramStatus, { color: colors.warning }]}>
                {(program.status === 'coach_approved' || program.status === 'auto_approved') ? 'Ready to Start' : 'Under Review'}
              </Text>
            </View>
            <ChevronRight size={20} color={colors.text + '60'} />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderQuickStats = () => {
    // Loading state
    if (isLoadingStats) {
      return (
        <View style={styles.quickStats}>
          {[1, 2, 3].map((index) => (
            <View key={index} style={[styles.statCard, { backgroundColor: colors.accent }]}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={[styles.statValue, { color: colors.text + '60' }]}>--</Text>
              <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
                {index === 1 ? 'Workouts' : index === 2 ? 'Completion' : 'Achievements'}
              </Text>
            </View>
          ))}
        </View>
      );
    }

    // Error state
    if (statsError) {
      return (
        <View style={styles.quickStats}>
          <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
            <AlertCircle size={20} color={colors.error} />
            <Text style={[styles.statLabel, { color: colors.error, textAlign: 'center' }]}>
              Failed to load stats
            </Text>
            <TouchableOpacity onPress={fetchWorkoutStatistics}>
              <Text style={[styles.statLabel, { color: colors.primary }]}>Retry</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // No data state
    if (!workoutStats) {
      return (
        <View style={styles.quickStats}>
          {[1, 2, 3].map((index) => (
            <View key={index} style={[styles.statCard, { backgroundColor: colors.accent }]}>
              <View style={{ opacity: 0.5 }}>
                {index === 1 && <TrendingUp size={20} color={colors.success} />}
                {index === 2 && <Target size={20} color={colors.primary} />}
                {index === 3 && <Award size={20} color={colors.warning} />}
              </View>
              <Text style={[styles.statValue, { color: colors.text + '60' }]}>--</Text>
              <Text style={[styles.statLabel, { color: colors.text + '80' }]}>
                {index === 1 ? 'Workouts' : index === 2 ? 'Completion' : 'Achievements'}
              </Text>
            </View>
          ))}
        </View>
      );
    }

    // Real data display
    return (
      <View style={styles.quickStats}>
        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <TrendingUp size={20} color={colors.success} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {workoutStats.totalWorkouts}
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>Workouts</Text>
        </View>

        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <Target size={20} color={colors.primary} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {workoutStats.completionRate}%
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>Completion</Text>
        </View>

        <View style={[styles.statCard, { backgroundColor: colors.accent }]}>
          <Award size={20} color={colors.warning} />
          <Text style={[styles.statValue, { color: colors.text }]}>
            {workoutStats.achievements}
          </Text>
          <Text style={[styles.statLabel, { color: colors.text + '80' }]}>Achievements</Text>
        </View>
      </View>
    );
  };

  const renderProgramGeneratingState = () => {
    return (
      <View style={styles.emptyState}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
        <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
          Your Program is Being Crafted
        </Text>
        <Text style={[styles.emptyStateDescription, { color: colors.text + '80' }]}>
          Your coach is creating a personalized program tailored specifically for you. Check back soon...
        </Text>
        <View style={styles.statusContainer}>
          <View style={styles.statusItem}>
            <CheckCircle size={16} color={colors.success} />
            <Text style={[styles.statusText, { color: colors.text + 'DD' }]}>
              Intake Completed
            </Text>
          </View>

          <View style={styles.statusItem}>
            <ActivityIndicator size={16} color={colors.primary} />
            <Text style={[styles.statusText, { color: colors.text + 'DD' }]}>
              Crafting Your Program
            </Text>
          </View>

          <View style={[styles.statusItem, { opacity: 0.5 }]}>
            <Clock size={16} color={colors.text + '60'} />
            <Text style={[styles.statusText, { color: colors.text + '60' }]}>
              Ready to Start
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => {
    return (
      <View style={styles.emptyState}>
        <Dumbbell size={48} color={colors.text + '40'} />
        <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
          No Active Program
        </Text>
        <Text style={[styles.emptyStateDescription, { color: colors.text + '80' }]}>
          Complete your intake form to get your first AI-generated workout program
        </Text>
        <TouchableOpacity
          style={[styles.emptyStateAction, { backgroundColor: colors.primary }]}
          onPress={onRequestNewProgram}
        >
          <Text style={[styles.emptyStateActionText, { color: colors.overlayText }]}>
            Get Started
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Loading your programs...
        </Text>
      </View>
    );
  }

  const programStatus = getProgramStatus();
  const hasPrograms = programs.length > 0;
  const activeProgram = getActiveProgram();
  const pendingPrograms = getPendingPrograms();
  const programsUnderReview = getProgramsUnderReview();

  // Error boundary wrapper
  const renderWithErrorBoundary = (renderFunction: () => React.ReactNode, fallback: React.ReactNode) => {
    try {
      return renderFunction();
    } catch (error) {
      console.error('Render error in EnhancedProgramOverview:', error);
      return fallback;
    }
  };

  return (
    <View style={styles.container}>
      {programStatus === 'program_generating' ? (
        renderProgramGeneratingState()
      ) : programStatus === 'intake_needed' ? (
        renderEmptyState()
      ) : hasPrograms ? (
        <>
          {renderWithErrorBoundary(
            () => renderCycleProgress(),
            <View style={styles.errorFallback}>
              <Text style={[styles.errorText, { color: colors.error }]}>
                Unable to load cycle progress
              </Text>
            </View>
          )}
          {renderWithErrorBoundary(
            () => renderActiveProgram(),
            null
          )}
          {renderWithErrorBoundary(
            () => renderQuickStats(),
            <View style={styles.errorFallback}>
              <Text style={[styles.errorText, { color: colors.error }]}>
                Unable to load workout stats
              </Text>
            </View>
          )}
          {renderPendingPrograms()}
          {/* Show waiting state for programs under review (regular users only) */}
          {programsUnderReview.length > 0 && (
            <View style={styles.waitingStateContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Program Under Review
              </Text>
              <ProgramWaitingState
                programId={programsUnderReview[0].id}
                submittedDate={programsUnderReview[0].created_at}
                onRefresh={refreshAllData}
                onContactSupport={() => {
                  console.log('Contact support requested');
                }}
              />
            </View>
          )}
        </>
      ) : (
        renderEmptyState()
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  cycleCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cycleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  cycleInfo: {
    flex: 1,
  },
  cycleTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  cycleSubtitle: {
    fontSize: 14,
  },
  cycleStats: {
    alignItems: 'flex-end',
  },
  progressPercentage: {
    fontSize: 24,
    fontWeight: '700',
  },
  progressTrack: {
    height: 8,
    borderRadius: 4,
    marginBottom: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  transitionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transitionText: {
    fontSize: 12,
    marginLeft: 6,
  },
  activeProgramCard: {
    borderRadius: 16,
    marginBottom: 20,
    overflow: 'hidden',
  },
  activeProgramGradient: {
    padding: 20,
  },
  activeProgramHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  activeProgramInfo: {
    flex: 1,
    marginRight: 16,
  },
  activeProgramTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  activeProgramDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  activeIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  programActions: {
    flexDirection: 'row',
    gap: 12,
  },
  primaryAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    gap: 8,
  },
  primaryActionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    gap: 4,
  },
  secondaryActionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  quickStats: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  pendingProgramCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  pendingProgramInfo: {
    flex: 1,
  },
  pendingProgramTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  pendingProgramStatus: {
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    marginTop: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyStateAction: {
    paddingHorizontal: 32,
    paddingVertical: 14,
    borderRadius: 12,
  },
  emptyStateActionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  waitingStateContainer: {
    marginTop: 24,
  },
  statusContainer: {
    marginTop: 24,
    gap: 12,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorFallback: {
    padding: 20,
    alignItems: 'center',
    borderRadius: 12,
    marginBottom: 20,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
});
