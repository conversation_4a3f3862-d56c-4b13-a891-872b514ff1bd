import { useState, useEffect, useCallback } from 'react';
import { offlineSyncManager, SyncResult, NetworkStatus } from '../utils/offlineSync';

/**
 * Hook for managing offline sync functionality
 */
export const useOfflineSync = () => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true,
    isInternetReachable: true,
    type: 'unknown',
  });
  const [syncStatus, setSyncStatus] = useState({
    isOnline: true,
    isSyncing: false,
    queueLength: 0,
  });
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);

  useEffect(() => {
    // Initialize offline sync manager
    offlineSyncManager.initialize().catch(console.error);

    // Setup network status listener
    const removeNetworkListener = offlineSyncManager.addNetworkListener((status) => {
      setNetworkStatus(status);
    });

    // Setup sync result listener
    const removeSyncListener = offlineSyncManager.addSyncListener((result) => {
      setLastSyncResult(result);
    });

    // Update sync status periodically
    const updateSyncStatus = () => {
      setSyncStatus(offlineSyncManager.getNetworkStatus());
    };

    updateSyncStatus();
    const interval = setInterval(updateSyncStatus, 1000);

    return () => {
      removeNetworkListener();
      removeSyncListener();
      clearInterval(interval);
    };
  }, []);

  const queueAction = useCallback(
    async (
      type: string,
      payload: any,
      priority: 'high' | 'medium' | 'low' = 'medium',
      maxRetries: number = 3
    ) => {
      return await offlineSyncManager.queueAction(type, payload, priority, maxRetries);
    },
    []
  );

  const syncNow = useCallback(async () => {
    return await offlineSyncManager.syncQueuedActions();
  }, []);

  const clearQueue = useCallback(async () => {
    await offlineSyncManager.clearQueue();
  }, []);

  return {
    networkStatus,
    syncStatus,
    lastSyncResult,
    queueAction,
    syncNow,
    clearQueue,
    isOnline: networkStatus.isConnected && networkStatus.isInternetReachable,
    hasQueuedActions: syncStatus.queueLength > 0,
  };
};

/**
 * Hook for network status only
 */
export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true,
    isInternetReachable: true,
    type: 'unknown',
  });

  useEffect(() => {
    const removeListener = offlineSyncManager.addNetworkListener(setNetworkStatus);
    return removeListener;
  }, []);

  return {
    ...networkStatus,
    isOnline: networkStatus.isConnected && networkStatus.isInternetReachable,
  };
};

/**
 * Hook for offline-aware data operations
 */
export const useOfflineOperation = () => {
  const { queueAction, isOnline } = useOfflineSync();

  const executeOperation = useCallback(
    async (
      operation: () => Promise<any>,
      fallbackAction?: {
        type: string;
        payload: any;
        priority?: 'high' | 'medium' | 'low';
      }
    ) => {
      if (isOnline) {
        try {
          return await operation();
        } catch (error) {
          // If operation fails and we have a fallback, queue it
          if (fallbackAction) {
            await queueAction(
              fallbackAction.type,
              fallbackAction.payload,
              fallbackAction.priority
            );
          }
          throw error;
        }
      } else {
        // If offline and we have a fallback action, queue it
        if (fallbackAction) {
          return await queueAction(
            fallbackAction.type,
            fallbackAction.payload,
            fallbackAction.priority
          );
        } else {
          throw new Error('Operation not available offline');
        }
      }
    },
    [isOnline, queueAction]
  );

  return {
    executeOperation,
    isOnline,
  };
};
