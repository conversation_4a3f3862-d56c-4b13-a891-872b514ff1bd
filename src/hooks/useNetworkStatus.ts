import { useState, useEffect, useCallback } from 'react';
import NetInfo from '@react-native-community/netinfo';
import { create } from 'zustand';
import { persist } from '../utils/zustandPersistence';

/**
 * Network status types
 */
export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
  isExpensive?: boolean;
  details?: any;
}

export interface NetworkStatusStore {
  networkState: NetworkState;
  connectionHistory: Array<{
    timestamp: number;
    isConnected: boolean;
    type: string;
  }>;
  lastConnectedAt: number | null;
  lastDisconnectedAt: number | null;
  updateNetworkState: (state: NetworkState) => void;
  addConnectionEvent: (isConnected: boolean, type: string) => void;
  getConnectionStats: () => {
    totalConnections: number;
    totalDisconnections: number;
    averageConnectionDuration: number;
    currentConnectionDuration: number;
  };
}

/**
 * Zustand store for network status
 */
export const useNetworkStatusStore = create<NetworkStatusStore>()(
  persist(
    (set, get) => ({
      networkState: {
        isConnected: true,
        isInternetReachable: true,
        type: 'unknown',
      },
      connectionHistory: [],
      lastConnectedAt: null,
      lastDisconnectedAt: null,

      updateNetworkState: (state: NetworkState) => {
        const currentState = get().networkState;
        const wasConnected = currentState.isConnected && currentState.isInternetReachable;
        const isNowConnected = state.isConnected && state.isInternetReachable;

        // Track connection state changes
        if (wasConnected !== isNowConnected) {
          get().addConnectionEvent(isNowConnected, state.type);
        }

        set({ networkState: state });
      },

      addConnectionEvent: (isConnected: boolean, type: string) => {
        const now = Date.now();
        const currentHistory = get().connectionHistory;
        
        // Add to history (keep last 100 events)
        const newHistory = [
          ...currentHistory.slice(-99),
          {
            timestamp: now,
            isConnected,
            type,
          },
        ];

        const updates: Partial<NetworkStatusStore> = {
          connectionHistory: newHistory,
        };

        if (isConnected) {
          updates.lastConnectedAt = now;
        } else {
          updates.lastDisconnectedAt = now;
        }

        set(updates);
      },

      getConnectionStats: () => {
        const { connectionHistory, lastConnectedAt, lastDisconnectedAt } = get();
        
        const connections = connectionHistory.filter(event => event.isConnected);
        const disconnections = connectionHistory.filter(event => !event.isConnected);
        
        // Calculate average connection duration
        let totalConnectionTime = 0;
        let connectionCount = 0;
        
        for (let i = 0; i < connectionHistory.length - 1; i++) {
          const current = connectionHistory[i];
          const next = connectionHistory[i + 1];
          
          if (current.isConnected && !next.isConnected) {
            totalConnectionTime += next.timestamp - current.timestamp;
            connectionCount++;
          }
        }
        
        const averageConnectionDuration = connectionCount > 0 
          ? totalConnectionTime / connectionCount 
          : 0;
        
        // Calculate current connection duration
        const currentConnectionDuration = lastConnectedAt && get().networkState.isConnected
          ? Date.now() - lastConnectedAt
          : 0;

        return {
          totalConnections: connections.length,
          totalDisconnections: disconnections.length,
          averageConnectionDuration,
          currentConnectionDuration,
        };
      },
    }),
    {
      name: 'network-status-store',
      partialize: (state) => ({
        connectionHistory: state.connectionHistory.slice(-50), // Keep only recent history
        lastConnectedAt: state.lastConnectedAt,
        lastDisconnectedAt: state.lastDisconnectedAt,
      }),
    }
  )
);

/**
 * Hook for network status monitoring
 */
export const useNetworkStatus = () => {
  const {
    networkState,
    updateNetworkState,
    getConnectionStats,
    connectionHistory,
  } = useNetworkStatusStore();

  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    const initializeNetworkMonitoring = async () => {
      try {
        // Get initial network state
        const initialState = await NetInfo.fetch();
        updateNetworkState({
          isConnected: initialState.isConnected ?? false,
          isInternetReachable: initialState.isInternetReachable ?? false,
          type: initialState.type,
          isExpensive: initialState.isConnectionExpensive,
          details: initialState.details,
        });

        // Subscribe to network state changes
        unsubscribe = NetInfo.addEventListener((state) => {
          updateNetworkState({
            isConnected: state.isConnected ?? false,
            isInternetReachable: state.isInternetReachable ?? false,
            type: state.type,
            isExpensive: state.isConnectionExpensive,
            details: state.details,
          });
        });

        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize network monitoring:', error);
      }
    };

    initializeNetworkMonitoring();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [updateNetworkState]);

  const refreshNetworkStatus = useCallback(async () => {
    try {
      const state = await NetInfo.fetch();
      updateNetworkState({
        isConnected: state.isConnected ?? false,
        isInternetReachable: state.isInternetReachable ?? false,
        type: state.type,
        isExpensive: state.isConnectionExpensive,
        details: state.details,
      });
    } catch (error) {
      console.error('Failed to refresh network status:', error);
    }
  }, [updateNetworkState]);

  return {
    ...networkState,
    isOnline: networkState.isConnected && networkState.isInternetReachable,
    isInitialized,
    connectionHistory,
    connectionStats: getConnectionStats(),
    refreshNetworkStatus,
  };
};

/**
 * Hook for simple online/offline status
 */
export const useOnlineStatus = () => {
  const { isConnected, isInternetReachable } = useNetworkStatus();
  return isConnected && isInternetReachable;
};

/**
 * Hook for network-aware operations
 */
export const useNetworkAwareOperation = () => {
  const { isOnline } = useNetworkStatus();
  
  const executeWhenOnline = useCallback(
    async <T>(operation: () => Promise<T>): Promise<T> => {
      if (!isOnline) {
        throw new Error('Operation requires internet connection');
      }
      return await operation();
    },
    [isOnline]
  );

  const executeWithFallback = useCallback(
    async <T>(
      operation: () => Promise<T>,
      fallback: () => Promise<T> | T
    ): Promise<T> => {
      if (isOnline) {
        try {
          return await operation();
        } catch (error) {
          console.warn('Online operation failed, using fallback:', error);
          return await fallback();
        }
      } else {
        return await fallback();
      }
    },
    [isOnline]
  );

  return {
    isOnline,
    executeWhenOnline,
    executeWithFallback,
  };
};

/**
 * Hook for monitoring connection quality
 */
export const useConnectionQuality = () => {
  const { networkState, connectionStats } = useNetworkStatus();
  
  const getConnectionQuality = useCallback(() => {
    const { type, isExpensive } = networkState;
    const { averageConnectionDuration, totalDisconnections } = connectionStats;
    
    // Basic quality assessment
    let quality: 'excellent' | 'good' | 'fair' | 'poor' = 'good';
    
    if (type === 'wifi') {
      quality = 'excellent';
    } else if (type === 'cellular') {
      quality = isExpensive ? 'fair' : 'good';
    } else if (type === 'none') {
      quality = 'poor';
    }
    
    // Adjust based on stability
    if (totalDisconnections > 10) {
      quality = quality === 'excellent' ? 'good' : 'fair';
    }
    
    if (averageConnectionDuration < 30000) { // Less than 30 seconds average
      quality = 'poor';
    }
    
    return {
      quality,
      type,
      isExpensive: isExpensive ?? false,
      isStable: totalDisconnections < 5,
      averageConnectionDuration,
    };
  }, [networkState, connectionStats]);

  return getConnectionQuality();
};
