import { DataMigration } from './storage';
import { StoreBatchOperations } from './zustandPersistence';

/**
 * App initialization utilities for offline storage setup
 */
export class AppInitialization {
  private static isInitialized = false;

  /**
   * Initialize the app with offline storage capabilities
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('AppInitialization: Already initialized, skipping...');
      return;
    }

    try {
      console.log('AppInitialization: Starting app initialization...');

      // Run data migrations
      await DataMigration.runMigrations();
      console.log('✅ AppInitialization: Data migrations completed');

      // Log storage information
      const storageInfo = await StoreBatchOperations.getStorageInfo();
      console.log('📊 AppInitialization: Storage info:', storageInfo);

      this.isInitialized = true;
      console.log('✅ AppInitialization: App initialization completed successfully');
    } catch (error) {
      console.error('❌ AppInitialization: Failed to initialize app:', error);
      throw error;
    }
  }

  /**
   * Reset all app data (useful for development/testing)
   */
  static async resetAppData(): Promise<void> {
    try {
      console.log('AppInitialization: Resetting all app data...');
      
      await StoreBatchOperations.clearAllStores();
      
      this.isInitialized = false;
      
      console.log('✅ AppInitialization: App data reset completed');
    } catch (error) {
      console.error('❌ AppInitialization: Failed to reset app data:', error);
      throw error;
    }
  }

  /**
   * Check if app is initialized
   */
  static isAppInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get app initialization status and storage info
   */
  static async getInitializationStatus(): Promise<{
    isInitialized: boolean;
    storageInfo: {
      totalKeys: number;
      zustandKeys: number;
      estimatedSize: number;
    };
  }> {
    const storageInfo = await StoreBatchOperations.getStorageInfo();
    
    return {
      isInitialized: this.isInitialized,
      storageInfo,
    };
  }
}

/**
 * Hook to ensure app is initialized before rendering
 */
import { useState, useEffect } from 'react';

export const useAppInitialization = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<Error | null>(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        await AppInitialization.initialize();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setInitializationError(error as Error);
      }
    };

    initializeApp();
  }, []);

  return {
    isInitialized,
    initializationError,
    resetAppData: AppInitialization.resetAppData,
    getStatus: AppInitialization.getInitializationStatus,
  };
};
