import { StateCreator, StoreMutatorIdentifier } from 'zustand';
import { storage, STORAGE_KEYS, PersistenceManager } from './storage';

/**
 * Zustand persistence middleware for offline storage
 */
export interface PersistOptions<T> {
  name: string;
  storage?: typeof storage;
  partialize?: (state: T) => Partial<T>;
  onRehydrateStorage?: (state: T) => void | Promise<void>;
  version?: number;
  migrate?: (persistedState: any, version: number) => T | Promise<T>;
  skipHydration?: boolean;
}

export interface PersistStorage<T> {
  getItem: (name: string) => T | null | Promise<T | null>;
  setItem: (name: string, value: T) => void | Promise<void>;
  removeItem: (name: string) => void | Promise<void>;
}

type PersistImpl = <
  T,
  Mps extends [StoreMutatorIdentifier, unknown][] = [],
  M<PERSON> extends [StoreMutatorIdentifier, unknown][] = []
>(
  storeInitializer: StateCreator<T, Mps, Mcs>,
  options: PersistOptions<T>
) => StateCreator<T, Mps, Mcs>;

type Persist = PersistImpl;

const persistImpl: PersistImpl = (storeInitializer, options) => (set, get, store) => {
  const {
    name,
    storage: customStorage = storage,
    partialize = (state) => state,
    onRehydrateStorage,
    version = 0,
    migrate,
    skipHydration = false,
  } = options;

  let hasHydrated = false;
  const hydrationListeners = new Set<() => void>();
  const finishHydrationListeners = new Set<() => void>();

  const storageKey = `zustand-${name}`;

  // Create the store with persistence
  const persistedStore = storeInitializer(
    (...args) => {
      set(...args);
      // Persist state after each update
      const state = get();
      const stateToStore = partialize(state);
      PersistenceManager.persistData(storageKey, {
        state: stateToStore,
        version,
      }).catch((error) => {
        console.error(`Failed to persist store ${name}:`, error);
      });
    },
    get,
    store
  );

  // Hydration function
  const hydrate = async () => {
    if (hasHydrated || skipHydration) return;

    try {
      const persistedData = await PersistenceManager.retrieveData<{
        state: Partial<T>;
        version: number;
      }>(storageKey);

      if (persistedData) {
        let { state: persistedState, version: persistedVersion } = persistedData;

        // Handle migration if needed
        if (migrate && persistedVersion !== version) {
          try {
            persistedState = await migrate(persistedState, persistedVersion);
          } catch (error) {
            console.error(`Migration failed for store ${name}:`, error);
            return;
          }
        }

        // Merge persisted state with current state
        const currentState = get();
        const mergedState = { ...currentState, ...persistedState };
        
        // Set the hydrated state
        set(mergedState as T, true);

        // Call onRehydrateStorage callback
        if (onRehydrateStorage) {
          try {
            await onRehydrateStorage(mergedState as T);
          } catch (error) {
            console.error(`onRehydrateStorage failed for store ${name}:`, error);
          }
        }
      }
    } catch (error) {
      console.error(`Failed to hydrate store ${name}:`, error);
    } finally {
      hasHydrated = true;
      hydrationListeners.forEach((listener) => listener());
      finishHydrationListeners.forEach((listener) => listener());
    }
  };

  // Auto-hydrate on store creation
  hydrate();

  // Add persistence methods to the store
  (store as any).persist = {
    setOptions: (newOptions: Partial<PersistOptions<T>>) => {
      Object.assign(options, newOptions);
    },
    clearStorage: async () => {
      await PersistenceManager.clearData(storageKey);
    },
    rehydrate: hydrate,
    hasHydrated: () => hasHydrated,
    onHydrate: (listener: () => void) => {
      hydrationListeners.add(listener);
      return () => hydrationListeners.delete(listener);
    },
    onFinishHydration: (listener: () => void) => {
      finishHydrationListeners.add(listener);
      if (hasHydrated) {
        listener();
      }
      return () => finishHydrationListeners.delete(listener);
    },
  };

  return persistedStore;
};

export const persist = persistImpl as Persist;

/**
 * Hook to check if a persisted store has been hydrated
 */
export const useHydration = (store: any) => {
  const [hasHydrated, setHasHydrated] = useState(false);

  useEffect(() => {
    if (store.persist) {
      const unsubscribe = store.persist.onFinishHydration(() => {
        setHasHydrated(true);
      });
      
      // Check if already hydrated
      if (store.persist.hasHydrated()) {
        setHasHydrated(true);
      }

      return unsubscribe;
    } else {
      setHasHydrated(true);
    }
  }, [store]);

  return hasHydrated;
};

/**
 * Utility to create a persisted store with common options
 */
export const createPersistedStore = <T>(
  storeInitializer: StateCreator<T>,
  name: string,
  options: Partial<PersistOptions<T>> = {}
) => {
  return persist(storeInitializer, {
    name,
    ...options,
  });
};

/**
 * Predefined storage configurations for different data types
 */
export const PERSIST_CONFIGS = {
  // User data - persist everything
  USER: {
    partialize: (state: any) => state,
  },
  
  // Workout data - persist critical data only
  WORKOUT: {
    partialize: (state: any) => ({
      programs: state.programs,
      currentProgram: state.currentProgram,
    }),
  },
  
  // Active workout - persist session data
  ACTIVE_WORKOUT: {
    partialize: (state: any) => ({
      currentWorkoutPlan: state.currentWorkoutPlan,
      loggedData: state.loggedData,
      sessionStartTime: state.sessionStartTime,
    }),
  },
  
  // Theme - persist theme preference and initialization state
  THEME: {
    partialize: (state: any) => ({
      currentTheme: state.currentTheme,
      isInitialized: state.isInitialized,
    }),
  },
  
  // Check-in - persist form data
  CHECKIN: {
    partialize: (state: any) => ({
      currentCheckInForm: state.currentCheckInForm,
      checkInHistory: state.checkInHistory,
    }),
  },
} as const;

/**
 * Batch operations for multiple stores
 */
export class StoreBatchOperations {
  /**
   * Clear all persisted store data
   */
  static async clearAllStores(): Promise<void> {
    try {
      const keys = await storage.getAllKeys();
      const zustandKeys = keys.filter(key => key.startsWith('zustand-'));
      
      for (const key of zustandKeys) {
        await storage.removeItem(key);
      }
      
      console.log(`Cleared ${zustandKeys.length} persisted stores`);
    } catch (error) {
      console.error('Failed to clear all stores:', error);
    }
  }

  /**
   * Get storage usage information
   */
  static async getStorageInfo(): Promise<{
    totalKeys: number;
    zustandKeys: number;
    estimatedSize: number;
  }> {
    try {
      const keys = await storage.getAllKeys();
      const zustandKeys = keys.filter(key => key.startsWith('zustand-'));
      
      // Estimate size (rough calculation)
      let estimatedSize = 0;
      for (const key of zustandKeys) {
        const data = await storage.getItem(key);
        if (data) {
          estimatedSize += JSON.stringify(data).length;
        }
      }
      
      return {
        totalKeys: keys.length,
        zustandKeys: zustandKeys.length,
        estimatedSize,
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        totalKeys: 0,
        zustandKeys: 0,
        estimatedSize: 0,
      };
    }
  }
}

// Import React hooks for useHydration
import { useState, useEffect } from 'react';
