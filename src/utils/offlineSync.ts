import NetInfo from '@react-native-community/netinfo';
import { storage, STORAGE_KEYS, PersistenceManager } from './storage';
import { supabase } from '../services/supabaseClient';

/**
 * Types for offline sync functionality
 */
export interface QueuedAction {
  id: string;
  type: string;
  payload: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: 'high' | 'medium' | 'low';
}

export interface SyncResult {
  success: boolean;
  error?: string;
  syncedActions: number;
  failedActions: number;
}

export interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
}

/**
 * Offline sync manager for handling queued actions and data synchronization
 */
export class OfflineSyncManager {
  private static instance: OfflineSyncManager;
  private actionQueue: QueuedAction[] = [];
  private isOnline: boolean = true;
  private isSyncing: boolean = false;
  private syncListeners: Array<(result: SyncResult) => void> = [];
  private networkListeners: Array<(status: NetworkStatus) => void> = [];
  private netInfoUnsubscribe?: () => void;

  public static getInstance(): OfflineSyncManager {
    if (!OfflineSyncManager.instance) {
      OfflineSyncManager.instance = new OfflineSyncManager();
    }
    return OfflineSyncManager.instance;
  }

  /**
   * Initialize the offline sync manager
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 OfflineSyncManager: Initializing...');

      // Load queued actions from storage
      await this.loadQueueFromStorage();

      // Setup network monitoring
      this.setupNetworkMonitoring();

      // Check initial network status
      const networkState = await NetInfo.fetch();
      this.updateNetworkStatus(networkState);

      console.log('✅ OfflineSyncManager: Initialized successfully');
    } catch (error) {
      console.error('❌ OfflineSyncManager: Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Setup network status monitoring
   */
  private setupNetworkMonitoring(): void {
    this.netInfoUnsubscribe = NetInfo.addEventListener((state) => {
      this.updateNetworkStatus(state);
    });
  }

  /**
   * Update network status and trigger sync if coming back online
   */
  private updateNetworkStatus(networkState: any): void {
    const wasOnline = this.isOnline;
    this.isOnline = networkState.isConnected && networkState.isInternetReachable;

    const status: NetworkStatus = {
      isConnected: networkState.isConnected,
      isInternetReachable: networkState.isInternetReachable,
      type: networkState.type,
    };

    console.log('🌐 OfflineSyncManager: Network status updated:', status);

    // Notify listeners
    this.networkListeners.forEach(listener => listener(status));

    // If we just came back online, trigger sync
    if (!wasOnline && this.isOnline && this.actionQueue.length > 0) {
      console.log('🔄 OfflineSyncManager: Back online, triggering sync...');
      this.syncQueuedActions();
    }
  }

  /**
   * Queue an action for offline sync
   */
  async queueAction(
    type: string,
    payload: any,
    priority: 'high' | 'medium' | 'low' = 'medium',
    maxRetries: number = 3
  ): Promise<string> {
    const action: QueuedAction = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      payload,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
      priority,
    };

    this.actionQueue.push(action);
    await this.saveQueueToStorage();

    console.log(`📝 OfflineSyncManager: Action queued (${type}):`, action.id);

    // If online, try to sync immediately
    if (this.isOnline) {
      this.syncQueuedActions();
    }

    return action.id;
  }

  /**
   * Sync all queued actions
   */
  async syncQueuedActions(): Promise<SyncResult> {
    if (this.isSyncing) {
      console.log('🔄 OfflineSyncManager: Sync already in progress, skipping...');
      return { success: false, error: 'Sync already in progress', syncedActions: 0, failedActions: 0 };
    }

    if (!this.isOnline) {
      console.log('🔄 OfflineSyncManager: Offline, cannot sync');
      return { success: false, error: 'Device is offline', syncedActions: 0, failedActions: 0 };
    }

    this.isSyncing = true;
    let syncedActions = 0;
    let failedActions = 0;

    try {
      console.log(`🔄 OfflineSyncManager: Starting sync of ${this.actionQueue.length} actions...`);

      // Sort actions by priority and timestamp
      const sortedActions = this.actionQueue.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return a.timestamp - b.timestamp;
      });

      // Process actions one by one
      for (const action of sortedActions) {
        try {
          await this.processAction(action);
          syncedActions++;
          
          // Remove successful action from queue
          this.actionQueue = this.actionQueue.filter(a => a.id !== action.id);
        } catch (error) {
          console.error(`❌ OfflineSyncManager: Failed to process action ${action.id}:`, error);
          
          // Increment retry count
          action.retryCount++;
          
          if (action.retryCount >= action.maxRetries) {
            console.error(`❌ OfflineSyncManager: Action ${action.id} exceeded max retries, removing from queue`);
            this.actionQueue = this.actionQueue.filter(a => a.id !== action.id);
            failedActions++;
          } else {
            console.log(`🔄 OfflineSyncManager: Action ${action.id} will be retried (${action.retryCount}/${action.maxRetries})`);
          }
        }
      }

      // Save updated queue
      await this.saveQueueToStorage();

      const result: SyncResult = {
        success: true,
        syncedActions,
        failedActions,
      };

      console.log('✅ OfflineSyncManager: Sync completed:', result);

      // Notify listeners
      this.syncListeners.forEach(listener => listener(result));

      return result;
    } catch (error) {
      console.error('❌ OfflineSyncManager: Sync failed:', error);
      const result: SyncResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        syncedActions,
        failedActions,
      };
      
      this.syncListeners.forEach(listener => listener(result));
      return result;
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Process a single queued action
   */
  private async processAction(action: QueuedAction): Promise<void> {
    console.log(`🔄 OfflineSyncManager: Processing action ${action.type}:`, action.id);

    switch (action.type) {
      case 'WORKOUT_LOG_CREATE':
        await this.syncWorkoutLog(action.payload);
        break;
      case 'CHECKIN_SUBMIT':
        await this.syncCheckIn(action.payload);
        break;
      case 'MESSAGE_SEND':
        await this.syncMessage(action.payload);
        break;
      case 'PROGRESS_PHOTO_UPLOAD':
        await this.syncProgressPhoto(action.payload);
        break;
      case 'PROFILE_UPDATE':
        await this.syncProfileUpdate(action.payload);
        break;
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Sync workout log data
   */
  private async syncWorkoutLog(payload: any): Promise<void> {
    const { error } = await supabase
      .from('workout_logs')
      .insert(payload);

    if (error) throw error;
  }

  /**
   * Sync check-in data
   */
  private async syncCheckIn(payload: any): Promise<void> {
    const { error } = await supabase
      .from('weekly_checkins')
      .insert(payload);

    if (error) throw error;
  }

  /**
   * Sync message data
   */
  private async syncMessage(payload: any): Promise<void> {
    const { error } = await supabase
      .from('messages')
      .insert(payload);

    if (error) throw error;
  }

  /**
   * Sync progress photo
   */
  private async syncProgressPhoto(payload: any): Promise<void> {
    // First upload the photo to storage
    if (payload.photoUri) {
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('progress-photos')
        .upload(payload.fileName, payload.photoUri);

      if (uploadError) throw uploadError;

      payload.photo_url = uploadData.path;
      delete payload.photoUri;
      delete payload.fileName;
    }

    // Then save the record
    const { error } = await supabase
      .from('progress_tracking')
      .insert(payload);

    if (error) throw error;
  }

  /**
   * Sync profile update
   */
  private async syncProfileUpdate(payload: any): Promise<void> {
    const { error } = await supabase
      .from('profiles')
      .update(payload.updates)
      .eq('id', payload.userId);

    if (error) throw error;
  }

  /**
   * Load action queue from storage
   */
  private async loadQueueFromStorage(): Promise<void> {
    try {
      const queue = await PersistenceManager.retrieveData<QueuedAction[]>(
        STORAGE_KEYS.OFFLINE_QUEUE,
        []
      );
      this.actionQueue = queue || [];
      console.log(`📝 OfflineSyncManager: Loaded ${this.actionQueue.length} queued actions from storage`);
    } catch (error) {
      console.error('❌ OfflineSyncManager: Failed to load queue from storage:', error);
      this.actionQueue = [];
    }
  }

  /**
   * Save action queue to storage
   */
  private async saveQueueToStorage(): Promise<void> {
    try {
      await PersistenceManager.persistData(STORAGE_KEYS.OFFLINE_QUEUE, this.actionQueue);
    } catch (error) {
      console.error('❌ OfflineSyncManager: Failed to save queue to storage:', error);
    }
  }

  /**
   * Get current network status
   */
  getNetworkStatus(): { isOnline: boolean; isSyncing: boolean; queueLength: number } {
    return {
      isOnline: this.isOnline,
      isSyncing: this.isSyncing,
      queueLength: this.actionQueue.length,
    };
  }

  /**
   * Add sync listener
   */
  addSyncListener(listener: (result: SyncResult) => void): () => void {
    this.syncListeners.push(listener);
    return () => {
      this.syncListeners = this.syncListeners.filter(l => l !== listener);
    };
  }

  /**
   * Add network status listener
   */
  addNetworkListener(listener: (status: NetworkStatus) => void): () => void {
    this.networkListeners.push(listener);
    return () => {
      this.networkListeners = this.networkListeners.filter(l => l !== listener);
    };
  }

  /**
   * Clear all queued actions (use with caution)
   */
  async clearQueue(): Promise<void> {
    this.actionQueue = [];
    await this.saveQueueToStorage();
    console.log('🗑️ OfflineSyncManager: Queue cleared');
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.netInfoUnsubscribe) {
      this.netInfoUnsubscribe();
    }
    this.syncListeners = [];
    this.networkListeners = [];
  }
}

// Export singleton instance
export const offlineSyncManager = OfflineSyncManager.getInstance();
