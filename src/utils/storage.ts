import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

/**
 * Unified storage interface for both web and mobile platforms
 * Uses AsyncStorage for React Native and localStorage for web
 */
export class StorageManager {
  private static instance: StorageManager;
  
  public static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  /**
   * Store data with key
   */
  async setItem(key: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      
      if (Platform.OS === 'web') {
        localStorage.setItem(key, serializedValue);
      } else {
        await AsyncStorage.setItem(key, serializedValue);
      }
    } catch (error) {
      console.error(`StorageManager.setItem error for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Retrieve data by key
   */
  async getItem<T>(key: string): Promise<T | null> {
    try {
      let serializedValue: string | null;
      
      if (Platform.OS === 'web') {
        serializedValue = localStorage.getItem(key);
      } else {
        serializedValue = await AsyncStorage.getItem(key);
      }
      
      if (serializedValue === null) {
        return null;
      }
      
      return JSON.parse(serializedValue) as T;
    } catch (error) {
      console.error(`StorageManager.getItem error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Remove data by key
   */
  async removeItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        localStorage.removeItem(key);
      } else {
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`StorageManager.removeItem error for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Clear all stored data
   */
  async clear(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        localStorage.clear();
      } else {
        await AsyncStorage.clear();
      }
    } catch (error) {
      console.error('StorageManager.clear error:', error);
      throw error;
    }
  }

  /**
   * Get all keys
   */
  async getAllKeys(): Promise<string[]> {
    try {
      if (Platform.OS === 'web') {
        return Object.keys(localStorage);
      } else {
        return await AsyncStorage.getAllKeys();
      }
    } catch (error) {
      console.error('StorageManager.getAllKeys error:', error);
      return [];
    }
  }

  /**
   * Get multiple items at once
   */
  async multiGet(keys: string[]): Promise<Array<[string, any]>> {
    try {
      const results: Array<[string, any]> = [];
      
      for (const key of keys) {
        const value = await this.getItem(key);
        results.push([key, value]);
      }
      
      return results;
    } catch (error) {
      console.error('StorageManager.multiGet error:', error);
      return [];
    }
  }

  /**
   * Set multiple items at once
   */
  async multiSet(keyValuePairs: Array<[string, any]>): Promise<void> {
    try {
      for (const [key, value] of keyValuePairs) {
        await this.setItem(key, value);
      }
    } catch (error) {
      console.error('StorageManager.multiSet error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const storage = StorageManager.getInstance();

// Storage keys constants
export const STORAGE_KEYS = {
  // User data
  USER_PROFILE: 'user_profile',
  AUTH_SESSION: 'auth_session',
  THEME_PREFERENCE: 'theme_preference',
  
  // Workout data
  WORKOUT_PROGRAMS: 'workout_programs',
  CURRENT_PROGRAM: 'current_program',
  ACTIVE_WORKOUT: 'active_workout',
  WORKOUT_HISTORY: 'workout_history',
  
  // Progress data
  PROGRESS_PHOTOS: 'progress_photos',
  MEASUREMENTS: 'measurements',
  GOALS: 'goals',
  
  // Communication
  MESSAGES: 'messages',
  CHECK_INS: 'check_ins',
  COACH_FEEDBACK: 'coach_feedback',
  
  // App state
  OFFLINE_QUEUE: 'offline_queue',
  LAST_SYNC: 'last_sync',
  APP_VERSION: 'app_version',
  PUSH_TOKEN: 'push_token',

  // Device tracking
  DEVICE_ID: 'device_id',
  USER_DEVICE_INFO: 'user_device_info',
  LAST_LOGIN: 'last_login',
} as const;

/**
 * Data migration utility for handling schema changes
 */
export class DataMigration {
  private static readonly CURRENT_VERSION = '1.0.0';
  private static readonly VERSION_KEY = STORAGE_KEYS.APP_VERSION;

  static async runMigrations(): Promise<void> {
    try {
      const currentVersion = await storage.getItem<string>(this.VERSION_KEY);
      
      if (!currentVersion) {
        // First time installation
        await this.initialSetup();
      } else if (currentVersion !== this.CURRENT_VERSION) {
        // Run migrations based on version
        await this.migrate(currentVersion, this.CURRENT_VERSION);
      }
      
      // Update version
      await storage.setItem(this.VERSION_KEY, this.CURRENT_VERSION);
    } catch (error) {
      console.error('DataMigration.runMigrations error:', error);
    }
  }

  private static async initialSetup(): Promise<void> {
    console.log('DataMigration: Running initial setup');
    // Initialize default values if needed
    await storage.setItem(STORAGE_KEYS.OFFLINE_QUEUE, []);
    await storage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());
  }

  private static async migrate(fromVersion: string, toVersion: string): Promise<void> {
    console.log(`DataMigration: Migrating from ${fromVersion} to ${toVersion}`);
    
    // Add migration logic here as needed
    // Example:
    // if (fromVersion === '0.9.0' && toVersion === '1.0.0') {
    //   await this.migrateFrom090To100();
    // }
  }
}

/**
 * Utility for handling data persistence with error recovery
 */
export class PersistenceManager {
  /**
   * Safely persist data with error handling and retry logic
   */
  static async persistData<T>(key: string, data: T, retries = 3): Promise<boolean> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        await storage.setItem(key, data);
        return true;
      } catch (error) {
        console.error(`PersistenceManager.persistData attempt ${attempt} failed:`, error);
        
        if (attempt === retries) {
          return false;
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
      }
    }
    
    return false;
  }

  /**
   * Safely retrieve data with error handling
   */
  static async retrieveData<T>(key: string, defaultValue: T | null = null): Promise<T | null> {
    try {
      const data = await storage.getItem<T>(key);
      return data !== null ? data : defaultValue;
    } catch (error) {
      console.error(`PersistenceManager.retrieveData error for key ${key}:`, error);
      return defaultValue;
    }
  }

  /**
   * Clear specific data with error handling
   */
  static async clearData(key: string): Promise<boolean> {
    try {
      await storage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`PersistenceManager.clearData error for key ${key}:`, error);
      return false;
    }
  }
}
