import { supabase } from './supabaseClient';
import { profileService } from './profileService';

export interface WorkoutLog {
  id: string;
  user_id: string;
  workout_program_id?: string;
  planned_workout_id?: string;
  workout_name_actual: string;
  started_at: string;
  completed_at: string;
  duration_seconds: number;
  overall_session_rpe?: number;
  client_notes_for_session?: string;
  created_at: string;
  updated_at: string;
}

export interface WorkoutExerciseLog {
  id: string;
  workout_log_id: string;
  exercise_id: string;
  planned_workout_exercise_id?: string;
  exercise_order: number;
  sets_logged: SetLog[];
  exercise_notes?: string;
  created_at: string;
  updated_at: string;
  exercise?: {
    id: string;
    name: string;
    description?: string;
    video_url?: string;
    target_muscles_primary: string[];
    equipment_required: string[];
  };
}

export interface SetLog {
  set_number: number;
  reps_actual?: number;
  weight_kg_actual?: number;
  duration_seconds_actual?: number;
  rir_actual?: number;
  rpe_actual?: number;
  rest_completed_seconds?: number;
  notes?: string;
}

export interface WorkoutLogData {
  workout_program_id?: string;
  planned_workout_id?: string;
  workout_name_actual: string;
  started_at: string;
  completed_at: string;
  duration_seconds: number;
  overall_session_rpe?: number;
  client_notes_for_session?: string;
}

export interface ExerciseLogData {
  exercise_id: string;
  planned_workout_exercise_id?: string;
  exercise_order: number;
  sets_logged: SetLog[];
  exercise_notes?: string;
}

export interface DetailedWorkoutLog extends WorkoutLog {
  exercise_logs: WorkoutExerciseLog[];
}

export const workoutLoggingService = {
  // Save a completed workout session
  async saveCompletedWorkout(
    workoutLogData: WorkoutLogData,
    exerciseLogsData: ExerciseLogData[]
  ): Promise<WorkoutLog> {
    console.log('💾 WorkoutLoggingService: Saving completed workout');
    console.log('📋 Workout data:', workoutLogData);
    console.log('🏋️ Exercise logs count:', exerciseLogsData.length);

    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    try {
      // Insert workout log
      const { data: workoutLog, error: workoutError } = await supabase
        .from('workout_logs')
        .insert({
          user_id: user.id,
          ...workoutLogData,
        })
        .select()
        .single();

      if (workoutError) {
        console.error('❌ Error inserting workout log:', workoutError);
        throw workoutError;
      }

      console.log('✅ Workout log created:', workoutLog.id);

      // Insert exercise logs
      const exerciseLogsToInsert = exerciseLogsData.map(exerciseLog => ({
        workout_log_id: workoutLog.id,
        ...exerciseLog,
      }));

      const { error: exerciseLogsError } = await supabase
        .from('workout_exercise_logs')
        .insert(exerciseLogsToInsert);

      if (exerciseLogsError) {
        console.error('❌ Error inserting exercise logs:', exerciseLogsError);
        // Try to clean up the workout log if exercise logs failed
        await supabase
          .from('workout_logs')
          .delete()
          .eq('id', workoutLog.id);
        throw exerciseLogsError;
      }

      console.log('✅ Exercise logs created successfully');
      return workoutLog;
    } catch (error) {
      console.error('❌ WorkoutLoggingService: Error saving workout:', error);
      throw error;
    }
  },

  // Fetch workout history (paginated)
  async fetchWorkoutHistory(
    limit: number = 20,
    offset: number = 0
  ): Promise<WorkoutLog[]> {
    console.log('📚 WorkoutLoggingService: Fetching workout history with limit:', limit, 'offset:', offset);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.error('❌ WorkoutLoggingService: No authenticated user found');
        throw new Error('No authenticated user');
      }

      console.log('🔍 WorkoutLoggingService: Executing Supabase query for user:', user.id);
      
      const { data, error } = await supabase
        .from('workout_logs')
        .select('*')
        .eq('user_id', user.id)
        .order('completed_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('❌ WorkoutLoggingService: Error fetching workout history:', error);
        throw error;
      }

      console.log('✅ WorkoutLoggingService: Fetched', data?.length || 0, 'workout logs');
      console.log('📊 WorkoutLoggingService: First few results:', data?.slice(0, 2));
      
      return data || [];
    } catch (error) {
      console.error('❌ WorkoutLoggingService: Error in fetchWorkoutHistory:', error);
      throw error;
    }
  },

  // Fetch detailed workout log
  async fetchDetailedWorkoutLog(workoutLogId: string): Promise<DetailedWorkoutLog> {
    console.log('🔍 WorkoutLoggingService: Fetching detailed workout log:', workoutLogId);

    const { data: workoutLog, error: workoutError } = await supabase
      .from('workout_logs')
      .select('*')
      .eq('id', workoutLogId)
      .single();

    if (workoutError) {
      console.error('❌ Error fetching workout log:', workoutError);
      throw workoutError;
    }

    const { data: exerciseLogs, error: exerciseLogsError } = await supabase
      .from('workout_exercise_logs')
      .select(`
        *,
        exercises (
          id,
          name,
          description,
          video_url,
          target_muscles_primary,
          equipment_required
        )
      `)
      .eq('workout_log_id', workoutLogId)
      .order('exercise_order');

    if (exerciseLogsError) {
      console.error('❌ Error fetching exercise logs:', exerciseLogsError);
      throw exerciseLogsError;
    }

    console.log('✅ Fetched detailed workout log with', exerciseLogs?.length || 0, 'exercises');

    return {
      ...workoutLog,
      exercise_logs: exerciseLogs || [],
    };
  },

  // Get workout statistics
  async getWorkoutStats(): Promise<{
    totalWorkouts: number;
    totalDuration: number;
    averageRpe: number;
    lastWorkoutDate?: string;
  }> {
    console.log('📊 WorkoutLoggingService: Fetching workout statistics');

    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.error('❌ WorkoutLoggingService: No authenticated user found for stats');
        throw new Error('No authenticated user');
      }

      console.log('🔍 WorkoutLoggingService: Executing stats query for user:', user.id);
      
      const { data, error } = await supabase
        .from('workout_logs')
        .select('duration_seconds, overall_session_rpe, completed_at')
        .eq('user_id', user.id)
        .order('completed_at', { ascending: false });

      if (error) {
        console.error('❌ WorkoutLoggingService: Error fetching workout stats:', error);
        throw error;
      }

      const totalWorkouts = data?.length || 0;
      const totalDuration = data?.reduce((sum, log) => sum + log.duration_seconds, 0) || 0;
      const rpeValues = data?.filter(log => log.overall_session_rpe).map(log => log.overall_session_rpe) || [];
      const averageRpe = rpeValues.length > 0 
        ? rpeValues.reduce((sum, rpe) => sum + rpe, 0) / rpeValues.length 
        : 0;
      const lastWorkoutDate = data?.[0]?.completed_at;

      console.log('✅ WorkoutLoggingService: Stats calculated:', { 
        totalWorkouts, 
        totalDuration, 
        averageRpe: Math.round(averageRpe * 10) / 10,
        lastWorkoutDate: lastWorkoutDate || 'none'
      });

      return {
        totalWorkouts,
        totalDuration,
        averageRpe: Math.round(averageRpe * 10) / 10, // Round to 1 decimal
        lastWorkoutDate,
      };
    } catch (error) {
      console.error('❌ WorkoutLoggingService: Error getting workout stats:', error);
      return {
        totalWorkouts: 0,
        totalDuration: 0,
        averageRpe: 0
      };
    }
  },
  
  // Get exercise performance history
  async getExercisePerformanceHistory(exerciseId: string, limit: number = 5): Promise<any[]> {
    console.log('📈 WorkoutLoggingService: Fetching exercise performance history for:', exerciseId);

    const { data, error } = await supabase
      .from('workout_exercise_logs')
      .select(`
        id,
        exercise_id,
        sets_logged,
        exercise_notes,
        workout_log_id,
        workout_logs (
          completed_at
        )
      `)
      .eq('exercise_id', exerciseId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ Error fetching exercise performance history:', error);
      throw error;
    }

    console.log('✅ Fetched exercise performance history:', data?.length || 0, 'entries');
    return data || [];
  },

  // Get weekly workout completion rates
  async getWeeklyCompletionRates(weeks: number = 4): Promise<{
    weekStartDate: string;
    completedWorkouts: number;
    totalPlannedWorkouts: number;
    completionRate: number;
  }[]> {
    console.log('📊 WorkoutLoggingService: Fetching weekly completion rates');

    try {
      // Get current date and calculate start date for the period
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - (weeks * 7));
      
      // Fetch all workout logs in the period
      const { data: workoutLogs, error: workoutLogsError } = await supabase
        .from('workout_logs')
        .select('*')
        .gte('completed_at', startDate.toISOString())
        .lte('completed_at', endDate.toISOString())
        .order('completed_at', { ascending: true });

      if (workoutLogsError) {
        throw workoutLogsError;
      }

      // Group workouts by week
      const weeklyData: Record<string, {
        completedWorkouts: number;
        totalPlannedWorkouts: number; // This would ideally come from the workout program
      }> = {};

      // Process each week in the period
      for (let i = 0; i < weeks; i++) {
        const weekStart = new Date(endDate);
        weekStart.setDate(endDate.getDate() - (7 * (weeks - i)));
        
        // Format as ISO string and truncate to date part
        const weekStartStr = weekStart.toISOString().split('T')[0];
        
        // Initialize the week data
        weeklyData[weekStartStr] = {
          completedWorkouts: 0,
          totalPlannedWorkouts: 4, // Assuming 4 planned workouts per week as a placeholder
        };
      }

      // Count completed workouts per week
      if (workoutLogs) {
        workoutLogs.forEach(log => {
          const logDate = new Date(log.completed_at);
          // Find the start of the week for this log
          const weekStart = new Date(logDate);
          const day = weekStart.getDay(); // 0 = Sunday, 1 = Monday, etc.
          weekStart.setDate(weekStart.getDate() - day); // Go back to the start of the week (Sunday)
          
          const weekStartStr = weekStart.toISOString().split('T')[0];
          
          // If this week is in our tracking period, increment the count
          if (weeklyData[weekStartStr]) {
            weeklyData[weekStartStr].completedWorkouts++;
          }
        });
      }

      // Convert to array and calculate completion rates
      const result = Object.entries(weeklyData).map(([weekStartDate, data]) => {
        const completionRate = data.totalPlannedWorkouts > 0 
          ? (data.completedWorkouts / data.totalPlannedWorkouts) * 100 
          : 0;
        
        return {
          weekStartDate,
          completedWorkouts: data.completedWorkouts,
          totalPlannedWorkouts: data.totalPlannedWorkouts,
          completionRate: Math.round(completionRate),
        };
      });

      console.log('✅ Weekly completion rates calculated:', result);
      return result;
    } catch (error) {
      console.error('❌ Error calculating weekly completion rates:', error);
      throw error;
    }
  },

  // Get intensity trends (average RPE per week)
  async getIntensityTrends(weeks: number = 4): Promise<{
    weekStartDate: string;
    averageRpe: number;
  }[]> {
    console.log('📈 WorkoutLoggingService: Fetching intensity trends');

    try {
      // Get current date and calculate start date for the period
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - (weeks * 7));
      
      // Fetch all workout logs with RPE data in the period
      const { data: workoutLogs, error: workoutLogsError } = await supabase
        .from('workout_logs')
        .select('completed_at, overall_session_rpe')
        .gte('completed_at', startDate.toISOString())
        .lte('completed_at', endDate.toISOString())
        .not('overall_session_rpe', 'is', null)
        .order('completed_at', { ascending: true });

      if (workoutLogsError) {
        throw workoutLogsError;
      }

      // Group RPE values by week
      const weeklyRpe: Record<string, number[]> = {};

      // Process each week in the period
      for (let i = 0; i < weeks; i++) {
        const weekStart = new Date(endDate);
        weekStart.setDate(endDate.getDate() - (7 * (weeks - i)));
        
        // Format as ISO string and truncate to date part
        const weekStartStr = weekStart.toISOString().split('T')[0];
        
        // Initialize the week data
        weeklyRpe[weekStartStr] = [];
      }

      // Group RPE values by week
      if (workoutLogs) {
        workoutLogs.forEach(log => {
          if (log.overall_session_rpe) {
            const logDate = new Date(log.completed_at);
            // Find the start of the week for this log
            const weekStart = new Date(logDate);
            const day = weekStart.getDay(); // 0 = Sunday, 1 = Monday, etc.
            weekStart.setDate(weekStart.getDate() - day); // Go back to the start of the week (Sunday)
            
            const weekStartStr = weekStart.toISOString().split('T')[0];
            
            // If this week is in our tracking period, add the RPE value
            if (weeklyRpe[weekStartStr]) {
              weeklyRpe[weekStartStr].push(log.overall_session_rpe);
            }
          }
        });
      }

      // Calculate average RPE for each week
      const result = Object.entries(weeklyRpe).map(([weekStartDate, rpeValues]) => {
        const averageRpe = rpeValues.length > 0 
          ? rpeValues.reduce((sum, rpe) => sum + rpe, 0) / rpeValues.length 
          : 0;
        
        return {
          weekStartDate,
          averageRpe: Math.round(averageRpe * 10) / 10, // Round to 1 decimal
        };
      });

      console.log('✅ Intensity trends calculated:', result);
      return result;
    } catch (error) {
      console.error('❌ Error calculating intensity trends:', error);
      throw error;
    }
  },

  // Calculate current workout streak
  async calculateWorkoutStreak(): Promise<number> {
    console.log('🔥 WorkoutLoggingService: Calculating workout streak');

    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.error('❌ WorkoutLoggingService: No authenticated user found for streak calculation');
        throw new Error('No authenticated user');
      }

      // Get recent workout logs ordered by completion date
      const { data: recentLogs, error } = await supabase
        .from('workout_logs')
        .select('completed_at')
        .eq('user_id', user.id)
        .order('completed_at', { ascending: false })
        .limit(30); // Get enough logs to calculate streak

      if (error) {
        throw error;
      }

      if (!recentLogs || recentLogs.length === 0) {
        console.log('ℹ️ WorkoutLoggingService: No workouts logged yet, streak is 0');
        return 0; // No workouts logged yet
      }

      // Get the most recent workout date
      const mostRecentDate = new Date(recentLogs[0].completed_at);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      // Check if the most recent workout was today or yesterday
      const mostRecentDay = mostRecentDate.setHours(0, 0, 0, 0);
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (mostRecentDay < yesterday.getTime()) {
        console.log('ℹ️ WorkoutLoggingService: Streak broken - most recent workout was before yesterday');
        return 0; // Streak broken - most recent workout was before yesterday
      }

      // Convert workout dates to day-only format for comparison
      const workoutDays = recentLogs.map(log => {
        const date = new Date(log.completed_at);
        date.setHours(0, 0, 0, 0);
        return date.getTime();
      });

      // Sort and remove duplicates (only count one workout per day)
      const uniqueWorkoutDays = [...new Set(workoutDays)].sort((a, b) => b - a);

      // Calculate streak by checking for consecutive days
      let streak = 1; // Start with 1 for the most recent day
      let currentDate = uniqueWorkoutDays[0];
      
      for (let i = 1; i < uniqueWorkoutDays.length; i++) {
        const prevDate = new Date(currentDate);
        prevDate.setDate(prevDate.getDate() - 1);
        
        if (uniqueWorkoutDays[i] === prevDate.getTime()) {
          // This workout was on the previous consecutive day
          streak++;
          currentDate = uniqueWorkoutDays[i];
        } else {
          // Streak broken
          break;
        }
      }

      console.log('✅ WorkoutLoggingService: Current workout streak calculated:', streak);
      return streak;
    } catch (error) {
      console.error('❌ WorkoutLoggingService: Error calculating workout streak:', error);
      return 0; // Return 0 on error
    }
  }
};