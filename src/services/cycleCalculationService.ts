import { WorkoutProgram } from './workoutService';
import { CycleProgress } from './workoutStatsService';

export const cycleCalculationService = {
  calculateRealCycleInfo(program: WorkoutProgram): CycleProgress {
    const startDate = new Date(program.client_start_date || program.created_at);
    const now = new Date();
    
    // Calculate days since program start
    const daysSinceStart = Math.floor(
      (now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    // Ensure we don't have negative days
    const safeDaysSinceStart = Math.max(0, daysSinceStart);
    
    // Calculate cycle information
    const daysInCycle = 28; // 4 weeks per cycle
    const currentCycleDay = safeDaysSinceStart % daysInCycle;
    const currentCycle = Math.floor(safeDaysSinceStart / daysInCycle) + 1;
    const totalCycles = Math.ceil((program.duration_weeks || 12) / 4);
    
    // Calculate progress percentage within current cycle
    const cycleProgress = Math.min(100, Math.round((currentCycleDay / daysInCycle) * 100));
    
    // Calculate next transition date
    const nextTransitionDate = new Date(startDate);
    nextTransitionDate.setDate(startDate.getDate() + (currentCycle * daysInCycle));
    
    // Calculate current week within cycle (1-4)
    const weekProgress = Math.floor(currentCycleDay / 7) + 1;
    
    // Check if program is still active
    const isActive = currentCycle <= totalCycles && program.status === 'active_by_client';

    return {
      currentCycle: Math.min(currentCycle, totalCycles),
      totalCycles,
      cycleProgress,
      daysInCycle,
      daysCompleted: currentCycleDay,
      nextTransitionDate: nextTransitionDate.toISOString().split('T')[0],
      weekProgress: Math.min(weekProgress, 4),
      isActive
    };
  },

  formatTransitionDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays > 0) return `In ${diffDays} days`;
    return date.toLocaleDateString();
  },

  // Calculate detailed cycle metrics for advanced analytics
  calculateDetailedCycleMetrics(program: WorkoutProgram): {
    cycleInfo: CycleProgress;
    weeklyBreakdown: Array<{
      weekNumber: number;
      startDate: string;
      endDate: string;
      isCurrentWeek: boolean;
      isCompleted: boolean;
    }>;
    milestones: Array<{
      name: string;
      date: string;
      isReached: boolean;
      daysUntil: number;
    }>;
  } {
    const cycleInfo = this.calculateRealCycleInfo(program);
    const startDate = new Date(program.client_start_date || program.created_at);
    const now = new Date();

    // Calculate weekly breakdown for current cycle
    const weeklyBreakdown = [];
    const currentCycleStartDate = new Date(startDate);
    currentCycleStartDate.setDate(startDate.getDate() + ((cycleInfo.currentCycle - 1) * 28));

    for (let week = 1; week <= 4; week++) {
      const weekStartDate = new Date(currentCycleStartDate);
      weekStartDate.setDate(currentCycleStartDate.getDate() + ((week - 1) * 7));
      
      const weekEndDate = new Date(weekStartDate);
      weekEndDate.setDate(weekStartDate.getDate() + 6);

      const isCurrentWeek = week === cycleInfo.weekProgress;
      const isCompleted = now > weekEndDate;

      weeklyBreakdown.push({
        weekNumber: week,
        startDate: weekStartDate.toISOString().split('T')[0],
        endDate: weekEndDate.toISOString().split('T')[0],
        isCurrentWeek,
        isCompleted
      });
    }

    // Calculate program milestones
    const milestones = [];
    
    // Quarter program milestone (25%)
    const quarterDate = new Date(startDate);
    quarterDate.setDate(startDate.getDate() + Math.floor((program.duration_weeks || 12) * 7 * 0.25));
    
    // Half program milestone (50%)
    const halfDate = new Date(startDate);
    halfDate.setDate(startDate.getDate() + Math.floor((program.duration_weeks || 12) * 7 * 0.5));
    
    // Three-quarter program milestone (75%)
    const threeQuarterDate = new Date(startDate);
    threeQuarterDate.setDate(startDate.getDate() + Math.floor((program.duration_weeks || 12) * 7 * 0.75));
    
    // Program completion
    const completionDate = new Date(startDate);
    completionDate.setDate(startDate.getDate() + ((program.duration_weeks || 12) * 7));

    const milestoneDates = [
      { name: '25% Complete', date: quarterDate },
      { name: '50% Complete', date: halfDate },
      { name: '75% Complete', date: threeQuarterDate },
      { name: 'Program Complete', date: completionDate }
    ];

    milestoneDates.forEach(milestone => {
      const isReached = now >= milestone.date;
      const daysUntil = Math.ceil((milestone.date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      milestones.push({
        name: milestone.name,
        date: milestone.date.toISOString().split('T')[0],
        isReached,
        daysUntil: Math.max(0, daysUntil)
      });
    });

    return {
      cycleInfo,
      weeklyBreakdown,
      milestones
    };
  },

  // Calculate program completion percentage
  calculateProgramCompletion(program: WorkoutProgram): {
    overallProgress: number;
    weeksCompleted: number;
    totalWeeks: number;
    daysRemaining: number;
    estimatedCompletionDate: string;
  } {
    const startDate = new Date(program.client_start_date || program.created_at);
    const now = new Date();
    const totalWeeks = program.duration_weeks || 12;
    
    const daysSinceStart = Math.max(0, Math.floor(
      (now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    ));
    
    const weeksCompleted = Math.min(totalWeeks, Math.floor(daysSinceStart / 7));
    const overallProgress = Math.min(100, Math.round((weeksCompleted / totalWeeks) * 100));
    
    const totalDays = totalWeeks * 7;
    const daysRemaining = Math.max(0, totalDays - daysSinceStart);
    
    const estimatedCompletionDate = new Date(startDate);
    estimatedCompletionDate.setDate(startDate.getDate() + totalDays);

    return {
      overallProgress,
      weeksCompleted,
      totalWeeks,
      daysRemaining,
      estimatedCompletionDate: estimatedCompletionDate.toISOString().split('T')[0]
    };
  },

  // Utility function to check if a program needs cycle transition
  shouldTransitionCycle(program: WorkoutProgram): {
    shouldTransition: boolean;
    reason: string;
    nextCycleNumber: number;
    transitionDate: string;
  } {
    const cycleInfo = this.calculateRealCycleInfo(program);
    const shouldTransition = cycleInfo.cycleProgress >= 100 && cycleInfo.isActive;
    
    return {
      shouldTransition,
      reason: shouldTransition 
        ? `Cycle ${cycleInfo.currentCycle} is complete` 
        : `Cycle ${cycleInfo.currentCycle} is ${cycleInfo.cycleProgress}% complete`,
      nextCycleNumber: cycleInfo.currentCycle + 1,
      transitionDate: cycleInfo.nextTransitionDate
    };
  }
};
