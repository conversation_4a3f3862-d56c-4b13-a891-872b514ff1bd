import { supabase } from './supabaseClient';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { notificationService } from './notificationService';
import { storage, STORAGE_KEYS } from '../utils/storage';

interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  deviceType: string;
  osName: string;
  osVersion: string;
  appVersion: string;
  platform: string;
}

export const authService = {
  /**
   * Get device information for tracking
   */
  async getDeviceInfo(): Promise<DeviceInfo> {
    const deviceId = await this.getOrCreateDeviceId();

    return {
      deviceId,
      deviceName: Device.deviceName || 'Unknown Device',
      deviceType: Device.deviceType?.toString() || 'Unknown',
      osName: Device.osName || Platform.OS,
      osVersion: Device.osVersion || 'Unknown',
      appVersion: Constants.expoConfig?.version || '1.0.0',
      platform: Platform.OS,
    };
  },

  /**
   * Get or create a unique device ID
   */
  async getOrCreateDeviceId(): Promise<string> {
    let deviceId = await storage.getItem<string>(STORAGE_KEYS.DEVICE_ID);

    if (!deviceId) {
      // Generate a unique device ID
      deviceId = `${Platform.OS}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      await storage.setItem(STORAGE_KEYS.DEVICE_ID, deviceId);
    }

    return deviceId;
  },

  async signUp(email: string, password: string, fullName: string) {
    const deviceInfo = await this.getDeviceInfo();

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
          device_id: deviceInfo.deviceId,
          device_info: deviceInfo,
        },
      },
    });

    if (error) throw error;

    // If signup successful, setup push notifications
    if (data.user) {
      try {
        await notificationService.initialize();
      } catch (notificationError) {
        console.warn('Failed to setup push notifications:', notificationError);
      }
    }

    return data;
  },

  async signIn(email: string, password: string) {
    const deviceInfo = await this.getDeviceInfo();

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    // Update device info and push token after successful sign in
    if (data.user) {
      try {
        // Update profile with current device info
        await this.updateDeviceInfo(data.user.id, deviceInfo);

        // Initialize push notifications
        await notificationService.initialize();
      } catch (updateError) {
        console.warn('Failed to update device info or setup notifications:', updateError);
      }
    }

    return data;
  },

  /**
   * Update device information in user profile
   */
  async updateDeviceInfo(userId: string, deviceInfo: DeviceInfo): Promise<void> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          device_id: deviceInfo.deviceId,
          device_info: deviceInfo,
          last_seen_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (error) throw error;

      console.log('✅ AuthService: Device info updated successfully');
    } catch (error) {
      console.error('❌ AuthService: Failed to update device info:', error);
      throw error;
    }
  },

  /**
   * Mobile-specific sign in with device tracking
   */
  async mobileSignIn(email: string, password: string, deviceId?: string): Promise<any> {
    const deviceInfo = await this.getDeviceInfo();

    // Use provided device ID or generate one
    if (deviceId) {
      deviceInfo.deviceId = deviceId;
      await storage.setItem(STORAGE_KEYS.DEVICE_ID, deviceId);
    }

    const result = await this.signIn(email, password);

    // Store additional mobile-specific data
    if (result.user) {
      await storage.setItem(STORAGE_KEYS.LAST_LOGIN, new Date().toISOString());
      await storage.setItem(STORAGE_KEYS.USER_DEVICE_INFO, deviceInfo);
    }

    return result;
  },

  async signOut() {
    try {
      // Clear push token from backend before signing out
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('profiles')
          .update({
            push_token: null,
            push_token_updated_at: null,
          })
          .eq('id', user.id);
      }
    } catch (error) {
      console.warn('Failed to clear push token on signout:', error);
    }

    // Clear local storage
    await storage.removeItem(STORAGE_KEYS.PUSH_TOKEN);
    await storage.removeItem(STORAGE_KEYS.USER_DEVICE_INFO);

    // Cleanup notification service
    notificationService.cleanup();

    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) throw error;
  },

  /**
   * Get current device session info
   */
  async getDeviceSession(): Promise<{
    deviceInfo: DeviceInfo | null;
    lastLogin: string | null;
    pushToken: string | null;
  }> {
    const deviceInfo = await storage.getItem<DeviceInfo>(STORAGE_KEYS.USER_DEVICE_INFO);
    const lastLogin = await storage.getItem<string>(STORAGE_KEYS.LAST_LOGIN);
    const pushToken = await storage.getItem<string>(STORAGE_KEYS.PUSH_TOKEN);

    return {
      deviceInfo,
      lastLogin,
      pushToken,
    };
  },

  /**
   * Update push notification preferences
   */
  async updatePushNotificationPreferences(enabled: boolean): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { error } = await supabase
        .from('profiles')
        .update({ push_notifications_enabled: enabled })
        .eq('id', user.id);

      if (error) throw error;

      console.log(`✅ AuthService: Push notifications ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('❌ AuthService: Failed to update push notification preferences:', error);
      throw error;
    }
  },

  /**
   * Refresh push token
   */
  async refreshPushToken(): Promise<string | null> {
    try {
      return await notificationService.registerForPushNotifications();
    } catch (error) {
      console.error('❌ AuthService: Failed to refresh push token:', error);
      return null;
    }
  },
};