import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { supabase } from './supabaseClient';
import { notificationService } from './notificationService';

export interface CoachNotificationData {
  type: 'message' | 'feedback' | 'check_in_response' | 'program_update' | 'modification_request';
  coachId: string;
  coachName: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  priority?: 'low' | 'medium' | 'high';
  silent?: boolean;
}

export interface NotificationPreferences {
  messageNotifications: boolean;
  feedbackNotifications: boolean;
  checkInReminders: boolean;
  programUpdates: boolean;
  achievementNotifications: boolean;
  marketingNotifications: boolean;
  quietHoursStart?: string; // HH:MM format
  quietHoursEnd?: string; // HH:MM format
  timezone: string;
}

class CoachNotificationService {
  private isInitialized = false;
  private notificationPreferences: NotificationPreferences | null = null;

  async initialize() {
    if (this.isInitialized) return;

    try {
      console.log('🔔 CoachNotificationService: Initializing...');

      // Don't override the main notification handler - let NotificationService handle it
      // Instead, just setup our listeners and load preferences

      // Setup notification listeners
      this.setupNotificationListeners();

      // Load notification preferences
      await this.getNotificationPreferences();

      this.isInitialized = true;
      console.log('✅ CoachNotificationService: Initialized successfully');
    } catch (error) {
      console.error('❌ CoachNotificationService: Failed to initialize:', error);
    }
  }

  private setupNotificationListeners() {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      const data = notification.request.content.data;
      console.log('Coach notification received:', data);
      
      // Log notification for analytics
      this.logNotificationEvent(notification.request.identifier, 'received', data);
    });

    // Handle notification response (user tapped notification)
    Notifications.addNotificationResponseReceivedListener((response) => {
      const data = response.notification.request.content.data;
      console.log('Coach notification tapped:', data);
      
      // Log notification interaction
      this.logNotificationEvent(response.notification.request.identifier, 'clicked', data);
      
      // Handle navigation based on notification type
      this.handleNotificationNavigation(data);
    });
  }

  private async logNotificationEvent(
    notificationId: string,
    event: 'received' | 'clicked',
    data: any
  ) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const logData = {
        user_id: user.id,
        notification_type: data?.type || 'unknown',
        title: data?.title || '',
        body: data?.body || '',
        data: data || {},
        status: event === 'received' ? 'delivered' : 'clicked',
        delivered_at: event === 'received' ? new Date().toISOString() : null,
        clicked_at: event === 'clicked' ? new Date().toISOString() : null,
      };

      await supabase
        .from('push_notification_logs')
        .upsert(logData, { onConflict: 'notification_id' });
    } catch (error) {
      console.error('Failed to log notification event:', error);
    }
  }

  private handleNotificationNavigation(data: any) {
    const { type, conversationId, feedbackId, checkInId, programId } = data;

    // This would integrate with your navigation system
    // For now, we'll just log the intended navigation
    switch (type) {
      case 'message':
        console.log('Navigate to conversation:', conversationId);
        // NavigationService.navigate('Conversation', { conversationId });
        break;
      case 'feedback':
        console.log('Navigate to feedback:', feedbackId);
        // NavigationService.navigate('CoachFeedback', { feedbackId });
        break;
      case 'check_in_response':
        console.log('Navigate to check-in:', checkInId);
        // NavigationService.navigate('CheckInFeedback', { checkInId });
        break;
      case 'program_update':
        console.log('Navigate to program:', programId);
        // NavigationService.navigate('Program', { programId });
        break;
      default:
        console.log('Navigate to coach communication hub');
        // NavigationService.navigate('CoachHub');
        break;
    }
  }

  async sendNewMessageNotification(
    clientId: string,
    coachName: string,
    messagePreview: string,
    conversationId: string
  ) {
    const notificationData: CoachNotificationData = {
      type: 'message',
      coachId: '',
      coachName,
      title: `New message from ${coachName}`,
      body: messagePreview,
      data: {
        conversationId,
        type: 'message',
      },
      priority: 'medium',
    };

    await this.sendNotificationToUser(clientId, notificationData);
  }

  async sendFeedbackNotification(
    clientId: string,
    coachName: string,
    feedbackType: string,
    feedbackId: string
  ) {
    const notificationData: CoachNotificationData = {
      type: 'feedback',
      coachId: '',
      coachName,
      title: `New feedback from ${coachName}`,
      body: `Your coach has provided feedback on your ${feedbackType}`,
      data: {
        feedbackId,
        feedbackType,
        type: 'feedback',
      },
      priority: 'medium',
    };

    await this.sendNotificationToUser(clientId, notificationData);
  }

  async sendCheckInResponseNotification(
    clientId: string,
    coachName: string,
    weekStartDate: string,
    checkInId: string
  ) {
    const notificationData: CoachNotificationData = {
      type: 'check_in_response',
      coachId: '',
      coachName,
      title: `Check-in feedback from ${coachName}`,
      body: `Your coach has reviewed your weekly check-in for ${new Date(weekStartDate).toLocaleDateString()}`,
      data: {
        checkInId,
        weekStartDate,
        type: 'check_in_response',
      },
      priority: 'medium',
    };

    await this.sendNotificationToUser(clientId, notificationData);
  }

  async sendProgramUpdateNotification(
    clientId: string,
    coachName: string,
    programName: string,
    updateType: string,
    programId: string
  ) {
    const notificationData: CoachNotificationData = {
      type: 'program_update',
      coachId: '',
      coachName,
      title: `Program update from ${coachName}`,
      body: `Your ${programName} has been ${updateType}`,
      data: {
        programId,
        programName,
        updateType,
        type: 'program_update',
      },
      priority: 'high',
    };

    await this.sendNotificationToUser(clientId, notificationData);
  }

  async sendModificationRequestResponseNotification(
    clientId: string,
    coachName: string,
    requestTitle: string,
    status: 'approved' | 'rejected',
    requestId: string
  ) {
    const statusText = status === 'approved' ? 'approved' : 'declined';
    const notificationData: CoachNotificationData = {
      type: 'modification_request',
      coachId: '',
      coachName,
      title: `Modification request ${statusText}`,
      body: `Your coach has ${statusText} your request: "${requestTitle}"`,
      data: {
        requestId,
        requestTitle,
        status,
        type: 'modification_request',
      },
      priority: 'medium',
    };

    await this.sendNotificationToUser(clientId, notificationData);
  }

  private async sendNotificationToUser(
    userId: string,
    notificationData: CoachNotificationData
  ) {
    try {
      // Get user's push token and preferences
      const [pushToken, preferences] = await Promise.all([
        this.getUserPushToken(userId),
        this.getNotificationPreferences(userId),
      ]);

      if (!pushToken) {
        console.log('No push token found for user:', userId);
        return;
      }

      // Check if notifications are enabled for this type
      if (!this.shouldShowNotification(notificationData.type, preferences)) {
        console.log('Notifications disabled for type:', notificationData.type);
        return;
      }

      // Check quiet hours
      if (this.isInQuietHours(preferences)) {
        console.log('In quiet hours, scheduling for later');
        await this.scheduleForLater(userId, notificationData, preferences);
        return;
      }

      // Send the notification
      await this.sendPushNotification(pushToken, notificationData);

      // Log the notification
      await this.logNotificationSent(userId, notificationData);
    } catch (error) {
      console.error('Failed to send notification to user:', error);
    }
  }

  private async getUserPushToken(userId: string): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('user_devices')
        .select('push_token')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('last_used_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) return null;
      return data.push_token;
    } catch (error) {
      console.error('Failed to get user push token:', error);
      return null;
    }
  }

  private async getNotificationPreferences(userId?: string): Promise<NotificationPreferences> {
    if (this.notificationPreferences && !userId) {
      return this.notificationPreferences;
    }

    try {
      const targetUserId = userId || (await supabase.auth.getUser()).data.user?.id;
      if (!targetUserId) {
        return this.getDefaultPreferences();
      }

      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', targetUserId)
        .single();

      if (error || !data) {
        return this.getDefaultPreferences();
      }

      const preferences: NotificationPreferences = {
        messageNotifications: data.message_notifications,
        feedbackNotifications: data.feedback_notifications,
        checkInReminders: data.check_in_reminders,
        programUpdates: data.program_updates,
        achievementNotifications: data.achievement_notifications,
        marketingNotifications: data.marketing_notifications,
        quietHoursStart: data.quiet_hours_start,
        quietHoursEnd: data.quiet_hours_end,
        timezone: data.timezone || 'UTC',
      };

      if (!userId) {
        this.notificationPreferences = preferences;
      }

      return preferences;
    } catch (error) {
      console.error('Failed to get notification preferences:', error);
      return this.getDefaultPreferences();
    }
  }

  private getDefaultPreferences(): NotificationPreferences {
    return {
      messageNotifications: true,
      feedbackNotifications: true,
      checkInReminders: true,
      programUpdates: true,
      achievementNotifications: true,
      marketingNotifications: false,
      timezone: 'UTC',
    };
  }

  private shouldShowNotification(
    type: string,
    preferences: NotificationPreferences
  ): boolean {
    switch (type) {
      case 'message':
        return preferences.messageNotifications;
      case 'feedback':
      case 'check_in_response':
        return preferences.feedbackNotifications;
      case 'program_update':
      case 'modification_request':
        return preferences.programUpdates;
      default:
        return true;
    }
  }

  private isInQuietHours(preferences: NotificationPreferences): boolean {
    if (!preferences.quietHoursStart || !preferences.quietHoursEnd) {
      return false;
    }

    const now = new Date();
    const currentTime = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      timeZone: preferences.timezone,
    });

    const startTime = preferences.quietHoursStart;
    const endTime = preferences.quietHoursEnd;

    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    }

    // Handle same-day quiet hours (e.g., 12:00 to 14:00)
    return currentTime >= startTime && currentTime <= endTime;
  }

  private async scheduleForLater(
    userId: string,
    notificationData: CoachNotificationData,
    preferences: NotificationPreferences
  ) {
    // Calculate when quiet hours end
    const now = new Date();
    const endTime = preferences.quietHoursEnd;
    
    if (!endTime) return;

    const [hours, minutes] = endTime.split(':').map(Number);
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);

    // If end time is tomorrow (overnight quiet hours)
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1);
    }

    // Schedule the notification
    await Notifications.scheduleNotificationAsync({
      content: {
        title: notificationData.title,
        body: notificationData.body,
        data: notificationData.data,
        priority: notificationData.priority === 'high' ? 
          Notifications.AndroidNotificationPriority.HIGH : 
          Notifications.AndroidNotificationPriority.DEFAULT,
      },
      trigger: {
        date: scheduledTime,
      },
    });
  }

  private async sendPushNotification(
    pushToken: string,
    notificationData: CoachNotificationData
  ) {
    const message = {
      to: pushToken,
      sound: 'default',
      title: notificationData.title,
      body: notificationData.body,
      data: notificationData.data,
      priority: notificationData.priority === 'high' ? 'high' : 'normal',
      channelId: 'coach-interactions',
    };

    await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
  }

  private async logNotificationSent(
    userId: string,
    notificationData: CoachNotificationData
  ) {
    try {
      const logData = {
        user_id: userId,
        notification_type: notificationData.type,
        title: notificationData.title,
        body: notificationData.body,
        data: notificationData.data || {},
        status: 'sent',
        sent_at: new Date().toISOString(),
      };

      await supabase
        .from('push_notification_logs')
        .insert(logData);
    } catch (error) {
      console.error('Failed to log notification:', error);
    }
  }

  async updateNotificationPreferences(
    preferences: Partial<NotificationPreferences>
  ) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const updateData = {
        user_id: user.id,
        message_notifications: preferences.messageNotifications,
        feedback_notifications: preferences.feedbackNotifications,
        check_in_reminders: preferences.checkInReminders,
        program_updates: preferences.programUpdates,
        achievement_notifications: preferences.achievementNotifications,
        marketing_notifications: preferences.marketingNotifications,
        quiet_hours_start: preferences.quietHoursStart,
        quiet_hours_end: preferences.quietHoursEnd,
        timezone: preferences.timezone,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('notification_preferences')
        .upsert(updateData);

      if (error) throw error;

      // Update cached preferences
      this.notificationPreferences = {
        ...this.notificationPreferences,
        ...preferences,
      } as NotificationPreferences;
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
      throw error;
    }
  }

  async clearNotificationPreferencesCache() {
    this.notificationPreferences = null;
  }

  /**
   * Test function to send a sample feedback notification
   */
  async sendTestFeedbackNotification(clientId: string) {
    console.log('🧪 CoachNotificationService: Sending test feedback notification');

    const notificationData: CoachNotificationData = {
      type: 'feedback',
      coachId: '',
      coachName: 'Test Coach',
      title: 'Test Feedback Notification',
      body: 'This is a test notification to verify the feedback notification system is working.',
      data: {
        feedbackId: 'test-feedback-id',
        feedbackType: 'test',
        type: 'feedback',
      },
      priority: 'medium',
    };

    await this.sendNotificationToUser(clientId, notificationData);
  }
}

export const coachNotificationService = new CoachNotificationService();
