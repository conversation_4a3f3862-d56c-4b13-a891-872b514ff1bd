import { supabase } from './supabaseClient';

export interface WeeklyCheckIn {
  id: string;
  user_id: string;
  program_week_identifier?: string;
  checkin_date: string; // ISO date string 'YYYY-MM-DD'
  submitted_at?: string; // ISO timestamp
  wins?: string;
  challenges?: string;
  progress_reflection?: string;
  training_performance_rating?: number; // 1-10
  recovery_rating?: number; // 1-10
  energy_levels_rating?: number; // 1-10
  additional_notes_for_coach?: string;
  created_at?: string;
  updated_at?: string;
  // Add optional profiles property for joined data
  profiles?: {
    full_name: string;
  };
}

export const checkInService = {
  /**
   * Submit a new weekly check-in
   */
  async submitCheckIn(checkInData: Omit<WeeklyCheckIn, 'id' | 'user_id' | 'submitted_at'>): Promise<WeeklyCheckIn> {
    console.log('📝 CheckInService: Submitting weekly check-in');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    // Prepare data for submission
    const now = new Date().toISOString();
    const data = {
      user_id: user.id,
      submitted_at: now,
      ...checkInData,
    };

    console.log('📋 CheckInService: Check-in data:', data);

    const { data: checkIn, error } = await supabase
      .from('weekly_checkins')
      .insert(data)
      .select()
      .single();

    if (error) {
      console.error('❌ CheckInService: Error submitting check-in:', error);
      throw error;
    }

    console.log('✅ CheckInService: Check-in submitted successfully');
    return checkIn;
  },

  /**
   * Check if a check-in exists for the given date
   */
  async hasSubmittedCheckInForDate(date: string): Promise<boolean> {
    console.log('🔍 CheckInService: Checking if check-in exists for date:', date);
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    const { data, error } = await supabase
      .from('weekly_checkins')
      .select('id')
      .eq('user_id', user.id)
      .eq('checkin_date', date)
      .maybeSingle();

    if (error) {
      console.error('❌ CheckInService: Error checking for existing check-in:', error);
      throw error;
    }

    return !!data;
  },

  /**
   * Fetch recent check-ins for all clients (for coach dashboard)
   * This implementation avoids using the direct relationship between weekly_checkins and profiles
   * by fetching them separately and combining them in the client
   */
  async fetchRecentClientCheckIns(days: number = 7, limit: number = 10): Promise<WeeklyCheckIn[]> {
    console.log('📚 CheckInService: Fetching recent client check-ins');
    
    // Calculate date range (last X days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    try {
      // Step 1: Fetch check-ins
      const { data: checkIns, error: checkInsError } = await supabase
        .from('weekly_checkins')
        .select('*')
        .gte('checkin_date', startDate.toISOString().split('T')[0])
        .lte('checkin_date', endDate.toISOString().split('T')[0])
        .order('checkin_date', { ascending: false })
        .limit(limit);

      if (checkInsError) {
        console.error('❌ CheckInService: Error fetching recent check-ins:', checkInsError);
        throw checkInsError;
      }

      if (!checkIns || checkIns.length === 0) {
        console.log('✅ CheckInService: No recent check-ins found');
        return [];
      }

      // Step 2: Get unique user IDs from check-ins
      const userIds = [...new Set(checkIns.map(checkIn => checkIn.user_id))];
      
      // Step 3: Fetch profiles for these users
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name')
        .in('id', userIds);

      if (profilesError) {
        console.error('❌ CheckInService: Error fetching profiles for check-ins:', profilesError);
        throw profilesError;
      }

      // Step 4: Create a map of user_id to profile data for quick lookup
      const profileMap = new Map();
      profiles?.forEach(profile => {
        profileMap.set(profile.id, profile);
      });

      // Step 5: Combine check-ins with profile data
      const checkInsWithProfiles = checkIns.map(checkIn => ({
        ...checkIn,
        profiles: profileMap.get(checkIn.user_id) || { full_name: 'Unknown User' }
      }));

      console.log('✅ CheckInService: Fetched and combined', checkInsWithProfiles.length, 'recent check-ins with profiles');
      return checkInsWithProfiles;
    } catch (error) {
      console.error('❌ CheckInService: Error in fetchRecentClientCheckIns:', error);
      throw error;
    }
  },

  /**
   * Fetch check-in history for the current user
   */
  async fetchCheckInHistory(limit: number = 10, offset: number = 0): Promise<WeeklyCheckIn[]> {
    console.log('📚 CheckInService: Fetching check-in history');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    const { data, error } = await supabase
      .from('weekly_checkins')
      .select('*')
      .eq('user_id', user.id)
      .order('checkin_date', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('❌ CheckInService: Error fetching check-in history:', error);
      throw error;
    }

    console.log('✅ CheckInService: Fetched', data?.length || 0, 'check-ins');
    return data || [];
  },

  /**
   * Fetch a specific check-in by ID
   */
  async fetchCheckInById(checkInId: string): Promise<WeeklyCheckIn | null> {
    console.log('🔍 CheckInService: Fetching check-in by ID:', checkInId);
    
    const { data, error } = await supabase
      .from('weekly_checkins')
      .select('*')
      .eq('id', checkInId)
      .maybeSingle();

    if (error) {
      console.error('❌ CheckInService: Error fetching check-in:', error);
      throw error;
    }

    if (!data) {
      console.log('ℹ️ CheckInService: No check-in found with ID:', checkInId);
      return null;
    }

    console.log('✅ CheckInService: Check-in fetched successfully');
    return data;
  },

  /**
   * Determine if the check-in window is currently open
   * Check-in window is open every Sunday from 6 AM to 11:59 PM in the user's local timezone
   * There's a 3-day grace period after signup where check-ins are not available
   */
  isCheckInWindowOpen(userCreatedAt?: string): { 
    status: 'Open' | 'Closed' | 'Submitted'; 
    date?: string;
    reason?: 'grace_period' | 'time_window_closed' | 'already_submitted';
  } {
    // Check if user is within the 3-day grace period after signup
    if (userCreatedAt) {
      const createdAtDate = new Date(userCreatedAt);
      const now = new Date();
      const gracePeriodEndDate = new Date(createdAtDate);
      gracePeriodEndDate.setDate(createdAtDate.getDate() + 3); // 3-day grace period
      
      if (now < gracePeriodEndDate) {
        console.log('ℹ️ CheckInService: User is within 3-day grace period after signup');
        return { 
          status: 'Closed', 
          reason: 'grace_period'
        };
      }
    }
    
    // Get current date in user's local timezone
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const hour = now.getHours();
    
    // Check if today is Sunday (0) and time is between 6 AM and 11:59 PM
    if (dayOfWeek === 0 && hour >= 6) {
      // Format today's date as YYYY-MM-DD for database query
      const today = now.toISOString().split('T')[0];
      
      // We'll check if the user has already submitted a check-in for today
      // This will be done in the component that uses this function
      // since we need to make an async call to the database
      
      return { 
        status: 'Open', 
        date: today 
      };
    }
    
    return { 
      status: 'Closed',
      reason: 'time_window_closed'
    };
    
    // FOR TESTING ONLY: Uncomment to force check-in window to be open
    // const now = new Date();
    // const today = now.toISOString().split('T')[0];
    // return { status: 'Open', date: today };
  }
};