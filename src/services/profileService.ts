import { supabase } from './supabaseClient';

export interface Profile {
  id: string;
  full_name: string;
  gender_preference: 'Woman' | 'Man' | 'Neutral';
  role: 'client' | 'coach' | 'admin';
  email?: string;
  created_at: string;
  updated_at: string;

  // Intake data fields
  intake_gender?: 'Woman' | 'Man';
  age?: number;
  height_cm?: number;
  weight_kg?: number;
  primary_fitness_goal?: string[]; // Changed to array
  training_experience_level?: 'Beginner' | 'Intermediate' | 'Advanced';
  goal_timeline_months?: number;
  equipment_access_type?: 'Full Gym' | 'Home Gym Basic' | 'Bodyweight Only';
  available_equipment?: {
    barbell_plates?: boolean;
    dumbbells?: boolean;
    resistance_bands?: boolean;
    yoga_mat?: boolean;
    other?: string;
  };
  custom_equipment_notes?: string;
  training_days_per_week?: number;
  preferred_training_days?: string[];
  preferred_session_duration_minutes?: number;
  injuries_limitations?: string;
  training_preferences_notes?: string;
  has_specific_event?: boolean;

  // Metric preferences
  height_unit?: 'cm' | 'ft';
  weight_unit?: 'kg' | 'lbs';

  // Intake tracking
  intake_status?: 'not_started' | 'in_progress' | 'completed';
  intake_completed_at?: string;
  intake_explicitly_submitted?: boolean; // NEW: Track explicit user submission
}

export interface IntakeData {
  intake_gender?: 'Woman' | 'Man';
  age?: number;
  height_cm?: number;
  weight_kg?: number;
  primary_fitness_goal?: string[]; // Changed to array
  training_experience_level?: 'Beginner' | 'Intermediate' | 'Advanced';
  goal_timeline_months?: number;
  equipment_access_type?: 'Full Gym' | 'Home Gym Basic' | 'Bodyweight Only';
  available_equipment?: {
    barbell_plates?: boolean;
    dumbbells?: boolean;
    resistance_bands?: boolean;
    yoga_mat?: boolean;
    other?: string;
  };
  custom_equipment_notes?: string;
  training_days_per_week?: number;
  preferred_training_days?: string[];
  preferred_session_duration_minutes?: number;
  injuries_limitations?: string;
  training_preferences_notes?: string;
  has_specific_event?: boolean;
  height_unit?: 'cm' | 'ft';
  weight_unit?: 'kg' | 'lbs';
  // Add intake status fields
  intake_status?: 'not_started' | 'in_progress' | 'completed';
  intake_completed_at?: string;
  intake_explicitly_submitted?: boolean; // NEW: Track explicit user submission
}

export const profileService = {
  async getProfile(): Promise<Profile> {
    console.log('👤 ProfileService: getProfile called');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error('❌ ProfileService: No authenticated user found');
      throw new Error('No authenticated user');
    }

    console.log('🔍 ProfileService: Fetching profile for user ID:', user.id);

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('❌ ProfileService: Error fetching profile:', error);
      throw error;
    }
    
    console.log('✅ ProfileService: Profile fetched successfully:', data);
    
    return {
      ...data,
      email: user.email,
    };
  },

  async getProfileById(profileId: string): Promise<Profile> {
    console.log('👤 ProfileService: getProfileById called for ID:', profileId);
    
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', profileId)
      .single();

    if (error) {
      console.error('❌ ProfileService: Error fetching profile by ID:', error);
      throw error;
    }
    
    console.log('✅ ProfileService: Profile fetched successfully by ID:', data);
    
    return {
      ...data,
      email: 'Email unavailable', // Cannot fetch email from client-side without admin privileges
    };
  },

  async getProfilesByRole(role: 'client' | 'coach' | 'admin'): Promise<Profile[]> {
    console.log('👥 ProfileService: getProfilesByRole called for role:', role);

    // Fetch all profiles with the specified role, including email from profiles table
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('role', role)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ ProfileService: Error fetching profiles by role:', error);
      throw error;
    }

    console.log('✅ ProfileService: Profiles fetched successfully:', profiles?.length || 0, 'profiles');

    // Return profiles with email from the profiles table
    const profilesWithEmail = (profiles || []).map((profile) => ({
      ...profile,
      email: profile.email || 'Email not available', // Use email from profiles table
    }));

    return profilesWithEmail;
  },

  async updateProfile(updates: Partial<Pick<Profile, 'full_name' | 'gender_preference' | 'role'>>): Promise<Profile> {
    console.log('👤 ProfileService: updateProfile called with updates:', updates);
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error('❌ ProfileService: No authenticated user found');
      throw new Error('No authenticated user');
    }

    console.log('🔄 ProfileService: Updating profile for user ID:', user.id);

    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      console.error('❌ ProfileService: Error updating profile:', error);
      throw error;
    }
    
    console.log('✅ ProfileService: Profile updated successfully:', data);
    
    return {
      ...data,
      email: user.email,
    };
  },

  async updateIntakeData(intakeData: IntakeData): Promise<Profile> {
    console.log('🎯 ProfileService: updateIntakeData called (DRAFT SAVE)');
    console.log('📋 ProfileService: Intake data received:', intakeData);

    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('❌ ProfileService: No authenticated user found');
      throw new Error('No authenticated user');
    }

    console.log('👤 ProfileService: Updating intake data for user ID:', user.id);

    // Prepare updates but ensure explicit submission flag is NOT set (draft save)
    const updates: any = { ...intakeData };

    // Explicitly ensure submission flag is FALSE for draft saves
    updates.intake_explicitly_submitted = false;

    // Also update gender_preference if intake_gender is provided
    if (intakeData.intake_gender) {
      updates.gender_preference = intakeData.intake_gender;
      console.log('🎨 ProfileService: Also updating gender_preference to:', intakeData.intake_gender);
    }

    console.log('🔄 ProfileService: Final updates object (DRAFT - NO SUBMISSION):', updates);
    console.log('🚀 ProfileService: Calling Supabase update for DRAFT SAVE...');

    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      console.error('❌ ProfileService: Supabase update error:', error);
      console.error('Error details:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      throw error;
    }
    
    console.log('✅ ProfileService: Intake data updated successfully');
    console.log('📊 ProfileService: Updated profile data:', data);
    
    return {
      ...data,
      email: user.email,
    };
  },

  async getIntakeData(): Promise<IntakeData | null> {
    console.log('📥 ProfileService: getIntakeData called');
    
    const profile = await this.getProfile();
    
    // Extract only intake-related fields
    const intakeData: IntakeData = {
      intake_gender: profile.intake_gender,
      age: profile.age,
      height_cm: profile.height_cm,
      weight_kg: profile.weight_kg,
      primary_fitness_goal: profile.primary_fitness_goal,
      training_experience_level: profile.training_experience_level,
      goal_timeline_months: profile.goal_timeline_months,
      equipment_access_type: profile.equipment_access_type,
      available_equipment: profile.available_equipment,
      custom_equipment_notes: profile.custom_equipment_notes,
      training_days_per_week: profile.training_days_per_week,
      preferred_training_days: profile.preferred_training_days,
      preferred_session_duration_minutes: profile.preferred_session_duration_minutes,
      injuries_limitations: profile.injuries_limitations,
      training_preferences_notes: profile.training_preferences_notes,
      has_specific_event: profile.has_specific_event,
      height_unit: profile.height_unit,
      weight_unit: profile.weight_unit,
      intake_status: profile.intake_status,
      intake_completed_at: profile.intake_completed_at,
    };

    console.log('📋 ProfileService: Extracted intake data:', intakeData);
    return intakeData;
  },

  // Utility functions for metric conversion
  convertHeightToCm(feet: number, inches: number): number {
    return Math.round((feet * 12 + inches) * 2.54);
  },

  convertWeightToKg(lbs: number): number {
    return Math.round(lbs / 2.20462 * 100) / 100;
  },

  convertCmToFeetInches(cm: number): { feet: number; inches: number } {
    const totalInches = cm / 2.54;
    const feet = Math.floor(totalInches / 12);
    const inches = Math.round(totalInches % 12);
    return { feet, inches };
  },

  convertKgToLbs(kg: number): number {
    return Math.round(kg * 2.20462);
  },

  // Display helpers that respect user's unit preference
  getDisplayHeight(profile: Profile): string {
    if (!profile.height_cm) return '';
    
    if (profile.height_unit === 'ft') {
      const { feet, inches } = this.convertCmToFeetInches(profile.height_cm);
      return `${feet}'${inches}"`;
    }
    
    return `${profile.height_cm} cm`;
  },

  getDisplayWeight(profile: Profile): string {
    if (!profile.weight_kg) return '';
    
    if (profile.weight_unit === 'lbs') {
      const lbs = this.convertKgToLbs(profile.weight_kg);
      return `${lbs} lbs`;
    }
    
    return `${profile.weight_kg} kg`;
  },

  // Display helper for fitness goals
  getDisplayGoals(profile: Profile): string {
    if (!profile.primary_fitness_goal || profile.primary_fitness_goal.length === 0) {
      return 'No goals set';
    }
    
    return profile.primary_fitness_goal.join(', ');
  },

  // Helper to check if user is a coach
  isCoach(profile: Profile): boolean {
    return profile.role === 'coach';
  },

  // Helper to check if user is an admin
  isAdmin(profile: Profile): boolean {
    return profile.role === 'admin';
  },

  // NEW: Submit intake with explicit submission flag
  async submitIntakeData(intakeData: IntakeData): Promise<Profile> {
    console.log('🎯 ProfileService: submitIntakeData called (EXPLICIT SUBMISSION)');
    console.log('📋 ProfileService: Intake data received:', intakeData);

    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('❌ ProfileService: No authenticated user found');
      throw new Error('No authenticated user');
    }

    console.log('👤 ProfileService: Submitting intake data for user ID:', user.id);

    // Set explicit submission flag to TRUE for final submission
    const updates: any = {
      ...intakeData,
      intake_explicitly_submitted: true  // This triggers completion in database trigger
    };

    // Also update gender_preference if intake_gender is provided
    if (intakeData.intake_gender) {
      updates.gender_preference = intakeData.intake_gender;
      console.log('🎨 ProfileService: Also updating gender_preference to:', intakeData.intake_gender);
    }

    console.log('🔄 ProfileService: Final updates object (WITH EXPLICIT SUBMISSION):', updates);
    console.log('🚀 ProfileService: Calling Supabase update for FINAL SUBMISSION...');

    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      console.error('❌ ProfileService: Supabase update error during submission:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        updates: updates
      });
      throw error;
    }

    console.log('✅ ProfileService: Intake submitted successfully');
    console.log('📊 ProfileService: Updated profile after submission:', data);

    return {
      ...data,
      email: user.email,
    };
  },
};