import { supabase } from './supabaseClient';

export interface WorkoutStatistics {
  totalWorkouts: number;
  completionRate: number;
  achievements: number;
  currentStreak: number;
  weeklyAverage: number;
  lastWorkoutDate?: string;
}

export interface CycleProgress {
  currentCycle: number;
  totalCycles: number;
  cycleProgress: number; // 0-100 percentage
  daysInCycle: number;
  daysCompleted: number;
  nextTransitionDate: string;
  weekProgress: number; // Current week within cycle (1-4)
  isActive: boolean;
}

export const workoutStatsService = {
  async getDetailedWorkoutStats(userId?: string): Promise<WorkoutStatistics> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;

    if (!targetUserId) {
      throw new Error('No user ID provided');
    }

    try {
      // Get workout statistics from the last 30 days
      // Note: workout_logs table uses completed_at to determine if workout was completed
      const { data: workoutData, error: workoutError } = await supabase
        .from('workout_logs')
        .select('completed_at, started_at, created_at, duration_seconds')
        .eq('user_id', targetUserId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      if (workoutError) {
        console.error('Error fetching workout data:', workoutError);
        throw workoutError;
      }

      // Calculate achievements based on workout milestones since user_achievements table doesn't exist
      const achievements = this.calculateAchievementsFromWorkouts(workoutData || []);

      // Calculate statistics based on actual schema
      const totalWorkouts = workoutData?.length || 0;
      const completedWorkouts = workoutData?.filter(w => w.completed_at !== null).length || 0;
      const completionRate = totalWorkouts > 0 ? Math.round((completedWorkouts / totalWorkouts) * 100) : 0;

      // Calculate current streak using completed workouts
      const currentStreak = this.calculateCurrentStreak(workoutData || []);

      // Calculate weekly average (last 4 weeks)
      const weeklyAverage = Math.round(totalWorkouts / 4);

      // Get last workout date
      const lastWorkout = workoutData
        ?.filter(w => w.completed_at !== null)
        ?.sort((a, b) => new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime())[0];

      return {
        totalWorkouts,
        completionRate,
        achievements,
        currentStreak,
        weeklyAverage,
        lastWorkoutDate: lastWorkout?.completed_at
      };
    } catch (error) {
      console.error('Error in getDetailedWorkoutStats:', error);
      // Return default values instead of throwing to prevent UI crashes
      return {
        totalWorkouts: 0,
        completionRate: 0,
        achievements: 0,
        currentStreak: 0,
        weeklyAverage: 0,
        lastWorkoutDate: undefined
      };
    }
  },

  calculateCurrentStreak(workoutData: any[]): number {
    const completedWorkouts = workoutData
      .filter(w => w.completed_at !== null) // Use completed_at instead of status
      .map(w => new Date(w.completed_at).toDateString())
      .filter((date, index, arr) => arr.indexOf(date) === index) // Remove duplicates
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let streak = 0;
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();

    // Check if user worked out today or yesterday to start streak
    if (completedWorkouts.includes(today) || completedWorkouts.includes(yesterday)) {
      for (let i = 0; i < completedWorkouts.length; i++) {
        const currentDate = new Date(completedWorkouts[i]);
        const expectedDate = new Date(Date.now() - i * 24 * 60 * 60 * 1000);

        if (currentDate.toDateString() === expectedDate.toDateString()) {
          streak++;
        } else {
          break;
        }
      }
    }

    return streak;
  },

  // Get extended workout statistics for detailed analysis
  async getExtendedWorkoutStats(userId?: string): Promise<{
    totalWorkoutsAllTime: number;
    averageWorkoutDuration: number;
    favoriteWorkoutType: string;
    longestStreak: number;
    thisWeekWorkouts: number;
    thisMonthWorkouts: number;
  }> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;
    
    if (!targetUserId) {
      throw new Error('No user ID provided');
    }

    // Get all workout logs for comprehensive analysis
    const { data: allWorkouts, error: allWorkoutsError } = await supabase
      .from('workout_logs')
      .select('completed_at, created_at, duration_seconds, workout_name_actual')
      .eq('user_id', targetUserId)
      .not('completed_at', 'is', null); // Only get completed workouts

    if (allWorkoutsError) throw allWorkoutsError;

    const workouts = allWorkouts || [];
    const now = new Date();
    const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Calculate extended statistics
    const totalWorkoutsAllTime = workouts.length;
    const averageWorkoutDuration = workouts.length > 0
      ? Math.round(workouts.reduce((sum, w) => sum + (w.duration_seconds || 0), 0) / workouts.length / 60) // Convert to minutes
      : 0;

    // Find favorite workout type (using workout_name_actual)
    const workoutTypeCounts = workouts.reduce((acc, w) => {
      const type = w.workout_name_actual || 'Unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const favoriteWorkoutType = Object.entries(workoutTypeCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'None';

    // Calculate this week and month workouts
    const thisWeekWorkouts = workouts.filter(w => 
      new Date(w.completed_at) >= weekStart
    ).length;

    const thisMonthWorkouts = workouts.filter(w => 
      new Date(w.completed_at) >= monthStart
    ).length;

    // Calculate longest streak (simplified version)
    const longestStreak = this.calculateLongestStreak(workouts);

    return {
      totalWorkoutsAllTime,
      averageWorkoutDuration,
      favoriteWorkoutType,
      longestStreak,
      thisWeekWorkouts,
      thisMonthWorkouts
    };
  },

  calculateLongestStreak(workouts: any[]): number {
    if (workouts.length === 0) return 0;

    const workoutDates = workouts
      .filter(w => w.completed_at !== null) // Filter completed workouts
      .map(w => new Date(w.completed_at).toDateString())
      .filter((date, index, arr) => arr.indexOf(date) === index)
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

    if (workoutDates.length === 0) return 0;
    if (workoutDates.length === 1) return 1;

    let longestStreak = 1;
    let currentStreak = 1;

    for (let i = 1; i < workoutDates.length; i++) {
      const currentDate = new Date(workoutDates[i]);
      const previousDate = new Date(workoutDates[i - 1]);
      const dayDifference = Math.floor((currentDate.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24));

      if (dayDifference === 1) {
        currentStreak++;
        longestStreak = Math.max(longestStreak, currentStreak);
      } else {
        currentStreak = 1;
      }
    }

    return longestStreak;
  },

  // Calculate achievements based on workout milestones
  calculateAchievementsFromWorkouts(workoutData: any[]): number {
    const completedWorkouts = workoutData.filter(w => w.completed_at !== null);
    let achievements = 0;

    // Achievement milestones
    if (completedWorkouts.length >= 1) achievements++; // First workout
    if (completedWorkouts.length >= 5) achievements++; // 5 workouts
    if (completedWorkouts.length >= 10) achievements++; // 10 workouts
    if (completedWorkouts.length >= 25) achievements++; // 25 workouts
    if (completedWorkouts.length >= 50) achievements++; // 50 workouts

    // Streak achievements
    const currentStreak = this.calculateCurrentStreak(workoutData);
    if (currentStreak >= 3) achievements++; // 3-day streak
    if (currentStreak >= 7) achievements++; // 1-week streak
    if (currentStreak >= 14) achievements++; // 2-week streak

    // Duration achievements (if duration data is available)
    const totalDuration = completedWorkouts.reduce((sum, w) => sum + (w.duration_seconds || 0), 0);
    const totalHours = totalDuration / 3600;
    if (totalHours >= 10) achievements++; // 10 hours total
    if (totalHours >= 25) achievements++; // 25 hours total

    return achievements;
  }
};
