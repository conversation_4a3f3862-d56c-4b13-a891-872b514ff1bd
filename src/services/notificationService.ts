import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { router } from 'expo-router';
import { supabase } from './supabaseClient';
import { storage, STORAGE_KEYS } from '../utils/storage';

// Configure notification behavior - this will be the main handler for all notifications
Notifications.setNotificationHandler({
  handleNotification: async (notification) => {
    try {
      const data = notification.request.content.data;
      const type = data?.type as string;

      console.log('🔔 NotificationService: Handling notification:', type);

      // Default behavior - show all notifications
      // TODO: Integrate with coach notification preferences for more sophisticated handling
      return {
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      };
    } catch (error) {
      console.error('❌ NotificationService: Error in notification handler:', error);
      // Fallback to showing notification
      return {
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      };
    }
  },
});

export interface PushNotificationData {
  type: 'message' | 'workout_reminder' | 'coach_feedback' | 'program_update' | 'program_approved' | 'program_rejected' | 'check_in_reminder';
  title: string;
  body: string;
  data?: Record<string, any>;
}

export interface NotificationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: Notifications.PermissionStatus;
}

/**
 * Push notification service for handling all notification-related functionality
 */
export class NotificationService {
  private static instance: NotificationService;
  private pushToken: string | null = null;
  private notificationListeners: Array<() => void> = [];

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔔 NotificationService: Initializing...');
      
      // Setup notification listeners
      this.setupNotificationListeners();
      
      // Request permissions and register for push notifications
      await this.registerForPushNotifications();
      
      console.log('✅ NotificationService: Initialized successfully');
    } catch (error) {
      console.error('❌ NotificationService: Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Request notification permissions and register for push notifications
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      // Check if device supports push notifications
      if (!Device.isDevice) {
        console.log('🔔 NotificationService: Push notifications not supported on simulator');
        return null;
      }

      // Request permissions
      const permissionStatus = await this.requestPermissions();
      if (!permissionStatus.granted) {
        console.log('🔔 NotificationService: Push notification permissions not granted');
        return null;
      }

      // Get push token
      const projectId = process.env.EXPO_PUBLIC_PROJECT_ID;
      if (!projectId) {
        console.error('❌ NotificationService: EXPO_PUBLIC_PROJECT_ID not configured');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId,
      });

      this.pushToken = token.data;
      console.log('🔔 NotificationService: Push token obtained:', this.pushToken);

      // Store token locally
      await storage.setItem(STORAGE_KEYS.PUSH_TOKEN, this.pushToken);

      // Register token with backend
      await this.registerTokenWithBackend(this.pushToken);

      return this.pushToken;
    } catch (error) {
      console.error('❌ NotificationService: Failed to register for push notifications:', error);
      return null;
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<NotificationPermissionStatus> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // For Android, also request additional permissions
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      return {
        granted: finalStatus === 'granted',
        canAskAgain: finalStatus !== 'denied',
        status: finalStatus,
      };
    } catch (error) {
      console.error('❌ NotificationService: Failed to request permissions:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'undetermined',
      };
    }
  }

  /**
   * Register push token with backend
   */
  private async registerTokenWithBackend(token: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('🔔 NotificationService: No authenticated user, skipping token registration');
        return;
      }

      // Update user profile with push token
      const { error } = await supabase
        .from('profiles')
        .update({ 
          push_token: token,
          push_token_updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      console.log('✅ NotificationService: Push token registered with backend');
    } catch (error) {
      console.error('❌ NotificationService: Failed to register token with backend:', error);
      throw error;
    }
  }

  /**
   * Setup notification event listeners
   */
  private setupNotificationListeners(): void {
    // Listener for notifications received while app is foregrounded
    const notificationListener = Notifications.addNotificationReceivedListener(
      this.handleNotificationReceived.bind(this)
    );

    // Listener for when user taps on notification
    const responseListener = Notifications.addNotificationResponseReceivedListener(
      this.handleNotificationResponse.bind(this)
    );

    this.notificationListeners.push(
      () => notificationListener.remove(),
      () => responseListener.remove()
    );
  }

  /**
   * Handle notification received while app is in foreground
   */
  private handleNotificationReceived(notification: Notifications.Notification): void {
    console.log('🔔 NotificationService: Notification received:', notification);
    
    const { type, data } = notification.request.content.data as any;
    
    // Handle different notification types
    switch (type) {
      case 'message':
        this.handleMessageNotification(data);
        break;
      case 'coach_feedback':
      case 'feedback':
      case 'check_in_response':
        this.handleCoachFeedbackNotification(data);
        break;
      case 'workout_reminder':
        this.handleWorkoutReminderNotification(data);
        break;
      case 'program_update':
        this.handleProgramUpdateNotification(data);
        break;
      case 'program_approved':
        this.handleProgramApprovedNotification(data);
        break;
      case 'program_rejected':
        this.handleProgramRejectedNotification(data);
        break;
      case 'check_in_reminder':
        this.handleCheckInReminderNotification(data);
        break;
      default:
        console.log('🔔 NotificationService: Unknown notification type:', type);
    }
  }

  /**
   * Handle notification response (when user taps notification)
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    console.log('🔔 NotificationService: Notification response:', response);
    
    const { type, data } = response.notification.request.content.data as any;
    
    // Navigate to appropriate screen based on notification type
    switch (type) {
      case 'message':
        // Navigate to messages screen
        try {
          router.push('/(tabs)/messages');
          console.log('🔔 NotificationService: Navigated to messages');
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'coach_feedback':
        // Navigate to feedback screen
        try {
          if (data?.feedbackId) {
            router.push(`/(tabs)/checkin/${data.feedbackId}`);
            console.log('🔔 NotificationService: Navigated to feedback:', data.feedbackId);
          } else {
            router.push('/(tabs)/checkin');
            console.log('🔔 NotificationService: Navigated to check-in tab');
          }
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'workout_reminder':
        // Navigate to workouts screen
        try {
          router.push('/(tabs)/workouts');
          console.log('🔔 NotificationService: Navigated to workouts');
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'program_update':
        // Navigate to program screen
        try {
          router.push('/(tabs)/workouts');
          console.log('🔔 NotificationService: Navigated to workouts for program update');
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'program_approved':
        // Navigate to approved program
        try {
          if (data?.program_id) {
            router.push(`/(tabs)/workouts/${data.program_id}`);
            console.log('🔔 NotificationService: Navigated to approved program:', data.program_id);
          } else {
            router.push('/(tabs)/workouts');
            console.log('🔔 NotificationService: Navigated to workouts for approved program');
          }
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'program_rejected':
        // Navigate to workouts screen to request new program
        try {
          router.push('/(tabs)/workouts');
          console.log('🔔 NotificationService: Navigated to workouts for rejected program');
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'check_in_reminder':
        // Navigate to check-in screen
        try {
          router.push('/(tabs)/checkin');
          console.log('🔔 NotificationService: Navigated to check-in tab');
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'check_in_response':
        // Navigate to specific check-in feedback
        try {
          if (data?.checkInId) {
            router.push(`/(tabs)/checkin/${data.checkInId}`);
            console.log('🔔 NotificationService: Navigated to check-in:', data.checkInId);
          } else if (data?.feedbackId) {
            router.push(`/(tabs)/checkin/${data.feedbackId}`);
            console.log('🔔 NotificationService: Navigated to feedback:', data.feedbackId);
          } else {
            router.push('/(tabs)/checkin');
            console.log('🔔 NotificationService: Navigated to check-in tab');
          }
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
      case 'feedback':
        // Navigate to specific feedback
        try {
          if (data?.feedbackId) {
            router.push(`/(tabs)/checkin/${data.feedbackId}`);
            console.log('🔔 NotificationService: Navigated to feedback:', data.feedbackId);
          } else {
            router.push('/(tabs)/checkin');
            console.log('🔔 NotificationService: Navigated to check-in tab');
          }
        } catch (error) {
          console.error('❌ NotificationService: Navigation failed:', error);
        }
        break;
    }
  }

  /**
   * Handle different notification types
   */
  private handleMessageNotification(data: any): void {
    console.log('💬 NotificationService: New message notification:', data);

    // Show in-app banner notification
    try {
      import('@/services/notificationBannerService').then(({ notificationBannerService }) => {
        notificationBannerService.showCustomNotification({
          id: `message-${Date.now()}`,
          type: 'message',
          title: 'New Message',
          body: 'You have received a new message',
          data,
          priority: 'medium',
          autoHide: true,
          duration: 5000,
        });
      });
    } catch (error) {
      console.error('❌ NotificationService: Failed to show banner notification:', error);
    }
  }

  private handleCoachFeedbackNotification(data: any): void {
    console.log('👨‍💼 NotificationService: Coach feedback notification:', data);

    // Show in-app banner notification
    try {
      import('@/services/notificationBannerService').then(({ notificationBannerService }) => {
        notificationBannerService.showFeedbackNotification(
          'New Feedback Available',
          'Your coach has provided feedback on your check-in',
          data
        );
      });
    } catch (error) {
      console.error('❌ NotificationService: Failed to show banner notification:', error);
    }

    // Trigger feedback store refresh to update unread count
    try {
      // Import feedback store dynamically to avoid circular dependencies
      import('@/store/feedbackStore').then(({ useFeedbackStore }) => {
        const { getUnreadFeedbackCount } = useFeedbackStore.getState();
        getUnreadFeedbackCount().catch(console.error);
      });
    } catch (error) {
      console.error('❌ NotificationService: Failed to refresh feedback store:', error);
    }
  }

  private handleWorkoutReminderNotification(data: any): void {
    console.log('🏋️ NotificationService: Workout reminder notification:', data);

    // Show in-app banner notification
    try {
      import('@/services/notificationBannerService').then(({ notificationBannerService }) => {
        notificationBannerService.showWorkoutReminderNotification(
          'Workout Reminder',
          "It's time for your workout! Let's get moving.",
          data
        );
      });
    } catch (error) {
      console.error('❌ NotificationService: Failed to show banner notification:', error);
    }
  }

  private handleProgramUpdateNotification(data: any): void {
    console.log('📋 NotificationService: Program update notification:', data);

    // Show in-app banner notification
    try {
      import('@/services/notificationBannerService').then(({ notificationBannerService }) => {
        notificationBannerService.showProgramUpdateNotification(
          'Program Updated',
          'Your workout program has been updated by your coach',
          data
        );
      });
    } catch (error) {
      console.error('❌ NotificationService: Failed to show banner notification:', error);
    }
  }

  private handleProgramApprovedNotification(data: any): void {
    console.log('🎉 NotificationService: Program approved notification:', data);
    // Refresh workout programs to show the newly approved program
    // This could trigger a store refresh or emit an event
    if (data?.program_id) {
      // Emit event or call store method to refresh programs
      console.log('🔄 NotificationService: Refreshing programs due to approval');
    }
  }

  private handleProgramRejectedNotification(data: any): void {
    console.log('❌ NotificationService: Program rejected notification:', data);
    // Handle program rejection - might need to show user feedback
    // and allow them to request a new program
    if (data?.program_id) {
      console.log('🔄 NotificationService: Program rejected, user may need to request new program');
    }
  }

  private handleCheckInReminderNotification(data: any): void {
    console.log('📝 NotificationService: Check-in reminder notification:', data);
    // Update check-in store or trigger reminder
  }

  /**
   * Schedule a local notification
   */
  async scheduleLocalNotification(
    title: string,
    body: string,
    trigger: Notifications.NotificationTriggerInput,
    data?: Record<string, any>
  ): Promise<string> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
        },
        trigger,
      });

      console.log('🔔 NotificationService: Local notification scheduled:', identifier);
      return identifier;
    } catch (error) {
      console.error('❌ NotificationService: Failed to schedule local notification:', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      console.log('🔔 NotificationService: Notification cancelled:', identifier);
    } catch (error) {
      console.error('❌ NotificationService: Failed to cancel notification:', error);
      throw error;
    }
  }

  /**
   * Get current push token
   */
  getPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Cleanup notification listeners
   */
  cleanup(): void {
    this.notificationListeners.forEach(removeListener => removeListener());
    this.notificationListeners = [];
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();

// Add push token to storage keys
declare module '../utils/storage' {
  interface StorageKeys {
    PUSH_TOKEN: 'push_token';
  }
}
