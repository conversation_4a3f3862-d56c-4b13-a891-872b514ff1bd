import { supabase } from './supabaseClient';
import { notificationService } from './notificationService';
import { workoutLoggingService } from './workoutLoggingService';

export interface ProgramTransitionData {
  currentProgram: {
    id: string;
    name: string;
    cycle: number;
    completionRate: number;
    startDate: string;
    endDate: string;
  };
  nextProgram?: {
    id: string;
    name: string;
    cycle: number;
    estimatedStartDate: string;
    changes: string[];
  };
  stats: {
    workoutsCompleted: number;
    totalWorkouts: number;
    averageRPE: number;
    strengthGains: number;
    consistencyScore: number;
  };
  achievements: string[];
}

export class ProgramTransitionService {
  private static instance: ProgramTransitionService;

  public static getInstance(): ProgramTransitionService {
    if (!ProgramTransitionService.instance) {
      ProgramTransitionService.instance = new ProgramTransitionService();
    }
    return ProgramTransitionService.instance;
  }

  /**
   * Check if a program transition is due
   */
  async checkForProgramTransition(userId: string): Promise<ProgramTransitionData | null> {
    try {
      // Get current active program
      const { data: currentProgram, error: programError } = await supabase
        .from('workout_programs')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active_by_client')
        .single();

      if (programError || !currentProgram) {
        console.log('No active program found for transition check');
        return null;
      }

      // Check if program is due for transition (28 days completed)
      const startDate = new Date(currentProgram.client_start_date || currentProgram.created_at);
      const daysSinceStart = Math.floor((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysSinceStart < 28) {
        console.log(`Program not ready for transition. Days since start: ${daysSinceStart}`);
        return null;
      }

      // Calculate program stats
      const stats = await this.calculateProgramStats(userId, currentProgram.id);
      
      // Get achievements
      const achievements = await this.calculateAchievements(userId, currentProgram.id, stats);

      // Check for next program
      const nextProgram = await this.getNextProgram(userId, currentProgram.cycle_number || 1);

      const transitionData: ProgramTransitionData = {
        currentProgram: {
          id: currentProgram.id,
          name: currentProgram.name,
          cycle: currentProgram.cycle_number || 1,
          completionRate: stats.completionRate,
          startDate: currentProgram.client_start_date || currentProgram.created_at,
          endDate: new Date().toISOString(),
        },
        nextProgram,
        stats: {
          workoutsCompleted: stats.workoutsCompleted,
          totalWorkouts: stats.totalWorkouts,
          averageRPE: stats.averageRPE,
          strengthGains: stats.strengthGains,
          consistencyScore: stats.consistencyScore,
        },
        achievements,
      };

      return transitionData;
    } catch (error) {
      console.error('Error checking for program transition:', error);
      return null;
    }
  }

  /**
   * Calculate program statistics
   */
  private async calculateProgramStats(userId: string, programId: string) {
    try {
      // Get all workouts for the program
      const { data: workouts, error: workoutsError } = await supabase
        .from('workouts')
        .select('id')
        .eq('program_id', programId);

      if (workoutsError) throw workoutsError;

      const totalWorkouts = workouts?.length || 0;

      // Get completed workout logs
      const { data: workoutLogs, error: logsError } = await supabase
        .from('workout_logs')
        .select('workout_id, rpe, completed_at')
        .eq('user_id', userId)
        .in('workout_id', workouts?.map(w => w.id) || []);

      if (logsError) throw logsError;

      const uniqueCompletedWorkouts = new Set(workoutLogs?.map(log => log.workout_id) || []).size;
      const completionRate = totalWorkouts > 0 ? Math.round((uniqueCompletedWorkouts / totalWorkouts) * 100) : 0;

      // Calculate average RPE
      const rpeValues = workoutLogs?.filter(log => log.rpe).map(log => log.rpe) || [];
      const averageRPE = rpeValues.length > 0 
        ? rpeValues.reduce((sum, rpe) => sum + rpe, 0) / rpeValues.length 
        : 0;

      // Calculate consistency score (workouts per week)
      const workoutDates = workoutLogs?.map(log => new Date(log.completed_at)) || [];
      const consistencyScore = this.calculateConsistencyScore(workoutDates);

      // Mock strength gains calculation (in real app, this would compare weights over time)
      const strengthGains = Math.min(Math.max(Math.round(completionRate * 0.3), 0), 25);

      return {
        workoutsCompleted: uniqueCompletedWorkouts,
        totalWorkouts,
        completionRate,
        averageRPE,
        strengthGains,
        consistencyScore,
      };
    } catch (error) {
      console.error('Error calculating program stats:', error);
      return {
        workoutsCompleted: 0,
        totalWorkouts: 0,
        completionRate: 0,
        averageRPE: 0,
        strengthGains: 0,
        consistencyScore: 0,
      };
    }
  }

  /**
   * Calculate consistency score based on workout frequency
   */
  private calculateConsistencyScore(workoutDates: Date[]): number {
    if (workoutDates.length === 0) return 0;

    // Group workouts by week
    const weeklyWorkouts: { [week: string]: number } = {};
    
    workoutDates.forEach(date => {
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekKey = weekStart.toISOString().split('T')[0];
      
      weeklyWorkouts[weekKey] = (weeklyWorkouts[weekKey] || 0) + 1;
    });

    const weeks = Object.keys(weeklyWorkouts);
    if (weeks.length === 0) return 0;

    // Calculate average workouts per week
    const totalWorkouts = Object.values(weeklyWorkouts).reduce((sum, count) => sum + count, 0);
    const averagePerWeek = totalWorkouts / weeks.length;

    // Target is 3-4 workouts per week, so calculate percentage
    const targetWorkoutsPerWeek = 3.5;
    const consistencyScore = Math.min(Math.round((averagePerWeek / targetWorkoutsPerWeek) * 100), 100);

    return consistencyScore;
  }

  /**
   * Calculate achievements based on program performance
   */
  private async calculateAchievements(userId: string, programId: string, stats: any): Promise<string[]> {
    const achievements: string[] = [];

    // Completion-based achievements
    if (stats.completionRate >= 100) {
      achievements.push('Perfect Completion - Finished every workout!');
    } else if (stats.completionRate >= 90) {
      achievements.push('Consistency Champion - 90%+ completion rate');
    } else if (stats.completionRate >= 75) {
      achievements.push('Dedicated Trainer - 75%+ completion rate');
    }

    // RPE-based achievements
    if (stats.averageRPE >= 7) {
      achievements.push('High Intensity Warrior - Averaged RPE 7+');
    } else if (stats.averageRPE >= 5) {
      achievements.push('Balanced Effort - Maintained good intensity');
    }

    // Consistency achievements
    if (stats.consistencyScore >= 90) {
      achievements.push('Routine Master - Exceptional consistency');
    } else if (stats.consistencyScore >= 75) {
      achievements.push('Steady Progress - Great workout frequency');
    }

    // Strength gains achievements
    if (stats.strengthGains >= 20) {
      achievements.push('Strength Surge - Significant gains this cycle');
    } else if (stats.strengthGains >= 10) {
      achievements.push('Progressive Overload - Steady strength improvements');
    }

    // Special achievements
    if (stats.workoutsCompleted >= 20) {
      achievements.push('Workout Warrior - 20+ workouts completed');
    }

    if (achievements.length === 0) {
      achievements.push('Journey Started - Every step counts!');
    }

    return achievements;
  }

  /**
   * Get next program if available
   */
  private async getNextProgram(userId: string, currentCycle: number) {
    try {
      // Check if there's a coach-approved program ready
      const { data: nextProgram, error } = await supabase
        .from('workout_programs')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'coach_approved')
        .eq('cycle_number', currentCycle + 1)
        .single();

      if (error || !nextProgram) {
        return null;
      }

      // Mock program changes (in real app, this would be calculated based on program differences)
      const changes = [
        'Increased weight recommendations',
        'New exercise variations',
        'Adjusted rep ranges for progression',
        'Enhanced recovery protocols',
      ];

      return {
        id: nextProgram.id,
        name: nextProgram.name,
        cycle: nextProgram.cycle_number || currentCycle + 1,
        estimatedStartDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        changes,
      };
    } catch (error) {
      console.error('Error getting next program:', error);
      return null;
    }
  }

  /**
   * Complete program transition
   */
  async completeTransition(userId: string, currentProgramId: string, nextProgramId?: string): Promise<void> {
    try {
      // Mark current program as completed
      const { error: updateError } = await supabase
        .from('workout_programs')
        .update({ 
          status: 'completed_by_client',
          client_end_date: new Date().toISOString(),
        })
        .eq('id', currentProgramId)
        .eq('user_id', userId);

      if (updateError) throw updateError;

      // If next program exists, activate it
      if (nextProgramId) {
        const { error: activateError } = await supabase
          .from('workout_programs')
          .update({ 
            status: 'active_by_client',
            client_start_date: new Date().toISOString(),
          })
          .eq('id', nextProgramId)
          .eq('user_id', userId);

        if (activateError) throw activateError;

        // Send notification about new program
        await this.sendTransitionNotification(userId, 'program_started');
      } else {
        // Send notification that new program is being prepared
        await this.sendTransitionNotification(userId, 'program_pending');
      }

      console.log('✅ Program transition completed successfully');
    } catch (error) {
      console.error('❌ Error completing program transition:', error);
      throw error;
    }
  }

  /**
   * Send transition notification
   */
  private async sendTransitionNotification(userId: string, type: 'program_started' | 'program_pending'): Promise<void> {
    try {
      const { data, error } = await supabase.rpc('send_push_notification', {
        p_user_id: userId,
        p_notification_type: 'program_update',
        p_title: type === 'program_started' 
          ? 'New Program Started!' 
          : 'New Program Coming Soon',
        p_body: type === 'program_started'
          ? 'Your new workout program is ready. Time to level up!'
          : 'Your coach is preparing your next program based on your progress.',
        p_data: { type, timestamp: new Date().toISOString() },
      });

      if (error) throw error;
      
      console.log('✅ Transition notification sent');
    } catch (error) {
      console.warn('Failed to send transition notification:', error);
    }
  }

  /**
   * Schedule automatic transition check
   */
  async scheduleTransitionCheck(userId: string): Promise<void> {
    try {
      // Schedule a notification to check for transitions in 28 days
      const checkDate = new Date();
      checkDate.setDate(checkDate.getDate() + 28);

      await notificationService.scheduleLocalNotification(
        'Program Review Time',
        'Time to review your progress and transition to your next program!',
        { date: checkDate },
        {
          type: 'program_transition_check',
          userId,
        }
      );

      console.log('✅ Transition check scheduled');
    } catch (error) {
      console.warn('Failed to schedule transition check:', error);
    }
  }
}

// Export singleton instance
export const programTransitionService = ProgramTransitionService.getInstance();
