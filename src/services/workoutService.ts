import { supabase } from './supabaseClient';

export interface WorkoutProgram {
  id: string;
  user_id: string;
  name: string;
  description: string;
  duration_weeks: number;
  status: 'ai_generated_pending_review' | 'coach_edited' | 'coach_approved' | 'auto_approved' | 'active_by_client' | 'completed_by_client' | 'archived';
  ai_prompt_data?: any;
  generated_by_ai_at?: string;
  coach_id?: string;
  coach_reviewed_at?: string;
  coach_notes_for_client?: string;
  client_start_date?: string;
  created_at: string;
  updated_at: string;
}

export interface ProgramWeek {
  id: string;
  workout_program_id: string;
  week_number: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Workout {
  id: string;
  program_week_id: string;
  day_identifier: string;
  title: string;
  description?: string;
  estimated_duration_minutes?: number;
  order_in_week: number;
  created_at: string;
  updated_at: string;
}

export interface WorkoutExercise {
  id: string;
  workout_id: string;
  exercise_id: string;
  order_in_workout: number;
  prescribed_sets: number;
  prescribed_reps_min?: number;
  prescribed_reps_max?: number;
  prescribed_duration_seconds?: number;
  prescribed_rir?: number;
  prescribed_rpe?: number;
  prescribed_tempo?: string;
  rest_period_seconds_after_set: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  exercise?: Exercise;
}

export interface Exercise {
  id: string;
  name: string;
  description?: string;
  video_url?: string;
  target_muscles_primary: string[];
  target_muscles_secondary: string[];
  equipment_required: string[];
  difficulty_level: 'Beginner' | 'Intermediate' | 'Advanced';
  created_at: string;
  updated_at: string;
}

// Helper function to check program access permissions
const checkProgramAccess = async (program: WorkoutProgram, userId: string, userRole: string): Promise<void> => {
  const isCoachOrAdmin = userRole === 'coach' || userRole === 'admin';
  const isOwner = program.user_id === userId;

  // Coaches and admins have full access
  if (isCoachOrAdmin) {
    return;
  }

  // Regular users can only access their own programs
  if (!isOwner) {
    throw new Error('Unauthorized access to program');
  }

  // Regular users can only access approved programs
  const approvedStatuses = ['coach_approved', 'auto_approved', 'active_by_client', 'completed_by_client'];
  if (!approvedStatuses.includes(program.status)) {
    throw new Error('Program not yet approved for access');
  }
};

export const workoutService = {
  // Get user's workout programs (filtered by approval status for regular users)
  async getWorkoutPrograms(): Promise<WorkoutProgram[]> {
    // Get current user to determine role
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError) throw profileError;

    // Coaches and admins can see all programs, regular users only see approved programs
    const isCoachOrAdmin = profile?.role === 'coach' || profile?.role === 'admin';

    let query = supabase
      .from('workout_programs')
      .select('*')
      .order('created_at', { ascending: false });

    // Filter by approval status for regular users
    if (!isCoachOrAdmin) {
      query = query.in('status', [
        'coach_approved',
        'auto_approved',
        'active_by_client',
        'completed_by_client'
      ]);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  },

  // Get workout programs for a specific user (for coaches)
  async getWorkoutProgramsForUser(userId: string): Promise<WorkoutProgram[]> {
    const { data, error } = await supabase
      .from('workout_programs')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Get a specific workout program with all details
  async getWorkoutProgramDetails(programId: string): Promise<{
    program: WorkoutProgram;
    weeks: (ProgramWeek & {
      workouts: (Workout & {
        exercises: WorkoutExercise[];
      })[];
    })[];
  }> {
    // Get current user to determine role and access rights
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError) throw profileError;

    // Get the program
    const { data: program, error: programError } = await supabase
      .from('workout_programs')
      .select('*')
      .eq('id', programId)
      .single();

    if (programError) throw programError;

    // Check access permissions using helper function
    await checkProgramAccess(program, user.id, profile?.role || 'client');

    // Get weeks with workouts and exercises
    const { data: weeks, error: weeksError } = await supabase
      .from('program_weeks')
      .select(`
        *,
        workouts (
          *,
          workout_exercises (
            *,
            exercises (*)
          )
        )
      `)
      .eq('workout_program_id', programId)
      .order('week_number');

    if (weeksError) throw weeksError;

    // Transform the data to match our interface
    const transformedWeeks = weeks?.map(week => ({
      ...week,
      workouts: week.workouts
        .sort((a, b) => a.order_in_week - b.order_in_week)
        .map(workout => ({
          ...workout,
          exercises: workout.workout_exercises
            .sort((a, b) => a.order_in_workout - b.order_in_workout)
            .map(we => ({
              ...we,
              exercise: we.exercises,
            })),
        })),
    })) || [];

    return {
      program,
      weeks: transformedWeeks,
    };
  },

  // Update a workout program (for coaches)
  async updateWorkoutProgram(programId: string, updates: Partial<WorkoutProgram>): Promise<WorkoutProgram> {
    const { data, error } = await supabase
      .from('workout_programs')
      .update(updates)
      .eq('id', programId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Start a workout program (mark as active)
  async startWorkoutProgram(programId: string): Promise<WorkoutProgram> {
    const { data, error } = await supabase
      .from('workout_programs')
      .update({
        status: 'active_by_client',
        client_start_date: new Date().toISOString().split('T')[0], // Today's date
      })
      .eq('id', programId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get all exercises
  async getExercises(): Promise<Exercise[]> {
    const { data, error } = await supabase
      .from('exercises')
      .select('*')
      .order('name');

    if (error) throw error;
    return data || [];
  },

  // Trigger AI program generation manually (for testing)
  async triggerProgramGeneration(): Promise<{ success: boolean; programId?: string; error?: string }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('No authenticated user');
      }

      const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/generate-workout-program`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate program');
      }

      return result;
    } catch (error) {
      console.error('Error triggering program generation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  },

  // Regenerate a workout program with coach feedback
  async regenerateWorkoutProgram(
    programId: string, 
    coachNotes: string
  ): Promise<{ success: boolean; newProgramId?: string; error?: string }> {
    try {
      console.log('🔄 WorkoutService: Starting program regeneration for program:', programId);
      
      // First, get the current program details to extract user info
      const { data: currentProgram, error: programError } = await supabase
        .from('workout_programs')
        .select('user_id, ai_prompt_data')
        .eq('id', programId)
        .single();

      if (programError) {
        console.error('❌ WorkoutService: Error fetching current program:', programError);
        throw new Error('Failed to fetch current program details');
      }

      console.log('📋 WorkoutService: Current program data:', currentProgram);

      // Archive the current program
      const { error: archiveError } = await supabase
        .from('workout_programs')
        .update({ 
          status: 'archived',
          coach_reviewed_at: new Date().toISOString(),
          coach_notes_for_client: `Program regenerated. Coach feedback: ${coachNotes}`
        })
        .eq('id', programId);

      if (archiveError) {
        console.error('❌ WorkoutService: Error archiving current program:', archiveError);
        throw new Error('Failed to archive current program');
      }

      console.log('✅ WorkoutService: Current program archived successfully');

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 120000); // 2 minutes timeout

      try {
        // Call the regeneration edge function with timeout
        const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/generate-workout-program`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            userId: currentProgram.user_id,
            regenerationNotes: coachNotes,
            previousProgramId: programId,
            isRegeneration: true
          }),
          signal: controller.signal, // Add abort signal for timeout
        });

        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        const result = await response.json();
        
        if (!response.ok) {
          console.error('❌ WorkoutService: Edge function error:', result);
          throw new Error(result.error || 'Failed to regenerate program');
        }

        console.log('✅ WorkoutService: Program regeneration completed successfully');
        console.log('📊 WorkoutService: New program ID:', result.programId);

        return {
          success: true,
          newProgramId: result.programId,
        };
      } catch (fetchError) {
        // Clear timeout in case of error
        clearTimeout(timeoutId);
        
        // Check if the error was due to timeout
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          console.error('❌ WorkoutService: Request timed out after 2 minutes');
          throw new Error('Program regeneration timed out. Please try again later.');
        }
        
        // Re-throw other errors
        throw fetchError;
      }
    } catch (error) {
      console.error('❌ WorkoutService: Error in regenerateWorkoutProgram:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  },

  // Check if program generation has failed for a user
  async checkProgramGenerationStatus(userId?: string): Promise<{
    status: 'success' | 'pending' | 'failed';
    message: string;
    canRetry: boolean;
  }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        throw new Error('No user ID provided');
      }

      // Get user profile to check intake status
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('intake_status, intake_completed_at')
        .eq('id', targetUserId)
        .single();

      if (profileError) throw profileError;

      // If intake not completed, no failure
      if (profile.intake_status !== 'completed') {
        return {
          status: 'pending',
          message: 'Intake not completed',
          canRetry: false
        };
      }

      // Check for existing programs
      const { data: programs, error: programsError } = await supabase
        .from('workout_programs')
        .select('id, status, created_at')
        .eq('user_id', targetUserId)
        .order('created_at', { ascending: false });

      if (programsError) throw programsError;

      // If has approved programs, generation was successful
      const approvedPrograms = programs?.filter(p =>
        p.status === 'coach_approved' || p.status === 'auto_approved' || p.status === 'active_by_client'
      );

      if (approvedPrograms && approvedPrograms.length > 0) {
        return {
          status: 'success',
          message: 'Program generation successful',
          canRetry: false
        };
      }

      // If has pending programs, still in progress
      const pendingPrograms = programs?.filter(p => p.status === 'ai_generated_pending_review');
      if (pendingPrograms && pendingPrograms.length > 0) {
        return {
          status: 'pending',
          message: 'Program generation in progress',
          canRetry: false
        };
      }

      // Check if enough time has passed since intake completion to consider it failed
      const intakeCompletedAt = new Date(profile.intake_completed_at);
      const now = new Date();
      const timeDiff = now.getTime() - intakeCompletedAt.getTime();
      const hoursSinceCompletion = timeDiff / (1000 * 60 * 60);

      // Consider failed if more than 2 hours have passed without any program
      if (hoursSinceCompletion > 2 && (!programs || programs.length === 0)) {
        return {
          status: 'failed',
          message: 'Program generation appears to have failed - no program created after intake completion',
          canRetry: true
        };
      }

      // Still within reasonable time window
      return {
        status: 'pending',
        message: 'Program generation in progress',
        canRetry: false
      };

    } catch (error) {
      console.error('Error checking program generation status:', error);
      return {
        status: 'failed',
        message: `Error checking status: ${error instanceof Error ? error.message : String(error)}`,
        canRetry: true
      };
    }
  },

  // Trigger program generation for a user (resubmit intake)
  async resubmitIntakeForProgramGeneration(userId?: string): Promise<{
    success: boolean;
    message: string;
    error?: string;
  }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        throw new Error('No user ID provided');
      }

      // First check if user has completed intake
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('intake_status')
        .eq('id', targetUserId)
        .single();

      if (profileError) throw profileError;

      if (profile.intake_status !== 'completed') {
        return {
          success: false,
          message: 'Cannot resubmit - intake not completed',
          error: 'Intake must be completed before program generation can be triggered'
        };
      }

      // Call the generate-workout-program Edge Function
      const { data, error } = await supabase.functions.invoke('generate-workout-program', {
        body: {
          userId: targetUserId,
          generationType: 'manual', // Indicate this is a manual retry
          source: 'resubmit_intake'
        }
      });

      if (error) {
        console.error('Error calling generate-workout-program function:', error);
        return {
          success: false,
          message: 'Failed to trigger program generation',
          error: error.message
        };
      }

      console.log('Program generation triggered successfully:', data);

      return {
        success: true,
        message: 'Program generation has been triggered successfully. Please check back in a few minutes.',
      };

    } catch (error) {
      console.error('Error resubmitting intake for program generation:', error);
      return {
        success: false,
        message: 'Failed to resubmit intake',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  },
};