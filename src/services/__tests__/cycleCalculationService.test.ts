import { cycleCalculationService } from '../cycleCalculationService';
import { WorkoutProgram } from '../workoutService';

describe('cycleCalculationService', () => {
  const mockProgram: WorkoutProgram = {
    id: 'test-program-id',
    user_id: 'test-user-id',
    name: 'Test Program',
    description: 'Test Description',
    duration_weeks: 12,
    status: 'active_by_client',
    created_at: '2025-01-01T00:00:00Z',
    client_start_date: '2025-01-01',
    cycle_number: 1
  };

  describe('calculateRealCycleInfo', () => {
    beforeEach(() => {
      // Mock current date to be consistent
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2025-01-15T12:00:00Z')); // 14 days after start
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should calculate cycle info correctly for active program', () => {
      const result = cycleCalculationService.calculateRealCycleInfo(mockProgram);

      expect(result).toEqual({
        currentCycle: 1,
        totalCycles: 3, // 12 weeks / 4 weeks per cycle
        cycleProgress: 50, // 14 days out of 28 days = 50%
        daysInCycle: 28,
        daysCompleted: 14,
        nextTransitionDate: '2025-01-29', // 28 days after start
        weekProgress: 3, // 14 days / 7 = 2 weeks + 1
        isActive: true
      });
    });

    it('should handle program with no client_start_date', () => {
      const programWithoutStartDate = {
        ...mockProgram,
        client_start_date: undefined
      };

      const result = cycleCalculationService.calculateRealCycleInfo(programWithoutStartDate);

      expect(result.currentCycle).toBe(1);
      expect(result.totalCycles).toBe(3);
      expect(result.isActive).toBe(true);
    });

    it('should calculate second cycle correctly', () => {
      // Set current time to 35 days after start (into second cycle)
      jest.setSystemTime(new Date('2025-02-05T12:00:00Z'));

      const result = cycleCalculationService.calculateRealCycleInfo(mockProgram);

      expect(result.currentCycle).toBe(2);
      expect(result.daysCompleted).toBe(7); // 35 % 28 = 7
      expect(result.cycleProgress).toBe(25); // 7/28 = 25%
      expect(result.weekProgress).toBe(2); // 7/7 + 1 = 2
    });

    it('should handle inactive program status', () => {
      const inactiveProgram = {
        ...mockProgram,
        status: 'completed_by_client' as const
      };

      const result = cycleCalculationService.calculateRealCycleInfo(inactiveProgram);

      expect(result.isActive).toBe(false);
    });

    it('should not exceed total cycles', () => {
      // Set current time to way in the future (beyond program duration)
      jest.setSystemTime(new Date('2025-06-01T12:00:00Z'));

      const result = cycleCalculationService.calculateRealCycleInfo(mockProgram);

      expect(result.currentCycle).toBe(3); // Should not exceed totalCycles
      expect(result.weekProgress).toBe(4); // Should not exceed 4
    });

    it('should handle negative days (future start date)', () => {
      const futureProgram = {
        ...mockProgram,
        client_start_date: '2025-02-01' // Future date
      };

      jest.setSystemTime(new Date('2025-01-15T12:00:00Z'));

      const result = cycleCalculationService.calculateRealCycleInfo(futureProgram);

      expect(result.currentCycle).toBe(1);
      expect(result.daysCompleted).toBe(0);
      expect(result.cycleProgress).toBe(0);
    });
  });

  describe('formatTransitionDate', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2025-01-15T12:00:00Z'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should format today correctly', () => {
      const result = cycleCalculationService.formatTransitionDate('2025-01-15');
      expect(result).toBe('Today');
    });

    it('should format tomorrow correctly', () => {
      const result = cycleCalculationService.formatTransitionDate('2025-01-16');
      expect(result).toBe('Tomorrow');
    });

    it('should format future dates correctly', () => {
      const result = cycleCalculationService.formatTransitionDate('2025-01-20');
      expect(result).toBe('In 5 days');
    });

    it('should format past dates correctly', () => {
      const result = cycleCalculationService.formatTransitionDate('2025-01-10');
      expect(result).toBe('1/10/2025'); // Fallback to locale date string
    });
  });

  describe('calculateDetailedCycleMetrics', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2025-01-15T12:00:00Z')); // 14 days after start
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should return detailed cycle metrics', () => {
      const result = cycleCalculationService.calculateDetailedCycleMetrics(mockProgram);

      expect(result.cycleInfo).toBeDefined();
      expect(result.weeklyBreakdown).toHaveLength(4);
      expect(result.milestones).toHaveLength(4);

      // Check weekly breakdown structure
      expect(result.weeklyBreakdown[0]).toEqual({
        weekNumber: 1,
        startDate: '2025-01-01',
        endDate: '2025-01-07',
        isCurrentWeek: false,
        isCompleted: true
      });

      // Check milestones structure
      expect(result.milestones[0]).toEqual({
        name: '25% Complete',
        date: expect.any(String),
        isReached: expect.any(Boolean),
        daysUntil: expect.any(Number)
      });
    });
  });

  describe('calculateProgramCompletion', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2025-01-15T12:00:00Z')); // 14 days after start
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should calculate program completion correctly', () => {
      const result = cycleCalculationService.calculateProgramCompletion(mockProgram);

      expect(result).toEqual({
        overallProgress: 17, // 2 weeks out of 12 weeks = 16.67% rounded to 17%
        weeksCompleted: 2,
        totalWeeks: 12,
        daysRemaining: 70, // 84 total days - 14 completed days
        estimatedCompletionDate: '2025-03-26' // 84 days after start
      });
    });

    it('should handle completed programs', () => {
      // Set time to after program completion
      jest.setSystemTime(new Date('2025-04-01T12:00:00Z'));

      const result = cycleCalculationService.calculateProgramCompletion(mockProgram);

      expect(result.overallProgress).toBe(100);
      expect(result.weeksCompleted).toBe(12);
      expect(result.daysRemaining).toBe(0);
    });
  });

  describe('shouldTransitionCycle', () => {
    it('should indicate transition needed when cycle is complete', () => {
      // Set time to exactly 28 days after start (cycle complete)
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2025-01-29T12:00:00Z'));

      const result = cycleCalculationService.shouldTransitionCycle(mockProgram);

      expect(result.shouldTransition).toBe(true);
      expect(result.reason).toContain('Cycle 1 is complete');
      expect(result.nextCycleNumber).toBe(2);

      jest.useRealTimers();
    });

    it('should not indicate transition when cycle is incomplete', () => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2025-01-15T12:00:00Z')); // 14 days after start

      const result = cycleCalculationService.shouldTransitionCycle(mockProgram);

      expect(result.shouldTransition).toBe(false);
      expect(result.reason).toContain('50% complete');
      expect(result.nextCycleNumber).toBe(2);

      jest.useRealTimers();
    });
  });
});
