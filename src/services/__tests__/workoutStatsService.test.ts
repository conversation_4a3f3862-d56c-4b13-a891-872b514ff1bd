import { workoutStatsService } from '../workoutStatsService';
import { supabase } from '../supabaseClient';

// Mock Supabase
jest.mock('../supabaseClient', () => ({
  supabase: {
    auth: {
      getUser: jest.fn()
    },
    from: jest.fn()
  }
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('workoutStatsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock for auth
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'test-user-id' } },
      error: null
    });
  });

  describe('getDetailedWorkoutStats', () => {
    it('should return workout statistics with real data', async () => {
      const mockWorkoutData = [
        { status: 'completed', completed_at: '2025-01-15T10:00:00Z', created_at: '2025-01-15T09:00:00Z' },
        { status: 'completed', completed_at: '2025-01-14T10:00:00Z', created_at: '2025-01-14T09:00:00Z' },
        { status: 'skipped', completed_at: null, created_at: '2025-01-13T09:00:00Z' },
        { status: 'completed', completed_at: '2025-01-12T10:00:00Z', created_at: '2025-01-12T09:00:00Z' }
      ];

      const mockAchievementData = [
        { id: 'achievement-1' },
        { id: 'achievement-2' },
        { id: 'achievement-3' }
      ];

      // Mock workout logs query
      const mockWorkoutQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockResolvedValue({ data: mockWorkoutData, error: null })
      };

      // Mock achievements query
      const mockAchievementQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ data: mockAchievementData, error: null })
      };

      mockSupabase.from
        .mockReturnValueOnce(mockWorkoutQuery as any)
        .mockReturnValueOnce(mockAchievementQuery as any);

      const result = await workoutStatsService.getDetailedWorkoutStats();

      expect(result).toEqual({
        totalWorkouts: 4,
        completionRate: 75, // 3 completed out of 4 total
        achievements: 3,
        currentStreak: expect.any(Number),
        weeklyAverage: 1, // 4 workouts / 4 weeks
        lastWorkoutDate: '2025-01-15T10:00:00Z'
      });
    });

    it('should handle empty workout data', async () => {
      const mockWorkoutQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockResolvedValue({ data: [], error: null })
      };

      const mockAchievementQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ data: [], error: null })
      };

      mockSupabase.from
        .mockReturnValueOnce(mockWorkoutQuery as any)
        .mockReturnValueOnce(mockAchievementQuery as any);

      const result = await workoutStatsService.getDetailedWorkoutStats();

      expect(result).toEqual({
        totalWorkouts: 0,
        completionRate: 0,
        achievements: 0,
        currentStreak: 0,
        weeklyAverage: 0,
        lastWorkoutDate: undefined
      });
    });

    it('should handle database errors gracefully', async () => {
      const mockWorkoutQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockResolvedValue({ data: null, error: new Error('Database error') })
      };

      mockSupabase.from.mockReturnValueOnce(mockWorkoutQuery as any);

      await expect(workoutStatsService.getDetailedWorkoutStats()).rejects.toThrow('Database error');
    });

    it('should throw error when no user ID is provided', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      });

      await expect(workoutStatsService.getDetailedWorkoutStats()).rejects.toThrow('No user ID provided');
    });
  });

  describe('calculateCurrentStreak', () => {
    it('should calculate streak correctly for consecutive days', () => {
      const today = new Date().toISOString();
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString();

      const workoutData = [
        { status: 'completed', completed_at: today },
        { status: 'completed', completed_at: yesterday },
        { status: 'completed', completed_at: twoDaysAgo }
      ];

      const streak = workoutStatsService.calculateCurrentStreak(workoutData);
      expect(streak).toBeGreaterThan(0);
    });

    it('should return 0 for no recent workouts', () => {
      const oldWorkout = new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString();
      
      const workoutData = [
        { status: 'completed', completed_at: oldWorkout }
      ];

      const streak = workoutStatsService.calculateCurrentStreak(workoutData);
      expect(streak).toBe(0);
    });

    it('should handle empty workout data', () => {
      const streak = workoutStatsService.calculateCurrentStreak([]);
      expect(streak).toBe(0);
    });
  });

  describe('getExtendedWorkoutStats', () => {
    it('should return extended statistics', async () => {
      const mockWorkoutData = [
        { 
          status: 'completed', 
          completed_at: '2025-01-15T10:00:00Z', 
          duration_minutes: 45,
          workout_type: 'strength'
        },
        { 
          status: 'completed', 
          completed_at: '2025-01-14T10:00:00Z', 
          duration_minutes: 30,
          workout_type: 'cardio'
        },
        { 
          status: 'completed', 
          completed_at: '2025-01-13T10:00:00Z', 
          duration_minutes: 60,
          workout_type: 'strength'
        }
      ];

      const mockWorkoutQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnValueOnce({
          eq: jest.fn().mockResolvedValue({ data: mockWorkoutData, error: null })
        })
      };

      mockSupabase.from.mockReturnValueOnce(mockWorkoutQuery as any);

      const result = await workoutStatsService.getExtendedWorkoutStats();

      expect(result).toEqual({
        totalWorkoutsAllTime: 3,
        averageWorkoutDuration: 45, // (45 + 30 + 60) / 3
        favoriteWorkoutType: 'strength', // appears twice
        longestStreak: expect.any(Number),
        thisWeekWorkouts: expect.any(Number),
        thisMonthWorkouts: expect.any(Number)
      });
    });
  });

  describe('calculateLongestStreak', () => {
    it('should calculate longest streak correctly', () => {
      const workouts = [
        { completed_at: '2025-01-10T10:00:00Z' },
        { completed_at: '2025-01-11T10:00:00Z' },
        { completed_at: '2025-01-12T10:00:00Z' },
        { completed_at: '2025-01-14T10:00:00Z' }, // gap
        { completed_at: '2025-01-15T10:00:00Z' }
      ];

      const longestStreak = workoutStatsService.calculateLongestStreak(workouts);
      expect(longestStreak).toBe(3); // First three consecutive days
    });

    it('should return 0 for empty workout array', () => {
      const longestStreak = workoutStatsService.calculateLongestStreak([]);
      expect(longestStreak).toBe(0);
    });

    it('should return 1 for single workout', () => {
      const workouts = [{ completed_at: '2025-01-15T10:00:00Z' }];
      const longestStreak = workoutStatsService.calculateLongestStreak(workouts);
      expect(longestStreak).toBe(1);
    });
  });
});
