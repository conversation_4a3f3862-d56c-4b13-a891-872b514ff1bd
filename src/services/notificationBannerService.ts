import { create } from 'zustand';

export interface BannerNotification {
  id: string;
  type: 'feedback' | 'check_in_response' | 'workout_reminder' | 'program_update' | 'program_approved' | 'message' | 'default';
  title: string;
  body?: string;
  message?: string; // For backward compatibility
  data?: any;
  priority?: 'low' | 'medium' | 'high';
  autoHide?: boolean;
  duration?: number; // in milliseconds
  read?: boolean;
}

interface NotificationBannerStore {
  currentNotification: BannerNotification | null;
  notificationQueue: BannerNotification[];
  
  // Actions
  showNotification: (notification: BannerNotification) => void;
  dismissCurrentNotification: () => void;
  clearAllNotifications: () => void;
  markAsRead: (notificationId: string) => void;
}

export const useNotificationBannerStore = create<NotificationBannerStore>((set, get) => ({
  currentNotification: null,
  notificationQueue: [],

  showNotification: (notification: BannerNotification) => {
    const { currentNotification, notificationQueue } = get();
    
    // If no current notification, show immediately
    if (!currentNotification) {
      set({ currentNotification: notification });
    } else {
      // Add to queue, prioritizing by importance
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const newPriority = priorityOrder[notification.priority || 'medium'];
      
      const updatedQueue = [...notificationQueue, notification].sort((a, b) => {
        const aPriority = priorityOrder[a.priority || 'medium'];
        const bPriority = priorityOrder[b.priority || 'medium'];
        return bPriority - aPriority; // Higher priority first
      });
      
      set({ notificationQueue: updatedQueue });
    }
  },

  dismissCurrentNotification: () => {
    const { notificationQueue } = get();
    
    // Show next notification from queue if available
    if (notificationQueue.length > 0) {
      const nextNotification = notificationQueue[0];
      const remainingQueue = notificationQueue.slice(1);
      
      set({
        currentNotification: nextNotification,
        notificationQueue: remainingQueue,
      });
    } else {
      set({ currentNotification: null });
    }
  },

  clearAllNotifications: () => {
    set({
      currentNotification: null,
      notificationQueue: [],
    });
  },

  markAsRead: (notificationId: string) => {
    const { currentNotification, notificationQueue } = get();
    
    // Mark current notification as read
    if (currentNotification?.id === notificationId) {
      set({
        currentNotification: {
          ...currentNotification,
          read: true,
        },
      });
    }
    
    // Mark queued notification as read
    const updatedQueue = notificationQueue.map(notification =>
      notification.id === notificationId
        ? { ...notification, read: true }
        : notification
    );
    
    set({ notificationQueue: updatedQueue });
  },
}));

// Helper functions for creating notifications
export const createFeedbackNotification = (
  title: string,
  body: string,
  data?: any
): BannerNotification => ({
  id: `feedback-${Date.now()}-${Math.random()}`,
  type: 'feedback',
  title,
  body,
  data,
  priority: 'medium',
  autoHide: true,
  duration: 5000,
});

export const createCheckInResponseNotification = (
  title: string,
  body: string,
  data?: any
): BannerNotification => ({
  id: `checkin-${Date.now()}-${Math.random()}`,
  type: 'check_in_response',
  title,
  body,
  data,
  priority: 'medium',
  autoHide: true,
  duration: 5000,
});

export const createWorkoutReminderNotification = (
  title: string,
  body: string,
  data?: any
): BannerNotification => ({
  id: `workout-${Date.now()}-${Math.random()}`,
  type: 'workout_reminder',
  title,
  body,
  data,
  priority: 'low',
  autoHide: true,
  duration: 7000,
});

export const createProgramUpdateNotification = (
  title: string,
  body: string,
  data?: any
): BannerNotification => ({
  id: `program-${Date.now()}-${Math.random()}`,
  type: 'program_update',
  title,
  body,
  data,
  priority: 'high',
  autoHide: false, // Important notifications should not auto-hide
});

// Service class for managing banner notifications
class NotificationBannerService {
  private static instance: NotificationBannerService;

  static getInstance(): NotificationBannerService {
    if (!NotificationBannerService.instance) {
      NotificationBannerService.instance = new NotificationBannerService();
    }
    return NotificationBannerService.instance;
  }

  showFeedbackNotification(title: string, body: string, data?: any) {
    const notification = createFeedbackNotification(title, body, data);
    useNotificationBannerStore.getState().showNotification(notification);
  }

  showCheckInResponseNotification(title: string, body: string, data?: any) {
    const notification = createCheckInResponseNotification(title, body, data);
    useNotificationBannerStore.getState().showNotification(notification);
  }

  showWorkoutReminderNotification(title: string, body: string, data?: any) {
    const notification = createWorkoutReminderNotification(title, body, data);
    useNotificationBannerStore.getState().showNotification(notification);
  }

  showProgramUpdateNotification(title: string, body: string, data?: any) {
    const notification = createProgramUpdateNotification(title, body, data);
    useNotificationBannerStore.getState().showNotification(notification);
  }

  showCustomNotification(notification: BannerNotification) {
    useNotificationBannerStore.getState().showNotification(notification);
  }

  dismissCurrent() {
    useNotificationBannerStore.getState().dismissCurrentNotification();
  }

  clearAll() {
    useNotificationBannerStore.getState().clearAllNotifications();
  }

  // Test function to verify the notification system
  testNotificationSystem() {
    console.log('🧪 Testing notification banner system...');

    // Test feedback notification
    setTimeout(() => {
      this.showFeedbackNotification(
        'Test Feedback Notification',
        'This is a test feedback notification to verify the system is working',
        { feedbackId: 'test-feedback-123' }
      );
    }, 1000);

    // Test workout reminder
    setTimeout(() => {
      this.showWorkoutReminderNotification(
        'Test Workout Reminder',
        'This is a test workout reminder notification',
        { workoutId: 'test-workout-456' }
      );
    }, 6000);

    // Test program update
    setTimeout(() => {
      this.showProgramUpdateNotification(
        'Test Program Update',
        'This is a test program update notification',
        { programId: 'test-program-789' }
      );
    }, 11000);
  }
}

export const notificationBannerService = NotificationBannerService.getInstance();
