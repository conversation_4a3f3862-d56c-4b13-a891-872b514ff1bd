import { supabase } from './supabaseClient';

export interface WorkoutCompletionStatus {
  workoutId: string;
  isCompleted: boolean;
  completedAt?: string;
  completionCount: number; // How many times this workout was completed
}

export interface WorkoutRecommendation {
  nextWorkoutId?: string;
  nextWorkoutTitle?: string;
  completedWorkouts: string[];
  totalWorkouts: number;
  weekProgress: {
    [weekNumber: number]: {
      completed: number;
      total: number;
    };
  };
}

export const workoutCompletionService = {
  /**
   * Get completion status for multiple workouts
   */
  async getWorkoutCompletionStatus(
    workoutIds: string[],
    userId?: string
  ): Promise<WorkoutCompletionStatus[]> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;
    
    if (!targetUserId) {
      throw new Error('No user ID provided');
    }

    if (workoutIds.length === 0) {
      return [];
    }

    try {
      // Query workout_logs to see which workouts have been completed
      const { data: completionData, error } = await supabase
        .from('workout_logs')
        .select('planned_workout_id, completed_at')
        .eq('user_id', targetUserId)
        .in('planned_workout_id', workoutIds)
        .not('completed_at', 'is', null) // Only get completed workouts
        .order('completed_at', { ascending: false });

      if (error) {
        console.error('Error fetching workout completion status:', error);
        throw error;
      }

      // Create a map of workout completion status
      const completionMap = new Map<string, { isCompleted: boolean; completedAt?: string; count: number }>();
      
      // Initialize all workouts as not completed
      workoutIds.forEach(id => {
        completionMap.set(id, { isCompleted: false, count: 0 });
      });

      // Update with actual completion data
      completionData?.forEach(log => {
        if (log.planned_workout_id) {
          const existing = completionMap.get(log.planned_workout_id);
          if (existing) {
            completionMap.set(log.planned_workout_id, {
              isCompleted: true,
              completedAt: existing.completedAt || log.completed_at, // Keep the most recent
              count: existing.count + 1
            });
          }
        }
      });

      // Convert map to array
      return workoutIds.map(workoutId => {
        const status = completionMap.get(workoutId)!;
        return {
          workoutId,
          isCompleted: status.isCompleted,
          completedAt: status.completedAt,
          completionCount: status.count
        };
      });

    } catch (error) {
      console.error('Error in getWorkoutCompletionStatus:', error);
      // Return default status for all workouts on error
      return workoutIds.map(workoutId => ({
        workoutId,
        isCompleted: false,
        completionCount: 0
      }));
    }
  },

  /**
   * Get workout recommendation for a program
   * Returns the next workout that should be completed
   */
  async getWorkoutRecommendation(
    programId: string,
    userId?: string
  ): Promise<WorkoutRecommendation> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;
    
    if (!targetUserId) {
      throw new Error('No user ID provided');
    }

    try {
      // Get all workouts for this program
      const { data: programData, error: programError } = await supabase
        .from('workout_programs')
        .select(`
          id,
          program_weeks (
            week_number,
            workouts (
              id,
              title,
              order_in_week
            )
          )
        `)
        .eq('id', programId)
        .single();

      if (programError) throw programError;

      if (!programData?.program_weeks) {
        return {
          completedWorkouts: [],
          totalWorkouts: 0,
          weekProgress: {}
        };
      }

      // Flatten all workouts and sort them
      const allWorkouts = programData.program_weeks
        .flatMap(week => 
          week.workouts.map(workout => ({
            ...workout,
            weekNumber: week.week_number
          }))
        )
        .sort((a, b) => {
          // Sort by week first, then by order within week
          if (a.weekNumber !== b.weekNumber) {
            return a.weekNumber - b.weekNumber;
          }
          return a.order_in_week - b.order_in_week;
        });

      const workoutIds = allWorkouts.map(w => w.id);
      
      // Get completion status for all workouts
      const completionStatuses = await this.getWorkoutCompletionStatus(workoutIds, targetUserId);
      const completionMap = new Map(
        completionStatuses.map(status => [status.workoutId, status.isCompleted])
      );

      // Find completed workouts
      const completedWorkouts = completionStatuses
        .filter(status => status.isCompleted)
        .map(status => status.workoutId);

      // Calculate week progress
      const weekProgress: { [weekNumber: number]: { completed: number; total: number } } = {};
      
      programData.program_weeks.forEach(week => {
        const weekWorkouts = week.workouts;
        const completedInWeek = weekWorkouts.filter(w => completionMap.get(w.id)).length;
        
        weekProgress[week.week_number] = {
          completed: completedInWeek,
          total: weekWorkouts.length
        };
      });

      // Find next workout to do (first incomplete workout in sequence)
      const nextWorkout = allWorkouts.find(workout => !completionMap.get(workout.id));

      return {
        nextWorkoutId: nextWorkout?.id,
        nextWorkoutTitle: nextWorkout?.title,
        completedWorkouts,
        totalWorkouts: allWorkouts.length,
        weekProgress
      };

    } catch (error) {
      console.error('Error in getWorkoutRecommendation:', error);
      return {
        completedWorkouts: [],
        totalWorkouts: 0,
        weekProgress: {}
      };
    }
  },

  /**
   * Check if a specific workout has been completed
   */
  async isWorkoutCompleted(workoutId: string, userId?: string): Promise<boolean> {
    const statuses = await this.getWorkoutCompletionStatus([workoutId], userId);
    return statuses[0]?.isCompleted || false;
  },

  /**
   * Get the most recently completed workout for a program
   */
  async getLastCompletedWorkout(programId: string, userId?: string): Promise<{
    workoutId: string;
    workoutTitle: string;
    completedAt: string;
  } | null> {
    const { data: { user } } = await supabase.auth.getUser();
    const targetUserId = userId || user?.id;
    
    if (!targetUserId) {
      throw new Error('No user ID provided');
    }

    try {
      const { data, error } = await supabase
        .from('workout_logs')
        .select(`
          planned_workout_id,
          completed_at,
          workouts!inner (
            title
          )
        `)
        .eq('user_id', targetUserId)
        .eq('workout_program_id', programId)
        .not('completed_at', 'is', null)
        .order('completed_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) return null;

      return {
        workoutId: data.planned_workout_id,
        workoutTitle: (data.workouts as any)?.title || 'Unknown Workout',
        completedAt: data.completed_at
      };

    } catch (error) {
      console.error('Error getting last completed workout:', error);
      return null;
    }
  }
};
