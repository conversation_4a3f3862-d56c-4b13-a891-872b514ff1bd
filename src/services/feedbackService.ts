import { supabase } from './supabaseClient';
import { coachNotificationService } from './coachNotificationService';

export interface CoachFeedback {
  id: string;
  user_id: string;
  coach_id: string;
  related_checkin_id?: string;
  feedback_content: string;
  status: 'draft_by_ai' | 'draft_by_coach' | 'published' | 'read_by_client';
  published_at?: string;
  read_at?: string;
  created_at: string;
  updated_at: string;
  // Added fields for joined check-in data
  weekly_checkin?: {
    id: string;
    checkin_date: string;
    wins?: string;
    challenges?: string;
    progress_reflection?: string;
    training_performance_rating?: number;
    recovery_rating?: number;
    energy_levels_rating?: number;
  };
}

export const feedbackService = {
  /**
   * Fetch published feedback for the current user
   * If limit is undefined, fetch all records
   */
  async fetchPublishedFeedback(limit?: number, offset: number = 0): Promise<CoachFeedback[]> {
    console.log('📚 FeedbackService: Fetching published feedback with limit:', limit, 'offset:', offset);
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    // Build the query
    let query = supabase
      .from('coach_feedback')
      .select(`
        *,
        weekly_checkin:related_checkin_id (
          id,
          checkin_date,
          wins,
          challenges,
          progress_reflection,
          training_performance_rating,
          recovery_rating,
          energy_levels_rating
        )
      `)
      .eq('user_id', user.id)
      .in('status', ['published', 'read_by_client'])
      .order('published_at', { ascending: false });
    
    // Apply limit only if specified
    if (limit !== undefined) {
      query = query.range(offset, offset + limit - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('❌ FeedbackService: Error fetching feedback:', error);
      throw error;
    }

    console.log('✅ FeedbackService: Fetched', data?.length || 0, 'feedback items');
    return data || [];
  },

  /**
   * Fetch feedback related to a specific check-in
   */
  async fetchFeedbackForCheckIn(checkInId: string): Promise<CoachFeedback | null> {
    console.log('🔍 FeedbackService: Fetching feedback for check-in:', checkInId);
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    const { data, error } = await supabase
      .from('coach_feedback')
      .select(`
        *,
        weekly_checkin:related_checkin_id (
          id,
          checkin_date,
          wins,
          challenges,
          progress_reflection,
          training_performance_rating,
          recovery_rating,
          energy_levels_rating
        )
      `)
      .eq('user_id', user.id)
      .eq('related_checkin_id', checkInId)
      .in('status', ['published', 'read_by_client'])
      .maybeSingle();

    if (error) {
      console.error('❌ FeedbackService: Error fetching feedback for check-in:', error);
      throw error;
    }

    return data;
  },

  /**
   * Fetch feedback drafts that need coach review
   * If limit is undefined, fetch all records
   */
  async fetchFeedbackDrafts(limit?: number, offset: number = 0): Promise<CoachFeedback[]> {
    console.log('📚 FeedbackService: Fetching feedback drafts for coach review');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    // Get the coach's profile ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('❌ FeedbackService: Error fetching coach profile:', profileError);
      throw profileError;
    }

    // Build the query
    let query = supabase
      .from('coach_feedback')
      .select(`
        *,
        weekly_checkin:related_checkin_id (
          id,
          checkin_date,
          wins,
          challenges,
          progress_reflection,
          training_performance_rating,
          recovery_rating,
          energy_levels_rating
        )
      `)
      .eq('status', 'draft_by_ai')
      .order('created_at', { ascending: false });
    
    // Apply limit only if specified
    if (limit !== undefined) {
      query = query.range(offset, offset + limit - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('❌ FeedbackService: Error fetching feedback drafts:', error);
      throw error;
    }

    console.log('✅ FeedbackService: Fetched', data?.length || 0, 'feedback drafts');
    return data || [];
  },

  /**
   * Update a feedback draft
   */
  async updateFeedbackDraft(feedbackId: string, content: string): Promise<CoachFeedback> {
    console.log('📝 FeedbackService: Updating feedback draft:', feedbackId);
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    // Get the coach's profile ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('❌ FeedbackService: Error fetching coach profile:', profileError);
      throw profileError;
    }

    // Update the feedback draft
    const { data, error } = await supabase
      .from('coach_feedback')
      .update({
        feedback_content: content,
        status: 'draft_by_coach',
        coach_id: profile.id
      })
      .eq('id', feedbackId)
      .eq('status', 'draft_by_ai')
      .select()
      .single();

    if (error) {
      console.error('❌ FeedbackService: Error updating feedback draft:', error);
      throw error;
    }

    console.log('✅ FeedbackService: Feedback draft updated successfully');
    return data;
  },

  /**
   * Publish feedback to the client
   */
  async publishFeedback(feedbackId: string, content?: string): Promise<CoachFeedback> {
    console.log('📤 FeedbackService: Publishing feedback:', feedbackId);
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    // Get the coach's profile ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('❌ FeedbackService: Error fetching coach profile:', profileError);
      throw profileError;
    }

    // Prepare update data
    const updateData: any = {
      status: 'published',
      published_at: new Date().toISOString(),
      coach_id: profile.id
    };

    // If content is provided, update it
    if (content) {
      updateData.feedback_content = content;
    }

    // Publish the feedback
    const { data, error } = await supabase
      .from('coach_feedback')
      .update(updateData)
      .eq('id', feedbackId)
      .in('status', ['draft_by_ai', 'draft_by_coach'])
      .select(`
        *,
        weekly_checkin:related_checkin_id (
          id,
          checkin_date
        )
      `)
      .single();

    if (error) {
      console.error('❌ FeedbackService: Error publishing feedback:', error);
      throw error;
    }

    console.log('✅ FeedbackService: Feedback published successfully');

    // Send notification to the client
    try {
      console.log('🔔 FeedbackService: Sending feedback notification to client');

      // Get coach's name from profile
      const { data: coachProfile, error: coachError } = await supabase
        .from('profiles')
        .select('full_name')
        .eq('id', profile.id)
        .single();

      const coachName = coachProfile?.full_name || 'Your Coach';

      // Send check-in response notification
      if (data.related_checkin_id && data.weekly_checkin) {
        await coachNotificationService.sendCheckInResponseNotification(
          data.user_id,
          coachName,
          data.weekly_checkin.checkin_date,
          data.related_checkin_id
        );
      } else {
        // Send general feedback notification
        await coachNotificationService.sendFeedbackNotification(
          data.user_id,
          coachName,
          'check-in',
          data.id
        );
      }

      console.log('✅ FeedbackService: Notification sent successfully');
    } catch (notificationError) {
      console.error('⚠️ FeedbackService: Failed to send notification (non-critical):', notificationError);
      // Don't throw - feedback was published successfully, notification failure is non-critical
    }

    return data;
  },

  /**
   * Mark feedback as read by the client
   */
  async markFeedbackAsRead(feedbackId: string): Promise<void> {
    console.log('📝 FeedbackService: Marking feedback as read:', feedbackId);
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    const { error } = await supabase
      .from('coach_feedback')
      .update({
        status: 'read_by_client',
        read_at: new Date().toISOString()
      })
      .eq('id', feedbackId)
      .eq('user_id', user.id)
      .eq('status', 'published');

    if (error) {
      console.error('❌ FeedbackService: Error marking feedback as read:', error);
      throw error;
    }

    console.log('✅ FeedbackService: Feedback marked as read successfully');
  },

  /**
   * Get unread feedback count for the current user
   */
  async getUnreadFeedbackCount(): Promise<number> {
    console.log('🔢 FeedbackService: Getting unread feedback count');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('No authenticated user');
    }

    const { count, error } = await supabase
      .from('coach_feedback')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('status', 'published');

    if (error) {
      console.error('❌ FeedbackService: Error getting unread feedback count:', error);
      throw error;
    }

    console.log('✅ FeedbackService: Unread feedback count:', count || 0);
    return count || 0;
  }
};