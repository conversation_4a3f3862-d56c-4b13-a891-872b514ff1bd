# Test Enhanced Error Handling & Logging System

## Overview
The enhanced error handling and logging system provides comprehensive monitoring, error tracking, and performance analysis for all scheduled operations in the hybrid AI workout program generation system.

## Key Components

### 1. **Enhanced Logger** (`EnhancedLogger`)
- Structured logging with operation context
- Multiple log levels (success, error, warning, info)
- Automatic database persistence in `operation_logs` table
- Console logging with structured format

### 2. **Job Status Manager** (`JobStatusManager`)
- Manages `scheduled_generation_jobs` status updates
- Tracks job lifecycle (pending → running → completed/failed)
- Retry eligibility checking
- Error message persistence

### 3. **Retry Manager** (`RetryManager`)
- Configurable retry logic with exponential backoff
- Context-aware error handling
- Comprehensive retry attempt logging
- Graceful failure after max retries

### 4. **Performance Monitor** (`PerformanceMonitor`)
- Execution time tracking for all operations
- Slow operation detection and alerting
- Performance metrics collection
- Operation timing correlation

### 5. **Error Aggregator** (`ErrorAggregator`)
- Batch error collection for bulk operations
- Centralized error logging
- Error pattern analysis
- Bulk error reporting

### 6. **System Health Monitor** (Edge Function)
- Comprehensive system health metrics
- Automated alert generation
- Performance trend analysis
- Actionable recommendations

## Usage Examples

### **Basic Logging**
```typescript
const logger = new EnhancedLogger(supabaseClient, 'MyOperation')

// Success logging
await logger.logSuccess('userProcessing', { userId, result }, executionTime)

// Error logging
await logger.logError('userProcessing', error, { 
  userId, 
  additionalData: { step: 'validation' } 
})

// Warning logging
await logger.logWarning('userProcessing', 'User has incomplete profile', { userId })

// Info logging
await logger.logInfo('userProcessing', 'Processing started', { userId, batchSize })
```

### **Job Management**
```typescript
const jobManager = new JobStatusManager(supabaseClient, logger)

// Start job
await jobManager.startJob(jobId)

// Complete job
await jobManager.completeJob(jobId, { programId, cycleNumber })

// Fail job
await jobManager.failJob(jobId, error)

// Check retry eligibility
const shouldRetry = await jobManager.shouldRetryJob(jobId)
```

### **Retry Logic**
```typescript
const retryManager = new RetryManager(logger)

const result = await retryManager.executeWithRetry(
  async () => {
    // Your operation here
    return await generateProgram(userId)
  },
  {
    maxRetries: 3,
    retryDelayMs: 1000,
    exponentialBackoff: true
  },
  {
    operation: 'programGeneration',
    userId,
    additionalData: { cycleNumber }
  }
)
```

### **Performance Monitoring**
```typescript
const performanceMonitor = new PerformanceMonitor(logger)

performanceMonitor.startTiming('operation-123')
// ... perform operation ...
const executionTime = await performanceMonitor.endTiming(
  'operation-123',
  'programGeneration',
  userId,
  programId,
  { cycleNumber }
)
```

### **Error Aggregation**
```typescript
const errorAggregator = new ErrorAggregator(logger)

// Collect errors during batch processing
for (const user of users) {
  try {
    await processUser(user)
  } catch (error) {
    errorAggregator.addError(error, { 
      operation: 'batchProcessing',
      userId: user.id 
    })
  }
}

// Log all errors at once
await errorAggregator.logAllErrors()
console.log(`Total errors: ${errorAggregator.getErrorCount()}`)
```

## System Health Monitoring

### **Health Check Endpoint**
```bash
curl -X GET https://your-project.supabase.co/functions/v1/system-health-monitor \
  -H "Authorization: Bearer YOUR_SERVICE_KEY"
```

### **Sample Health Response**
```json
{
  "success": true,
  "status": "warning",
  "metrics": {
    "timestamp": "2025-07-10T10:00:00Z",
    "scheduledJobs": {
      "total": 150,
      "pending": 5,
      "running": 2,
      "completed": 140,
      "failed": 3,
      "overdue": 1
    },
    "programGeneration": {
      "last24Hours": 45,
      "successRate": 95.6,
      "averageExecutionTime": 8500,
      "failureReasons": [
        { "reason": "Intake not completed", "count": 2 }
      ]
    },
    "transitions": {
      "last24Hours": 12,
      "successRate": 100,
      "pendingTransitions": 3
    },
    "errors": {
      "last24Hours": 8,
      "criticalErrors": 0,
      "topErrors": [
        { "operation": "DailyScheduler.processUser", "count": 3, "lastOccurrence": "2025-07-10T09:30:00Z" }
      ]
    },
    "performance": {
      "averageSchedulerTime": 2100,
      "averageGenerationTime": 8500,
      "averageTransitionTime": 450,
      "slowOperations": []
    }
  },
  "alerts": [
    "1 scheduled jobs are overdue",
    "Low program generation success rate: 95.6%"
  ],
  "recommendations": [
    "Check scheduler execution and system resources",
    "Investigate program generation failures and improve reliability"
  ]
}
```

## Database Schema Integration

### **operation_logs Table**
```sql
CREATE TABLE operation_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  operation_type TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  program_id UUID REFERENCES workout_programs(id),
  job_id UUID REFERENCES scheduled_generation_jobs(id),
  operation_data JSONB,
  execution_status TEXT CHECK (execution_status IN ('success', 'error', 'warning', 'info')),
  error_details JSONB,
  execution_time_ms INTEGER,
  retry_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

### **Enhanced scheduled_generation_jobs**
- Uses existing table with `error_message`, `retry_count`, `max_retries` columns
- Status updates via `update_job_status()` function
- Integration with job lifecycle management

## Alert Thresholds

### **Warning Conditions**
- Job failure rate > 10%
- Program generation success rate < 90%
- Average execution time > 30 seconds
- Error count > 50 in 24 hours
- Overdue scheduled jobs > 0

### **Critical Conditions**
- Critical errors > 0 in 24 hours
- Database connectivity issues
- System health monitoring failure
- Job failure rate > 50%

## Benefits

1. **Comprehensive Visibility**: Full system observability with detailed metrics
2. **Proactive Monitoring**: Automated alerts before issues become critical
3. **Performance Optimization**: Detailed timing and performance analysis
4. **Error Pattern Analysis**: Identify and address recurring issues
5. **Operational Intelligence**: Data-driven insights for system improvements
6. **Audit Trail**: Complete history of all operations and errors
7. **Scalable Architecture**: Designed to handle high-volume operations

## Integration with Existing Functions

All existing Edge Functions have been enhanced with:
- Structured logging throughout execution
- Performance monitoring for key operations
- Error aggregation for batch processing
- Job status management for scheduled operations
- Comprehensive error context and recovery
