import { useEffect, useRef, useState } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAuthStore } from '@/store/authStore';
import { useUserStore } from '@/store/userStore';
import { useThemeStore } from '@/store/themeStore';
import { supabase } from '@/services/supabaseClient'
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { notificationService } from '@/services/notificationService';
import { coachNotificationService } from '@/services/coachNotificationService';
import { NotificationBanner } from '@/components/NotificationBanner';
import { useNotificationBannerStore } from '@/services/notificationBannerService';

export default function RootLayout() {
  useFrameworkReady();
  const { setSession } = useAuthStore();
  const { fetchProfile } = useUserStore();
  const { setTheme, initializeThemeFromProfile, resetTheme } = useThemeStore();
  const { currentNotification, dismissCurrentNotification } = useNotificationBannerStore();
  const [isInitializing, setIsInitializing] = useState(true);
  const hasInitializedThemeRef = useRef(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Get initial session
        const { data: { session } } = await supabase.auth.getSession();
        console.log('🚀 App initialization - Initial session:', session ? 'authenticated' : 'null');
        setSession(session);

        if (session) {
          // User is authenticated - fetch profile and initialize theme
          console.log('👤 Fetching profile for authenticated user...');
          await fetchProfile();
          console.log('✅ Profile fetched, theme will be initialized');

          // Initialize notification services for authenticated users
          console.log('🔔 Initializing notification services...');
          try {
            await notificationService.initialize();
            await coachNotificationService.initialize();
            console.log('✅ Notification services initialized successfully');
          } catch (error) {
            console.error('❌ Failed to initialize notification services:', error);
            // Don't throw - continue app initialization even if notifications fail
          }
        } else {
          // No session - ensure neutral theme
          console.log('🎨 No session - resetting to neutral theme');
          resetTheme();
        }
      } catch (error) {
        console.error('❌ Error during app initialization:', error);
        resetTheme(); // Fallback to neutral theme
      } finally {
        setIsInitializing(false);
      }
    };

    initializeApp();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state change:', event, 'Session:', session ? 'authenticated' : 'null');
      setSession(session);

      if (session && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
        // User signed in or token refreshed - fetch profile and update theme
        console.log('👤 Fetching profile after auth change...');
        try {
          await fetchProfile();
          console.log('✅ Profile fetched after auth change');

          // Initialize notification services for newly authenticated users
          if (event === 'SIGNED_IN') {
            console.log('🔔 Initializing notification services after sign in...');
            try {
              await notificationService.initialize();
              await coachNotificationService.initialize();
              console.log('✅ Notification services initialized after sign in');
            } catch (error) {
              console.error('❌ Failed to initialize notification services after sign in:', error);
            }
          }
        } catch (error) {
          console.error('❌ Error fetching profile after auth change:', error);
        }
      } else if (!session && event === 'SIGNED_OUT') {
        // User signed out - reset to neutral theme
        console.log('🎨 User signed out - resetting to neutral theme');
        resetTheme();
        hasInitializedThemeRef.current = false;
      }
    });

    return () => subscription.unsubscribe();
  }, [setSession, fetchProfile, resetTheme]);

  // Initialize theme from profile when available
  useEffect(() => {
    const profile = useUserStore.getState().profile;
    console.log('🎨 Checking initial profile for theme:', profile?.gender_preference);
    if (profile?.gender_preference && !hasInitializedThemeRef.current) {
      console.log('🎨 Initializing theme from profile:', profile.gender_preference);
      initializeThemeFromProfile(profile.gender_preference);
      hasInitializedThemeRef.current = true;
    }
  }, [initializeThemeFromProfile]);

  // Watch for profile changes
  useEffect(() => {
    const unsubscribe = useUserStore.subscribe((state) => {
      console.log('🎨 Profile state changed:', state.profile?.gender_preference, 'initialized:', hasInitializedThemeRef.current);
      if (state.profile?.gender_preference && !hasInitializedThemeRef.current) {
        console.log('🎨 Profile loaded, initializing theme:', state.profile.gender_preference);
        initializeThemeFromProfile(state.profile.gender_preference);
        hasInitializedThemeRef.current = true;
      }
    });

    return unsubscribe;
  }, [initializeThemeFromProfile]);

  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="(auth)" />
        <Stack.Screen name="(intake)" />
        <Stack.Screen name="(tabs)" />
        <Stack.Screen name="(coach)" />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />

      {/* Global notification banner */}
      <NotificationBanner
        onNavigateToProgram={(programId) => {
          // This will be handled by the enhanced banner's navigation
          console.log('🔔 RootLayout: Program navigation requested:', programId);
        }}
      />
    </>
  );
}