import { useState, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { registrationSchema } from '@/utils/validationSchemas';
import { useAuthStore } from '@/store/authStore';
import { useThemeStore } from '@/store/themeStore';
import { router } from 'expo-router';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard } from '@/components/ThemedComponents';

interface RegistrationForm {
  fullName: string;
  email: string;
  password: string;
}

export default function RegisterScreen() {
  const [serverError, setServerError] = useState<string | null>(null);
  const { signUp, isLoading } = useAuthStore();
  const { colors } = useThemeStore();

  // Refs for input navigation
  const fullNameRef = useRef<TextInput>(null);
  const emailRef = useRef<TextInput>(null);
  const passwordRef = useRef<TextInput>(null);
  
  const { control, handleSubmit, formState: { errors } } = useForm<RegistrationForm>({
    resolver: yupResolver(registrationSchema),
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: RegistrationForm) => {
    try {
      setServerError(null);
      await signUp(data.email, data.password, data.fullName);
      // Redirect to intake form instead of main tabs
      router.replace('/(intake)/welcome');
    } catch (error) {
      setServerError(error instanceof Error ? error.message : 'An error occurred during registration');
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <ThemedView style={styles.container}>
        <ThemedCard style={styles.formContainer}>
        <ThemedText style={styles.title}>Create Account</ThemedText>
        
        {serverError && (
          <Text style={[styles.serverError, { backgroundColor: `${colors.primary}20` }]}>{serverError}</Text>
        )}

        <Controller
          control={control}
          name="fullName"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <ThemedInput
                ref={fullNameRef}
                placeholder="Full Name"
                onChangeText={onChange}
                value={value}
                autoCapitalize="words"
                returnKeyType="next"
                onSubmitEditing={() => emailRef.current?.focus()}
                blurOnSubmit={false}
              />
              {errors.fullName && (
                <Text style={[styles.errorText, { color: colors.primary }]}>{errors.fullName.message}</Text>
              )}
            </View>
          )}
        />

        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <ThemedInput
                ref={emailRef}
                placeholder="Email"
                onChangeText={onChange}
                value={value}
                autoCapitalize="none"
                keyboardType="email-address"
                returnKeyType="next"
                onSubmitEditing={() => passwordRef.current?.focus()}
                blurOnSubmit={false}
              />
              {errors.email && (
                <Text style={[styles.errorText, { color: colors.primary }]}>{errors.email.message}</Text>
              )}
            </View>
          )}
        />

        <Controller
          control={control}
          name="password"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <ThemedInput
                ref={passwordRef}
                placeholder="Password"
                onChangeText={onChange}
                value={value}
                secureTextEntry
                autoCapitalize="none"
                returnKeyType="go"
                onSubmitEditing={handleSubmit(onSubmit)}
                blurOnSubmit={true}
              />
              {errors.password && (
                <Text style={[styles.errorText, { color: colors.primary }]}>{errors.password.message}</Text>
              )}
            </View>
          )}
        />

        <ThemedButton
          title={isLoading ? 'Creating Account...' : 'Sign Up'}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
          style={styles.button}
        />

        <TouchableOpacity
          style={styles.linkButton}
          onPress={() => router.push('/login')}
        >
          <ThemedText variant="accent" style={styles.linkText}>Already have an account? Sign In</ThemedText>
        </TouchableOpacity>
      </ThemedCard>
    </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  formContainer: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  serverError: {
    textAlign: 'center',
    marginBottom: 16,
    padding: 8,
    borderRadius: 4,
  },
  button: {
    marginTop: 8,
    paddingVertical: 14,
  },
  linkButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
  },
});