import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { authService } from '@/services/authService';
import { useThemeStore } from '@/store/themeStore';
import { router } from 'expo-router';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard } from '@/components/ThemedComponents';

const forgotPasswordSchema = yup.object().shape({
  email: yup
    .string()
    .required('Email is required')
    .email('Please enter a valid email'),
});

interface ForgotPasswordForm {
  email: string;
}

export default function ForgotPasswordScreen() {
  const [isLoading, setIsLoading] = useState(false);
  const [serverError, setServerError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const { colors } = useThemeStore();
  
  const { control, handleSubmit, formState: { errors } } = useForm<ForgotPasswordForm>({
    resolver: yupResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: ForgotPasswordForm) => {
    try {
      setIsLoading(true);
      setServerError(null);
      await authService.resetPassword(data.email);
      setIsSuccess(true);
    } catch (error) {
      setServerError(error instanceof Error ? error.message : 'An error occurred while sending reset email');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <ThemedView style={styles.container}>
        <ThemedCard style={styles.formContainer}>
          <ThemedText style={styles.title}>Check Your Email</ThemedText>
          <Text style={[styles.successMessage, { backgroundColor: `${colors.primary}20`, color: colors.primary }]}>
            We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.
          </Text>
          <ThemedButton
            title="Back to Login"
            onPress={() => router.push('/login')}
          />
        </ThemedCard>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedCard style={styles.formContainer}>
        <ThemedText style={styles.title}>Reset Password</ThemedText>
        <ThemedText style={styles.subtitle}>
          Enter your email address and we'll send you a link to reset your password.
        </ThemedText>
        
        {serverError && (
          <Text style={[styles.serverError, { backgroundColor: `${colors.primary}20` }]}>{serverError}</Text>
        )}

        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <ThemedInput
                placeholder="Email"
                onChangeText={onChange}
                value={value}
                autoCapitalize="none"
                keyboardType="email-address"
              />
              {errors.email && (
                <Text style={[styles.errorText, { color: colors.primary }]}>{errors.email.message}</Text>
              )}
            </View>
          )}
        />

        <ThemedButton
          title={isLoading ? 'Sending...' : 'Send Reset Link'}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
          style={styles.button}
        />

        <TouchableOpacity
          style={styles.linkButton}
          onPress={() => router.push('/login')}
        >
          <ThemedText variant="accent" style={styles.linkText}>Back to Login</ThemedText>
        </TouchableOpacity>
      </ThemedCard>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  formContainer: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: 16,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  serverError: {
    textAlign: 'center',
    marginBottom: 16,
    padding: 8,
    borderRadius: 4,
  },
  successMessage: {
    textAlign: 'center',
    marginBottom: 24,
    padding: 12,
    borderRadius: 4,
    lineHeight: 20,
  },
  button: {
    marginTop: 8,
    paddingVertical: 14,
  },
  linkButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
  },
});