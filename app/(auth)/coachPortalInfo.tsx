import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Linking } from 'react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useAuthStore } from '@/store/authStore';
import { ThemedView, ThemedText, ThemedButton, ThemedCard } from '@/components/ThemedComponents';
import { ArrowLeft, ExternalLink, Monitor, Users, BarChart3, MessageSquare } from 'lucide-react-native';

export default function CoachPortalInfoScreen() {
  const { colors } = useThemeStore();
  const { signOut } = useAuthStore();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleOpenCoachPortal = () => {
    // In a real implementation, this would open the actual coach dashboard URL
    // For now, we'll just show an alert or open a placeholder URL
    Linking.openURL('https://coach.yourapp.com').catch(() => {
      // Fallback if URL can't be opened
      console.log('Coach portal would open here');
    });
  };

  const features = [
    {
      icon: <Users size={24} color={colors.primary} />,
      title: 'Client Management',
      description: 'View and manage all your assigned clients in one place'
    },
    {
      icon: <BarChart3 size={24} color={colors.primary} />,
      title: 'Progress Tracking',
      description: 'Monitor client progress with detailed analytics and reports'
    },
    {
      icon: <MessageSquare size={24} color={colors.primary} />,
      title: 'Program Review',
      description: 'Review AI-generated programs and provide personalized feedback'
    },
    {
      icon: <Monitor size={24} color={colors.primary} />,
      title: 'Dashboard Analytics',
      description: 'Comprehensive insights into client engagement and outcomes'
    }
  ];

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Coach Portal</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.heroSection}>
          <View style={[styles.heroIcon, { backgroundColor: `${colors.primary}20` }]}>
            <Monitor size={48} color={colors.primary} />
          </View>
          <ThemedText style={styles.heroTitle}>Welcome, Coach!</ThemedText>
          <ThemedText style={styles.heroSubtitle}>
            Your coaching dashboard is available on our dedicated coach platform for the best experience.
          </ThemedText>
        </View>

        <ThemedCard style={styles.accessCard}>
          <ThemedText style={styles.accessTitle}>Access Your Coach Dashboard</ThemedText>
          <ThemedText style={styles.accessDescription}>
            The full coach experience is available through our web-based coach portal, designed specifically for managing clients and reviewing programs.
          </ThemedText>
          
          <ThemedButton
            title="Open Coach Portal"
            onPress={handleOpenCoachPortal}
            style={[styles.portalButton, { backgroundColor: colors.primary }]}
          >
            <View style={styles.buttonContent}>
              <Text style={[styles.portalButtonText, { color: colors.contrastText }]}>
                Open Coach Portal
              </Text>
              <ExternalLink size={20} color={colors.contrastText} />
            </View>
          </ThemedButton>
        </ThemedCard>

        <View style={styles.featuresSection}>
          <ThemedText style={styles.sectionTitle}>Coach Dashboard Features</ThemedText>
          {features.map((feature, index) => (
            <View key={index} style={[styles.featureItem, { backgroundColor: colors.background, borderColor: colors.accent }]}>
              <View style={[styles.featureIcon, { backgroundColor: `${colors.primary}15` }]}>
                {feature.icon}
              </View>
              <View style={styles.featureContent}>
                <ThemedText style={styles.featureTitle}>{feature.title}</ThemedText>
                <ThemedText style={styles.featureDescription}>{feature.description}</ThemedText>
              </View>
            </View>
          ))}
        </View>

        <ThemedCard style={[styles.infoCard, { backgroundColor: colors.accent }]}>
          <ThemedText style={styles.infoTitle}>Why a Separate Platform?</ThemedText>
          <ThemedText style={styles.infoText}>
            Our coach dashboard is optimized for desktop use with advanced analytics, bulk client management, and comprehensive reporting tools that work best on larger screens.
          </ThemedText>
        </ThemedCard>

        <View style={styles.actionsSection}>
          <ThemedButton
            title="Sign Out"
            onPress={handleSignOut}
            variant="outline"
            style={styles.signOutButton}
          />
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  heroSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  heroIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    opacity: 0.8,
  },
  accessCard: {
    padding: 24,
    marginBottom: 24,
    alignItems: 'center',
  },
  accessTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  accessDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
    opacity: 0.8,
  },
  portalButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  portalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  featuresSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  infoCard: {
    padding: 20,
    marginBottom: 24,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  actionsSection: {
    marginBottom: 32,
  },
  signOutButton: {
    paddingVertical: 14,
  },
});