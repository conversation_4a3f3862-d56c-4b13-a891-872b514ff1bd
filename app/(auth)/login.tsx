import { useState, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuthStore } from '@/store/authStore';
import { useUserStore } from '@/store/userStore';
import { useThemeStore } from '@/store/themeStore';
import { router } from 'expo-router';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard } from '@/components/ThemedComponents';

const loginSchema = yup.object().shape({
  email: yup
    .string()
    .required('Email is required')
    .email('Please enter a valid email'),
  password: yup
    .string()
    .required('Password is required'),
});

interface LoginForm {
  email: string;
  password: string;
}

type UserRole = 'Client' | 'Coach';

export default function LoginScreen() {
  const [serverError, setServerError] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<UserRole>('Client');
  const { signIn, isLoading } = useAuthStore();
  const { fetchProfile } = useUserStore();
  const { colors } = useThemeStore();

  // Refs for input navigation
  const emailRef = useRef<TextInput>(null);
  const passwordRef = useRef<TextInput>(null);
  
  const { control, handleSubmit, formState: { errors } } = useForm<LoginForm>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginForm) => {
    try {
      setServerError(null);
      await signIn(data.email, data.password);
      
      // Fetch user profile to get their actual role
      await fetchProfile();
      const userProfile = useUserStore.getState().profile;
      
      // Validate that the selected role matches the user's actual role
      if (selectedRole === 'Coach' && userProfile?.role !== 'coach') {
        setServerError('You do not have coach access. Please contact an administrator if you believe this is an error.');
        return;
      }
      
      // Navigate based on user's actual role (not just selected role)
      if (userProfile?.role === 'coach') {
        // Redirect coach users to the coach dashboard
        router.replace('/(coach)');
      } else {
        // Regular client users go to main tabs
        router.replace('/(tabs)');
      }
    } catch (error) {
      setServerError(error instanceof Error ? error.message : 'An error occurred during login');
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <ThemedView style={styles.container}>
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <View style={[styles.logo, { backgroundColor: colors.primary }]}>
              <Text style={styles.logoText}>f</Text>
            </View>
          </View>

          <ThemedText style={styles.title}>Welcome!</ThemedText>
          <ThemedText style={styles.subtitle}>
            Please sign into your account below
          </ThemedText>
        </View>

        <ThemedCard style={styles.formContainer}>
        {/* Role Selection */}
        <View style={styles.roleContainer}>
          <View style={[styles.roleToggle, { backgroundColor: colors.accent }]}>
            <TouchableOpacity
              style={[
                styles.roleOption,
                selectedRole === 'Client' && { backgroundColor: colors.background },
                selectedRole === 'Client' && styles.roleOptionSelected,
              ]}
              onPress={() => setSelectedRole('Client')}
            >
              <Text
                style={[
                  styles.roleText,
                  { color: selectedRole === 'Client' ? colors.text : colors.text },
                  selectedRole === 'Client' && styles.roleTextSelected,
                ]}
              >
                Client
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.roleOption,
                selectedRole === 'Coach' && { backgroundColor: colors.background },
                selectedRole === 'Coach' && styles.roleOptionSelected,
              ]}
              onPress={() => setSelectedRole('Coach')}
            >
              <Text
                style={[
                  styles.roleText,
                  { color: selectedRole === 'Coach' ? colors.text : colors.text },
                  selectedRole === 'Coach' && styles.roleTextSelected,
                ]}
              >
                Coach
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {serverError && (
          <Text style={[styles.serverError, { backgroundColor: `${colors.primary}20`, color: colors.error }]}>
            {serverError}
          </Text>
        )}

        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>Email</ThemedText>
              <ThemedInput
                ref={emailRef}
                placeholder="Enter your username"
                onChangeText={onChange}
                value={value}
                autoCapitalize="none"
                keyboardType="email-address"
                returnKeyType="next"
                onSubmitEditing={() => passwordRef.current?.focus()}
                blurOnSubmit={false}
              />
              {errors.email && (
                <Text style={[styles.errorText, { color: colors.error }]}>{errors.email.message}</Text>
              )}
            </View>
          )}
        />

        <Controller
          control={control}
          name="password"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>Password</ThemedText>
              <ThemedInput
                ref={passwordRef}
                placeholder="Enter your password"
                onChangeText={onChange}
                value={value}
                secureTextEntry
                autoCapitalize="none"
                returnKeyType="go"
                onSubmitEditing={handleSubmit(onSubmit)}
                blurOnSubmit={true}
              />
              {errors.password && (
                <Text style={[styles.errorText, { color: colors.error }]}>{errors.password.message}</Text>
              )}
            </View>
          )}
        />

        <ThemedButton
          title={isLoading ? 'Signing In...' : 'Login'}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
          style={[styles.loginButton, { backgroundColor: colors.primary }]}
        />

        <View style={styles.footer}>
          <Text style={[styles.termsText, { color: colors.text }]}>
            By proceeding you acknowledge that you have read, understood{'\n'}
            and agreed to our{' '}
            <Text style={[styles.termsLink, { color: colors.primary }]}>Terms & Condition</Text>
          </Text>
        </View>

        <TouchableOpacity
          style={styles.linkButton}
          onPress={() => router.push('/forgotPassword')}
        >
          <ThemedText variant="accent" style={styles.linkText}>Forgot Password?</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.linkButton}
          onPress={() => router.push('/register')}
        >
          <ThemedText variant="accent" style={styles.linkText}>Don't have an account? Sign Up</ThemedText>
        </TouchableOpacity>
      </ThemedCard>
    </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    paddingTop: 80,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: 'flex-start',
    marginBottom: 40,
  },
  logo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.7,
    lineHeight: 22,
  },
  formContainer: {
    flex: 1,
    margin: 0,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 32,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  roleContainer: {
    marginBottom: 32,
  },
  roleToggle: {
    flexDirection: 'row',
    borderRadius: 16,
    padding: 4,
  },
  roleOption: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  roleOptionSelected: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  roleText: {
    fontSize: 16,
    fontWeight: '600',
  },
  roleTextSelected: {
    fontWeight: '700',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  serverError: {
    textAlign: 'center',
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    fontSize: 14,
  },
  loginButton: {
    paddingVertical: 18,
    borderRadius: 16,
    marginTop: 8,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  footer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
    opacity: 0.7,
  },
  termsLink: {
    fontWeight: '600',
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 8,
  },
  linkText: {
    fontSize: 14,
  },
});