import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { workoutService, WorkoutProgram } from '@/services/workoutService';
import { feedbackService, CoachFeedback } from '@/services/feedbackService';
import { ThemedView, ThemedText, ThemedCard, ThemedButton, ThemedInput, ThemedStatusIndicator } from '@/components/ThemedComponents';
import ConfirmationModal from '@/components/ConfirmationModal';
import RegenerationNotesModal from '@/components/RegenerationNotesModal';
import CoachFeedbackEditModal from '@/components/CoachFeedbackEditModal';
import { ArrowLeft, Calendar, Clock, Target, User, CircleCheck as CheckCircle, LocationEdit as Edit3, Save, <PERSON>, Du<PERSON><PERSON>, Timer, RotateCcw, MessageSquare } from 'lucide-react-native';

export default function ProgramReviewScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { colors } = useThemeStore();
  const [program, setProgram] = useState<WorkoutProgram | null>(null);
  const [programDetails, setProgramDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [coachNotes, setCoachNotes] = useState('');
  const [showRegenerationModal, setShowRegenerationModal] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [pendingFeedback, setPendingFeedback] = useState<CoachFeedback[]>([]);
  const [selectedFeedback, setSelectedFeedback] = useState<CoachFeedback | null>(null);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [isLoadingFeedback, setIsLoadingFeedback] = useState(false);

  // Program name editing state
  const [isEditingProgramName, setIsEditingProgramName] = useState(false);
  const [editedProgramName, setEditedProgramName] = useState('');
  const [isSavingProgramName, setIsSavingProgramName] = useState(false);

  // Confirmation modal state
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [confirmationTitle, setConfirmationTitle] = useState('');
  const [confirmationMessage, setConfirmationMessage] = useState('');
  const [onConfirmAction, setOnConfirmAction] = useState<() => void>(() => {});
  const [confirmButtonVariant, setConfirmButtonVariant] = useState<'primary' | 'secondary' | 'outline'>('primary');

  // Temporary message state
  const [displayMessage, setDisplayMessage] = useState<string | null>(null);
  const [messageType, setMessageType] = useState<'success' | 'error' | 'warning' | 'info'>('info');

  useEffect(() => {
    if (id) {
      fetchProgramDetails();
      fetchPendingFeedback();
    }
  }, [id]);

  const showTemporaryMessage = useCallback((message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration: number = 3000) => {
    setDisplayMessage(message);
    setMessageType(type);
    setTimeout(() => {
      setDisplayMessage(null);
    }, duration);
  }, []);

  const fetchProgramDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const details = await workoutService.getWorkoutProgramDetails(id);
      setProgram(details.program);
      setProgramDetails(details);
      setCoachNotes(details.program.coach_notes_for_client || '');
    } catch (error) {
      console.error('Error fetching program details:', error);
      setError('Failed to load program details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPendingFeedback = async () => {
    try {
      setIsLoadingFeedback(true);
      console.log('Fetching pending feedback for program:', id);
      
      // Fetch feedback drafts that need coach review
      const feedback = await feedbackService.fetchFeedbackDrafts();
      console.log(`Found ${feedback.length} pending feedback items`);
      
      setPendingFeedback(feedback);
    } catch (error) {
      console.error('Error fetching pending feedback:', error);
    } finally {
      setIsLoadingFeedback(false);
    }
  };

  const handleStatusUpdate = async (newStatus: string) => {
    console.log('🚀 DEBUG: handleStatusUpdate called with newStatus:', newStatus);
    console.log('🚀 DEBUG: Current program state:', program);
    console.log('🚀 DEBUG: Current isUpdating state:', isUpdating);
    
    if (!program) {
      console.log('❌ DEBUG: No program found, exiting handleStatusUpdate');
      return;
    }

    console.log('🚀 DEBUG: Program exists, proceeding with status update');

    const statusMessages = {
      'coach_approved': 'approve this program',
      'coach_edited': 'mark this program as edited',
      'archived': 'archive this program',
    };

    console.log('🚀 DEBUG: Status messages:', statusMessages);
    console.log('🚀 DEBUG: Selected message:', statusMessages[newStatus as keyof typeof statusMessages]);

    // Set up confirmation modal
    setConfirmationTitle('Confirm Action');
    setConfirmationMessage(`Are you sure you want to ${statusMessages[newStatus as keyof typeof statusMessages]}?`);
    setConfirmButtonVariant('primary');
    
    // Set up the action to be executed when confirmed
    setOnConfirmAction(() => async () => {
      console.log('🚀 DEBUG: User confirmed action - starting update process');
      console.log('🚀 DEBUG: Setting isUpdating to true');
      
      try {
        setIsUpdating(true);
        console.log('🚀 DEBUG: isUpdating set to true, current state should be true');
        
        const updates: any = {
          status: newStatus,
          coach_reviewed_at: new Date().toISOString(),
        };

        console.log('🚀 DEBUG: Base updates object created:', updates);

        if (coachNotes.trim()) {
          updates.coach_notes_for_client = coachNotes.trim();
          console.log('🚀 DEBUG: Added coach notes to updates:', coachNotes.trim());
        }

        console.log('🚀 DEBUG: Final updates object:', updates);
        console.log('🚀 DEBUG: About to call workoutService.updateWorkoutProgram');
        console.log('🚀 DEBUG: Program ID:', program.id);

        const updatedProgram = await workoutService.updateWorkoutProgram(program.id, updates);
        
        console.log('🚀 DEBUG: workoutService.updateWorkoutProgram completed successfully');
        console.log('🚀 DEBUG: Updated program received:', updatedProgram);
        
        setProgram(updatedProgram);
        setIsEditingNotes(false);
        
        console.log('🚀 DEBUG: State updated, showing success message');
        showTemporaryMessage('Program updated successfully!', 'success');
        console.log('🚀 DEBUG: Success message shown');
        
      } catch (error) {
        console.error('❌ DEBUG: Error in handleStatusUpdate:', error);
        console.error('❌ DEBUG: Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });
        showTemporaryMessage('Failed to update program. Please try again.', 'error');
      } finally {
        console.log('🚀 DEBUG: Setting isUpdating to false in finally block');
        setIsUpdating(false);
        setShowConfirmationModal(false);
        console.log('🚀 DEBUG: isUpdating set to false, update process complete');
      }
    });

    // Show the confirmation modal
    setShowConfirmationModal(true);
    console.log('🚀 DEBUG: Confirmation modal should now be visible');
  };

  const handleSaveNotes = async () => {
    if (!program) return;

    try {
      setIsUpdating(true);

      const updates = {
        coach_notes_for_client: coachNotes.trim() || null,
      };

      const updatedProgram = await workoutService.updateWorkoutProgram(program.id, updates);
      setProgram(updatedProgram);
      setIsEditingNotes(false);

      showTemporaryMessage('Notes saved successfully!', 'success');
    } catch (error) {
      console.error('Error saving notes:', error);
      showTemporaryMessage('Failed to save notes. Please try again.', 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSaveProgramName = async () => {
    if (!program || !editedProgramName.trim()) return;

    // Validate program name
    const trimmedName = editedProgramName.trim();
    if (trimmedName.length < 3) {
      showTemporaryMessage('Program name must be at least 3 characters long.', 'error');
      return;
    }
    if (trimmedName.length > 100) {
      showTemporaryMessage('Program name must be less than 100 characters.', 'error');
      return;
    }

    try {
      setIsSavingProgramName(true);

      const updates = {
        name: trimmedName,
      };

      const updatedProgram = await workoutService.updateWorkoutProgram(program.id, updates);
      setProgram(updatedProgram);
      setIsEditingProgramName(false);

      showTemporaryMessage('Program name updated successfully!', 'success');
    } catch (error) {
      console.error('Error updating program name:', error);
      showTemporaryMessage('Failed to update program name. Please try again.', 'error');
    } finally {
      setIsSavingProgramName(false);
    }
  };

  const handleStartEditingProgramName = () => {
    if (!program) return;
    setEditedProgramName(program.name);
    setIsEditingProgramName(true);
  };

  const handleCancelEditingProgramName = () => {
    setIsEditingProgramName(false);
    setEditedProgramName('');
  };

  const handleRegenerate = async (regenerationNotes: string) => {
    if (!program) return;

    try {
      setIsRegenerating(true);
      
      const result = await workoutService.regenerateWorkoutProgram(program.id, regenerationNotes);
      
      if (result.success) {
        showTemporaryMessage('Program regenerated successfully! Redirecting to new program...', 'success', 5000);
        
        // Navigate to new program after showing success message
        setTimeout(() => {
          setShowRegenerationModal(false);
          if (result.newProgramId) {
            router.replace(`/(coach)/program/${result.newProgramId}`);
          } else {
            router.back();
          }
        }, 2000);
      } else {
        // Enhanced error handling for timeout
        let errorMessage = result.error || 'Failed to regenerate program. Please try again.';
        
        if (result.error?.includes('timed out')) {
          errorMessage = 'The regeneration process is taking longer than expected. This may be due to high server load. Please try again in a few minutes.';
        }
        
        showTemporaryMessage(errorMessage, 'error', 5000);
      }
    } catch (error) {
      console.error('Error regenerating program:', error);
      
      // Enhanced error handling for timeout
      let errorMessage = 'Failed to regenerate program. Please try again.';
      
      if (error instanceof Error && error.message.includes('timed out')) {
        errorMessage = 'The regeneration process is taking longer than expected. This may be due to high server load. Please try again in a few minutes.';
      }
      
      showTemporaryMessage(errorMessage, 'error', 5000);
    } finally {
      setIsRegenerating(false);
    }
  };

  const handleEditFeedback = (feedback: CoachFeedback) => {
    setSelectedFeedback(feedback);
    setShowFeedbackModal(true);
  };

  const handleFeedbackSaved = async () => {
    setShowFeedbackModal(false);
    setSelectedFeedback(null);
    await fetchPendingFeedback();
    showTemporaryMessage('Feedback updated successfully!', 'success');
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'ai_generated_pending_review':
        return {
          icon: <Clock size={20} color={colors.warning} />,
          text: 'Pending Review',
          statusType: 'warning' as const,
          description: 'This program has been created and needs coach review.',
        };
      case 'coach_approved':
        return {
          icon: <CheckCircle size={20} color={colors.success} />,
          text: 'Approved',
          statusType: 'success' as const,
          description: 'This program has been approved and is ready for the client.',
        };
      case 'coach_edited':
        return {
          icon: <Edit3 size={20} color={colors.info} />,
          text: 'Edited',
          statusType: 'info' as const,
          description: 'This program has been modified by a coach.',
        };
      case 'active_by_client':
        return {
          icon: <Target size={20} color={colors.primary} />,
          text: 'Active',
          statusType: 'primary' as const,
          description: 'The client is currently following this program.',
        };
      default:
        return {
          icon: <Clock size={20} color={colors.text} />,
          text: 'Unknown',
          statusType: 'info' as const,
          description: 'Status unknown.',
        };
    }
  };

  const formatReps = (exercise: any) => {
    if (exercise.prescribed_reps_min && exercise.prescribed_reps_max) {
      if (exercise.prescribed_reps_min === exercise.prescribed_reps_max) {
        return `${exercise.prescribed_reps_min} reps`;
      }
      return `${exercise.prescribed_reps_min}-${exercise.prescribed_reps_max} reps`;
    }
    if (exercise.prescribed_reps_min) {
      return `${exercise.prescribed_reps_min}+ reps`;
    }
    if (exercise.prescribed_duration_seconds) {
      const minutes = Math.floor(exercise.prescribed_duration_seconds / 60);
      const seconds = exercise.prescribed_duration_seconds % 60;
      if (minutes > 0) {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
      }
      return `${exercise.prescribed_duration_seconds}s`;
    }
    return 'As prescribed';
  };

  const formatRestTime = (seconds: number) => {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      if (remainingSeconds === 0) {
        return `${minutes}min`;
      }
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${seconds}s`;
  };

  const getMessageCardStyle = () => {
    switch (messageType) {
      case 'success':
        return { backgroundColor: colors.success + '20', borderColor: colors.success + '40' };
      case 'error':
        return { backgroundColor: colors.error + '20', borderColor: colors.error + '40' };
      case 'warning':
        return { backgroundColor: colors.warning + '20', borderColor: colors.warning + '40' };
      default:
        return { backgroundColor: colors.info + '20', borderColor: colors.info + '40' };
    }
  };

  const getMessageTextColor = () => {
    switch (messageType) {
      case 'success':
        return colors.success;
      case 'error':
        return colors.error;
      case 'warning':
        return colors.warning;
      default:
        return colors.info;
    }
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Program Review</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading program details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error || !program) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Program Review</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={[styles.errorText, { color: colors.error }]}>
            {error || 'Program not found'}
          </ThemedText>
          <ThemedButton title="Go Back" onPress={() => router.back()} />
        </View>
      </ThemedView>
    );
  }

  const statusInfo = getStatusInfo(program.status);

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Program Review</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Temporary Message Display */}
        {displayMessage && (
          <ThemedCard style={[styles.messageCard, getMessageCardStyle()]}>
            <ThemedText style={[styles.messageText, { color: getMessageTextColor() }]}>
              {displayMessage}
            </ThemedText>
          </ThemedCard>
        )}

        {/* Program Overview */}
        <ThemedCard style={styles.programCard}>
          <View style={styles.programHeader}>
            {/* Editable Program Name */}
            {isEditingProgramName ? (
              <View style={styles.editingNameContainer}>
                <ThemedInput
                  value={editedProgramName}
                  onChangeText={setEditedProgramName}
                  placeholder="Enter program name..."
                  style={styles.programNameInput}
                  maxLength={100}
                  autoFocus
                />
                <View style={styles.nameEditActions}>
                  <TouchableOpacity
                    onPress={handleSaveProgramName}
                    disabled={isSavingProgramName || !editedProgramName.trim()}
                    style={[
                      styles.nameActionButton,
                      styles.saveButton,
                      { backgroundColor: colors.success }
                    ]}
                  >
                    {isSavingProgramName ? (
                      <ActivityIndicator size="small" color="white" />
                    ) : (
                      <Save size={16} color="white" />
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={handleCancelEditingProgramName}
                    disabled={isSavingProgramName}
                    style={[
                      styles.nameActionButton,
                      styles.cancelButton,
                      { backgroundColor: colors.error }
                    ]}
                  >
                    <X size={16} color="white" />
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <>
                <View style={styles.programTitleContainer}>
                  <ThemedText style={styles.programTitle}>{program.name}</ThemedText>
                  {program.status === 'ai_generated_pending_review' && (
                    <TouchableOpacity
                      onPress={handleStartEditingProgramName}
                      style={[styles.editNameButton, { backgroundColor: colors.accent }]}
                      activeOpacity={0.7}
                    >
                      <Edit3 size={16} color={colors.primary} />
                    </TouchableOpacity>
                  )}
                </View>
                {program.status === 'ai_generated_pending_review' && (
                  <ThemedText style={[styles.editHint, { color: colors.text + '60' }]}>
                    Tap the edit icon to customize the program name
                  </ThemedText>
                )}
              </>
            )}
            <ThemedStatusIndicator
              status={statusInfo.statusType}
              text={statusInfo.text}
              icon={statusInfo.icon}
              style={styles.statusBadge}
              textStyle={styles.statusText}
            />
          </View>

          <ThemedText style={styles.programDescription}>{program.description}</ThemedText>

          <View style={styles.programMeta}>
            <View style={styles.metaItem}>
              <Calendar size={16} color={colors.text} />
              <ThemedText style={styles.metaText}>{program.duration_weeks} weeks</ThemedText>
            </View>
            <View style={styles.metaItem}>
              <Clock size={16} color={colors.text} />
              <ThemedText style={styles.metaText}>
                Created {new Date(program.created_at).toLocaleDateString()}
              </ThemedText>
            </View>
            {program.generated_by_ai_at && (
              <View style={styles.metaItem}>
                <Target size={16} color={colors.text} />
                <ThemedText style={styles.metaText}>AI Generated</ThemedText>
              </View>
            )}
          </View>

          <View style={[styles.statusDescription, { backgroundColor: colors.accent }]}>
            <ThemedText style={styles.statusDescriptionText}>{statusInfo.description}</ThemedText>
          </View>
        </ThemedCard>

        {/* Coach Notes Section */}
        <ThemedCard style={styles.notesCard}>
          <View style={styles.notesHeader}>
            <ThemedText style={styles.notesTitle}>Coach Notes for Client</ThemedText>
            {!isEditingNotes && (
              <TouchableOpacity
                style={[styles.editButton, { backgroundColor: colors.accent }]}
                onPress={() => setIsEditingNotes(true)}
              >
                <Edit3 size={16} color={colors.primary} />
              </TouchableOpacity>
            )}
          </View>

          {isEditingNotes ? (
            <View style={styles.notesEditContainer}>
              <ThemedInput
                value={coachNotes}
                onChangeText={setCoachNotes}
                placeholder="Add notes for the client about this program..."
                multiline
                numberOfLines={4}
                textAlignVertical="top"
                style={styles.notesInput}
              />
              <View style={styles.notesActions}>
                <TouchableOpacity
                  style={[styles.noteActionButton, { backgroundColor: colors.error + '20' }]}
                  onPress={() => {
                    setCoachNotes(program.coach_notes_for_client || '');
                    setIsEditingNotes(false);
                  }}
                >
                  <X size={16} color={colors.error} />
                  <Text style={[styles.noteActionText, { color: colors.error }]}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.noteActionButton, { backgroundColor: colors.success + '20' }]}
                  onPress={handleSaveNotes}
                  disabled={isUpdating}
                >
                  <Save size={16} color={colors.success} />
                  <Text style={[styles.noteActionText, { color: colors.success }]}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <ThemedText style={styles.notesContent}>
              {program.coach_notes_for_client || 'No notes added yet. Click the edit button to add notes for the client.'}
            </ThemedText>
          )}
        </ThemedCard>

        {/* Pending Feedback Section */}
        {pendingFeedback.length > 0 && (
          <ThemedCard style={styles.feedbackSection}>
            <View style={styles.feedbackSectionHeader}>
              <MessageSquare size={20} color={colors.primary} />
              <ThemedText style={styles.feedbackSectionTitle}>Pending Feedback</ThemedText>
            </View>
            
            <ThemedText style={styles.feedbackSectionDescription}>
              {pendingFeedback.length} feedback draft{pendingFeedback.length !== 1 ? 's' : ''} need{pendingFeedback.length === 1 ? 's' : ''} your review
            </ThemedText>
            
            {pendingFeedback.map((feedback) => (
              <ThemedCard key={feedback.id} style={styles.feedbackItem}>
                <View style={styles.feedbackItemHeader}>
                  <View>
                    <ThemedText style={styles.feedbackItemTitle}>
                      Generated Feedback
                    </ThemedText>
                    <ThemedText style={styles.feedbackItemDate}>
                      Created {new Date(feedback.created_at).toLocaleDateString()}
                    </ThemedText>
                  </View>
                  <ThemedStatusIndicator
                    status="warning"
                    text="Needs Review"
                    icon={<Clock size={14} color={colors.warning} />}
                    style={styles.feedbackStatusBadge}
                    textStyle={styles.feedbackStatusText}
                  />
                </View>
                
                <ThemedText style={styles.feedbackItemPreview} numberOfLines={3}>
                  {feedback.feedback_content}
                </ThemedText>
                
                <ThemedButton
                  title="Review & Edit"
                  onPress={() => handleEditFeedback(feedback)}
                  variant="outline"
                  style={styles.feedbackItemButton}
                />
              </ThemedCard>
            ))}
          </ThemedCard>
        )}

        {/* Program Structure Overview */}
        {programDetails && (
          <ThemedCard style={styles.structureCard}>
            <ThemedText style={styles.structureTitle}>Program Structure</ThemedText>
            {programDetails.weeks.map((week: any, weekIndex: number) => (
              <View key={week.id} style={styles.weekContainer}>
                <ThemedText style={styles.weekTitle}>Week {week.week_number}</ThemedText>
                {week.notes && (
                  <ThemedText style={styles.weekNotes}>{week.notes}</ThemedText>
                )}
                <View style={styles.workoutsContainer}>
                  {week.workouts.map((workout: any, workoutIndex: number) => (
                    <View key={workout.id} style={[styles.workoutItem, { backgroundColor: colors.accent }]}>
                      <View style={styles.workoutHeader}>
                        <ThemedText style={styles.workoutTitle}>{workout.title}</ThemedText>
                        <View style={styles.workoutMeta}>
                          <View style={styles.workoutMetaItem}>
                            <Clock size={12} color={colors.text} />
                            <ThemedText style={styles.workoutMetaText}>
                              {workout.estimated_duration_minutes} min
                            </ThemedText>
                          </View>
                          <View style={styles.workoutMetaItem}>
                            <Dumbbell size={12} color={colors.text} />
                            <ThemedText style={styles.workoutMetaText}>
                              {workout.exercises.length} exercises
                            </ThemedText>
                          </View>
                        </View>
                      </View>
                      
                      {workout.description && (
                        <ThemedText style={styles.workoutDescription}>{workout.description}</ThemedText>
                      )}

                      {/* Exercise List */}
                      <View style={styles.exercisesContainer}>
                        {workout.exercises.map((exercise: any, exerciseIndex: number) => (
                          <View key={exercise.id} style={[styles.exerciseItem, { borderLeftColor: colors.primary }]}>
                            <View style={styles.exerciseHeader}>
                              <ThemedText style={styles.exerciseName}>
                                {exerciseIndex + 1}. {exercise.exercise?.name || 'Unknown Exercise'}
                              </ThemedText>
                            </View>
                            
                            <View style={styles.exerciseDetails}>
                              <View style={styles.exerciseDetailRow}>
                                <View style={styles.exerciseDetailItem}>
                                  <ThemedText style={styles.exerciseDetailLabel}>Sets:</ThemedText>
                                  <ThemedText style={styles.exerciseDetailValue}>
                                    {exercise.prescribed_sets}
                                  </ThemedText>
                                </View>
                                
                                <View style={styles.exerciseDetailItem}>
                                  <ThemedText style={styles.exerciseDetailLabel}>Reps:</ThemedText>
                                  <ThemedText style={styles.exerciseDetailValue}>
                                    {formatReps(exercise)}
                                  </ThemedText>
                                </View>
                                
                                <View style={styles.exerciseDetailItem}>
                                  <Timer size={12} color={colors.text} />
                                  <ThemedText style={styles.exerciseDetailValue}>
                                    {formatRestTime(exercise.rest_period_seconds_after_set || 60)}
                                  </ThemedText>
                                </View>
                              </View>

                              {/* Additional prescription details */}
                              {(exercise.prescribed_rir || exercise.prescribed_rpe || exercise.prescribed_tempo) && (
                                <View style={styles.exerciseDetailRow}>
                                  {exercise.prescribed_rir && (
                                    <View style={styles.exerciseDetailItem}>
                                      <ThemedText style={styles.exerciseDetailLabel}>RIR:</ThemedText>
                                      <ThemedText style={styles.exerciseDetailValue}>
                                        {exercise.prescribed_rir}
                                      </ThemedText>
                                    </View>
                                  )}
                                  
                                  {exercise.prescribed_rpe && (
                                    <View style={styles.exerciseDetailItem}>
                                      <ThemedText style={styles.exerciseDetailLabel}>RPE:</ThemedText>
                                      <ThemedText style={styles.exerciseDetailValue}>
                                        {exercise.prescribed_rpe}
                                      </ThemedText>
                                    </View>
                                  )}
                                  
                                  {exercise.prescribed_tempo && (
                                    <View style={styles.exerciseDetailItem}>
                                      <RotateCcw size={12} color={colors.text} />
                                      <ThemedText style={styles.exerciseDetailValue}>
                                        {exercise.prescribed_tempo}
                                      </ThemedText>
                                    </View>
                                  )}
                                </View>
                              )}

                              {/* Exercise notes */}
                              {exercise.notes && (
                                <View style={[styles.exerciseNotes, { backgroundColor: colors.background }]}>
                                  <ThemedText style={styles.exerciseNotesText}>
                                    {exercise.notes}
                                  </ThemedText>
                                </View>
                              )}
                            </View>
                          </View>
                        ))}
                      </View>
                    </View>
                  ))}
                </View>
              </View>
            ))}
          </ThemedCard>
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          {program.status === 'ai_generated_pending_review' && (
            <>
              <ThemedButton
                title="Approve Program"
                onPress={() => {
                  console.log('🚀 DEBUG: Approve Program button pressed');
                  handleStatusUpdate('coach_approved');
                }}
                disabled={isUpdating}
                style={[styles.actionButton, { backgroundColor: colors.success }]}
              />
              <ThemedButton
                title="Mark as Edited"
                onPress={() => {
                  console.log('🚀 DEBUG: Mark as Edited button pressed');
                  handleStatusUpdate('coach_edited');
                }}
                disabled={isUpdating}
                variant="outline"
                style={styles.actionButton}
              />
              <ThemedButton
                title="Regenerate Program"
                onPress={() => {
                  console.log('🚀 DEBUG: Regenerate Program button pressed');
                  setShowRegenerationModal(true);
                }}
                disabled={isUpdating}
                style={[styles.actionButton, { backgroundColor: colors.warning }]}
              />
            </>
          )}

          {(program.status === 'coach_approved' || program.status === 'coach_edited') && (
            <>
              <ThemedButton
                title="Regenerate Program"
                onPress={() => {
                  console.log('🚀 DEBUG: Regenerate Program button pressed (approved/edited status)');
                  setShowRegenerationModal(true);
                }}
                disabled={isUpdating}
                style={[styles.actionButton, { backgroundColor: colors.warning }]}
              />
              <ThemedButton
                title="Archive Program"
                onPress={() => {
                  console.log('🚀 DEBUG: Archive Program button pressed');
                  handleStatusUpdate('archived');
                }}
                disabled={isUpdating}
                variant="outline"
                style={[styles.actionButton, { borderColor: colors.error }]}
                textStyle={{ color: colors.error }}
              />
            </>
          )}
        </View>
      </ScrollView>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isVisible={showConfirmationModal}
        title={confirmationTitle}
        message={confirmationMessage}
        onConfirm={onConfirmAction}
        onCancel={() => setShowConfirmationModal(false)}
        confirmText="Confirm"
        cancelText="Cancel"
        isConfirming={isUpdating}
        confirmButtonVariant={confirmButtonVariant}
      />

      {/* Regeneration Modal */}
      <RegenerationNotesModal
        isVisible={showRegenerationModal}
        onClose={() => setShowRegenerationModal(false)}
        onRegenerate={handleRegenerate}
        isRegenerating={isRegenerating}
        programName={program.name}
      />

      {/* Feedback Edit Modal */}
      {selectedFeedback && (
        <CoachFeedbackEditModal
          isVisible={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          feedback={selectedFeedback}
          onSave={handleFeedbackSaved}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  messageCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  messageText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  programCard: {
    padding: 20,
    marginBottom: 20,
  },
  programHeader: {
    marginBottom: 12,
  },
  programTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  programTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginBottom: 8,
  },
  editNameButton: {
    marginLeft: 8,
    padding: 6,
    borderRadius: 6,
  },
  editHint: {
    fontSize: 12,
    fontStyle: 'italic',
    marginTop: -4,
    marginBottom: 4,
  },
  editingNameContainer: {
    flex: 1,
    marginBottom: 8,
  },
  programNameInput: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  nameEditActions: {
    flexDirection: 'row',
    gap: 8,
  },
  nameActionButton: {
    padding: 8,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 36,
  },
  saveButton: {
    // backgroundColor set dynamically
  },
  cancelButton: {
    // backgroundColor set dynamically
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
  programDescription: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 16,
    opacity: 0.8,
  },
  programMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metaText: {
    fontSize: 14,
    opacity: 0.7,
  },
  statusDescription: {
    padding: 12,
    borderRadius: 8,
  },
  statusDescriptionText: {
    fontSize: 14,
    lineHeight: 18,
  },
  notesCard: {
    padding: 20,
    marginBottom: 20,
  },
  notesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  notesTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  editButton: {
    padding: 8,
    borderRadius: 6,
  },
  notesEditContainer: {
    gap: 12,
  },
  notesInput: {
    minHeight: 100,
  },
  notesActions: {
    flexDirection: 'row',
    gap: 12,
  },
  noteActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  noteActionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  notesContent: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  feedbackSection: {
    padding: 20,
    marginBottom: 20,
  },
  feedbackSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  feedbackSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  feedbackSectionDescription: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 16,
  },
  feedbackItem: {
    padding: 16,
    marginBottom: 12,
  },
  feedbackItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  feedbackItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  feedbackItemDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  feedbackStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#FFF8E1',
  },
  feedbackStatusText: {
    fontSize: 11,
    fontWeight: '600',
  },
  feedbackItemPreview: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
    opacity: 0.8,
  },
  feedbackItemButton: {
    alignSelf: 'flex-start',
  },
  structureCard: {
    padding: 20,
    marginBottom: 20,
  },
  structureTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  weekContainer: {
    marginBottom: 24,
  },
  weekTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  weekNotes: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 12,
  },
  workoutsContainer: {
    gap: 12,
  },
  workoutItem: {
    padding: 16,
    borderRadius: 8,
  },
  workoutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  workoutTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
    marginRight: 12,
  },
  workoutMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  workoutMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  workoutMetaText: {
    fontSize: 11,
    opacity: 0.7,
  },
  workoutDescription: {
    fontSize: 12,
    opacity: 0.8,
    marginBottom: 12,
  },
  exercisesContainer: {
    gap: 8,
  },
  exerciseItem: {
    borderLeftWidth: 3,
    paddingLeft: 12,
    paddingVertical: 8,
  },
  exerciseHeader: {
    marginBottom: 6,
  },
  exerciseName: {
    fontSize: 13,
    fontWeight: '600',
  },
  exerciseDetails: {
    gap: 6,
  },
  exerciseDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  exerciseDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  exerciseDetailLabel: {
    fontSize: 11,
    opacity: 0.7,
    fontWeight: '500',
  },
  exerciseDetailValue: {
    fontSize: 11,
    fontWeight: '600',
  },
  exerciseNotes: {
    padding: 8,
    borderRadius: 4,
    marginTop: 4,
  },
  exerciseNotesText: {
    fontSize: 11,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  actionsContainer: {
    gap: 12,
    marginBottom: 32,
  },
  actionButton: {
    paddingVertical: 16,
  },
});