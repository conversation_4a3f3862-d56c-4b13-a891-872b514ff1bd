import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { checkInService, WeeklyCheckIn } from '@/services/checkInService';
import { feedbackService, CoachFeedback } from '@/services/feedbackService';
import { ThemedView, ThemedText, ThemedCard, ThemedButton } from '@/components/ThemedComponents';
import { ArrowLeft, Calendar, Star, Frown, Target, MessageSquare, Activity, Heart } from 'lucide-react-native';

export default function CoachCheckInDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { colors } = useThemeStore();
  const [checkIn, setCheckIn] = useState<WeeklyCheckIn | null>(null);
  const [feedback, setFeedback] = useState<CoachFeedback | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;
      
      setIsLoading(true);
      setErrorMessage(null);
      try {
        console.log('🔍 CoachCheckInDetailScreen: Fetching data for ID:', id);
        
        // Fetch check-in details
        try {
          const checkInData = await checkInService.fetchCheckInById(id as string);
          setCheckIn(checkInData);
          
          // If successful, try to fetch related feedback
          try {
            const feedbackData = await feedbackService.fetchFeedbackForCheckIn(id as string);
            setFeedback(feedbackData);
          } catch (feedbackError) {
            console.log('⚠️ CoachCheckInDetailScreen: No related feedback found or error:', feedbackError);
            // Continue without feedback - it's optional
          }
        } catch (checkInError) {
          console.error('❌ CoachCheckInDetailScreen: Check-in not found:', checkInError);
          setErrorMessage('Check-in not found');
        }
      } catch (error) {
        console.error('❌ CoachCheckInDetailScreen: Error fetching details:', error);
        setErrorMessage('Error loading data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Client Check-in Details</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  // If we have an error message, show it
  if (errorMessage) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Client Check-in Details</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>{errorMessage}</ThemedText>
          <ThemedButton 
            title="Go Back" 
            onPress={() => router.back()} 
            style={{ marginTop: 16 }}
          />
        </View>
      </ThemedView>
    );
  }

  // If we have neither check-in nor feedback, show error
  if (!checkIn) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Client Check-in Details</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>Check-in not found</ThemedText>
          <ThemedButton 
            title="Go Back" 
            onPress={() => router.back()} 
            style={{ marginTop: 16 }}
          />
        </View>
      </ThemedView>
    );
  }

  // If we have a check-in, show check-in details
  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Client Check-in Details</ThemedText>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Date Card */}
          <ThemedCard style={styles.dateCard}>
            <View style={styles.dateHeader}>
              <Calendar size={20} color={colors.primary} />
              <ThemedText style={styles.dateTitle}>Weekly Check-in</ThemedText>
            </View>
            <ThemedText style={styles.dateText}>
              {formatDate(checkIn.checkin_date)}
            </ThemedText>
          </ThemedCard>

          {/* Ratings Card */}
          <ThemedCard style={styles.ratingsCard}>
            <ThemedText style={styles.sectionTitle}>Ratings</ThemedText>
            
            <View style={styles.ratingsGrid}>
              <View style={[styles.ratingItem, { backgroundColor: colors.accent }]}>
                <Target size={20} color={colors.primary} />
                <ThemedText style={styles.ratingValue}>
                  {checkIn.training_performance_rating}/10
                </ThemedText>
                <ThemedText style={styles.ratingLabel}>Performance</ThemedText>
              </View>
              
              <View style={[styles.ratingItem, { backgroundColor: colors.accent }]}>
                <Activity size={20} color={colors.primary} />
                <ThemedText style={styles.ratingValue}>
                  {checkIn.recovery_rating}/10
                </ThemedText>
                <ThemedText style={styles.ratingLabel}>Recovery</ThemedText>
              </View>
              
              <View style={[styles.ratingItem, { backgroundColor: colors.accent }]}>
                <Heart size={20} color={colors.primary} />
                <ThemedText style={styles.ratingValue}>
                  {checkIn.energy_levels_rating}/10
                </ThemedText>
                <ThemedText style={styles.ratingLabel}>Energy</ThemedText>
              </View>
            </View>
          </ThemedCard>

          {/* Wins Section */}
          <ThemedCard style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Star size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Wins</ThemedText>
            </View>
            <ThemedText style={styles.sectionContent}>
              {checkIn.wins}
            </ThemedText>
          </ThemedCard>

          {/* Challenges Section */}
          <ThemedCard style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Frown size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Challenges</ThemedText>
            </View>
            <ThemedText style={styles.sectionContent}>
              {checkIn.challenges}
            </ThemedText>
          </ThemedCard>

          {/* Progress Reflection */}
          <ThemedCard style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Target size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Progress Reflection</ThemedText>
            </View>
            <ThemedText style={styles.sectionContent}>
              {checkIn.progress_reflection}
            </ThemedText>
          </ThemedCard>

          {/* Additional Notes */}
          {checkIn.additional_notes_for_coach && (
            <ThemedCard style={styles.sectionCard}>
              <View style={styles.sectionHeader}>
                <MessageSquare size={20} color={colors.primary} />
                <ThemedText style={styles.sectionTitle}>Additional Notes</ThemedText>
              </View>
              <ThemedText style={styles.sectionContent}>
                {checkIn.additional_notes_for_coach}
              </ThemedText>
            </ThemedCard>
          )}

          {/* Related Feedback Section - For coaches to see if feedback exists */}
          {feedback && (
            <ThemedCard style={styles.sectionCard}>
              <View style={styles.sectionHeader}>
                <MessageSquare size={20} color={colors.primary} />
                <ThemedText style={styles.sectionTitle}>Related Feedback</ThemedText>
              </View>
              <ThemedText style={styles.sectionContent}>
                {feedback.status === 'draft_by_ai' && 'AI-generated feedback draft is available for review.'}
                {feedback.status === 'draft_by_coach' && 'You have a draft feedback for this check-in.'}
                {feedback.status === 'published' && 'Feedback has been published to the client.'}
                {feedback.status === 'read_by_client' && 'Feedback has been read by the client.'}
              </ThemedText>
            </ThemedCard>
          )}
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  dateCard: {
    padding: 16,
    marginBottom: 16,
  },
  dateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  dateTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  dateText: {
    fontSize: 18,
    fontWeight: '600',
  },
  ratingsCard: {
    padding: 16,
    marginBottom: 16,
  },
  ratingsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 12,
  },
  ratingItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  ratingValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  ratingLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  sectionCard: {
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  feedbackCard: {
    padding: 16,
    marginBottom: 32,
  },
  feedbackHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  feedbackTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  feedbackPreview: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  viewFeedbackButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  viewFeedbackText: {
    fontSize: 14,
    fontWeight: '500',
  },
});