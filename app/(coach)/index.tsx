import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useUserStore } from '@/store/userStore';
import { useAuthStore } from '@/store/authStore';
import { profileService, Profile } from '@/services/profileService';
import { workoutService, WorkoutProgram } from '@/services/workoutService';
import { feedbackService, CoachFeedback } from '@/services/feedbackService';
import { checkInService, WeeklyCheckIn } from '@/services/checkInService';
import { ThemedView, ThemedText, ThemedCard, ThemedButton, ThemedStatusIndicator, ThemedInput } from '@/components/ThemedComponents';
import ConfirmationModal from '@/components/ConfirmationModal';
import CoachFeedbackEditModal from '@/components/CoachFeedbackEditModal';
import { Users, Calendar, Clock, Target, ChevronRight, RefreshCw, LogOut, Settings, MessageSquare, Bell, ClipboardCheck, Search, ChevronDown, ChevronUp, X } from 'lucide-react-native';

interface ClientWithProgram extends Profile {
  latestProgram?: WorkoutProgram;
  programsCount: number;
}

export default function CoachDashboardScreen() {
  const { colors } = useThemeStore();
  const { profile } = useUserStore();
  const { signOut } = useAuthStore();
  const [clients, setClients] = useState<ClientWithProgram[]>([]);
  const [pendingFeedback, setPendingFeedback] = useState<CoachFeedback[]>([]);
  const [recentCheckIns, setRecentCheckIns] = useState<WeeklyCheckIn[]>([]);
  const [selectedFeedback, setSelectedFeedback] = useState<CoachFeedback | null>(null);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [showSignOutConfirmation, setShowSignOutConfirmation] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // New state for collapsible sections
  const [showClients, setShowClients] = useState(true);
  const [showFeedback, setShowFeedback] = useState(true);
  const [showCheckIns, setShowCheckIns] = useState(true);
  
  // New state for client search
  const [searchTerm, setSearchTerm] = useState('');
  
  // New state for expanded clients
  const [expandedClientIds, setExpandedClientIds] = useState<Set<string>>(new Set());

  const fetchData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      console.log('🔍 CoachDashboard: Starting data fetch...');
      
      // Fetch all client profiles
      const clientProfiles = await profileService.getProfilesByRole('client');
      console.log('✅ CoachDashboard: Received client profiles:', clientProfiles.length);
      
      // Fetch programs for each client
      const clientsWithPrograms: ClientWithProgram[] = await Promise.all(
        clientProfiles.map(async (client) => {
          try {
            const programs = await workoutService.getWorkoutProgramsForUser(client.id);
            const latestProgram = programs.length > 0 ? programs[0] : undefined;
            
            return {
              ...client,
              latestProgram,
              programsCount: programs.length,
            };
          } catch (error) {
            console.error(`Error fetching programs for client ${client.id}:`, error);
            return {
              ...client,
              programsCount: 0,
            };
          }
        })
      );

      // Fetch pending feedback drafts
      const feedbackDrafts = await feedbackService.fetchFeedbackDrafts();
      console.log('✅ CoachDashboard: Received feedback drafts:', feedbackDrafts.length);
      
      // Fetch recent check-ins (last 7 days)
      const recentCheckins = await checkInService.fetchRecentClientCheckIns();
      console.log('✅ CoachDashboard: Received recent check-ins:', recentCheckins.length);
      
      setClients(clientsWithPrograms);
      setPendingFeedback(feedbackDrafts);
      setRecentCheckIns(recentCheckins);
    } catch (error) {
      console.error('❌ CoachDashboard: Error in fetchData:', error);
      setError('Failed to load data. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleRefresh = () => {
    fetchData(true);
  };

  const handleSignOut = () => {
    console.log('🚪 CoachDashboard: handleSignOut function called - showing confirmation modal');
    setShowSignOutConfirmation(true);
  };

  const confirmSignOut = async () => {
    console.log('✅ CoachDashboard: User confirmed sign out - starting logout process');
    try {
      setIsSigningOut(true);
      console.log('🔄 CoachDashboard: Set isSigningOut to true');
      
      console.log('📞 CoachDashboard: Calling signOut() from authStore...');
      await signOut();
      console.log('✅ CoachDashboard: signOut() completed successfully');
      
      // Hide the confirmation modal
      setShowSignOutConfirmation(false);
      
      // Navigation will be handled automatically by the auth state change
      console.log('🧭 CoachDashboard: Navigation should be handled automatically by auth state change');
      
    } catch (error) {
      console.error('❌ CoachDashboard: Error during sign out process:', error);
      setIsSigningOut(false);
      setShowSignOutConfirmation(false);
    }
  };

  const cancelSignOut = () => {
    console.log('❌ CoachDashboard: User cancelled sign out');
    setShowSignOutConfirmation(false);
  };

  const handleEditFeedback = (feedback: CoachFeedback) => {
    setSelectedFeedback(feedback);
    setShowFeedbackModal(true);
  };

  const handleFeedbackSaved = async () => {
    setShowFeedbackModal(false);
    setSelectedFeedback(null);
    await fetchData(true);
  };

  // Toggle client expansion
  const toggleClientExpansion = (clientId: string) => {
    setExpandedClientIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(clientId)) {
        newSet.delete(clientId);
      } else {
        newSet.add(clientId);
      }
      return newSet;
    });
  };

  // Filter clients based on search term
  const filteredClients = clients.filter(client => {
    if (!searchTerm.trim()) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      (client.full_name && client.full_name.toLowerCase().includes(searchLower)) ||
      (client.email && client.email.toLowerCase().includes(searchLower))
    );
  });

  const getStatusInfo = (status?: string) => {
    switch (status) {
      case 'ai_generated_pending_review':
        return {
          icon: <Clock size={16} color={colors.warning} />,
          text: 'Pending Review',
          statusType: 'warning' as const,
        };
      case 'coach_approved':
        return {
          icon: <Target size={16} color={colors.success} />,
          text: 'Approved',
          statusType: 'success' as const,
        };
      case 'active_by_client':
        return {
          icon: <Target size={16} color={colors.primary} />,
          text: 'Active',
          statusType: 'primary' as const,
        };
      case 'completed_by_client':
        return {
          icon: <Target size={16} color={colors.success} />,
          text: 'Completed',
          statusType: 'success' as const,
        };
      default:
        return {
          icon: <Clock size={16} color={colors.text} />,
          text: 'No Program',
          statusType: 'info' as const,
        };
    }
  };

  const getIntakeStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return colors.success;
      case 'in_progress':
        return colors.warning;
      default:
        return colors.error;
    }
  };

  const renderClientCard = (client: ClientWithProgram) => {
    const statusInfo = getStatusInfo(client.latestProgram?.status);
    const isExpanded = expandedClientIds.has(client.id);
    
    return (
      <ThemedCard key={client.id} style={styles.clientCard}>
        {/* Client Header - Always visible */}
        <TouchableOpacity 
          style={styles.clientHeader}
          onPress={() => toggleClientExpansion(client.id)}
        >
          <View style={styles.clientInfo}>
            <ThemedText style={styles.clientName}>{client.full_name || 'User'}</ThemedText>
            <ThemedText style={styles.clientEmail}>{client.email || 'No email'}</ThemedText>
          </View>
          {isExpanded ? (
            <ChevronUp size={20} color={colors.text} opacity={0.6} />
          ) : (
            <ChevronDown size={20} color={colors.text} opacity={0.6} />
          )}
        </TouchableOpacity>

        {/* Expanded Content */}
        {isExpanded && (
          <View style={styles.clientExpandedContent}>
            <View style={styles.clientStats}>
              <View style={styles.statItem}>
                <Users size={16} color={colors.primary} />
                <ThemedText style={styles.statLabel}>Intake:</ThemedText>
                <Text style={[
                  styles.statValue, 
                  { color: getIntakeStatusColor(client.intake_status) }
                ]}>
                  {client.intake_status === 'completed' ? 'Complete' : 
                   client.intake_status === 'in_progress' ? 'In Progress' : 'Not Started'}
                </Text>
              </View>

              <View style={styles.statItem}>
                <Calendar size={16} color={colors.primary} />
                <ThemedText style={styles.statLabel}>Programs:</ThemedText>
                <ThemedText style={styles.statValue}>{client.programsCount}</ThemedText>
              </View>
            </View>

            {client.latestProgram && (
              <View style={styles.programInfo}>
                <View style={styles.programHeader}>
                  <ThemedText style={styles.programTitle}>{client.latestProgram.name}</ThemedText>
                  <ThemedStatusIndicator
                    status={statusInfo.statusType}
                    text={statusInfo.text}
                    icon={statusInfo.icon}
                    style={styles.statusBadge}
                    textStyle={styles.statusText}
                  />
                </View>
                
                <ThemedText style={styles.programDescription} numberOfLines={2}>
                  {client.latestProgram.description}
                </ThemedText>

                <View style={styles.programMeta}>
                  <View style={styles.metaItem}>
                    <Calendar size={14} color={colors.text} />
                    <ThemedText style={styles.metaText}>
                      {client.latestProgram.duration_weeks} weeks
                    </ThemedText>
                  </View>
                  <View style={styles.metaItem}>
                    <Clock size={14} color={colors.text} />
                    <ThemedText style={styles.metaText}>
                      Created {new Date(client.latestProgram.created_at).toLocaleDateString()}
                    </ThemedText>
                  </View>
                </View>

                <ThemedButton
                  title="View Program Details"
                  onPress={() => router.push(`/(coach)/program/${client.latestProgram.id}`)}
                  variant="outline"
                  style={styles.viewProgramButton}
                />
              </View>
            )}

            {!client.latestProgram && client.intake_status === 'completed' && (
              <View style={[styles.noProgram, { backgroundColor: colors.accent }]}>
                <ThemedText style={styles.noProgramText}>
                  Client has completed intake but no program generated yet
                </ThemedText>
              </View>
            )}
          </View>
        )}
      </ThemedCard>
    );
  };

  const renderFeedbackCard = (feedback: CoachFeedback) => {
    return (
      <ThemedCard key={feedback.id} style={styles.feedbackCard} onPress={() => handleEditFeedback(feedback)}>
        <View style={styles.feedbackHeader}>
          <View style={styles.feedbackInfo}>
            <ThemedText style={styles.feedbackTitle}>AI-Generated Feedback</ThemedText>
            <ThemedText style={styles.feedbackDate}>
              Created {new Date(feedback.created_at).toLocaleDateString()}
            </ThemedText>
          </View>
          <ThemedStatusIndicator
            status="warning"
            text="Needs Review"
            icon={<Clock size={14} color={colors.warning} />}
            style={styles.feedbackStatusBadge}
            textStyle={styles.feedbackStatusText}
          />
        </View>
        
        <ThemedText style={styles.feedbackPreview} numberOfLines={2}>{feedback.feedback_content}</ThemedText>
        
        <TouchableOpacity 
          style={[styles.reviewButton, { backgroundColor: colors.primary + '20' }]}
          onPress={() => handleEditFeedback(feedback)}
        >
          <MessageSquare size={14} color={colors.primary} />
          <ThemedText style={[styles.reviewButtonText, { color: colors.primary }]}>
            Review & Edit
          </ThemedText>
        </TouchableOpacity>
      </ThemedCard>
    );
  };

  const renderCheckInCard = (checkIn: WeeklyCheckIn) => {
    return (
      <ThemedCard key={checkIn.id} style={styles.checkinCard}>
        <View style={styles.checkinHeader}>
          <View style={styles.checkinInfo}>
            <ThemedText style={styles.checkinTitle}>
              {(checkIn as any).profiles?.full_name || 'Client'}
            </ThemedText>
            <ThemedText style={styles.checkinDate}>
              Submitted {new Date(checkIn.submitted_at || '').toLocaleDateString()}
            </ThemedText>
          </View>
        </View>
        
        {checkIn.wins && (
          <View style={styles.checkinSection}>
            <ThemedText style={styles.checkinSectionTitle}>Wins:</ThemedText>
            <ThemedText style={styles.checkinContent} numberOfLines={2}>
              {checkIn.wins}
            </ThemedText>
          </View>
        )}
        
        {checkIn.challenges && (
          <View style={styles.checkinSection}>
            <ThemedText style={styles.checkinSectionTitle}>Challenges:</ThemedText>
            <ThemedText style={styles.checkinContent} numberOfLines={2}>
              {checkIn.challenges}
            </ThemedText>
          </View>
        )}
        
        <TouchableOpacity 
          style={[styles.viewButton, { backgroundColor: colors.primary + '20' }]}
          onPress={() => router.push(`/(coach)/checkin/${checkIn.id}`)}
        >
          <ThemedText style={[styles.viewButtonText, { color: colors.primary }]}>
            View Details
          </ThemedText>
          <ChevronRight size={14} color={colors.primary} />
        </TouchableOpacity>
      </ThemedCard>
    );
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <ThemedText style={styles.title}>Coach Dashboard</ThemedText>
          <ThemedText style={styles.welcomeText}>Welcome, {profile?.full_name}!</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading dashboard...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <ThemedText style={styles.title}>Coach Dashboard</ThemedText>
            <ThemedText style={styles.welcomeText}>Welcome, {profile?.full_name}!</ThemedText>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity 
              style={[styles.headerButton, { backgroundColor: colors.accent }]}
              onPress={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw 
                size={20} 
                color={colors.primary} 
                style={isRefreshing ? { transform: [{ rotate: '180deg' }] } : {}}
              />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.headerButton, { backgroundColor: colors.error + '20' }]}
              onPress={handleSignOut}
              disabled={isSigningOut}
            >
              {isSigningOut ? (
                <ActivityIndicator size="small" color={colors.error} />
              ) : (
                <LogOut size={20} color={colors.error} />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <ScrollView 
        style={styles.content} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {error && (
          <ThemedCard style={[styles.errorCard, { backgroundColor: colors.error + '20' }]}>
            <ThemedText style={[styles.errorText, { color: colors.error }]}>{error}</ThemedText>
          </ThemedCard>
        )}

        {/* Quick Stats - Dashboard Overview */}
        <View style={styles.statsOverview}>
          <ThemedText style={styles.sectionTitle}>Dashboard Overview</ThemedText>
          <View style={styles.statsGrid}>
            <ThemedCard style={styles.overviewCard}>
              <View style={styles.overviewItem}>
                <Users size={24} color={colors.primary} />
                <ThemedText style={styles.overviewNumber}>{clients.length}</ThemedText>
                <ThemedText style={styles.overviewLabel}>Total Clients</ThemedText>
              </View>
            </ThemedCard>

            <ThemedCard style={styles.overviewCard}>
              <View style={styles.overviewItem}>
                <Clock size={24} color={colors.warning} />
                <ThemedText style={styles.overviewNumber}>
                  {clients.filter(c => c.latestProgram?.status === 'ai_generated_pending_review').length}
                </ThemedText>
                <ThemedText style={styles.overviewLabel}>Programs Pending Review</ThemedText>
              </View>
            </ThemedCard>

            <ThemedCard style={styles.overviewCard}>
              <View style={styles.overviewItem}>
                <MessageSquare size={24} color={colors.info} />
                <ThemedText style={styles.overviewNumber}>
                  {pendingFeedback.length}
                </ThemedText>
                <ThemedText style={styles.overviewLabel}>Check-ins Pending Feedback</ThemedText>
              </View>
            </ThemedCard>
          </View>
        </View>

        {/* Clients Section */}
        <View style={styles.clientsSection}>
          <TouchableOpacity 
            style={styles.sectionHeader}
            onPress={() => setShowClients(!showClients)}
          >
            <View style={styles.sectionTitleContainer}>
              <Users size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Clients</ThemedText>
            </View>
            {showClients ? (
              <ChevronUp size={20} color={colors.text} />
            ) : (
              <ChevronDown size={20} color={colors.text} />
            )}
          </TouchableOpacity>
          
          {showClients && (
            <>
              {/* Search Bar */}
              <View style={styles.searchContainer}>
                <Search size={18} color={colors.text} style={styles.searchIcon} />
                <ThemedInput
                  placeholder="Search clients by name or email..."
                  value={searchTerm}
                  onChangeText={setSearchTerm}
                  style={styles.searchInput}
                />
                {searchTerm.length > 0 && (
                  <TouchableOpacity 
                    style={styles.clearSearchButton}
                    onPress={() => setSearchTerm('')}
                  >
                    <X size={16} color={colors.text} />
                  </TouchableOpacity>
                )}
              </View>
              
              {filteredClients.length === 0 ? (
                <ThemedCard style={styles.emptyState}>
                  {searchTerm ? (
                    <>
                      <Search size={48} color={colors.text} opacity={0.5} />
                      <ThemedText style={styles.emptyStateTitle}>No Matching Clients</ThemedText>
                      <ThemedText style={styles.emptyStateDescription}>
                        No clients found matching "{searchTerm}". Try a different search term.
                      </ThemedText>
                    </>
                  ) : (
                    <>
                      <Users size={48} color={colors.primary} />
                      <ThemedText style={styles.emptyStateTitle}>No Clients Yet</ThemedText>
                      <ThemedText style={styles.emptyStateDescription}>
                        Clients will appear here once they register and complete their intake forms.
                      </ThemedText>
                    </>
                  )}
                </ThemedCard>
              ) : (
                filteredClients.map(renderClientCard)
              )}
            </>
          )}
        </View>

        {/* Feedback Section */}
        <View style={styles.feedbackSection}>
          <TouchableOpacity 
            style={styles.sectionHeaderWithBadge}
            onPress={() => setShowFeedback(!showFeedback)}
          >
            <View style={styles.sectionTitleContainer}>
              <MessageSquare size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Client Feedback</ThemedText>
            </View>
            
            <View style={styles.headerRightContainer}>
              {pendingFeedback.length > 0 && (
                <View style={[styles.badge, { backgroundColor: colors.warning }]}>
                  <ThemedText style={[styles.badgeText, { color: colors.contrastText }]}>
                    {pendingFeedback.length}
                  </ThemedText>
                </View>
              )}
              {showFeedback ? (
                <ChevronUp size={20} color={colors.text} style={styles.collapseIcon} />
              ) : (
                <ChevronDown size={20} color={colors.text} style={styles.collapseIcon} />
              )}
            </View>
          </TouchableOpacity>
          
          {showFeedback && (
            <>
              <ThemedText style={styles.sectionDescription}>
                {pendingFeedback.length > 0 
                  ? "AI-generated feedback drafts that need your review before being sent to clients"
                  : "No pending feedback drafts to review at this time"}
              </ThemedText>
              
              {pendingFeedback.length > 0 ? (
                pendingFeedback.map(renderFeedbackCard)
              ) : (
                <ThemedCard style={styles.emptyFeedbackState}>
                  <MessageSquare size={32} color={colors.text} opacity={0.5} />
                  <ThemedText style={styles.emptyStateTitle}>No Pending Feedback</ThemedText>
                  <ThemedText style={styles.emptyStateDescription}>
                    When clients submit weekly check-ins, AI-generated feedback drafts will appear here for your review.
                  </ThemedText>
                </ThemedCard>
              )}
            </>
          )}
        </View>

        {/* Check-ins Section */}
        <View style={styles.checkinsSection}>
          <TouchableOpacity 
            style={styles.sectionHeaderWithBadge}
            onPress={() => setShowCheckIns(!showCheckIns)}
          >
            <View style={styles.sectionTitleContainer}>
              <ClipboardCheck size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Weekly Check-ins</ThemedText>
            </View>
            
            <View style={styles.headerRightContainer}>
              {recentCheckIns.length > 0 && (
                <View style={[styles.badge, { backgroundColor: colors.info }]}>
                  <ThemedText style={[styles.badgeText, { color: colors.contrastText }]}>
                    {recentCheckIns.length}
                  </ThemedText>
                </View>
              )}
              {showCheckIns ? (
                <ChevronUp size={20} color={colors.text} style={styles.collapseIcon} />
              ) : (
                <ChevronDown size={20} color={colors.text} style={styles.collapseIcon} />
              )}
            </View>
          </TouchableOpacity>
          
          {showCheckIns && (
            <>
              <ThemedText style={styles.sectionDescription}>
                {recentCheckIns.length > 0 
                  ? "Recent client check-ins from the past week"
                  : "No recent client check-ins to review"}
              </ThemedText>
              
              {recentCheckIns.length > 0 ? (
                recentCheckIns.map(renderCheckInCard)
              ) : (
                <ThemedCard style={styles.emptyFeedbackState}>
                  <ClipboardCheck size={32} color={colors.text} opacity={0.5} />
                  <ThemedText style={styles.emptyStateTitle}>No Recent Check-ins</ThemedText>
                  <ThemedText style={styles.emptyStateDescription}>
                    When clients submit their weekly check-ins, they will appear here for your review.
                  </ThemedText>
                </ThemedCard>
              )}
            </>
          )}
        </View>
      </ScrollView>

      <ConfirmationModal
        isVisible={showSignOutConfirmation}
        title="Sign Out"
        message="Are you sure you want to sign out of your coach account? You'll need to log in again to access the coach dashboard."
        onConfirm={confirmSignOut}
        onCancel={cancelSignOut}
        confirmText="Sign Out"
        cancelText="Cancel"
        isConfirming={isSigningOut}
        confirmButtonVariant="outline"
      />

      {/* Feedback Edit Modal */}
      {selectedFeedback && (
        <CoachFeedbackEditModal
          isVisible={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          feedback={selectedFeedback}
          onSave={handleFeedbackSaved}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  welcomeText: {
    fontSize: 16,
    opacity: 0.7,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 12,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorCard: {
    padding: 16,
    marginBottom: 20,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingVertical: 8,
  },
  sectionHeaderWithBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingVertical: 8,
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  collapseIcon: {
    marginLeft: 8,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionDescription: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 16,
  },
  badge: {
    minWidth: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
    position: 'relative',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
  },
  clearSearchButton: {
    padding: 8,
  },
  feedbackSection: {
    marginBottom: 24,
  },
  checkinsSection: {
    marginBottom: 24,
  },
  feedbackCard: {
    padding: 16,
    marginBottom: 12,
  },
  feedbackHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  feedbackInfo: {
    flex: 1,
  },
  feedbackTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  feedbackDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  feedbackStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#FFF8E1',
  },
  feedbackStatusText: {
    fontSize: 11,
    fontWeight: '600',
  },
  feedbackPreview: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
    opacity: 0.8,
  },
  reviewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  reviewButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  checkinCard: {
    padding: 16,
    marginBottom: 12,
  },
  checkinHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  checkinInfo: {
    flex: 1,
  },
  checkinTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  checkinDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  checkinStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  checkinStatusText: {
    fontSize: 11,
    fontWeight: '600',
  },
  checkinSection: {
    marginBottom: 8,
  },
  checkinSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  checkinContent: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    alignSelf: 'flex-end',
    marginTop: 8,
  },
  viewButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyFeedbackState: {
    padding: 24,
    alignItems: 'center',
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateDescription: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  statsOverview: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 12,
  },
  overviewCard: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  overviewItem: {
    alignItems: 'center',
  },
  overviewNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
  clientsSection: {
    marginBottom: 24,
  },
  clientCard: {
    padding: 20,
    marginBottom: 16,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  clientExpandedContent: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
    paddingTop: 16,
  },
  clientInfo: {
    flex: 1,
  },
  clientName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  clientEmail: {
    fontSize: 14,
    opacity: 0.7,
  },
  clientStats: {
    flexDirection: 'row',
    gap: 24,
    marginBottom: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  statValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  programInfo: {
    marginBottom: 16,
  },
  programHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  programTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '600',
    marginLeft: 4,
  },
  programDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
    marginBottom: 12,
  },
  programMeta: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 11,
    opacity: 0.7,
  },
  viewProgramButton: {
    paddingVertical: 10,
  },
  noProgram: {
    padding: 12,
    borderRadius: 8,
  },
  noProgramText: {
    fontSize: 12,
    textAlign: 'center',
    opacity: 0.8,
  },
});