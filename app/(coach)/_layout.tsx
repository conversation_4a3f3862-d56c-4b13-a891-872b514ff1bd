import { Stack, Redirect } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { useUserStore } from '@/store/userStore';
import { useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { useThemeStore } from '@/store/themeStore';

export default function CoachLayout() {
  const { session } = useAuthStore();
  const { profile, fetchProfile, isLoading } = useUserStore();
  const { colors } = useThemeStore();
  const [isCheckingRole, setIsCheckingRole] = useState(true);

  useEffect(() => {
    const checkCoachRole = async () => {
      if (session && !profile) {
        try {
          await fetchProfile();
        } catch (error) {
          console.error('Error fetching profile in coach layout:', error);
        }
      }
      setIsCheckingRole(false);
    };

    checkCoachRole();
  }, [session, profile, fetchProfile]);

  // Now that all hooks have been called, we can do conditional returns
  
  // Redirect if no session
  if (!session) {
    return <Redirect href="/(auth)/login" />;
  }

  // Show loading while checking role
  if (isLoading || isCheckingRole) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // Redirect if not a coach
  if (!profile || profile.role !== 'coach') {
    return <Redirect href="/(tabs)" />;
  }

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="index" />
      <Stack.Screen name="program/[id]" />
      <Stack.Screen name="checkin/[id]" />
    </Stack>
  );
}