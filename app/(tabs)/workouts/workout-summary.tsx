import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useActiveWorkoutStore } from '@/store/activeWorkoutStore';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard } from '@/components/ThemedComponents';
import { ArrowLeft, Target, Clock, Dumbbell, CircleCheck as CheckCircle } from 'lucide-react-native';

export default function WorkoutSummaryScreen() {
  const { colors } = useThemeStore();
  const { duration } = useLocalSearchParams<{ duration: string }>();
  const { 
    currentWorkoutPlan, 
    loggedData, 
    finishWorkout, 
    getTotalSetsLogged,
    isSaving,
  } = useActiveWorkoutStore();
  
  const [overallRpe, setOverallRpe] = useState('');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFinishWorkout = async () => {
    if (!currentWorkoutPlan) {
      router.replace('/workouts');
      return;
    }

    setIsSubmitting(true);
    try {
      await finishWorkout(
        overallRpe ? parseInt(overallRpe, 10) : undefined,
        notes.trim() || undefined
      );
      router.replace({
        pathname: '/workouts',
        params: { completed: 'true' }
      });
    } catch (error) {
      console.error('Error finishing workout:', error);
      setIsSubmitting(false);
    }
  };

  if (!currentWorkoutPlan) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Workout Summary</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>No active workout found</ThemedText>
          <ThemedButton
            title="Go to Workouts"
            onPress={() => router.replace('/workouts')}
          />
        </View>
      </ThemedView>
    );
  }

  const totalExercises = currentWorkoutPlan.workout.exercises.length;
  const exercisesLogged = Object.keys(loggedData).length;
  const totalSets = getTotalSetsLogged();

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Workout Summary</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.completionBanner}>
          <View style={[styles.completionIcon, { backgroundColor: colors.success }]}>
            <CheckCircle size={32} color={colors.contrastText} />
          </View>
          <ThemedText style={styles.completionTitle}>Workout Complete!</ThemedText>
          <ThemedText style={styles.completionSubtitle}>
            Great job on completing your workout
          </ThemedText>
        </View>

        <ThemedCard style={styles.summaryCard}>
          <ThemedText style={styles.summaryTitle}>{currentWorkoutPlan.workout.title}</ThemedText>
          
          <View style={styles.statsGrid}>
            <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
              <Clock size={20} color={colors.primary} />
              <ThemedText style={styles.statValue}>{duration || '0:00'}</ThemedText>
              <ThemedText style={styles.statLabel}>Duration</ThemedText>
            </View>
            
            <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
              <Dumbbell size={20} color={colors.primary} />
              <ThemedText style={styles.statValue}>{totalSets}</ThemedText>
              <ThemedText style={styles.statLabel}>Total Sets</ThemedText>
            </View>
            
            <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
              <Target size={20} color={colors.primary} />
              <ThemedText style={styles.statValue}>{exercisesLogged}/{totalExercises}</ThemedText>
              <ThemedText style={styles.statLabel}>Exercises</ThemedText>
            </View>
          </View>
        </ThemedCard>

        <ThemedCard style={styles.rpeCard}>
          <ThemedText style={styles.rpeTitle}>Rate Your Workout</ThemedText>
          <ThemedText style={styles.rpeSubtitle}>
            How difficult was this workout overall? (1-10)
          </ThemedText>
          
          <View style={styles.rpeInputContainer}>
            <ThemedInput
              value={overallRpe}
              onChangeText={setOverallRpe}
              placeholder="1-10"
              keyboardType="numeric"
              style={styles.rpeInput}
            />
          </View>
          
          <View style={styles.rpeScale}>
            <ThemedText style={styles.rpeScaleLabel}>Easy</ThemedText>
            <View style={styles.rpeScaleDots}>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                <TouchableOpacity
                  key={num}
                  style={[
                    styles.rpeScaleDot,
                    { 
                      backgroundColor: parseInt(overallRpe, 10) === num 
                        ? colors.primary 
                        : colors.accent 
                    }
                  ]}
                  onPress={() => setOverallRpe(num.toString())}
                />
              ))}
            </View>
            <ThemedText style={styles.rpeScaleLabel}>Hard</ThemedText>
          </View>
        </ThemedCard>

        <ThemedCard style={styles.notesCard}>
          <ThemedText style={styles.notesTitle}>Workout Notes</ThemedText>
          <ThemedText style={styles.notesSubtitle}>
            Add any notes about how this workout went
          </ThemedText>
          
          <ThemedInput
            value={notes}
            onChangeText={setNotes}
            placeholder="How did the workout feel? Any achievements or challenges?"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
            style={styles.notesInput}
          />
        </ThemedCard>

        <ThemedButton
          title={isSubmitting ? "Saving..." : "Save Workout"}
          onPress={handleFinishWorkout}
          disabled={isSubmitting || isSaving}
          style={[styles.saveButton, { backgroundColor: colors.success }]}
        />
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  completionBanner: {
    alignItems: 'center',
    marginBottom: 24,
  },
  completionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  completionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  completionSubtitle: {
    fontSize: 16,
    opacity: 0.8,
    textAlign: 'center',
  },
  summaryCard: {
    padding: 20,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  rpeCard: {
    padding: 20,
    marginBottom: 16,
  },
  rpeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  rpeSubtitle: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 16,
  },
  rpeInputContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  rpeInput: {
    width: 100,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
  },
  rpeScale: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  rpeScaleLabel: {
    fontSize: 12,
    opacity: 0.7,
    width: 40,
  },
  rpeScaleDots: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 8,
  },
  rpeScaleDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  notesCard: {
    padding: 20,
    marginBottom: 24,
  },
  notesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  notesSubtitle: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 16,
  },
  notesInput: {
    minHeight: 120,
  },
  saveButton: {
    paddingVertical: 16,
    marginBottom: 32,
  },
});