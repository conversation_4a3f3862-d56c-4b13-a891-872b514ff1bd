import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { workoutLoggingService, DetailedWorkoutLog, WorkoutExerciseLog } from '@/services/workoutLoggingService';
import { profileService } from '@/services/profileService';
import { ThemedView, ThemedText, ThemedCard, ThemedStatusIndicator } from '@/components/ThemedComponents';
import ExerciseVideoModal from '@/components/ExerciseVideoModal';
import { ArrowLeft, Calendar, Clock, Target, Dumbbell, MessageSquare, Play, BarChart } from 'lucide-react-native';

export default function WorkoutHistoryDetailScreen() {
  const { colors } = useThemeStore();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [workout, setWorkout] = useState<DetailedWorkoutLog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedExercise, setSelectedExercise] = useState<{
    name: string;
    videoUrl?: string;
  } | null>(null);

  useEffect(() => {
    const fetchWorkoutDetails = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        const workoutDetails = await workoutLoggingService.fetchDetailedWorkoutLog(id);
        setWorkout(workoutDetails);
      } catch (error) {
        console.error('Error fetching workout details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkoutDetails();
  }, [id]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString(undefined, {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  const formatWeight = (weightKg?: number) => {
    if (!weightKg) return '-';
    const weightLbs = profileService.convertKgToLbs(weightKg);
    return `${Math.round(weightLbs)} lbs`;
  };

  const handleShowVideo = (exercise: WorkoutExerciseLog) => {
    if (exercise.exercise?.video_url) {
      setSelectedExercise({
        name: exercise.exercise.name,
        videoUrl: exercise.exercise.video_url,
      });
    }
  };

  const getRpeColor = (rpe?: number) => {
    if (!rpe) return colors.text;
    if (rpe <= 3) return colors.success;
    if (rpe <= 6) return colors.info;
    if (rpe <= 8) return colors.warning;
    return colors.error;
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Workout Details</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading workout details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (!workout) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Workout Details</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>Workout not found</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Workout Details</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ThemedCard style={styles.summaryCard}>
          <ThemedText style={styles.workoutTitle}>{workout.workout_name_actual}</ThemedText>
          
          <View style={styles.metaInfo}>
            <View style={styles.metaItem}>
              <Calendar size={16} color={colors.primary} />
              <ThemedText style={styles.metaText}>
                {formatDate(workout.completed_at)}
              </ThemedText>
            </View>
            
            <View style={styles.metaItem}>
              <Clock size={16} color={colors.primary} />
              <ThemedText style={styles.metaText}>
                {formatTime(workout.completed_at)}
              </ThemedText>
            </View>
          </View>

          <View style={styles.statsGrid}>
            <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
              <Clock size={20} color={colors.primary} />
              <ThemedText style={styles.statValue}>
                {formatDuration(workout.duration_seconds)}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Duration</ThemedText>
            </View>
            
            <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
              <Dumbbell size={20} color={colors.primary} />
              <ThemedText style={styles.statValue}>
                {workout.exercise_logs.length}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Exercises</ThemedText>
            </View>
            
            {workout.overall_session_rpe && (
              <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
                <Target size={20} color={getRpeColor(workout.overall_session_rpe)} />
                <ThemedText style={[styles.statValue, { color: getRpeColor(workout.overall_session_rpe) }]}>
                  {workout.overall_session_rpe}
                </ThemedText>
                <ThemedText style={styles.statLabel}>RPE</ThemedText>
              </View>
            )}
          </View>

          {workout.client_notes_for_session && (
            <View style={[styles.notesSection, { backgroundColor: colors.accent }]}>
              <View style={styles.notesHeader}>
                <MessageSquare size={16} color={colors.primary} />
                <ThemedText style={styles.notesTitle}>Workout Notes:</ThemedText>
              </View>
              <ThemedText style={styles.notesText}>
                {workout.client_notes_for_session}
              </ThemedText>
            </View>
          )}
        </ThemedCard>

        <ThemedText style={styles.sectionTitle}>Exercises</ThemedText>

        {workout.exercise_logs.map((exerciseLog) => (
          <ThemedCard key={exerciseLog.id} style={styles.exerciseCard}>
            <View style={styles.exerciseHeader}>
              <View style={styles.exerciseInfo}>
                <ThemedText style={styles.exerciseName}>
                  {exerciseLog.exercise?.name || 'Unknown Exercise'}
                </ThemedText>
                {exerciseLog.exercise?.target_muscles_primary && (
                  <ThemedText style={styles.exerciseMuscles}>
                    {exerciseLog.exercise.target_muscles_primary.join(', ')}
                  </ThemedText>
                )}
              </View>
              
              {exerciseLog.exercise?.video_url && (
                <TouchableOpacity
                  style={[styles.videoButton, { backgroundColor: colors.primary }]}
                  onPress={() => handleShowVideo(exerciseLog)}
                >
                  <Play size={16} color={colors.contrastText} />
                </TouchableOpacity>
              )}
            </View>

            <View style={styles.setsContainer}>
              <View style={[styles.setHeader, { borderBottomColor: colors.accent }]}>
                <ThemedText style={[styles.setHeaderText, styles.setColumn]}>Set</ThemedText>
                <ThemedText style={[styles.setHeaderText, styles.repsColumn]}>Reps</ThemedText>
                <ThemedText style={[styles.setHeaderText, styles.weightColumn]}>Weight</ThemedText>
                <ThemedText style={[styles.setHeaderText, styles.rpeColumn]}>RPE</ThemedText>
              </View>
              
              {exerciseLog.sets_logged.map((set, index) => (
                <View key={index} style={[styles.setRow, { borderBottomColor: colors.accent + '50' }]}>
                  <ThemedText style={[styles.setCell, styles.setColumn]}>{set.set_number}</ThemedText>
                  <ThemedText style={[styles.setCell, styles.repsColumn]}>
                    {set.reps_actual || (set.duration_seconds_actual ? `${set.duration_seconds_actual}s` : '-')}
                  </ThemedText>
                  <ThemedText style={[styles.setCell, styles.weightColumn]}>
                    {formatWeight(set.weight_kg_actual)}
                  </ThemedText>
                  <ThemedText style={[styles.setCell, styles.rpeColumn, { color: getRpeColor(set.rpe_actual) }]}>
                    {set.rpe_actual || '-'}
                  </ThemedText>
                </View>
              ))}
            </View>

            {exerciseLog.exercise_notes && (
              <View style={[styles.exerciseNotes, { backgroundColor: colors.accent }]}>
                <ThemedText style={styles.exerciseNotesText}>
                  {exerciseLog.exercise_notes}
                </ThemedText>
              </View>
            )}
          </ThemedCard>
        ))}

        {/* Workout Summary Section */}
        <ThemedCard style={styles.summaryStatsCard}>
          <View style={styles.summaryStatsHeader}>
            <BarChart size={20} color={colors.primary} />
            <ThemedText style={styles.summaryStatsTitle}>Workout Summary</ThemedText>
          </View>
          
          <View style={styles.summaryStatsList}>
            <View style={styles.summaryStatsItem}>
              <ThemedText style={styles.summaryStatsLabel}>Total Sets:</ThemedText>
              <ThemedText style={styles.summaryStatsValue}>
                {workout.exercise_logs.reduce((total, ex) => total + ex.sets_logged.length, 0)}
              </ThemedText>
            </View>
            
            <View style={styles.summaryStatsItem}>
              <ThemedText style={styles.summaryStatsLabel}>Average RPE:</ThemedText>
              <ThemedText style={styles.summaryStatsValue}>
                {(() => {
                  const rpeValues = workout.exercise_logs.flatMap(ex => 
                    ex.sets_logged.filter(set => set.rpe_actual).map(set => set.rpe_actual!)
                  );
                  if (rpeValues.length === 0) return '-';
                  const avg = rpeValues.reduce((sum, rpe) => sum + rpe, 0) / rpeValues.length;
                  return avg.toFixed(1);
                })()}
              </ThemedText>
            </View>
            
            <View style={styles.summaryStatsItem}>
              <ThemedText style={styles.summaryStatsLabel}>Total Volume:</ThemedText>
              <ThemedText style={styles.summaryStatsValue}>
                {(() => {
                  const totalVolume = workout.exercise_logs.reduce((total, ex) => {
                    return total + ex.sets_logged.reduce((setTotal, set) => {
                      const reps = set.reps_actual || 0;
                      const weight = set.weight_kg_actual || 0;
                      return setTotal + (reps * weight);
                    }, 0);
                  }, 0);
                  
                  // Convert kg to lbs for display
                  const volumeLbs = Math.round(profileService.convertKgToLbs(totalVolume));
                  return volumeLbs > 0 ? `${volumeLbs} lbs` : '-';
                })()}
              </ThemedText>
            </View>
          </View>
        </ThemedCard>
      </ScrollView>

      <ExerciseVideoModal
        isVisible={!!selectedExercise}
        onClose={() => setSelectedExercise(null)}
        exerciseName={selectedExercise?.name || ''}
        videoUrl={selectedExercise?.videoUrl}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 20,
  },
  summaryCard: {
    padding: 20,
    marginBottom: 24,
  },
  workoutTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  metaInfo: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  metaText: {
    fontSize: 14,
    opacity: 0.8,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 16,
  },
  statItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  notesSection: {
    padding: 12,
    borderRadius: 8,
  },
  notesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 4,
  },
  notesTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    marginTop: 8,
  },
  exerciseCard: {
    padding: 16,
    marginBottom: 12,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  exerciseInfo: {
    flex: 1,
    marginRight: 12,
  },
  exerciseName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  exerciseMuscles: {
    fontSize: 12,
    opacity: 0.7,
    fontStyle: 'italic',
  },
  videoButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  setsContainer: {
    marginBottom: 12,
  },
  setHeader: {
    flexDirection: 'row',
    paddingVertical: 8,
    borderBottomWidth: 1,
    marginBottom: 8,
  },
  setHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.7,
  },
  setRow: {
    flexDirection: 'row',
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  setCell: {
    fontSize: 14,
  },
  setColumn: {
    width: '15%',
    textAlign: 'center',
  },
  repsColumn: {
    width: '25%',
    textAlign: 'center',
  },
  weightColumn: {
    width: '35%',
    textAlign: 'center',
  },
  rpeColumn: {
    width: '25%',
    textAlign: 'center',
  },
  exerciseNotes: {
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  exerciseNotesText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  summaryStatsCard: {
    padding: 16,
    marginBottom: 32,
  },
  summaryStatsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  summaryStatsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryStatsList: {
    gap: 8,
  },
  summaryStatsItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  summaryStatsLabel: {
    fontSize: 14,
    opacity: 0.8,
  },
  summaryStatsValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});