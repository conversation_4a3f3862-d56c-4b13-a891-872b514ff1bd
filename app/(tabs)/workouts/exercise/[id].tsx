import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { workoutService } from '@/services/workoutService';
import { ThemedView, ThemedText, ThemedCard } from '@/components/ThemedComponents';
import EmbeddedVideoPlayer from '@/components/EmbeddedVideoPlayer';
import { ArrowLeft, Target, Dumbbell, Info } from 'lucide-react-native';

interface Exercise {
  id: string;
  name: string;
  description?: string;
  video_url?: string;
  target_muscles_primary: string[];
  target_muscles_secondary: string[];
  equipment_required: string[];
  difficulty_level: string;
}

export default function ExerciseDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { colors } = useThemeStore();
  const [exercise, setExercise] = useState<Exercise | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchExerciseDetails();
    }
  }, [id]);

  const fetchExerciseDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Get all exercises and find the one with matching ID
      const exercises = await workoutService.getExercises();
      const foundExercise = exercises.find(ex => ex.id === id);
      
      if (foundExercise) {
        setExercise(foundExercise);
      } else {
        setError('Exercise not found');
      }
    } catch (error) {
      console.error('Error fetching exercise details:', error);
      setError('Failed to load exercise details');
    } finally {
      setIsLoading(false);
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner':
        return colors.success;
      case 'intermediate':
        return colors.warning;
      case 'advanced':
        return colors.error;
      default:
        return colors.text;
    }
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Exercise Details</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading exercise details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error || !exercise) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Exercise Details</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>{error || 'Exercise not found'}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Exercise Details</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Exercise Header */}
        <ThemedCard style={styles.exerciseCard}>
          <View style={styles.exerciseHeader}>
            <ThemedText style={styles.exerciseName}>{exercise.name}</ThemedText>
            <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(exercise.difficulty_level) + '20' }]}>
              <ThemedText style={[styles.difficultyText, { color: getDifficultyColor(exercise.difficulty_level) }]}>
                {exercise.difficulty_level}
              </ThemedText>
            </View>
          </View>

          {exercise.description && (
            <ThemedText style={styles.exerciseDescription}>{exercise.description}</ThemedText>
          )}
        </ThemedCard>

        {/* Video Player */}
        {exercise.video_url && (
          <ThemedCard style={styles.videoCard}>
            <ThemedText style={styles.sectionTitle}>Exercise Demonstration</ThemedText>
            <EmbeddedVideoPlayer videoUrl={exercise.video_url} style={styles.videoPlayer} />
          </ThemedCard>
        )}

        {/* Target Muscles */}
        {exercise.target_muscles_primary.length > 0 && (
          <ThemedCard style={styles.musclesCard}>
            <View style={styles.sectionHeader}>
              <Target size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Target Muscles</ThemedText>
            </View>
            
            <View style={styles.muscleSection}>
              <ThemedText style={styles.muscleLabel}>Primary:</ThemedText>
              <View style={styles.muscleTagsContainer}>
                {exercise.target_muscles_primary.map((muscle, index) => (
                  <View key={index} style={[styles.muscleTag, { backgroundColor: colors.primary + '20' }]}>
                    <ThemedText style={[styles.muscleTagText, { color: colors.primary }]}>
                      {muscle}
                    </ThemedText>
                  </View>
                ))}
              </View>
            </View>

            {exercise.target_muscles_secondary.length > 0 && (
              <View style={styles.muscleSection}>
                <ThemedText style={styles.muscleLabel}>Secondary:</ThemedText>
                <View style={styles.muscleTagsContainer}>
                  {exercise.target_muscles_secondary.map((muscle, index) => (
                    <View key={index} style={[styles.muscleTag, { backgroundColor: colors.accent }]}>
                      <ThemedText style={styles.muscleTagText}>{muscle}</ThemedText>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </ThemedCard>
        )}

        {/* Equipment Required */}
        {exercise.equipment_required.length > 0 && (
          <ThemedCard style={styles.equipmentCard}>
            <View style={styles.sectionHeader}>
              <Dumbbell size={20} color={colors.primary} />
              <ThemedText style={styles.sectionTitle}>Equipment Required</ThemedText>
            </View>
            
            <View style={styles.equipmentTagsContainer}>
              {exercise.equipment_required.map((equipment, index) => (
                <View key={index} style={[styles.equipmentTag, { backgroundColor: colors.accent }]}>
                  <ThemedText style={styles.equipmentTagText}>{equipment}</ThemedText>
                </View>
              ))}
            </View>
          </ThemedCard>
        )}

        {/* Exercise Tips */}
        <ThemedCard style={styles.tipsCard}>
          <View style={styles.sectionHeader}>
            <Info size={20} color={colors.primary} />
            <ThemedText style={styles.sectionTitle}>Exercise Tips</ThemedText>
          </View>
          
          <View style={styles.tipsList}>
            <ThemedText style={styles.tipItem}>• Focus on proper form over heavy weight</ThemedText>
            <ThemedText style={styles.tipItem}>• Control the movement throughout the full range of motion</ThemedText>
            <ThemedText style={styles.tipItem}>• Breathe properly - exhale during the exertion phase</ThemedText>
            <ThemedText style={styles.tipItem}>• Start with lighter weights if you're new to this exercise</ThemedText>
            <ThemedText style={styles.tipItem}>• Stop if you feel any pain or discomfort</ThemedText>
          </View>
        </ThemedCard>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  exerciseCard: {
    padding: 20,
    marginBottom: 16,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  exerciseName: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 12,
  },
  difficultyBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
  },
  exerciseDescription: {
    fontSize: 16,
    lineHeight: 22,
    opacity: 0.8,
  },
  videoCard: {
    padding: 20,
    marginBottom: 16,
  },
  videoPlayer: {
    marginTop: 12,
  },
  musclesCard: {
    padding: 20,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  muscleSection: {
    marginBottom: 12,
  },
  muscleLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
  },
  muscleTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  muscleTag: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  muscleTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  equipmentCard: {
    padding: 20,
    marginBottom: 16,
  },
  equipmentTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  equipmentTag: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  equipmentTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  tipsCard: {
    padding: 20,
    marginBottom: 32,
  },
  tipsList: {
    gap: 8,
  },
  tipItem: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
});