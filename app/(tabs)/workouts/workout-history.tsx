import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, RefreshControl, Text } from 'react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { workoutLoggingService, WorkoutLog } from '@/services/workoutLoggingService';
import { ThemedView, ThemedText, ThemedCard } from '@/components/ThemedComponents';
import WorkoutHistoryCard from '@/components/WorkoutHistoryCard';
import { ArrowLeft, Calendar, Clock, Dumbbell, Target, History } from 'lucide-react-native';

export default function WorkoutHistoryScreen() {
  const { colors } = useThemeStore();
  const [workouts, setWorkouts] = useState<WorkoutLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true); // Track if there's more data to load
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [stats, setStats] = useState({
    totalWorkouts: 0,
    totalDuration: 0,
    averageRpe: 0,
    lastWorkoutDate: undefined as string | undefined,
  });

  const fetchWorkouts = async (offset = 0, refresh = false) => {
    console.log('🔄 WorkoutHistoryScreen: fetchWorkouts called with offset:', offset, 'refresh:', refresh);
    
    try {
      if (refresh) {
        console.log('🔄 WorkoutHistoryScreen: Setting isRefreshing to true');
        setIsRefreshing(true);
        // Reset hasMore on refresh since we're starting over
        setHasMore(true);
      } else if (offset === 0) {
        console.log('🔄 WorkoutHistoryScreen: Setting isLoading to true');
        setIsLoading(true);
      } else {
        console.log('🔄 WorkoutHistoryScreen: Setting isLoadingMore to true');
        setIsLoadingMore(true);
      }

      setHasError(false);
      setErrorMessage('');

      console.log('🔍 WorkoutHistoryScreen: Calling workoutLoggingService.fetchWorkoutHistory');
      const fetchedWorkouts = await workoutLoggingService.fetchWorkoutHistory(20, offset);
      console.log('✅ WorkoutHistoryScreen: Received', fetchedWorkouts.length, 'workouts');
      
      // If we got fewer items than the limit, there are no more items to load
      if (fetchedWorkouts.length < 20) {
        console.log('ℹ️ WorkoutHistoryScreen: No more workouts to load (received fewer than limit)');
        setHasMore(false);
      }
      
      if (offset === 0 || refresh) {
        console.log('🔄 WorkoutHistoryScreen: Setting workouts state with new data');
        setWorkouts(fetchedWorkouts);
      } else {
        console.log('🔄 WorkoutHistoryScreen: Appending new workouts to existing data');
        setWorkouts(prevWorkouts => [...prevWorkouts, ...fetchedWorkouts]);
      }
    } catch (error) {
      console.error('❌ WorkoutHistoryScreen: Error fetching workout history:', error);
      setHasError(true);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to load workout history');
    } finally {
      console.log('🔄 WorkoutHistoryScreen: Resetting loading states');
      setIsLoading(false);
      setIsRefreshing(false);
      setIsLoadingMore(false);
    }
  };

  const fetchStats = async () => {
    console.log('📊 WorkoutHistoryScreen: Fetching workout stats');
    try {
      const workoutStats = await workoutLoggingService.getWorkoutStats();
      console.log('✅ WorkoutHistoryScreen: Received workout stats:', workoutStats);
      setStats(workoutStats);
    } catch (error) {
      console.error('❌ WorkoutHistoryScreen: Error fetching workout stats:', error);
    }
  };

  useEffect(() => {
    console.log('🔄 WorkoutHistoryScreen: Initial useEffect running');
    fetchWorkouts();
    fetchStats();
  }, []);

  const handleRefresh = useCallback(() => {
    console.log('🔄 WorkoutHistoryScreen: handleRefresh called');
    fetchWorkouts(0, true);
    fetchStats();
  }, []);

  const handleLoadMore = () => {
    console.log('🔄 WorkoutHistoryScreen: handleLoadMore called');
    // Only load more if we're not already loading and there's more data to load
    if (isLoadingMore || workouts.length === 0 || !hasMore) {
      console.log('⚠️ WorkoutHistoryScreen: Skipping load more - already loading, no workouts, or no more data');
      return;
    }
    console.log('🔄 WorkoutHistoryScreen: Loading more workouts from offset:', workouts.length);
    fetchWorkouts(workouts.length);
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  const renderFooter = () => {
    // Only show loading footer if we're loading more AND there's more data to load
    if (!isLoadingMore || !hasMore) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={colors.primary} />
        <ThemedText style={styles.footerText}>Loading more...</ThemedText>
      </View>
    );
  };

  console.log('🔄 WorkoutHistoryScreen: Rendering with states:', {
    isLoading,
    isRefreshing,
    isLoadingMore,
    workoutsCount: workouts.length,
    hasMore,
    hasError
  });

  if (isLoading && !isRefreshing && workouts.length === 0) {
    console.log('🔄 WorkoutHistoryScreen: Rendering loading state');
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Workout History</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading workout history...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (hasError) {
    console.log('🔄 WorkoutHistoryScreen: Rendering error state');
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Workout History</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorTitle}>Something went wrong</ThemedText>
          <ThemedText style={styles.errorText}>{errorMessage || 'Failed to load workout history'}</ThemedText>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={handleRefresh}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Workout History</ThemedText>
      </View>

      <ThemedCard style={styles.statsCard}>
        <View style={styles.statsHeader}>
          <View style={styles.statsHeaderLeft}>
            <History size={20} color={colors.primary} />
            <ThemedText style={styles.statsTitle}>Your Workout Stats</ThemedText>
          </View>
          <ThemedText style={styles.statsCount}>
            {stats.totalWorkouts} total
          </ThemedText>
        </View>
        
        <View style={styles.statsGrid}>
          <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
            <Dumbbell size={24} color={colors.primary} />
            <ThemedText style={styles.statValue}>{stats.totalWorkouts}</ThemedText>
            <ThemedText style={styles.statLabel}>Workouts</ThemedText>
          </View>
          
          <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
            <Clock size={24} color={colors.primary} />
            <ThemedText style={styles.statValue}>
              {formatDuration(stats.totalDuration)}
            </ThemedText>
            <ThemedText style={styles.statLabel}>Total Time</ThemedText>
          </View>
          
          <View style={[styles.statItem, { backgroundColor: colors.accent }]}>
            <Target size={24} color={colors.primary} />
            <ThemedText style={styles.statValue}>
              {stats.averageRpe > 0 ? stats.averageRpe.toFixed(1) : '-'}
            </ThemedText>
            <ThemedText style={styles.statLabel}>Avg. RPE</ThemedText>
          </View>
        </View>
      </ThemedCard>

      <View style={styles.listContainer}>
        <ThemedText style={styles.sectionTitle}>Workout Log</ThemedText>
        
        <FlatList
          data={workouts}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <WorkoutHistoryCard
              workout={item}
              onPress={() => router.push(`/workouts/workout-history/${item.id}`)}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Dumbbell size={48} color={colors.primary} opacity={0.5} />
              <ThemedText style={styles.emptyTitle}>No Workouts Yet</ThemedText>
              <ThemedText style={styles.emptyText}>
                Your completed workouts will appear here. Start a workout to begin tracking your progress!
              </ThemedText>
            </View>
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.7,
  },
  retryButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  statsCard: {
    margin: 20,
    marginBottom: 0,
    padding: 20,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statsHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statsCount: {
    fontSize: 14,
    opacity: 0.7,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  statItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  listContent: {
    paddingBottom: 20,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  footerLoader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  footerText: {
    marginLeft: 8,
    fontSize: 14,
  },
});