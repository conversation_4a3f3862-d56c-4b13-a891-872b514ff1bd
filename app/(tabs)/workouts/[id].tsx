import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useWorkoutStore } from '@/store/workoutStore';
import { ThemedView, ThemedText, ThemedCard, ThemedButton, ThemedStatusIndicator, ThemedSelectionOption } from '@/components/ThemedComponents';
import ExerciseListItemDetailed from '@/components/ExerciseListItemDetailed';
import { ProgramWaitingState } from '@/components/ProgramWaitingState';
import { ArrowLeft, Calendar, Clock, Target, Dumbbell, User, ChevronLeft, ChevronRight, History, CheckCircle, Circle } from 'lucide-react-native';
import { workoutCompletionService, WorkoutCompletionStatus, WorkoutRecommendation } from '../../../src/services/workoutCompletionService';

export default function WorkoutDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { colors } = useThemeStore();
  const { currentProgramDetails, fetchProgramDetails, startProgram, isLoading } = useWorkoutStore();
  const [isStarting, setIsStarting] = useState(false);
  const [selectedWeekNumber, setSelectedWeekNumber] = useState(1);
  const [accessError, setAccessError] = useState<string | null>(null);
  const [workoutCompletions, setWorkoutCompletions] = useState<WorkoutCompletionStatus[]>([]);
  const [workoutRecommendation, setWorkoutRecommendation] = useState<WorkoutRecommendation | null>(null);
  const [isLoadingCompletions, setIsLoadingCompletions] = useState(false);

  useEffect(() => {
    if (id) {
      fetchProgramDetails(id).catch((error) => {
        console.error('Error fetching program details:', error);
        if (error.message === 'Program not yet approved for access') {
          setAccessError('Program not yet approved for access');
        } else if (error.message === 'Unauthorized access to program') {
          setAccessError('Unauthorized access to program');
          // Redirect to workouts list after showing error
          setTimeout(() => {
            router.replace('/(tabs)/workouts');
          }, 2000);
        } else {
          setAccessError('Unable to load program details');
        }
      });
    }
  }, [id, fetchProgramDetails]);

  // Fetch workout completions when program details are loaded
  useEffect(() => {
    if (currentProgramDetails && !isLoadingCompletions) {
      fetchWorkoutCompletions();
    }
  }, [currentProgramDetails]);

  const handleStartProgram = async () => {
    if (!currentProgramDetails?.program) return;
    
    try {
      setIsStarting(true);
      await startProgram(currentProgramDetails.program.id);
    } catch (error) {
      console.error('Error starting program:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const handleStartWorkout = async (workout) => {
    if (!currentProgramDetails?.program) return;
    
    // If program is in coach_approved status, start it first
    if (currentProgramDetails.program.status === 'coach_approved') {
      try {
        setIsStarting(true);
        await startProgram(currentProgramDetails.program.id);
        setIsStarting(false);
      } catch (error) {
        console.error('Error starting program:', error);
        setIsStarting(false);
        return;
      }
    }
    
    // Navigate to active workout screen with the selected workout
    router.push({
      pathname: '/workouts/active-workout',
      params: { id: currentProgramDetails.program.id }
    });
  };

  const handleExercisePress = (exerciseId: string) => {
    router.push(`/workouts/exercise/${exerciseId}`);
  };

  // Fetch workout completion status
  const fetchWorkoutCompletions = async () => {
    if (!currentProgramDetails?.program?.id) return;

    setIsLoadingCompletions(true);
    try {
      // Get all workout IDs from the program
      const allWorkoutIds = currentProgramDetails.weeks
        .flatMap(week => week.workouts.map(workout => workout.id));

      // Fetch completion status and recommendation
      const [completions, recommendation] = await Promise.all([
        workoutCompletionService.getWorkoutCompletionStatus(allWorkoutIds),
        workoutCompletionService.getWorkoutRecommendation(currentProgramDetails.program.id)
      ]);

      setWorkoutCompletions(completions);
      setWorkoutRecommendation(recommendation);
    } catch (error) {
      console.error('Error fetching workout completions:', error);
    } finally {
      setIsLoadingCompletions(false);
    }
  };

  // Helper function to check if a workout is completed
  const isWorkoutCompleted = (workoutId: string): boolean => {
    return workoutCompletions.some(completion =>
      completion.workoutId === workoutId && completion.isCompleted
    );
  };

  // Helper function to check if a workout is the next recommended one
  const isNextRecommendedWorkout = (workoutId: string): boolean => {
    return workoutRecommendation?.nextWorkoutId === workoutId;
  };

  const handlePreviousWeek = () => {
    if (selectedWeekNumber > 1) {
      setSelectedWeekNumber(selectedWeekNumber - 1);
    }
  };

  const handleNextWeek = () => {
    if (currentProgramDetails && selectedWeekNumber < currentProgramDetails.weeks.length) {
      setSelectedWeekNumber(selectedWeekNumber + 1);
    }
  };

  // Handle access errors for unapproved programs
  if (accessError === 'Program not yet approved for access') {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Program Under Review</ThemedText>
        </View>
        <ScrollView
          style={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContentContainer}
        >
          <ProgramWaitingState
            programId={id}
            onRefresh={() => {
              setAccessError(null);
              if (id) {
                fetchProgramDetails(id).catch((error) => {
                  if (error.message === 'Program not yet approved for access') {
                    setAccessError('Program not yet approved for access');
                  }
                });
              }
            }}
            onContactSupport={() => {
              Alert.alert(
                'Contact Support',
                'Please contact our support team for assistance with your program review.',
                [{ text: 'OK' }]
              );
            }}
          />
        </ScrollView>
      </ThemedView>
    );
  }

  // Handle other access errors
  if (accessError) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Access Error</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>{accessError}</ThemedText>
          <ThemedButton
            title="Go Back"
            onPress={() => router.back()}
            style={styles.errorButton}
          />
        </View>
      </ThemedView>
    );
  }

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Workout Program</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading program details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (!currentProgramDetails) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Workout Program</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>Program not found</ThemedText>
          <ThemedButton title="Go Back" onPress={() => router.back()} />
        </View>
      </ThemedView>
    );
  }

  const { program, weeks } = currentProgramDetails;
  const selectedWeek = weeks.find(week => week.week_number === selectedWeekNumber);

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>{program.name}</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Coach Notes Section - Only show if there are notes */}
        {program.coach_notes_for_client && (
          <ThemedCard style={styles.notesCard}>
            <View style={styles.notesHeader}>
              <User size={16} color={colors.primary} />
              <ThemedText style={styles.notesTitle}>Coach Notes:</ThemedText>
            </View>
            <ThemedText style={styles.notesText}>
              {program.coach_notes_for_client}
            </ThemedText>
          </ThemedCard>
        )}

        {/* Week Selector */}
        <View style={styles.weekSelectorSection}>
          <ThemedText style={styles.sectionTitle}>Week {selectedWeekNumber}</ThemedText>
          
          <View style={styles.weekSelectorContainer}>
            {/* Navigation Arrows */}
            <TouchableOpacity
              style={[
                styles.weekNavButton,
                { backgroundColor: colors.accent },
                selectedWeekNumber === 1 && styles.weekNavButtonDisabled
              ]}
              onPress={handlePreviousWeek}
              disabled={selectedWeekNumber === 1}
            >
              <ChevronLeft 
                size={20} 
                color={selectedWeekNumber === 1 ? colors.text + '40' : colors.primary} 
              />
            </TouchableOpacity>

            {/* Week Selector */}
            <View style={styles.weekSelector}>
              <View style={styles.weekSelectorContent}>
                {weeks.map((week) => (
                  <ThemedSelectionOption
                    key={week.id}
                    selected={selectedWeekNumber === week.week_number}
                    onPress={() => setSelectedWeekNumber(week.week_number)}
                    style={styles.weekOption}
                  >
                    <ThemedText
                      style={[
                        styles.weekOptionText,
                        { color: selectedWeekNumber === week.week_number ? colors.contrastText : colors.text }
                      ]}
                    >
                      Week {week.week_number}
                    </ThemedText>
                  </ThemedSelectionOption>
                ))}
              </View>
            </View>

            {/* Navigation Arrows */}
            <TouchableOpacity
              style={[
                styles.weekNavButton,
                { backgroundColor: colors.accent },
                selectedWeekNumber === weeks.length && styles.weekNavButtonDisabled
              ]}
              onPress={handleNextWeek}
              disabled={selectedWeekNumber === weeks.length}
            >
              <ChevronRight 
                size={20} 
                color={selectedWeekNumber === weeks.length ? colors.text + '40' : colors.primary} 
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Selected Week Content */}
        {selectedWeek && (
          <View style={styles.weekContent}>
            {selectedWeek.notes && (
              <ThemedCard style={styles.weekNotesCard}>
                <ThemedText style={styles.weekNotesText}>{selectedWeek.notes}</ThemedText>
              </ThemedCard>
            )}

            <View style={styles.workoutsContainer}>
              {selectedWeek.workouts
                .sort((a, b) => a.order_in_week - b.order_in_week)
                .map((workout) => {
                  const isCompleted = isWorkoutCompleted(workout.id);
                  const isNextRecommended = isNextRecommendedWorkout(workout.id);

                  return (
                    <ThemedCard
                      key={workout.id}
                      style={[
                        styles.workoutCard,
                        isCompleted && styles.completedWorkoutCard,
                        isNextRecommended && styles.recommendedWorkoutCard
                      ]}
                    >
                      <View style={styles.workoutHeader}>
                        <View style={styles.workoutTitleContainer}>
                          <ThemedText style={[
                            styles.workoutTitle,
                            isCompleted && styles.completedWorkoutTitle
                          ]}>
                            {workout.title}
                          </ThemedText>
                          {isNextRecommended && (
                            <View style={[styles.recommendedBadge, { backgroundColor: colors.primary }]}>
                              <ThemedText style={[styles.recommendedBadgeText, { color: colors.overlayText }]}>
                                Next
                              </ThemedText>
                            </View>
                          )}
                        </View>
                        <View style={styles.workoutHeaderRight}>
                          <View style={styles.workoutMeta}>
                            <View style={styles.workoutMetaItem}>
                              <Clock size={12} color={colors.text} />
                              <ThemedText style={styles.workoutMetaText}>
                                {workout.estimated_duration_minutes} min
                              </ThemedText>
                            </View>
                            <View style={styles.workoutMetaItem}>
                              <Dumbbell size={12} color={colors.text} />
                              <ThemedText style={styles.workoutMetaText}>
                                {workout.exercises.length} exercises
                              </ThemedText>
                            </View>
                          </View>
                          <View style={styles.completionIndicator}>
                            {isCompleted ? (
                              <CheckCircle size={20} color={colors.success} />
                            ) : (
                              <Circle size={20} color={colors.text + '40'} />
                            )}
                          </View>
                        </View>
                      </View>

                    {workout.description && (
                      <ThemedText style={styles.workoutDescription}>{workout.description}</ThemedText>
                    )}

                    <View style={styles.exercisesContainer}>
                      {workout.exercises
                        .sort((a, b) => a.order_in_workout - b.order_in_workout)
                        .map((exercise) => (
                          <ExerciseListItemDetailed
                            key={exercise.id}
                            exercise={exercise}
                            onPress={() => handleExercisePress(exercise.exercise?.id || exercise.id)}
                          />
                        ))}
                    </View>

                      {(program.status === 'coach_approved' || program.status === 'active_by_client') && (
                        <ThemedButton
                          title={isCompleted ? "Do Again" : isNextRecommended ? "Start This Workout" : "Start Workout"}
                          onPress={() => handleStartWorkout(workout)}
                          style={[
                            styles.startWorkoutButton,
                            isNextRecommended && styles.recommendedWorkoutButton
                          ]}
                        />
                      )}
                    </ThemedCard>
                  );
                })}
            </View>
          </View>
        )}
        
        {/* View Workout History Button */}
        <ThemedButton
          title="View Workout History"
          onPress={() => router.push('/workouts/workout-history')}
          variant="outline"
          style={styles.historyButton}
        />
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 20,
  },
  notesCard: {
    padding: 16,
    marginBottom: 20,
  },
  notesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  notesTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  notesText: {
    fontSize: 14,
    lineHeight: 18,
  },
  weekSelectorSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  weekSelectorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  weekNavButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  weekNavButtonDisabled: {
    opacity: 0.5,
  },
  weekSelector: {
    flex: 1,
  },
  weekSelectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 4,
  },
  weekOption: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 16,
    alignItems: 'center',
  },
  weekOptionText: {
    fontSize: 13,
    fontWeight: '600',
  },
  weekContent: {
    marginBottom: 20,
  },
  weekNotesCard: {
    padding: 12,
    marginBottom: 16,
  },
  weekNotesText: {
    fontSize: 14,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  workoutsContainer: {
    gap: 16,
  },
  workoutCard: {
    padding: 16,
  },
  workoutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  workoutTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 12,
  },
  workoutMeta: {
    flexDirection: 'row',
    gap: 12,
  },
  workoutMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  workoutMetaText: {
    fontSize: 11,
    opacity: 0.7,
  },
  workoutDescription: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 16,
  },
  exercisesContainer: {
    gap: 8,
    marginBottom: 16,
  },
  startWorkoutButton: {
    marginTop: 8,
  },
  recommendedWorkoutButton: {
    marginTop: 8,
  },
  historyButton: {
    paddingVertical: 16,
    marginBottom: 32,
  },
  // New completion status styles
  completedWorkoutCard: {
    opacity: 0.8,
    borderLeftWidth: 4,
    borderLeftColor: '#34C759', // Success color
  },
  recommendedWorkoutCard: {
    borderWidth: 2,
    borderColor: '#007AFF', // Primary color
  },
  workoutTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  completedWorkoutTitle: {
    textDecorationLine: 'line-through',
    opacity: 0.7,
  },
  recommendedBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  recommendedBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  workoutHeaderRight: {
    alignItems: 'flex-end',
    gap: 8,
  },
  completionIndicator: {
    marginTop: 4,
  },
  errorButton: {
    marginTop: 16,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    padding: 16,
  },
});