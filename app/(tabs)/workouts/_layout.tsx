import { Stack } from 'expo-router';

export default function WorkoutsLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="index" />
      <Stack.Screen name="[id]" />
      <Stack.Screen name="exercise/[id]" />
      <Stack.Screen name="active-workout" />
      <Stack.Screen name="workout-summary" />
      <Stack.Screen name="workout-history" />
      <Stack.Screen name="workout-history/[id]" />
    </Stack>
  );
}