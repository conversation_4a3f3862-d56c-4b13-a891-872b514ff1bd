import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useEffect, useState } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useWorkoutStore } from '@/store/workoutStore';
import { workoutLoggingService } from '@/services/workoutLoggingService';
import { ThemedView, ThemedText, ThemedCard, ThemedButton, ThemedStatusIndicator } from '@/components/ThemedComponents';
import { EnhancedProgramOverview } from '@/components/EnhancedProgramOverview';
import { OfflineBanner } from '@/components/NetworkStatusIndicator';
import { Calendar, Clock, Target, Dumbbell, Play, CircleCheck as CheckCircle, CircleAlert as AlertCircle, History } from 'lucide-react-native';

export default function WorkoutsScreen() {
  const { colors } = useThemeStore();
  const { completed } = useLocalSearchParams<{ completed?: string }>();
  const { 
    programs, 
    isLoading, 
    fetchPrograms, 
    startProgram
  } = useWorkoutStore();
  const [workoutStats, setWorkoutStats] = useState({
    totalWorkouts: 0,
    lastWorkoutDate: undefined as string | undefined,
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [showCompletionMessage, setShowCompletionMessage] = useState(false);

  useEffect(() => {
    fetchPrograms();
    fetchWorkoutStats();
    
    if (completed === 'true') {
      setShowCompletionMessage(true);
      setTimeout(() => {
        setShowCompletionMessage(false);
      }, 5000);
    }
  }, [fetchPrograms, completed]);

  const fetchWorkoutStats = async () => {
    try {
      setIsLoadingStats(true);
      const stats = await workoutLoggingService.getWorkoutStats();
      setWorkoutStats({
        totalWorkouts: stats.totalWorkouts,
        lastWorkoutDate: stats.lastWorkoutDate,
      });
    } catch (error) {
      console.error('Error fetching workout stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const handleStartProgram = async (programId: string) => {
    try {
      await startProgram(programId);
      // Navigate to program details or current workout
      router.push(`/workouts/${programId}`);
    } catch (error) {
      console.error('Error starting program:', error);
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'ai_generated_pending_review':
        return {
          icon: <Clock size={16} color={colors.warning} />,
          text: 'Being Personalized',
          statusType: 'warning' as const,
        };
      case 'coach_approved':
        return {
          icon: <CheckCircle size={16} color={colors.success} />,
          text: 'Ready to Start',
          statusType: 'success' as const,
        };
      case 'auto_approved':
        return {
          icon: <CheckCircle size={16} color={colors.success} />,
          text: 'Ready to Start',
          statusType: 'success' as const,
        };
      case 'active_by_client':
        return {
          icon: <Dumbbell size={16} color={colors.primary} />,
          text: 'Active',
          statusType: 'primary' as const,
        };
      case 'completed_by_client':
        return {
          icon: <CheckCircle size={16} color={colors.success} />,
          text: 'Completed',
          statusType: 'success' as const,
        };
      default:
        return {
          icon: <AlertCircle size={16} color={colors.error} />,
          text: 'Unknown',
          statusType: 'error' as const,
        };
    }
  };

  // Format date helper function
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Calculate next program update date (28 days/4 weeks after start date)
  const getNextProgramUpdateDate = (program) => {
    if (!program) return null;
    
    // Use client_start_date if available, otherwise use created_at
    const startDate = program.client_start_date 
      ? new Date(program.client_start_date) 
      : new Date(program.created_at);
    
    // Add 28 days (4 weeks) to the start date
    const updateDate = new Date(startDate);
    updateDate.setDate(startDate.getDate() + 28);
    
    return formatDate(updateDate.toISOString());
  };

  const renderProgram = (program: any) => {
    const statusInfo = getStatusInfo(program.status);

    return (
      <ThemedCard key={program.id} style={styles.programCard}>
        <View style={styles.programHeader}>
          <View style={styles.programTitleContainer}>
            <ThemedText style={styles.programTitle}>{program.name}</ThemedText>
            <ThemedStatusIndicator
              status={statusInfo.statusType}
              text={statusInfo.text}
              icon={statusInfo.icon}
              style={styles.statusBadge}
              textStyle={styles.statusText}
            />
          </View>
        </View>

        {/* Program Dates Section */}
        <View style={[styles.programDates, { backgroundColor: colors.accent }]}>
          <ThemedText style={styles.programDatesTitle}>Program Dates</ThemedText>
          <View style={styles.dateRow}>
            <View style={styles.dateItem}>
              <Calendar size={16} color={colors.primary} />
              <ThemedText style={styles.dateLabel}>Started:</ThemedText>
              <ThemedText style={styles.dateValue}>
                {program.client_start_date 
                  ? formatDate(program.client_start_date)
                  : formatDate(program.created_at)}
              </ThemedText>
            </View>
          </View>
          <View style={styles.dateRow}>
            <View style={styles.dateItem}>
              <Calendar size={16} color={colors.primary} />
              <ThemedText style={styles.dateLabel}>Next Update:</ThemedText>
              <ThemedText style={styles.dateValue}>{getNextProgramUpdateDate(program)}</ThemedText>
            </View>
          </View>
        </View>

        <ThemedText style={styles.programDescription}>
          {program.description}
        </ThemedText>

        <View style={styles.programDetails}>
          <View style={styles.programDetailItem}>
            <Calendar size={16} color={colors.text} />
            <ThemedText style={styles.programDetailText}>
              {program.duration_weeks} weeks
            </ThemedText>
          </View>
          <View style={styles.programDetailItem}>
            <Clock size={16} color={colors.text} />
            <ThemedText style={styles.programDetailText}>
              Created {new Date(program.created_at).toLocaleDateString()}
            </ThemedText>
          </View>
        </View>

        {program.coach_notes_for_client && (
          <View style={[styles.coachNotes, { backgroundColor: colors.accent }]}>
            <ThemedText style={styles.coachNotesTitle}>Coach Notes:</ThemedText>
            <ThemedText style={styles.coachNotesText}>
              {program.coach_notes_for_client}
            </ThemedText>
          </View>
        )}

        <View style={styles.programActions}>
          {program.status === 'coach_approved' && (
            <ThemedButton
              title="Start Program"
              onPress={() => handleStartProgram(program.id)}
              style={styles.actionButton}
            />
          )}
          
          {program.status === 'active_by_client' && (
            <ThemedButton
              title="Continue program"
              onPress={() => router.push(`/workouts/${program.id}`)}
              style={styles.actionButton}
            />
          )}

          <ThemedButton
            title="View Details"
            variant="outline"
            onPress={() => router.push(`/workouts/${program.id}`)}
            style={styles.secondaryButton}
          />
        </View>
      </ThemedCard>
    );
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <ThemedText style={styles.title}>Workouts</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading your workouts...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <OfflineBanner />

      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <ThemedText style={styles.title}>Workouts</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Workout Completion Message */}
        {showCompletionMessage && (
          <ThemedCard style={[styles.completionMessage, { backgroundColor: colors.success + '20' }]}>
            <CheckCircle size={20} color={colors.success} />
            <ThemedText style={[styles.completionText, { color: colors.success }]}>
              Workout completed successfully!
            </ThemedText>
          </ThemedCard>
        )}

        <EnhancedProgramOverview
          onViewProgram={(programId) => {
            // Navigate to program details page to view/browse program
            router.push(`/workouts/${programId}`);
          }}
          onRequestNewProgram={() => {
            router.push('/(intake)/welcome');
          }}
        />

        {/* Workout History Card - Moved to bottom */}
        <ThemedCard style={styles.historyCard}>
          <View style={styles.historyHeader}>
            <View style={styles.historyTitleContainer}>
              <History size={20} color={colors.primary} />
              <ThemedText style={styles.historyTitle}>Workout History</ThemedText>
            </View>

            {!isLoadingStats && (
              <ThemedText style={styles.historyCount}>
                {workoutStats.totalWorkouts} workouts
              </ThemedText>
            )}
          </View>

          {isLoadingStats ? (
            <View style={styles.historyLoading}>
              <ActivityIndicator size="small" color={colors.primary} />
              <ThemedText style={styles.historyLoadingText}>Loading stats...</ThemedText>
            </View>
          ) : (
            <View style={styles.historyContent}>
              {workoutStats.totalWorkouts > 0 ? (
                <ThemedText style={styles.historyText}>
                  You've completed {workoutStats.totalWorkouts} workouts
                  {workoutStats.lastWorkoutDate && (
                    <>. Last workout: {new Date(workoutStats.lastWorkoutDate).toLocaleDateString()}</>
                  )}
                </ThemedText>
              ) : (
                <ThemedText style={styles.historyText}>
                  You haven't logged any workouts yet. Start a workout to track your progress!
                </ThemedText>
              )}
            </View>
          )}

          <ThemedButton
            title="View Workout History"
            onPress={() => router.push('/workouts/workout-history')}
            variant="outline"
            style={styles.historyButton}
          />
        </ThemedCard>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  completionMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 16,
    gap: 12,
  },
  completionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  historyCard: {
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
    marginTop: 20,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  historyTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  historyCount: {
    fontSize: 14,
    opacity: 0.7,
  },
  historyContent: {
    marginBottom: 16,
  },
  historyText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  historyLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  historyLoadingText: {
    fontSize: 14,
    opacity: 0.7,
  },
  historyButton: {
    paddingVertical: 12,
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateDescription: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
    marginBottom: 24,
  },
  debugButton: {
    marginTop: 16,
  },
  programsHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    opacity: 0.7,
  },
  programCard: {
    padding: 20,
    marginBottom: 16,
  },
  programHeader: {
    marginBottom: 12,
  },
  programTitleContainer: {
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  programTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  programDates: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  programDatesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  dateRow: {
    marginBottom: 6,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateLabel: {
    fontSize: 13,
    fontWeight: '500',
  },
  dateValue: {
    fontSize: 13,
  },
  programDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
    opacity: 0.8,
  },
  programDetails: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 16,
  },
  programDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  programDetailText: {
    fontSize: 12,
    marginLeft: 6,
    opacity: 0.7,
  },
  coachNotes: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  coachNotesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  coachNotesText: {
    fontSize: 14,
    lineHeight: 18,
  },
  programActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  secondaryButton: {
    flex: 1,
  },
});