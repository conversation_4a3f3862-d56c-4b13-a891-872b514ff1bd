import React, { useState, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useActiveWorkoutStore } from '@/store/activeWorkoutStore';
import { useWorkoutStore } from '@/store/workoutStore';
import { ThemedView, ThemedText, ThemedButton, ThemedCard } from '@/components/ThemedComponents';
import CurrentExerciseDisplay from '@/components/CurrentExerciseDisplay';
import SetInputRow from '@/components/SetInputRow';
import RestTimerModal from '@/components/RestTimerModal';
import ExerciseVideoModal from '@/components/ExerciseVideoModal';
import ExerciseNotesInput from '@/components/ExerciseNotesInput';
import WorkoutProgressBar from '@/components/WorkoutProgressBar';
import { <PERSON><PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight, Clock, Dumbbell, Timer, X } from 'lucide-react-native';
import ConfirmationModal from '@/components/ConfirmationModal';

export default function ActiveWorkoutScreen() {
  const { colors } = useThemeStore();
  const { id: workoutId } = useLocalSearchParams<{ id: string }>();
  const { currentProgramDetails, fetchProgramDetails, isLoading } = useWorkoutStore();
  const {
    startWorkout,
    getCurrentExercise,
    currentExerciseIndex,
    loggedData,
    logSet,
    updateExerciseNotes,
    nextExercise,
    previousExercise,
    getExerciseProgress,
    getWorkoutDuration,
    startRestTimer,
    restTimer,
    resetWorkout,
    currentWorkoutPlan,
  } = useActiveWorkoutStore();
  
  const [showRestTimer, setShowRestTimer] = useState(false);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [showFinishConfirmation, setShowFinishConfirmation] = useState(false);
  const [showExitConfirmation, setShowExitConfirmation] = useState(false);

  // Load workout details if not already loaded
  useEffect(() => {
    if (workoutId && (!currentProgramDetails || currentProgramDetails.program.id !== workoutId)) {
      fetchProgramDetails(workoutId);
    }
  }, [workoutId, currentProgramDetails, fetchProgramDetails]);

  // Start workout when details are loaded
  useEffect(() => {
    if (currentProgramDetails && !currentWorkoutPlan) {
      // Find the first workout in the program
      const firstWeek = currentProgramDetails.weeks[0];
      if (firstWeek && firstWeek.workouts.length > 0) {
        startWorkout(currentProgramDetails.program, firstWeek.workouts[0]);
      }
    }
  }, [currentProgramDetails, currentWorkoutPlan, startWorkout]);

  // Show rest timer when active
  useEffect(() => {
    if (restTimer.isActive) {
      setShowRestTimer(true);
    } else {
      setShowRestTimer(false);
    }
  }, [restTimer.isActive]);

  // Format workout duration
  const formatDuration = useCallback((seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Update workout duration display
  const [duration, setDuration] = useState('0:00');
  useEffect(() => {
    const interval = setInterval(() => {
      const durationSeconds = getWorkoutDuration();
      setDuration(formatDuration(durationSeconds));
    }, 1000);
    
    return () => clearInterval(interval);
  }, [getWorkoutDuration, formatDuration]);

  const currentExercise = getCurrentExercise();
  const { completed, total } = getExerciseProgress();

  const handleLogSet = (setData: any) => {
    if (!currentExercise?.exercise?.id) return;
    logSet(currentExercise.exercise.id, setData);
  };

  const handleStartRest = (duration: number) => {
    if (!currentExercise?.exercise?.id) return;
    startRestTimer(duration, currentExercise.exercise.id);
  };

  const handleSaveNotes = (notes: string) => {
    if (!currentExercise?.exercise?.id) return;
    updateExerciseNotes(currentExercise.exercise.id, notes);
  };

  const handleExitWorkout = () => {
    setShowExitConfirmation(true);
  };

  const confirmExitWorkout = () => {
    setIsExiting(true);
    resetWorkout();
    router.back();
  };

  const handleFinishWorkout = () => {
    // Check if any sets have been logged
    const totalSetsLogged = Object.values(loggedData).reduce(
      (total, data) => total + data.sets.length, 0
    );
    
    if (totalSetsLogged === 0) {
      setShowFinishConfirmation(true);
    } else {
      router.push({
        pathname: '/workouts/workout-summary',
        params: { duration }
      });
    }
  };

  const confirmFinishWorkout = () => {
    setShowFinishConfirmation(false);
    router.push({
      pathname: '/workouts/workout-summary',
      params: { duration }
    });
  };

  if (isLoading || !currentWorkoutPlan || !currentExercise) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <ThemedText style={styles.loadingText}>Loading workout...</ThemedText>
      </ThemedView>
    );
  }

  const currentExerciseData = currentExercise.exercise?.id 
    ? loggedData[currentExercise.exercise.id] 
    : undefined;
  
  const setsLogged = currentExerciseData?.sets.length || 0;
  const totalSets = currentExercise.prescribed_sets;
  const isExerciseCompleted = setsLogged >= totalSets;
  const isTimeBased = !!currentExercise.prescribed_duration_seconds;

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleExitWorkout}
          disabled={isExiting}
        >
          {isExiting ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <X size={24} color={colors.primary} />
          )}
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <ThemedText style={styles.title}>{currentWorkoutPlan.workout.title}</ThemedText>
          <View style={styles.timerContainer}>
            <Clock size={16} color={colors.text} />
            <ThemedText style={styles.timerText}>{duration}</ThemedText>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <WorkoutProgressBar completed={completed} total={total} />

        <CurrentExerciseDisplay
          exercise={currentExercise}
          exerciseNumber={currentExerciseIndex + 1}
          totalExercises={currentWorkoutPlan.workout.exercises.length}
          onShowVideo={() => setShowVideoModal(true)}
        />

        <ThemedCard style={styles.setsCard}>
          <View style={styles.setsHeader}>
            <ThemedText style={styles.setsTitle}>Log Your Sets</ThemedText>
            <ThemedText style={styles.setsProgress}>
              {setsLogged} / {totalSets} sets
            </ThemedText>
          </View>

          {/* Set Input Rows */}
          {Array.from({ length: totalSets }).map((_, index) => (
            <SetInputRow
              key={`${currentExercise.id}-${index}`}
              setNumber={index + 1}
              onLogSet={handleLogSet}
              onStartRest={handleStartRest}
              restDuration={currentExercise.rest_period_seconds_after_set}
              isTimeBased={isTimeBased}
              disabled={index > setsLogged}
              exerciseId={currentExercise.exercise?.id} // Pass exerciseId to reset state when exercise changes
            />
          ))}
        </ThemedCard>

        <ExerciseNotesInput
          initialNotes={currentExerciseData?.notes || ''}
          onSaveNotes={handleSaveNotes}
        />

        <View style={styles.navigationButtons}>
          <ThemedButton
            title="Previous"
            onPress={previousExercise}
            variant="outline"
            style={[
              styles.navButton,
              currentExerciseIndex === 0 && styles.disabledButton,
            ]}
            disabled={currentExerciseIndex === 0}
          />
          
          <ThemedButton
            title={
              currentExerciseIndex === currentWorkoutPlan.workout.exercises.length - 1
                ? "Finish Workout"
                : "Next Exercise"
            }
            onPress={
              currentExerciseIndex === currentWorkoutPlan.workout.exercises.length - 1
                ? handleFinishWorkout
                : nextExercise
            }
            style={[
              styles.navButton,
              currentExerciseIndex === currentWorkoutPlan.workout.exercises.length - 1 && {
                backgroundColor: colors.success,
              },
            ]}
          />
        </View>
      </ScrollView>

      {/* Rest Timer Modal */}
      <RestTimerModal
        isVisible={showRestTimer}
        onClose={() => setShowRestTimer(false)}
      />

      {/* Exercise Video Modal */}
      <ExerciseVideoModal
        isVisible={showVideoModal}
        onClose={() => setShowVideoModal(false)}
        exerciseName={currentExercise.exercise?.name || 'Exercise'}
        videoUrl={currentExercise.exercise?.video_url}
      />

      {/* Exit Workout Confirmation Modal */}
      <ConfirmationModal
        isVisible={showExitConfirmation}
        title="Exit Workout"
        message="Are you sure you want to exit? Your progress will be lost."
        onConfirm={confirmExitWorkout}
        onCancel={() => setShowExitConfirmation(false)}
        confirmText="Exit"
        cancelText="Cancel"
        confirmButtonVariant="outline"
      />

      {/* Finish Workout Confirmation Modal */}
      <ConfirmationModal
        isVisible={showFinishConfirmation}
        title="No Sets Logged"
        message="You haven't logged any sets. Are you sure you want to finish the workout?"
        onConfirm={confirmFinishWorkout}
        onCancel={() => setShowFinishConfirmation(false)}
        confirmText="Finish Anyway"
        cancelText="Cancel"
        confirmButtonVariant="primary"
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  timerText: {
    fontSize: 14,
    opacity: 0.8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  setsCard: {
    padding: 16,
    marginBottom: 16,
  },
  setsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  setsTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  setsProgress: {
    fontSize: 14,
    opacity: 0.7,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginBottom: 32,
  },
  navButton: {
    flex: 1,
    paddingVertical: 14,
  },
  disabledButton: {
    opacity: 0.5,
  },
});