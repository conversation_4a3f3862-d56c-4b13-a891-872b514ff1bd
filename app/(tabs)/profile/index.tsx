import { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { useUserStore } from '@/store/userStore';
import { useThemeStore } from '@/store/themeStore';
import { CreditCard as Edit, Lock, LogOut, User, Activity } from 'lucide-react-native';
import ConfirmationModal from '@/components/ConfirmationModal';
import { profileService } from '@/services/profileService';
import { ThemedView, ThemedText, ThemedCard } from '@/components/ThemedComponents';

export default function ProfileManagementScreen() {
  const { signOut, session } = useAuthStore();
  const { profile, fetchProfile, isLoading, clearProfile } = useUserStore();
  const { colors, resetTheme } = useThemeStore();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [showSignOutConfirmation, setShowSignOutConfirmation] = useState(false);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  // Monitor session changes and redirect when session becomes null
  useEffect(() => {
    console.log('Profile screen - session state changed:', session ? 'authenticated' : 'null');
    if (!session && !isSigningOut) {
      console.log('Session is null and not currently signing out, redirecting to login');
      router.replace('/(auth)/login');
    }
  }, [session, isSigningOut]);

  const handleSignOut = () => {
    console.log('handleSignOut function called - showing confirmation modal');
    setShowSignOutConfirmation(true);
  };

  const confirmSignOut = async () => {
    try {
      setIsSigningOut(true);
      console.log('🚪 Starting sign out process');

      // Clear user profile data first
      clearProfile();
      console.log('👤 Profile data cleared');

      // Reset theme to neutral
      resetTheme();
      console.log('🎨 Theme reset to neutral');

      // Sign out from Supabase
      await signOut();
      console.log('✅ Sign out completed');

      // Hide the confirmation modal
      setShowSignOutConfirmation(false);

      // Force navigation as fallback (in case auth state change doesn't trigger)
      setTimeout(() => {
        console.log('🔄 Fallback navigation triggered');
        router.replace('/(auth)/login');
      }, 100);

    } catch (error) {
      console.error('❌ Sign out error:', error);
      setIsSigningOut(false);
      setShowSignOutConfirmation(false);
    }
  };

  const cancelSignOut = () => {
    console.log('Sign out cancelled');
    setShowSignOutConfirmation(false);
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <ThemedText style={styles.loadingText}>Loading profile...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <ThemedText style={styles.title}>Profile</ThemedText>
      </View>

      <ThemedCard style={styles.profileCard}>
        <View style={styles.profileInfo}>
          <ThemedText style={styles.name}>{profile?.full_name || 'User'}</ThemedText>
          <ThemedText style={styles.email}>{profile?.email || 'No email'}</ThemedText>
          {profile?.gender_preference && (
            <Text style={[styles.gender, { color: colors.primary, backgroundColor: `${colors.primary}20` }]}>
              Theme: {profile.gender_preference}
            </Text>
          )}
          
          {/* Display intake completion status */}
          {profile?.intake_status && (
            <View style={styles.intakeStatusContainer}>
              <Text style={[
                styles.intakeStatus,
                profile.intake_status === 'completed' && styles.intakeStatusCompleted,
                profile.intake_status === 'in_progress' && styles.intakeStatusInProgress,
              ]}>
                Fitness Profile: {profile.intake_status === 'completed' ? 'Complete' : 
                                 profile.intake_status === 'in_progress' ? 'In Progress' : 'Not Started'}
              </Text>
            </View>
          )}

          {/* Display key fitness info if available */}
          {profile?.primary_fitness_goal && profile.primary_fitness_goal.length > 0 && (
            <View style={styles.fitnessInfoContainer}>
              <ThemedText style={styles.fitnessInfoLabel}>Goals:</ThemedText>
              <ThemedText style={styles.fitnessInfoValue}>
                {profileService.getDisplayGoals(profile)}
              </ThemedText>
            </View>
          )}
          
          {profile?.training_experience_level && (
            <View style={styles.fitnessInfoContainer}>
              <ThemedText style={styles.fitnessInfoLabel}>Experience:</ThemedText>
              <ThemedText style={styles.fitnessInfoValue}>{profile.training_experience_level}</ThemedText>
            </View>
          )}
        </View>
      </ThemedCard>

      <ThemedCard style={styles.menuSection}>
        <TouchableOpacity
          style={[styles.menuItem, { borderBottomColor: colors.accent }]}
          onPress={() => router.push('/profile/edit')}
        >
          <Edit size={20} color={colors.primary} />
          <ThemedText style={styles.menuText}>Edit Profile</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.menuItem, { borderBottomColor: colors.accent }]}
          onPress={() => router.push('/profile/intakeDetails')}
        >
          <Activity size={20} color={colors.primary} />
          <ThemedText style={styles.menuText}>Fitness Profile</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.menuItem, { borderBottomColor: colors.accent }]}
          onPress={() => router.push('/profile/changePassword')}
        >
          <Lock size={20} color={colors.primary} />
          <ThemedText style={styles.menuText}>Change Password</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.menuItem, styles.signOutItem]}
          onPress={handleSignOut}
          disabled={isSigningOut}
        >
          {isSigningOut ? (
            <ActivityIndicator size="small" color="#DC3545" />
          ) : (
            <LogOut size={20} color="#DC3545" />
          )}
          <Text style={[styles.menuText, styles.signOutText]}>
            {isSigningOut ? 'Signing Out...' : 'Sign Out'}
          </Text>
        </TouchableOpacity>
      </ThemedCard>

      <ConfirmationModal
        isVisible={showSignOutConfirmation}
        title="Sign Out"
        message="Are you sure you want to sign out? You'll need to log in again to access your account."
        onConfirm={confirmSignOut}
        onCancel={cancelSignOut}
        confirmText="Sign Out"
        cancelText="Cancel"
        isConfirming={isSigningOut}
        confirmButtonColor="#DC3545"
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  profileCard: {
    margin: 20,
    padding: 20,
  },
  profileInfo: {
    alignItems: 'center',
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    marginBottom: 8,
  },
  gender: {
    fontSize: 14,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 12,
  },
  intakeStatusContainer: {
    marginBottom: 12,
  },
  intakeStatus: {
    fontSize: 14,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    color: '#8E8E93',
    backgroundColor: '#F2F2F7',
  },
  intakeStatusCompleted: {
    color: '#28A745', // Keep for now - could be themed later
    backgroundColor: '#F3FFF3',
  },
  intakeStatusInProgress: {
    color: '#FF9500', // Keep for now - could be themed later
    backgroundColor: '#FFF8F0',
  },
  fitnessInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  fitnessInfoLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  fitnessInfoValue: {
    fontSize: 14,
    flex: 1,
  },
  menuSection: {
    marginHorizontal: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  menuText: {
    fontSize: 16,
    marginLeft: 12,
  },
  signOutItem: {
    borderBottomWidth: 0,
  },
  signOutText: {
    color: '#DC3545', // Keep for now - could use colors.error later
  },
});