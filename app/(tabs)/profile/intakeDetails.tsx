import React, { useEffect, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useThemeStore } from '@/store/themeStore';
import { profileService } from '@/services/profileService';
import { ArrowLeft, Check } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard, ThemedSelectionOption, ThemedErrorText } from '@/components/ThemedComponents';

// Comprehensive validation schema combining all intake steps
const createIntakeDetailsSchema = (heightUnit: string, weightUnit: string) => {
  const baseSchema = {
    intake_gender: yup
      .string()
      .required('Please select your gender')
      .oneOf(['Woman', 'Man'], 'Please select a valid option'),
    age: yup
      .number()
      .required('Age is required')
      .min(13, 'You must be at least 13 years old')
      .max(120, 'Please enter a valid age'),
    primary_fitness_goal: yup
      .array()
      .of(yup.string())
      .min(1, 'Please select at least one fitness goal')
      .max(3, 'Please select no more than 3 fitness goals')
      .required('Please select your fitness goals'),
    training_experience_level: yup
      .string()
      .required('Please select your experience level')
      .oneOf(['Beginner', 'Intermediate', 'Advanced'], 'Please select a valid option'),
    goal_timeline_months: yup
      .number()
      .nullable()
      .min(1, 'Timeline must be at least 1 month')
      .max(24, 'Timeline must be less than 24 months'),
    equipment_access_type: yup
      .string()
      .required('Please select your equipment access')
      .oneOf(['Full Gym', 'Home Gym Basic', 'Bodyweight Only'], 'Please select a valid option'),
    training_days_per_week: yup
      .number()
      .required('Please select how many days per week you want to train')
      .min(2, 'Minimum 2 days per week')
      .max(6, 'Maximum 6 days per week'),
    preferred_session_duration_minutes: yup
      .number()
      .required('Please select your preferred session duration'),
    has_specific_event: yup.boolean(),
    custom_equipment_notes: yup.string(),
    injuries_limitations: yup.string(),
    training_preferences_notes: yup.string(),
  };

  if (heightUnit === 'cm') {
    baseSchema.height_cm = yup
      .number()
      .required('Height is required')
      .min(100, 'Please enter a valid height')
      .max(250, 'Please enter a valid height');
  } else {
    baseSchema.height_feet = yup
      .number()
      .required('Feet is required')
      .min(3, 'Please enter a valid height')
      .max(8, 'Please enter a valid height');
    baseSchema.height_inches = yup
      .number()
      .required('Inches is required')
      .min(0, 'Inches must be 0 or greater')
      .max(11, 'Inches must be less than 12');
  }

  if (weightUnit === 'kg') {
    baseSchema.weight_kg = yup
      .number()
      .required('Weight is required')
      .min(30, 'Please enter a valid weight')
      .max(300, 'Please enter a valid weight');
  } else {
    baseSchema.weight_lbs = yup
      .number()
      .required('Weight is required')
      .min(66, 'Please enter a valid weight')
      .max(660, 'Please enter a valid weight');
  }

  return yup.object().shape(baseSchema);
};

interface IntakeDetailsForm {
  intake_gender: 'Woman' | 'Man';
  age: number;
  height_cm?: number;
  height_feet?: number;
  height_inches?: number;
  weight_kg?: number;
  weight_lbs?: number;
  primary_fitness_goal: string[];
  training_experience_level: 'Beginner' | 'Intermediate' | 'Advanced';
  goal_timeline_months?: number;
  equipment_access_type: 'Full Gym' | 'Home Gym Basic' | 'Bodyweight Only';
  training_days_per_week: number;
  preferred_session_duration_minutes: number;
  has_specific_event: boolean;
  custom_equipment_notes?: string;
  injuries_limitations?: string;
  training_preferences_notes?: string;
}

export default function IntakeDetailsScreen() {
  const { profile, updateIntakeData, isLoading } = useUserStore();
  const { setTheme, colors } = useThemeStore();
  const [serverError, setServerError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [heightUnit, setHeightUnit] = useState<'cm' | 'ft'>(profile?.height_unit || 'cm');
  const [weightUnit, setWeightUnit] = useState<'kg' | 'lbs'>(profile?.weight_unit || 'kg');
  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);
  const [selectedEquipment, setSelectedEquipment] = useState(profile?.available_equipment || {});
  const [selectedDays, setSelectedDays] = useState<string[]>(profile?.preferred_training_days || []);

  const intakeDetailsSchema = createIntakeDetailsSchema(heightUnit, weightUnit);

  const { control, handleSubmit, formState: { errors }, setValue, watch } = useForm<IntakeDetailsForm>({
    resolver: yupResolver(intakeDetailsSchema),
    defaultValues: {
      intake_gender: profile?.intake_gender,
      age: profile?.age,
      height_cm: profile?.height_cm,
      height_feet: profile?.height_cm ? profileService.convertCmToFeetInches(profile.height_cm).feet : undefined,
      height_inches: profile?.height_cm ? profileService.convertCmToFeetInches(profile.height_cm).inches : undefined,
      weight_kg: profile?.weight_kg,
      weight_lbs: profile?.weight_kg ? profileService.convertKgToLbs(profile.weight_kg) : undefined,
      primary_fitness_goal: profile?.primary_fitness_goal || [],
      training_experience_level: profile?.training_experience_level,
      goal_timeline_months: profile?.goal_timeline_months,
      equipment_access_type: profile?.equipment_access_type,
      training_days_per_week: profile?.training_days_per_week,
      preferred_session_duration_minutes: profile?.preferred_session_duration_minutes,
      has_specific_event: profile?.has_specific_event || false,
      custom_equipment_notes: profile?.custom_equipment_notes || '',
      injuries_limitations: profile?.injuries_limitations || '',
      training_preferences_notes: profile?.training_preferences_notes || '',
    },
  });

  const hasSpecificEvent = watch('has_specific_event');
  const equipmentAccessType = watch('equipment_access_type');

  useEffect(() => {
    if (profile?.primary_fitness_goal && Array.isArray(profile.primary_fitness_goal)) {
      setSelectedGoals(profile.primary_fitness_goal);
      setValue('primary_fitness_goal', profile.primary_fitness_goal);
    }
  }, [profile, setValue]);

  const onSubmit = async (data: IntakeDetailsForm) => {
    try {
      setIsUpdating(true);
      setServerError(null);

      // Convert all measurements to metric for storage
      let height_cm: number;
      let weight_kg: number;

      if (heightUnit === 'ft') {
        height_cm = profileService.convertHeightToCm(data.height_feet!, data.height_inches!);
      } else {
        height_cm = data.height_cm!;
      }

      if (weightUnit === 'lbs') {
        weight_kg = profileService.convertWeightToKg(data.weight_lbs!);
      } else {
        weight_kg = data.weight_kg!;
      }

      const intakeData = {
        intake_gender: data.intake_gender,
        age: data.age,
        height_cm,
        weight_kg,
        height_unit: heightUnit,
        weight_unit: weightUnit,
        primary_fitness_goal: data.primary_fitness_goal,
        training_experience_level: data.training_experience_level,
        goal_timeline_months: data.goal_timeline_months,
        equipment_access_type: data.equipment_access_type,
        available_equipment: equipmentAccessType === 'Home Gym Basic' ? selectedEquipment : {},
        custom_equipment_notes: data.custom_equipment_notes,
        training_days_per_week: data.training_days_per_week,
        preferred_training_days: selectedDays,
        preferred_session_duration_minutes: data.preferred_session_duration_minutes,
        injuries_limitations: data.injuries_limitations,
        training_preferences_notes: data.training_preferences_notes,
        has_specific_event: data.has_specific_event,
      };

      await updateIntakeData(intakeData);
      
      // Update theme based on gender preference
      setTheme(data.intake_gender);
      
      router.back();
    } catch (error) {
      setServerError(error instanceof Error ? error.message : 'An error occurred while updating your information');
    } finally {
      setIsUpdating(false);
    }
  };

  // Data for form options
  const genderOptions = [
    { label: 'Woman', value: 'Woman' as const },
    { label: 'Man', value: 'Man' as const },
  ];

  const fitnessGoals = [
    'Build Muscle',
    'Lose Weight',
    'Increase Strength',
    'Improve Endurance',
    'General Fitness',
    'Athletic Performance',
    'Rehabilitation',
    'Improve Flexibility',
    'Better Sleep',
    'Stress Relief',
  ];

  const experienceLevels = [
    { label: 'Beginner', value: 'Beginner' as const, description: 'New to working out or returning after a long break' },
    { label: 'Intermediate', value: 'Intermediate' as const, description: 'Regular exercise for 6+ months' },
    { label: 'Advanced', value: 'Advanced' as const, description: 'Consistent training for 2+ years' },
  ];

  const equipmentTypes = [
    { value: 'Full Gym' as const, title: 'Full Gym Access', description: 'Complete gym with all equipment available' },
    { value: 'Home Gym Basic' as const, title: 'Home Gym', description: 'Limited equipment at home' },
    { value: 'Bodyweight Only' as const, title: 'Bodyweight Only', description: 'No equipment, just your body' },
  ];

  const homeEquipmentOptions = [
    { key: 'barbell_plates', label: 'Barbell & Plates' },
    { key: 'dumbbells', label: 'Dumbbells' },
    { key: 'resistance_bands', label: 'Resistance Bands' },
    { key: 'yoga_mat', label: 'Yoga Mat' },
  ];

  const daysPerWeekOptions = [2, 3, 4, 5, 6];
  const sessionDurations = [
    { value: 30, label: '30 minutes', description: 'Quick and efficient' },
    { value: 45, label: '45 minutes', description: 'Balanced workout' },
    { value: 60, label: '60 minutes', description: 'Comprehensive session' },
    { value: 90, label: '90 minutes', description: 'Extended training' },
  ];

  const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  // Helper functions
  const toggleGoal = (goal: string) => {
    let updatedGoals: string[];
    
    if (selectedGoals.includes(goal)) {
      updatedGoals = selectedGoals.filter(g => g !== goal);
    } else {
      if (selectedGoals.length < 3) {
        updatedGoals = [...selectedGoals, goal];
      } else {
        Alert.alert('Maximum Goals Reached', 'You can select up to 3 fitness goals. Please deselect one to add another.');
        return;
      }
    }
    
    setSelectedGoals(updatedGoals);
    setValue('primary_fitness_goal', updatedGoals);
  };

  const toggleEquipment = (key: string) => {
    setSelectedEquipment(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const toggleDay = (day: string) => {
    setSelectedDays(prev => {
      if (prev.includes(day)) {
        return prev.filter(d => d !== day);
      } else {
        return [...prev, day];
      }
    });
  };

  const handleHeightUnitChange = (unit: 'cm' | 'ft') => {
    setHeightUnit(unit);
    
    if (unit === 'ft' && profile?.height_cm) {
      const { feet, inches } = profileService.convertCmToFeetInches(profile.height_cm);
      setValue('height_feet', feet);
      setValue('height_inches', inches);
    } else if (unit === 'cm' && profile?.height_cm) {
      setValue('height_cm', profile.height_cm);
    }
  };

  const handleWeightUnitChange = (unit: 'kg' | 'lbs') => {
    setWeightUnit(unit);
    
    if (unit === 'lbs' && profile?.weight_kg) {
      const lbs = profileService.convertKgToLbs(profile.weight_kg);
      setValue('weight_lbs', lbs);
    } else if (unit === 'kg' && profile?.weight_kg) {
      setValue('weight_kg', profile.weight_kg);
    }
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <ThemedText style={styles.loadingText}>Loading your fitness profile...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Fitness Profile</ThemedText>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          {serverError && (
            <Text style={[styles.serverError, { backgroundColor: `${colors.primary}20`, color: colors.primary }]}>{serverError}</Text>
          )}

          {/* Gender Selection */}
          <Controller
            control={control}
            name="intake_gender"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Gender</ThemedText>
                <View style={styles.genderContainer}>
                  {genderOptions.map((option) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.genderOption,
                        { backgroundColor: colors.background, borderColor: colors.accent },
                        value === option.value && { backgroundColor: colors.primary, borderColor: colors.primary },
                      ]}
                      onPress={() => onChange(option.value)}
                    >
                      <Text
                        style={[
                          styles.genderOptionText,
                          { color: colors.text },
                          value === option.value && styles.genderOptionTextSelected,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
                {errors.intake_gender && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.intake_gender.message}</Text>
                )}
              </View>
            )}
          />

          {/* Age */}
          <Controller
            control={control}
            name="age"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Age</ThemedText>
                <ThemedInput
                  placeholder="Enter your age"
                  onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                  value={value ? value.toString() : ''}
                  keyboardType="numeric"
                />
                {errors.age && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.age.message}</Text>
                )}
              </View>
            )}
          />

          {/* Height Section */}
          <View style={styles.inputContainer}>
            <ThemedText style={styles.label}>Height</ThemedText>
            
            <View style={[styles.unitToggleContainer, { backgroundColor: colors.accent }]}>
              <TouchableOpacity
                style={[
                  styles.unitToggle,
                  heightUnit === 'cm' && { backgroundColor: colors.primary },
                ]}
                onPress={() => handleHeightUnitChange('cm')}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: colors.text },
                    heightUnit === 'cm' && styles.unitToggleTextSelected,
                  ]}
                >
                  cm
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.unitToggle,
                  heightUnit === 'ft' && { backgroundColor: colors.primary },
                ]}
                onPress={() => handleHeightUnitChange('ft')}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: colors.text },
                    heightUnit === 'ft' && styles.unitToggleTextSelected,
                  ]}
                >
                  ft & in
                </Text>
              </TouchableOpacity>
            </View>

            {heightUnit === 'cm' ? (
              <Controller
                control={control}
                name="height_cm"
                render={({ field: { onChange, value } }) => (
                  <ThemedInput
                    placeholder="Enter your height in centimeters"
                    onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                  />
                )}
              />
            ) : (
              <View style={styles.feetInchesContainer}>
                <Controller
                  control={control}
                  name="height_feet"
                  render={({ field: { onChange, value } }) => (
                    <View style={styles.feetInchesInput}>
                      <ThemedInput
                        placeholder="Feet"
                        onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                        value={value ? value.toString() : ''}
                        keyboardType="numeric"
                      />
                      <Text style={[styles.unitLabel, { color: colors.text }]}>ft</Text>
                    </View>
                  )}
                />
                <Controller
                  control={control}
                  name="height_inches"
                  render={({ field: { onChange, value } }) => (
                    <View style={styles.feetInchesInput}>
                      <ThemedInput
                        placeholder="Inches"
                        onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                        value={value ? value.toString() : ''}
                        keyboardType="numeric"
                      />
                      <Text style={[styles.unitLabel, { color: colors.text }]}>in</Text>
                    </View>
                  )}
                />
              </View>
            )}
            
            {(errors.height_cm || errors.height_feet || errors.height_inches) && (
              <Text style={[styles.errorText, { color: colors.primary }]}>
                {errors.height_cm?.message || errors.height_feet?.message || errors.height_inches?.message}
              </Text>
            )}
          </View>

          {/* Weight Section */}
          <View style={styles.inputContainer}>
            <ThemedText style={styles.label}>Weight</ThemedText>
            
            <View style={[styles.unitToggleContainer, { backgroundColor: colors.accent }]}>
              <TouchableOpacity
                style={[
                  styles.unitToggle,
                  weightUnit === 'kg' && { backgroundColor: colors.primary },
                ]}
                onPress={() => handleWeightUnitChange('kg')}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: colors.text },
                    weightUnit === 'kg' && styles.unitToggleTextSelected,
                  ]}
                >
                  kg
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.unitToggle,
                  weightUnit === 'lbs' && { backgroundColor: colors.primary },
                ]}
                onPress={() => handleWeightUnitChange('lbs')}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: colors.text },
                    weightUnit === 'lbs' && styles.unitToggleTextSelected,
                  ]}
                >
                  lbs
                </Text>
              </TouchableOpacity>
            </View>

            {weightUnit === 'kg' ? (
              <Controller
                control={control}
                name="weight_kg"
                render={({ field: { onChange, value } }) => (
                  <ThemedInput
                    placeholder="Enter your weight in kilograms"
                    onChangeText={(text) => onChange(text ? parseFloat(text) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                  />
                )}
              />
            ) : (
              <Controller
                control={control}
                name="weight_lbs"
                render={({ field: { onChange, value } }) => (
                  <ThemedInput
                    placeholder="Enter your weight in pounds"
                    onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                  />
                )}
              />
            )}
            
            {(errors.weight_kg || errors.weight_lbs) && (
              <Text style={[styles.errorText, { color: colors.primary }]}>
                {errors.weight_kg?.message || errors.weight_lbs?.message}
              </Text>
            )}
          </View>

          {/* Fitness Goals */}
          <Controller
            control={control}
            name="primary_fitness_goal"
            render={({ field: { value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Primary Fitness Goals</ThemedText>
                <Text style={[styles.helperText, { color: colors.text }]}>
                  Select up to 3 goals ({selectedGoals.length}/3 selected)
                </Text>
                <View style={styles.goalsContainer}>
                  {fitnessGoals.map((goal) => (
                    <TouchableOpacity
                      key={goal}
                      style={[
                        styles.goalOption,
                        { backgroundColor: colors.background, borderColor: colors.accent },
                        selectedGoals.includes(goal) && { backgroundColor: colors.primary, borderColor: colors.primary },
                      ]}
                      onPress={() => toggleGoal(goal)}
                    >
                      <View style={styles.goalContent}>
                        <Text
                          style={[
                            styles.goalOptionText,
                            { color: colors.text },
                            selectedGoals.includes(goal) && styles.goalOptionTextSelected,
                          ]}
                        >
                          {goal}
                        </Text>
                        {selectedGoals.includes(goal) && (
                          <Check size={16} color="#FFFFFF" />
                        )}
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
                {errors.primary_fitness_goal && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.primary_fitness_goal.message}</Text>
                )}
              </View>
            )}
          />

          {/* Training Experience */}
          <Controller
            control={control}
            name="training_experience_level"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Training Experience</ThemedText>
                <View style={styles.experienceContainer}>
                  {experienceLevels.map((level) => (
                    <TouchableOpacity
                      key={level.value}
                      style={[
                        styles.experienceOption,
                        { backgroundColor: colors.background, borderColor: colors.accent },
                        value === level.value && { backgroundColor: colors.primary, borderColor: colors.primary },
                      ]}
                      onPress={() => onChange(level.value)}
                    >
                      <Text
                        style={[
                          styles.experienceOptionTitle,
                          { color: colors.text },
                          value === level.value && styles.experienceOptionTitleSelected,
                        ]}
                      >
                        {level.label}
                      </Text>
                      <Text
                        style={[
                          styles.experienceOptionDescription,
                          { color: colors.text },
                          value === level.value && styles.experienceOptionDescriptionSelected,
                        ]}
                      >
                        {level.description}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
                {errors.training_experience_level && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.training_experience_level.message}</Text>
                )}
              </View>
            )}
          />

          {/* Specific Event */}
          <Controller
            control={control}
            name="has_specific_event"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Do you have a specific event or date in mind?</ThemedText>
                <View style={styles.eventContainer}>
                  <TouchableOpacity
                    style={[
                      styles.eventOption,
                      { backgroundColor: colors.background, borderColor: colors.accent },
                      value === true && { backgroundColor: colors.primary, borderColor: colors.primary },
                    ]}
                    onPress={() => onChange(true)}
                  >
                    <Text
                      style={[
                        styles.eventOptionText,
                        { color: colors.text },
                        value === true && styles.eventOptionTextSelected,
                      ]}
                    >
                      Yes, I have a specific event
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.eventOption,
                      { backgroundColor: colors.background, borderColor: colors.accent },
                      value === false && { backgroundColor: colors.primary, borderColor: colors.primary },
                    ]}
                    onPress={() => onChange(false)}
                  >
                    <Text
                      style={[
                        styles.eventOptionText,
                        { color: colors.text },
                        value === false && styles.eventOptionTextSelected,
                      ]}
                    >
                      No, this is an ongoing goal
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          />

          {/* Goal Timeline */}
          {hasSpecificEvent && (
            <Controller
              control={control}
              name="goal_timeline_months"
              render={({ field: { onChange, value } }) => (
                <View style={styles.inputContainer}>
                  <ThemedText style={styles.label}>How many months until your event?</ThemedText>
                  <ThemedInput
                    placeholder="Enter number of months"
                    onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                  />
                  {errors.goal_timeline_months && (
                    <Text style={[styles.errorText, { color: colors.primary }]}>{errors.goal_timeline_months.message}</Text>
                  )}
                </View>
              )}
            />
          )}

          {/* Equipment Access */}
          <Controller
            control={control}
            name="equipment_access_type"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Equipment Access</ThemedText>
                <View style={styles.equipmentContainer}>
                  {equipmentTypes.map((type) => (
                    <TouchableOpacity
                      key={type.value}
                      style={[
                        styles.equipmentOption,
                        { backgroundColor: colors.background, borderColor: colors.accent },
                        value === type.value && { backgroundColor: colors.primary, borderColor: colors.primary },
                      ]}
                      onPress={() => onChange(type.value)}
                    >
                      <Text
                        style={[
                          styles.equipmentOptionTitle,
                          { color: colors.text },
                          value === type.value && styles.equipmentOptionTitleSelected,
                        ]}
                      >
                        {type.title}
                      </Text>
                      <Text
                        style={[
                          styles.equipmentOptionDescription,
                          { color: colors.text },
                          value === type.value && styles.equipmentOptionDescriptionSelected,
                        ]}
                      >
                        {type.description}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
                {errors.equipment_access_type && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.equipment_access_type.message}</Text>
                )}
              </View>
            )}
          />

          {/* Home Equipment Details */}
          {equipmentAccessType === 'Home Gym Basic' && (
            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>Available Equipment</ThemedText>
              <Text style={[styles.helperText, { color: colors.text }]}>Select all that apply</Text>
              
              <View style={styles.homeEquipmentContainer}>
                {homeEquipmentOptions.map((option) => (
                  <TouchableOpacity
                    key={option.key}
                    style={[
                      styles.equipmentCheckbox,
                      { backgroundColor: colors.background, borderColor: colors.accent },
                      selectedEquipment[option.key] && { backgroundColor: colors.primary, borderColor: colors.primary },
                    ]}
                    onPress={() => toggleEquipment(option.key)}
                  >
                    <View style={[styles.checkboxContainer, { borderColor: colors.accent }]}>
                      {selectedEquipment[option.key] && (
                        <Check size={16} color={colors.contrastText} />
                      )}
                    </View>
                    <Text
                      style={[
                        styles.equipmentCheckboxText,
                        { color: colors.text },
                        selectedEquipment[option.key] && styles.equipmentCheckboxTextSelected,
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Custom Equipment Notes */}
          <Controller
            control={control}
            name="custom_equipment_notes"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Equipment Notes (Optional)</ThemedText>
                <ThemedInput
                  placeholder="Any specific equipment preferences or limitations..."
                  onChangeText={onChange}
                  value={value || ''}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            )}
          />

          {/* Training Days Per Week */}
          <Controller
            control={control}
            name="training_days_per_week"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>How many days per week do you want to train?</ThemedText>
                <View style={styles.daysContainer}>
                  {daysPerWeekOptions.map((days) => (
                    <TouchableOpacity
                      key={days}
                      style={[
                        styles.dayOption,
                        { backgroundColor: colors.background, borderColor: colors.accent },
                        value === days && { backgroundColor: colors.primary, borderColor: colors.primary },
                      ]}
                      onPress={() => onChange(days)}
                    >
                      <Text
                        style={[
                          styles.dayOptionText,
                          { color: colors.text },
                          value === days && styles.dayOptionTextSelected,
                        ]}
                      >
                        {days} {days === 1 ? 'day' : 'days'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
                {errors.training_days_per_week && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.training_days_per_week.message}</Text>
                )}
              </View>
            )}
          />

          {/* Preferred Training Days */}
          <View style={styles.inputContainer}>
            <ThemedText style={styles.label}>Preferred Training Days (Optional)</ThemedText>
            <Text style={[styles.helperText, { color: colors.text }]}>Select your preferred days to workout</Text>
            <View style={styles.weekDaysContainer}>
              {weekDays.map((day) => (
                <TouchableOpacity
                  key={day}
                  style={[
                    styles.weekDayOption,
                    { backgroundColor: colors.background, borderColor: colors.accent },
                    selectedDays.includes(day) && { backgroundColor: colors.primary, borderColor: colors.primary },
                  ]}
                  onPress={() => toggleDay(day)}
                >
                  <View style={[styles.checkboxContainer, { borderColor: colors.accent }]}>
                    {selectedDays.includes(day) && (
                      <Check size={16} color="#FFFFFF" />
                    )}
                  </View>
                  <Text
                    style={[
                      styles.weekDayText,
                      { color: colors.text },
                      selectedDays.includes(day) && styles.weekDayTextSelected,
                    ]}
                  >
                    {day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Session Duration */}
          <Controller
            control={control}
            name="preferred_session_duration_minutes"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Preferred Session Duration</ThemedText>
                <View style={styles.durationContainer}>
                  {sessionDurations.map((duration) => (
                    <TouchableOpacity
                      key={duration.value}
                      style={[
                        styles.durationOption,
                        { backgroundColor: colors.background, borderColor: colors.accent },
                        value === duration.value && { backgroundColor: colors.primary, borderColor: colors.primary },
                      ]}
                      onPress={() => onChange(duration.value)}
                    >
                      <Text
                        style={[
                          styles.durationOptionTitle,
                          { color: colors.text },
                          value === duration.value && styles.durationOptionTitleSelected,
                        ]}
                      >
                        {duration.label}
                      </Text>
                      <Text
                        style={[
                          styles.durationOptionDescription,
                          { color: colors.text },
                          value === duration.value && styles.durationOptionDescriptionSelected,
                        ]}
                      >
                        {duration.description}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
                {errors.preferred_session_duration_minutes && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.preferred_session_duration_minutes.message}</Text>
                )}
              </View>
            )}
          />

          {/* Injuries & Limitations */}
          <Controller
            control={control}
            name="injuries_limitations"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Injuries or Physical Limitations</ThemedText>
                <ThemedInput
                  placeholder="Please describe any current or past injuries, physical limitations, or areas of concern..."
                  onChangeText={onChange}
                  value={value || ''}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
            )}
          />

          {/* Training Preferences */}
          <Controller
            control={control}
            name="training_preferences_notes"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Training Preferences & Notes</ThemedText>
                <ThemedInput
                  placeholder="Any specific preferences, dislikes, or additional information you'd like us to know..."
                  onChangeText={onChange}
                  value={value || ''}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
            )}
          />

          <View style={styles.buttonContainer}>
            <ThemedButton
              title={isUpdating ? 'Saving Changes...' : 'Save Changes'}
              onPress={handleSubmit(onSubmit)}
              disabled={isUpdating}
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
            />
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  serverError: {
    textAlign: 'center',
    marginBottom: 16,
    padding: 8,
    borderRadius: 4,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  helperText: {
    fontSize: 12,
    marginBottom: 12,
    opacity: 0.7,
  },
  genderContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  genderOption: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  genderOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  genderOptionTextSelected: {
    color: '#FFFFFF',
  },
  unitToggleContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    borderRadius: 8,
    padding: 4,
  },
  unitToggle: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  unitToggleText: {
    fontSize: 14,
    fontWeight: '500',
  },
  unitToggleTextSelected: {
    color: '#FFFFFF',
  },
  feetInchesContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  feetInchesInput: {
    flex: 1,
    position: 'relative',
  },
  unitLabel: {
    position: 'absolute',
    right: 16,
    top: 16,
    fontSize: 16,
  },
  goalsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  goalOption: {
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  goalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  goalOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  goalOptionTextSelected: {
    color: '#FFFFFF',
  },
  experienceContainer: {
    gap: 12,
  },
  experienceOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  experienceOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  experienceOptionTitleSelected: {
    color: '#FFFFFF',
  },
  experienceOptionDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  experienceOptionDescriptionSelected: {
    color: '#FFFFFF',
    opacity: 0.9,
  },
  eventContainer: {
    gap: 12,
  },
  eventOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  eventOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  eventOptionTextSelected: {
    color: '#FFFFFF',
  },
  equipmentContainer: {
    gap: 12,
  },
  equipmentOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  equipmentOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  equipmentOptionTitleSelected: {
    color: '#FFFFFF',
  },
  equipmentOptionDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  equipmentOptionDescriptionSelected: {
    color: '#FFFFFF',
    opacity: 0.9,
  },
  homeEquipmentContainer: {
    gap: 8,
  },
  equipmentCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  checkboxContainer: {
    width: 20,
    height: 20,
    borderRadius: 4,
    backgroundColor: 'transparent',
    borderWidth: 2,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  equipmentCheckboxText: {
    fontSize: 16,
    flex: 1,
  },
  equipmentCheckboxTextSelected: {
    color: '#FFFFFF',
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  dayOption: {
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  dayOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dayOptionTextSelected: {
    color: '#FFFFFF',
  },
  weekDaysContainer: {
    gap: 8,
  },
  weekDayOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  weekDayText: {
    fontSize: 16,
    flex: 1,
  },
  weekDayTextSelected: {
    color: '#FFFFFF',
  },
  durationContainer: {
    gap: 12,
  },
  durationOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  durationOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  durationOptionTitleSelected: {
    color: '#FFFFFF',
  },
  durationOptionDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  durationOptionDescriptionSelected: {
    // Color and opacity are now handled dynamically in the component
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  buttonContainer: {
    marginTop: 32,
    marginBottom: 32,
  },
  saveButton: {
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});