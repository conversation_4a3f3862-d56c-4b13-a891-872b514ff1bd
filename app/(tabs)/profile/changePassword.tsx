import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, ScrollView } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { supabase } from '@/services/supabaseClient';
import { useThemeStore } from '@/store/themeStore';
import { ArrowLeft } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard } from '@/components/ThemedComponents';

const changePasswordSchema = yup.object().shape({
  currentPassword: yup
    .string()
    .required('Current password is required'),
  newPassword: yup
    .string()
    .required('New password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    ),
  confirmPassword: yup
    .string()
    .required('Please confirm your new password')
    .oneOf([yup.ref('newPassword')], 'Passwords must match'),
});

interface ChangePasswordForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function ChangePasswordScreen() {
  const [serverError, setServerError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { colors } = useThemeStore();
  
  const { control, handleSubmit, formState: { errors }, reset } = useForm<ChangePasswordForm>({
    resolver: yupResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: ChangePasswordForm) => {
    try {
      setIsUpdating(true);
      setServerError(null);
      
      // First verify current password by attempting to sign in
      const { error: verifyError } = await supabase.auth.signInWithPassword({
        email: (await supabase.auth.getUser()).data.user?.email || '',
        password: data.currentPassword,
      });
      
      if (verifyError) {
        throw new Error('Current password is incorrect');
      }
      
      // Update password
      const { error } = await supabase.auth.updateUser({
        password: data.newPassword,
      });
      
      if (error) throw error;
      
      setIsSuccess(true);
      reset();
    } catch (error) {
      setServerError(error instanceof Error ? error.message : 'An error occurred while changing password');
    } finally {
      setIsUpdating(false);
    }
  };

  if (isSuccess) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Change Password</ThemedText>
        </View>
        
        <ThemedCard style={styles.successContainer}>
          <ThemedText style={[styles.successTitle, { color: colors.primary }]}>Password Changed Successfully!</ThemedText>
          <ThemedText style={styles.successMessage}>
            Your password has been updated. You can now use your new password to sign in.
          </ThemedText>
          <ThemedButton
            title="Back to Profile"
            onPress={() => router.back()}
          />
        </ThemedCard>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Change Password</ThemedText>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <ThemedCard style={styles.formContainer}>
          {serverError && (
            <Text style={[styles.serverError, { backgroundColor: `${colors.primary}20` }]}>{serverError}</Text>
          )}

          <Controller
            control={control}
            name="currentPassword"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Current Password</ThemedText>
                <ThemedInput
                  placeholder="Enter your current password"
                  onChangeText={onChange}
                  value={value}
                  secureTextEntry
                  autoCapitalize="none"
                />
                {errors.currentPassword && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.currentPassword.message}</Text>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="newPassword"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>New Password</ThemedText>
                <ThemedInput
                  placeholder="Enter your new password"
                  onChangeText={onChange}
                  value={value}
                  secureTextEntry
                  autoCapitalize="none"
                />
                {errors.newPassword && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.newPassword.message}</Text>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="confirmPassword"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Confirm New Password</ThemedText>
                <ThemedInput
                  placeholder="Confirm your new password"
                  onChangeText={onChange}
                  value={value}
                  secureTextEntry
                  autoCapitalize="none"
                />
                {errors.confirmPassword && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.confirmPassword.message}</Text>
                )}
              </View>
            )}
          />

          <ThemedButton
            title={isUpdating ? 'Changing Password...' : 'Change Password'}
            onPress={handleSubmit(onSubmit)}
            disabled={isUpdating}
            style={styles.button}
          />
        </ThemedCard>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollContainer: {
    flex: 1,
  },
  formContainer: {
    margin: 20,
    padding: 20,
  },
  successContainer: {
    margin: 20,
    padding: 20,
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  serverError: {
    textAlign: 'center',
    marginBottom: 16,
    padding: 8,
    borderRadius: 4,
  },
  button: {
    marginTop: 8,
    paddingVertical: 14,
  },
});