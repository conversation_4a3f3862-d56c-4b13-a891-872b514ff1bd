import { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, ScrollView } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useThemeStore } from '@/store/themeStore';
import { ArrowLeft } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard, ThemedSelectionOption, ThemedErrorText } from '@/components/ThemedComponents';

const editProfileSchema = yup.object().shape({
  fullName: yup
    .string()
    .required('Full name is required')
    .min(2, 'Full name must be at least 2 characters')
    .max(50, 'Full name must be less than 50 characters'),
  genderPreference: yup
    .string()
    .oneOf(['Woman', 'Man', 'Neutral'], 'Please select a valid gender preference'),
});

interface EditProfileForm {
  fullName: string;
  genderPreference: 'Woman' | 'Man' | 'Neutral';
}

export default function EditProfileScreen() {
  const { profile, updateProfile, isLoading } = useUserStore();
  const { setTheme, colors } = useThemeStore();
  const [serverError, setServerError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  
  const { control, handleSubmit, formState: { errors }, setValue } = useForm<EditProfileForm>({
    resolver: yupResolver(editProfileSchema),
    defaultValues: {
      fullName: '',
      genderPreference: 'Neutral',
    },
  });

  useEffect(() => {
    if (profile) {
      setValue('fullName', profile.full_name || '');
      setValue('genderPreference', profile.gender_preference || 'Neutral');
    }
  }, [profile, setValue]);

  const onSubmit = async (data: EditProfileForm) => {
    try {
      setIsUpdating(true);
      setServerError(null);
      
      await updateProfile({
        full_name: data.fullName,
        gender_preference: data.genderPreference,
      });
      
      // Update theme based on gender preference change
      console.log('Setting theme from profile edit:', data.genderPreference);
      setTheme(data.genderPreference);
      
      router.back();
    } catch (error) {
      setServerError(error instanceof Error ? error.message : 'An error occurred while updating profile');
    } finally {
      setIsUpdating(false);
    }
  };

  const genderOptions = [
    { label: 'Woman', value: 'Woman' as const },
    { label: 'Man', value: 'Man' as const },
    { label: 'Neutral', value: 'Neutral' as const },
  ];

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Edit Profile</ThemedText>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <ThemedCard style={styles.formContainer}>
          {serverError && (
            <Text style={[styles.serverError, { backgroundColor: `${colors.primary}20` }]}>{serverError}</Text>
          )}

          <Controller
            control={control}
            name="fullName"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Full Name</ThemedText>
                <ThemedInput
                  placeholder="Enter your full name"
                  onChangeText={onChange}
                  value={value}
                  autoCapitalize="words"
                />
                {errors.fullName && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.fullName.message}</Text>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="genderPreference"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Theme Preference</ThemedText>
                <View style={styles.genderContainer}>
                  {genderOptions.map((option) => (
                    <ThemedSelectionOption
                      key={option.value}
                      selected={value === option.value}
                      onPress={() => onChange(option.value)}
                      style={styles.genderOption}
                    >
                      <Text
                        style={[
                          styles.genderOptionText,
                          { color: value === option.value ? colors.contrastText : colors.text },
                        ]}
                      >
                        {option.label}
                      </Text>
                    </ThemedSelectionOption>
                  ))}
                </View>
                {errors.genderPreference && (
                  <ThemedErrorText style={styles.errorText}>{errors.genderPreference.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          <ThemedButton
            title={isUpdating ? 'Updating...' : 'Update Profile'}
            onPress={handleSubmit(onSubmit)}
            disabled={isUpdating || isLoading}
            style={styles.button}
          />
        </ThemedCard>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollContainer: {
    flex: 1,
  },
  formContainer: {
    margin: 20,
    padding: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  genderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  genderOption: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  genderOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  genderOptionTextSelected: {
    // Color is now handled dynamically in the component
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  serverError: {
    textAlign: 'center',
    marginBottom: 16,
    padding: 8,
    borderRadius: 4,
  },
  button: {
    marginTop: 8,
    paddingVertical: 14,
  },
});