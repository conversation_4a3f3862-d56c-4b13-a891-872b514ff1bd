import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useUserStore } from '@/store/userStore';
import { useFeedbackStore } from '@/store/feedbackStore';
import { checkInService, WeeklyCheckIn } from '@/services/checkInService';
import { feedbackService, CoachFeedback } from '@/services/feedbackService';
import { ThemedView, ThemedText, ThemedCard } from '@/components/ThemedComponents';
import CoachFeedbackDisplayModal from '@/components/CoachFeedbackDisplayModal';
import { ArrowLeft, Calendar, Star, Frown, Target, MessageSquare, Activity, Heart } from 'lucide-react-native';

export default function CheckInDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { colors } = useThemeStore();
  const { profile, isCoach } = useUserStore();
  const { markFeedbackAsRead } = useFeedbackStore();
  const [checkIn, setCheckIn] = useState<WeeklyCheckIn | null>(null);
  const [feedback, setFeedback] = useState<CoachFeedback | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isUserCoach = isCoach();

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;
      
      setIsLoading(true);
      setError(null);
      try {
        console.log('🔍 CheckInDetailScreen: Fetching data for ID:', id);
        
        // Try to fetch check-in details first
        try {
          const checkInData = await checkInService.fetchCheckInById(id as string);
          if (checkInData) {
            setCheckIn(checkInData);
            
            // If successful, try to fetch related feedback
            try {
              const feedbackData = await feedbackService.fetchFeedbackForCheckIn(id as string);
              setFeedback(feedbackData);
              
              // If user is a client (not a coach) and feedback status is 'published', mark it as read
              if (!isUserCoach && feedbackData && feedbackData.status === 'published') {
                console.log('🔄 CheckInDetailScreen: Auto-marking feedback as read:', feedbackData.id);
                await markFeedbackAsRead(feedbackData.id);
              }
            } catch (feedbackError) {
              console.log('⚠️ CheckInDetailScreen: No related feedback found or error:', feedbackError);
              // Continue without feedback - it's optional
            }
          } else {
            console.log('⚠️ CheckInDetailScreen: Check-in not found, trying as feedback ID');
            throw new Error('Check-in not found');
          }
        } catch (checkInError) {
          console.log('⚠️ CheckInDetailScreen: Check-in not found, trying as feedback ID');
          
          // If check-in fetch fails, this might be a feedback ID directly
          // Fetch the feedback directly and don't worry about the check-in
          const feedbackList = await feedbackService.fetchPublishedFeedback(50);
          const directFeedback = feedbackList.find(f => f.id === id);
          
          if (directFeedback) {
            setFeedback(directFeedback);
            
            // If user is a client and feedback status is 'published', mark it as read
            if (!isUserCoach && directFeedback.status === 'published') {
              console.log('🔄 CheckInDetailScreen: Auto-marking direct feedback as read:', directFeedback.id);
              await markFeedbackAsRead(directFeedback.id);
            }
            
            // If feedback has related check-in, fetch that too
            if (directFeedback.related_checkin_id) {
              try {
                const relatedCheckIn = await checkInService.fetchCheckInById(directFeedback.related_checkin_id);
                if (relatedCheckIn) {
                  setCheckIn(relatedCheckIn);
                }
              } catch (relatedCheckInError) {
                console.log('⚠️ CheckInDetailScreen: Related check-in not found:', relatedCheckInError);
                // Continue without check-in - we at least have the feedback
              }
            }
          } else {
            console.error('❌ CheckInDetailScreen: Neither check-in nor feedback found for ID:', id);
            setError('The requested check-in or feedback could not be found.');
          }
        }
      } catch (error) {
        console.error('❌ CheckInDetailScreen: Error fetching details:', error);
        setError('An error occurred while loading the data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [id, isUserCoach, markFeedbackAsRead]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Check-in Details</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Details</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>{error}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  // If we only have feedback (no check-in), show feedback modal directly
  if (feedback && !checkIn) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Coach Feedback</ThemedText>
        </View>
        
        <CoachFeedbackDisplayModal
          isVisible={true}
          onClose={() => router.back()}
          feedback={feedback}
        />
      </ThemedView>
    );
  }

  // If we have a check-in, show check-in details (and feedback if available)
  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Check-in Details</ThemedText>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Date Card */}
          <ThemedCard style={styles.dateCard}>
            <View style={styles.dateHeader}>
              <Calendar size={20} color={colors.primary} />
              <ThemedText style={styles.dateTitle}>Weekly Check-in</ThemedText>
            </View>
            <ThemedText style={styles.dateText}>
              {checkIn && formatDate(checkIn.checkin_date)}
            </ThemedText>
          </ThemedCard>

          {/* Ratings Card */}
          {checkIn && (
            <ThemedCard style={styles.ratingsCard}>
              <ThemedText style={styles.sectionTitle}>Your Ratings</ThemedText>
              
              <View style={styles.ratingsGrid}>
                <View style={[styles.ratingItem, { backgroundColor: colors.accent }]}>
                  <Target size={20} color={colors.primary} />
                  <ThemedText style={styles.ratingValue}>
                    {checkIn.training_performance_rating}/10
                  </ThemedText>
                  <ThemedText style={styles.ratingLabel}>Performance</ThemedText>
                </View>
                
                <View style={[styles.ratingItem, { backgroundColor: colors.accent }]}>
                  <Activity size={20} color={colors.primary} />
                  <ThemedText style={styles.ratingValue}>
                    {checkIn.recovery_rating}/10
                  </ThemedText>
                  <ThemedText style={styles.ratingLabel}>Recovery</ThemedText>
                </View>
                
                <View style={[styles.ratingItem, { backgroundColor: colors.accent }]}>
                  <Heart size={20} color={colors.primary} />
                  <ThemedText style={styles.ratingValue}>
                    {checkIn.energy_levels_rating}/10
                  </ThemedText>
                  <ThemedText style={styles.ratingLabel}>Energy</ThemedText>
                </View>
              </View>
            </ThemedCard>
          )}

          {/* Wins Section */}
          {checkIn && checkIn.wins && (
            <ThemedCard style={styles.sectionCard}>
              <View style={styles.sectionHeader}>
                <Star size={20} color={colors.primary} />
                <ThemedText style={styles.sectionTitle}>Wins</ThemedText>
              </View>
              <ThemedText style={styles.sectionContent}>
                {checkIn.wins}
              </ThemedText>
            </ThemedCard>
          )}

          {/* Challenges Section */}
          {checkIn && checkIn.challenges && (
            <ThemedCard style={styles.sectionCard}>
              <View style={styles.sectionHeader}>
                <Frown size={20} color={colors.primary} />
                <ThemedText style={styles.sectionTitle}>Challenges</ThemedText>
              </View>
              <ThemedText style={styles.sectionContent}>
                {checkIn.challenges}
              </ThemedText>
            </ThemedCard>
          )}

          {/* Progress Reflection */}
          {checkIn && checkIn.progress_reflection && (
            <ThemedCard style={styles.sectionCard}>
              <View style={styles.sectionHeader}>
                <Target size={20} color={colors.primary} />
                <ThemedText style={styles.sectionTitle}>Progress Reflection</ThemedText>
              </View>
              <ThemedText style={styles.sectionContent}>
                {checkIn.progress_reflection}
              </ThemedText>
            </ThemedCard>
          )}

          {/* Additional Notes */}
          {checkIn && checkIn.additional_notes_for_coach && (
            <ThemedCard style={styles.sectionCard}>
              <View style={styles.sectionHeader}>
                <MessageSquare size={20} color={colors.primary} />
                <ThemedText style={styles.sectionTitle}>Additional Notes</ThemedText>
              </View>
              <ThemedText style={styles.sectionContent}>
                {checkIn.additional_notes_for_coach}
              </ThemedText>
            </ThemedCard>
          )}

          {/* Coach Feedback */}
          {feedback && (
            <ThemedCard 
              style={styles.feedbackCard} 
              onPress={() => setShowFeedbackModal(true)}
            >
              <View style={styles.feedbackHeader}>
                <MessageSquare size={20} color={colors.primary} />
                <ThemedText style={styles.feedbackTitle}>Coach Feedback</ThemedText>
              </View>
              <ThemedText style={styles.feedbackPreview} numberOfLines={3}>
                {feedback.feedback_content}
              </ThemedText>
              <TouchableOpacity 
                style={[styles.viewFeedbackButton, { backgroundColor: colors.primary + '20' }]}
                onPress={() => setShowFeedbackModal(true)}
              >
                <ThemedText style={[styles.viewFeedbackText, { color: colors.primary }]}>
                  View Full Feedback
                </ThemedText>
              </TouchableOpacity>
            </ThemedCard>
          )}
        </View>
      </ScrollView>

      {/* Coach Feedback Modal */}
      {feedback && (
        <CoachFeedbackDisplayModal
          isVisible={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          feedback={feedback}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  dateCard: {
    padding: 16,
    marginBottom: 16,
  },
  dateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  dateTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  dateText: {
    fontSize: 18,
    fontWeight: '600',
  },
  ratingsCard: {
    padding: 16,
    marginBottom: 16,
  },
  ratingsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 12,
  },
  ratingItem: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  ratingValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  ratingLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  sectionCard: {
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  feedbackCard: {
    padding: 16,
    marginBottom: 32,
  },
  feedbackHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  feedbackTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  feedbackPreview: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  viewFeedbackButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  viewFeedbackText: {
    fontSize: 14,
    fontWeight: '500',
  },
});