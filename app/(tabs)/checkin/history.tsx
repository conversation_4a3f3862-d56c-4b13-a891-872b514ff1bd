import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useCheckInStore } from '@/store/checkInStore';
import { ThemedView, ThemedText, ThemedCard } from '@/components/ThemedComponents';
import { Calendar, ChevronRight, Star, Target, MessageSquare } from 'lucide-react-native';

export default function CheckInHistoryScreen() {
  const { colors } = useThemeStore();
  const { checkInHistory, loadCheckInHistory, isLoading } = useCheckInStore();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadCheckInHistory();
  }, [loadCheckInHistory]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadCheckInHistory();
    } finally {
      setRefreshing(false);
    }
  };

  const handleCheckInPress = (checkInId: string) => {
    router.push(`/checkin/${checkInId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const renderCheckInItem = ({ item }: { item: any }) => {
    return (
      <ThemedCard style={styles.checkInCard} onPress={() => handleCheckInPress(item.id)}>
        <View style={styles.checkInHeader}>
          <View style={styles.checkInDate}>
            <Calendar size={16} color={colors.primary} />
            <ThemedText style={styles.dateText}>{formatDate(item.checkin_date)}</ThemedText>
          </View>
          <ChevronRight size={20} color={colors.text} opacity={0.6} />
        </View>

        <View style={styles.highlights}>
          {item.wins && (
            <View style={styles.highlightItem}>
              <Star size={14} color={colors.primary} />
              <ThemedText style={styles.highlightText} numberOfLines={1}>
                {item.wins}
              </ThemedText>
            </View>
          )}
          
          {item.training_performance_rating && (
            <View style={styles.highlightItem}>
              <Target size={14} color={colors.primary} />
              <ThemedText style={styles.highlightText}>
                Performance: {item.training_performance_rating}/10
              </ThemedText>
            </View>
          )}
        </View>
      </ThemedCard>
    );
  };

  if (isLoading && !refreshing && checkInHistory.length === 0) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <ThemedText style={styles.title}>Check-in History</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading check-in history...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <ThemedText style={styles.title}>Check-in History</ThemedText>
      </View>

      <FlatList
        data={checkInHistory}
        renderItem={renderCheckInItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MessageSquare size={48} color={colors.primary} opacity={0.5} />
            <ThemedText style={styles.emptyTitle}>No Check-ins Yet</ThemedText>
            <ThemedText style={styles.emptyText}>
              Your weekly check-in history will appear here once you've submitted your first check-in.
            </ThemedText>
          </View>
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  listContent: {
    padding: 20,
    paddingBottom: 40,
  },
  checkInCard: {
    padding: 16,
    marginBottom: 12,
  },
  checkInHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkInDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '600',
  },
  highlights: {
    gap: 8,
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  highlightText: {
    fontSize: 14,
    opacity: 0.8,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
});