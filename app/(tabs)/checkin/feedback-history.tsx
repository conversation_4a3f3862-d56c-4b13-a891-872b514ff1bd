import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, ActivityIndicator, RefreshControl, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useFeedbackStore } from '@/store/feedbackStore';
import { ThemedView, ThemedText, ThemedCard } from '@/components/ThemedComponents';
import { MessageSquare, Calendar, ArrowRight, ChevronDown, ChevronUp, ArrowLeft } from 'lucide-react-native';

export default function FeedbackHistoryScreen() {
  const { colors } = useThemeStore();
  const { coachFeedbackList, loadCoachFeedback, isLoading } = useFeedbackStore();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedFeedbackIds, setExpandedFeedbackIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    const fetchFeedback = async () => {
      setError(null);
      try {
        // Pass undefined as limit to fetch all feedback records
        await loadCoachFeedback(undefined);
      } catch (err) {
        console.error('Error fetching feedback history:', err);
        setError('Failed to load feedback history. Please try again.');
      }
    };
    fetchFeedback();
  }, [loadCoachFeedback]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    setError(null);
    try {
      // Pass undefined as limit to fetch all feedback records
      await loadCoachFeedback(undefined);
    } catch (err) {
      console.error('Error refreshing feedback history:', err);
      setError('Failed to refresh feedback. Please try again.');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleViewFeedback = (feedbackId: string) => {
    router.push(`/checkin/${feedbackId}`);
  };

  const toggleExpand = (feedbackId: string) => {
    setExpandedFeedbackIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(feedbackId)) {
        newSet.delete(feedbackId);
      } else {
        newSet.add(feedbackId);
      }
      return newSet;
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading && !isRefreshing) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Feedback History</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading feedback history...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Feedback History</ThemedText>
        </View>
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>{error}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Feedback History</ThemedText>
      </View>

      <FlatList
        data={coachFeedbackList}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => {
          const isExpanded = expandedFeedbackIds.has(item.id);
          // Get the check-in date from the related weekly_checkin if available
          const checkInDate = item.weekly_checkin?.checkin_date 
            ? formatDate(item.weekly_checkin.checkin_date)
            : formatDate(item.published_at || item.created_at);
          
          return (
            <ThemedCard style={styles.feedbackCard}>
              <View style={styles.feedbackHeader}>
                <View style={styles.feedbackHeaderLeft}>
                  <MessageSquare size={16} color={colors.primary} />
                  <ThemedText style={styles.feedbackTitle}>{checkInDate}</ThemedText>
                </View>
              </View>
              
              {/* Feedback Content - Using maxHeight instead of numberOfLines */}
              <View>
                <ThemedText 
                  style={[
                    styles.feedbackContent,
                    { maxHeight: isExpanded ? undefined : 66, overflow: 'hidden' }
                  ]}
                >
                  {item.feedback_content}
                </ThemedText>
                
                {/* Expand/Collapse Button */}
                <TouchableOpacity 
                  style={styles.expandButton}
                  onPress={() => toggleExpand(item.id)}
                >
                  {isExpanded ? (
                    <View style={styles.expandButtonContent}>
                      <ThemedText style={[styles.expandButtonText, { color: colors.primary }]}>Show Less</ThemedText>
                      <ChevronUp size={16} color={colors.primary} />
                    </View>
                  ) : (
                    <View style={styles.expandButtonContent}>
                      <ThemedText style={[styles.expandButtonText, { color: colors.primary }]}>Show More</ThemedText>
                      <ChevronDown size={16} color={colors.primary} />
                    </View>
                  )}
                </TouchableOpacity>
              </View>
              
              {/* Footer */}
              <View style={styles.feedbackFooter}>
                <TouchableOpacity 
                  style={styles.viewCheckInButton}
                  onPress={() => handleViewFeedback(item.id)}
                >
                  <ThemedText style={[styles.readMoreText, { color: colors.primary }]}>See corresponding check-in</ThemedText>
                  <ArrowRight size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
            </ThemedCard>
          );
        }}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MessageSquare size={48} color={colors.primary} opacity={0.5} />
            <ThemedText style={styles.emptyTitle}>No Feedback Yet</ThemedText>
            <ThemedText style={styles.emptyText}>
              Your coach's feedback will appear here after you submit your weekly check-ins.
            </ThemedText>
          </View>
        }
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  listContent: {
    padding: 20,
    paddingBottom: 40,
  },
  feedbackCard: {
    padding: 16,
    marginBottom: 16,
  },
  feedbackHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  feedbackHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  feedbackTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  feedbackContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  expandButton: {
    marginTop: 4,
    marginBottom: 8,
  },
  expandButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  expandButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  feedbackFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  viewCheckInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingVertical: 4,
  },
  readMoreText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
});