import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useCheckInStore } from '@/store/checkInStore';
import { useFeedbackStore } from '@/store/feedbackStore';
import { ThemedView, ThemedText, ThemedCard, ThemedButton, ThemedStatusIndicator } from '@/components/ThemedComponents';
import WeeklyCheckInPrompt from '@/components/WeeklyCheckInPrompt';
import { Calendar, Clock, Target, Dumbbell, Play, CircleCheck as CheckCircle, CircleAlert as AlertCircle, History, ChevronRight, ArrowRight, MessageSquare, ClipboardCheck, ChevronDown, ChevronUp } from 'lucide-react-native';

export default function CheckInScreen() {
  const { colors } = useThemeStore();
  const { isCheckInWindowOpen, hasSubmittedCurrentCheckIn, currentCheckInDate, determineCheckInWindowStatus } = useCheckInStore();
  const { coachFeedbackList, unreadFeedbackCount, loadCoachFeedback, getUnreadFeedbackCount } = useFeedbackStore();
  const [isLoading, setIsLoading] = useState(true);
  const [nextCheckInDate, setNextCheckInDate] = useState<string>('');
  const [expandedFeedbackIds, setExpandedFeedbackIds] = useState<Set<string>>(new Set());

  // Wrap calculateNextCheckInDate in useCallback to ensure it's stable across renders
  const calculateNextCheckInDate = useCallback(() => {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysUntilNextSunday = dayOfWeek === 0 ? 7 : 7 - dayOfWeek;
    
    const nextSunday = new Date(now);
    nextSunday.setDate(now.getDate() + daysUntilNextSunday);
    
    // Format the date
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric' 
    };
    setNextCheckInDate(nextSunday.toLocaleDateString(undefined, options));
  }, []);

  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      setIsLoading(true);
      try {
        console.log('🔄 CheckInScreen: Loading data...');
        // Check if the check-in window is open
        await determineCheckInWindowStatus();
        
        // Load only the 2 most recent feedback items for the main screen
        await loadCoachFeedback(2);
        await getUnreadFeedbackCount();
        
        console.log('✅ CheckInScreen: Data loaded successfully');
      } catch (error) {
        console.error('❌ CheckInScreen: Error loading check-in data:', error);
      } finally {
        if (isMounted) setIsLoading(false);
      }
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [determineCheckInWindowStatus, loadCoachFeedback, getUnreadFeedbackCount]);

  // Add a new useEffect that watches isCheckInWindowOpen and calculates next date when closed
  useEffect(() => {
    if (!isCheckInWindowOpen) {
      calculateNextCheckInDate();
    }
  }, [isCheckInWindowOpen, calculateNextCheckInDate]);

  const handleStartCheckIn = () => {
    router.push('/checkin/form');
  };

  const handleViewHistory = () => {
    router.push('/checkin/history');
  };

  const handleViewFeedback = (feedbackId: string) => {
    router.push(`/checkin/${feedbackId}`);
  };

  const toggleFeedbackExpansion = (feedbackId: string) => {
    setExpandedFeedbackIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(feedbackId)) {
        newSet.delete(feedbackId);
      } else {
        newSet.add(feedbackId);
      }
      return newSet;
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}> 
          <ThemedText style={styles.title}>Weekly Check-ins</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading check-in data...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}> 
        <ThemedText style={styles.title}>Weekly Check-ins</ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ThemedCard style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
              <ClipboardCheck size={24} color={colors.primary} />
            </View>
            <View style={styles.textContainer}>
              <ThemedText style={styles.statusTitle}>Weekly Check-in</ThemedText>
            </View>
          </View>

          {isCheckInWindowOpen ? (
            <>
              <ThemedText style={styles.statusDescription}>
                {hasSubmittedCurrentCheckIn
                  ? "You've completed your weekly check-in. Thank you for your feedback!"
                  : "It's Sunday! Time to reflect on your week and share your progress with your coach."}
              </ThemedText>

              <ThemedButton
                title={hasSubmittedCurrentCheckIn ? "View Check-in History" : "Complete Check-in"}
                onPress={hasSubmittedCurrentCheckIn ? handleViewHistory : handleStartCheckIn}
                style={{ backgroundColor: hasSubmittedCurrentCheckIn ? colors.accent : colors.primary }}
                textStyle={{ color: hasSubmittedCurrentCheckIn ? colors.text : colors.contrastText }}
              />
            </>
          ) : (
            <>
              <ThemedText style={styles.statusDescription}>
                Weekly check-ins are available every Sunday from 6 AM to 11:59 PM. Come back then to share your progress!{"\n"}
                {"\n"}
                Remember: Regular check-ins help your coach provide better guidance and adjust your program as needed. Remember to complete your check-in every Sunday!
              </ThemedText>

              <View style={[styles.closedNotice, { backgroundColor: colors.accent }]}>
                <Calendar size={16} color={colors.text} />
                <ThemedText style={styles.closedNoticeText}>
                  Next check-in window: {nextCheckInDate} at 6 AM
                </ThemedText>
              </View>

              <ThemedButton
                title="View Check-in History"
                onPress={handleViewHistory}
                variant="outline"
              />
            </>
          )}
        </ThemedCard>

        <View style={styles.sectionHeader}>
          <ThemedText style={styles.sectionTitle}>Recent Coach Feedback</ThemedText>
          {unreadFeedbackCount > 0 && (
            <View style={[styles.badge, { backgroundColor: colors.primary }]}>
              <ThemedText style={[styles.badgeText, { color: colors.contrastText }]}> {unreadFeedbackCount} </ThemedText>
            </View>
          )}
        </View>

        {coachFeedbackList.length === 0 ? (
          <ThemedCard style={styles.emptyState}>
            <MessageSquare size={32} color={colors.text} opacity={0.5} />
            <ThemedText style={styles.emptyStateTitle}>No Feedback Yet</ThemedText>
            <ThemedText style={styles.emptyStateDescription}>
              Your coach will provide feedback based on your check-ins and progress. Check back later!
            </ThemedText>
          </ThemedCard>
        ) : (
          coachFeedbackList.slice(0, 2).map((feedback) => {
            const isExpanded = expandedFeedbackIds.has(feedback.id);
            // Get the check-in date from the related weekly_checkin if available
            const checkInDate = feedback.weekly_checkin?.checkin_date 
              ? formatDate(feedback.weekly_checkin.checkin_date)
              : formatDate(feedback.published_at || feedback.created_at);
            
            return (
              <ThemedCard key={feedback.id} style={styles.feedbackCard}>
                <View style={styles.feedbackHeader}>
                  <View style={styles.feedbackHeaderLeft}>
                    <MessageSquare size={16} color={colors.primary} />
                    <ThemedText style={styles.feedbackTitle}>{checkInDate}</ThemedText>
                  </View>
                  {feedback.status === 'published' && (
                    <ThemedStatusIndicator
                      status="primary"
                      text="New"
                      style={{ backgroundColor: colors.primary + '20', paddingHorizontal: 8, paddingVertical: 4, borderRadius: 12 }}
                      textStyle={{ fontSize: 10, fontWeight: 'bold' }}
                    />
                  )}
                </View>
                
                {/* Collapsible content */}
                <ThemedText 
                  style={[
                    styles.feedbackPreview, 
                    { maxHeight: isExpanded ? undefined : 60, overflow: 'hidden' }
                  ]}
                >
                  {feedback.feedback_content}
                </ThemedText>
                
                {/* Expand/Collapse button */}
                <TouchableOpacity 
                  style={styles.expandButton}
                  onPress={() => toggleFeedbackExpansion(feedback.id)}
                >
                  {isExpanded ? (
                    <View style={styles.expandButtonContent}>
                      <ThemedText style={[styles.expandButtonText, { color: colors.primary }]}>Show Less</ThemedText>
                      <ChevronUp size={16} color={colors.primary} />
                    </View>
                  ) : (
                    <View style={styles.expandButtonContent}>
                      <ThemedText style={[styles.expandButtonText, { color: colors.primary }]}>Show More</ThemedText>
                      <ChevronDown size={16} color={colors.primary} />
                    </View>
                  )}
                </TouchableOpacity>

                <View style={styles.feedbackFooter}>
                  <TouchableOpacity 
                    style={styles.viewCheckInButton}
                    onPress={() => handleViewFeedback(feedback.id)}
                  >
                    <ThemedText style={[styles.readMoreText, { color: colors.primary }]}>See corresponding check-in</ThemedText>
                    <ArrowRight size={16} color={colors.primary} />
                  </TouchableOpacity>
                </View>
              </ThemedCard>
            );
          })
        )}

        {coachFeedbackList.length > 0 && (
          <ThemedButton
            title="View Feedback History"
            onPress={() => router.push('/checkin/feedback-history')}
            style={styles.viewAllFeedbackButton}
          />
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  statusCard: {
    padding: 20,
    marginBottom: 24,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
    opacity: 0.8,
  },
  closedNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    gap: 8,
  },
  closedNoticeText: {
    fontSize: 14,
  },
  actionButton: {
    paddingVertical: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  badge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyState: {
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyStateTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 20,
  },
  feedbackCard: {
    padding: 16,
    marginBottom: 12,
  },
  feedbackHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  feedbackHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  feedbackTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  unreadBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  unreadBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  feedbackDate: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 8,
  },
  feedbackPreview: {
    fontSize: 14,
    lineHeight: 20,
  },
  expandButton: {
    marginTop: 4,
    marginBottom: 8,
  },
  expandButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  expandButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  feedbackFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  viewCheckInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingVertical: 4,
  },
  readMoreText: {
    fontSize: 12,
    fontWeight: '500',
  },
  viewAllFeedbackButton: {
    marginTop: 16,
    marginBottom: 24,
  },
});