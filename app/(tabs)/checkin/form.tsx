import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useThemeStore } from '@/store/themeStore';
import { useCheckInStore } from '@/store/checkInStore';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedCard, ThemedErrorText } from '@/components/ThemedComponents';
import { ArrowLeft, Star, Frown, Target, MessageSquare, Clock } from 'lucide-react-native';

// Validation schema
const checkInSchema = yup.object().shape({
  wins: yup.string().required('Please share at least one win from your week'),
  challenges: yup.string().required('Please share any challenges you faced'),
  progress_reflection: yup.string().required('Please reflect on your progress'),
  training_performance_rating: yup
    .number()
    .required('Please rate your training performance')
    .min(1, 'Rating must be between 1-10')
    .max(10, 'Rating must be between 1-10'),
  recovery_rating: yup
    .number()
    .required('Please rate your recovery')
    .min(1, 'Rating must be between 1-10')
    .max(10, 'Rating must be between 1-10'),
  energy_levels_rating: yup
    .number()
    .required('Please rate your energy levels')
    .min(1, 'Rating must be between 1-10')
    .max(10, 'Rating must be between 1-10'),
  additional_notes_for_coach: yup.string(),
});

interface CheckInForm {
  wins: string;
  challenges: string;
  progress_reflection: string;
  training_performance_rating: number;
  recovery_rating: number;
  energy_levels_rating: number;
  additional_notes_for_coach?: string;
}

export default function WeeklyCheckInFormScreen() {
  const { colors } = useThemeStore();
  const { updateCheckInForm, submitCheckIn, isSubmitting, isCheckInWindowOpen, hasSubmittedCurrentCheckIn, determineCheckInWindowStatus } = useCheckInStore();
  const [isSuccess, setIsSuccess] = useState(false);

  useEffect(() => {
    // Check if the check-in window is still open
    determineCheckInWindowStatus();
  }, [determineCheckInWindowStatus]);

  const { control, handleSubmit, formState: { errors } } = useForm<CheckInForm>({
    resolver: yupResolver(checkInSchema),
    defaultValues: {
      wins: '',
      challenges: '',
      progress_reflection: '',
      training_performance_rating: 5,
      recovery_rating: 5,
      energy_levels_rating: 5,
      additional_notes_for_coach: '',
    },
  });

  const onSubmit = async (data: CheckInForm) => {
    try {
      // Check if the check-in window is still open
      await determineCheckInWindowStatus();
      
      if (!isCheckInWindowOpen) {
        Alert.alert(
          "Check-in Window Closed",
          "The weekly check-in window is now closed. Check-ins are available every Sunday from 6 AM to 11:59 PM.",
          [{ text: "OK", onPress: () => router.replace('/checkin') }]
        );
        return;
      }
      
      if (hasSubmittedCurrentCheckIn) {
        Alert.alert(
          "Already Submitted",
          "You've already submitted a check-in for this week.",
          [{ text: "OK", onPress: () => router.replace('/checkin') }]
        );
        return;
      }
      
      updateCheckInForm(data);
      await submitCheckIn();
      setIsSuccess(true);
      
      // Navigate back after a short delay
      setTimeout(() => {
        router.replace('/checkin');
      }, 2000);
    } catch (error) {
      console.error('Error submitting check-in:', error);
      Alert.alert('Error', 'Failed to submit check-in. Please try again.');
    }
  };

  const RatingSelector = ({ 
    value, 
    onChange, 
    error 
  }: { 
    value: number; 
    onChange: (value: number) => void; 
    error?: string;
  }) => {
    return (
      <View style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((rating) => (
          <TouchableOpacity
            key={rating}
            style={[
              styles.ratingButton,
              { 
                backgroundColor: value === rating ? colors.primary : colors.accent,
                borderColor: value === rating ? colors.primary : 'transparent',
              },
            ]}
            onPress={() => onChange(rating)}
          >
            <ThemedText
              style={[
                styles.ratingText,
                { color: value === rating ? colors.contrastText : colors.text },
              ]}
            >
              {rating}
            </ThemedText>
          </TouchableOpacity>
        ))}
        {error && <ThemedErrorText style={styles.errorText}>{error}</ThemedErrorText>}
      </View>
    );
  };

  // If check-in window is closed, show a message
  if (!isCheckInWindowOpen && !isSuccess) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Weekly Check-in</ThemedText>
        </View>

        <View style={styles.closedContainer}>
          <ThemedCard style={styles.closedCard}>
            <Clock size={48} color={colors.warning} />
            <ThemedText style={styles.closedTitle}>Check-in Window Closed</ThemedText>
            <ThemedText style={styles.closedMessage}>
              Weekly check-ins are available every Sunday from 6 AM to 11:59 PM. Please come back during this time to submit your check-in.
            </ThemedText>
            <ThemedButton
              title="Back to Check-ins"
              onPress={() => router.replace('/checkin')}
              style={styles.closedButton}
            />
          </ThemedCard>
        </View>
      </ThemedView>
    );
  }

  // If already submitted, show a message
  if (hasSubmittedCurrentCheckIn && !isSuccess) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Weekly Check-in</ThemedText>
        </View>

        <View style={styles.closedContainer}>
          <ThemedCard style={styles.closedCard}>
            <Target size={48} color={colors.success} />
            <ThemedText style={styles.closedTitle}>Already Submitted</ThemedText>
            <ThemedText style={styles.closedMessage}>
              You've already submitted your check-in for this week. Thank you for your feedback!
            </ThemedText>
            <ThemedButton
              title="View Check-in History"
              onPress={() => router.push('/checkin/history')}
              style={[styles.closedButton, { backgroundColor: colors.success }]}
            />
          </ThemedCard>
        </View>
      </ThemedView>
    );
  }

  if (isSuccess) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
          </TouchableOpacity>
          <ThemedText style={styles.title}>Weekly Check-in</ThemedText>
        </View>

        <View style={styles.successContainer}>
          <View style={[styles.successIcon, { backgroundColor: colors.success }]}>
            <Target size={32} color={colors.contrastText} />
          </View>
          <ThemedText style={styles.successTitle}>Check-in Submitted!</ThemedText>
          <ThemedText style={styles.successMessage}>
            Thank you for your weekly check-in. Your coach will review your feedback and provide insights soon.
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={colors.primary} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Weekly Check-in</ThemedText>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <ThemedText style={styles.subtitle}>
            Reflect on your week and share your progress with your coach. This helps them provide better guidance and support.
          </ThemedText>

          {/* Wins */}
          <Controller
            control={control}
            name="wins"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <View style={styles.labelContainer}>
                  <Star size={16} color={colors.primary} />
                  <ThemedText style={styles.label}>Wins This Week</ThemedText>
                </View>
                <ThemedText style={styles.helperText}>
                  What went well? Share your achievements, no matter how small.
                </ThemedText>
                <ThemedInput
                  value={value}
                  onChangeText={onChange}
                  placeholder="I hit a new PR, completed all my workouts, etc."
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  style={styles.textArea}
                />
                {errors.wins && (
                  <ThemedErrorText style={styles.errorText}>{errors.wins.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          {/* Challenges */}
          <Controller
            control={control}
            name="challenges"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <View style={styles.labelContainer}>
                  <Frown size={16} color={colors.primary} />
                  <ThemedText style={styles.label}>Challenges Faced</ThemedText>
                </View>
                <ThemedText style={styles.helperText}>
                  What obstacles or difficulties did you encounter?
                </ThemedText>
                <ThemedInput
                  value={value}
                  onChangeText={onChange}
                  placeholder="I struggled with motivation, had time constraints, etc."
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  style={styles.textArea}
                />
                {errors.challenges && (
                  <ThemedErrorText style={styles.errorText}>{errors.challenges.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          {/* Progress Reflection */}
          <Controller
            control={control}
            name="progress_reflection"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <View style={styles.labelContainer}>
                  <Target size={16} color={colors.primary} />
                  <ThemedText style={styles.label}>Progress Reflection</ThemedText>
                </View>
                <ThemedText style={styles.helperText}>
                  How do you feel about your overall progress toward your goals?
                </ThemedText>
                <ThemedInput
                  value={value}
                  onChangeText={onChange}
                  placeholder="I feel I'm making steady progress, I'm seeing improvements in..."
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  style={styles.textArea}
                />
                {errors.progress_reflection && (
                  <ThemedErrorText style={styles.errorText}>{errors.progress_reflection.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          {/* Training Performance Rating */}
          <Controller
            control={control}
            name="training_performance_rating"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Training Performance (1-10)</ThemedText>
                <ThemedText style={styles.helperText}>
                  How would you rate your overall training performance this week?
                </ThemedText>
                <RatingSelector
                  value={value}
                  onChange={onChange}
                  error={errors.training_performance_rating?.message}
                />
              </View>
            )}
          />

          {/* Recovery Rating */}
          <Controller
            control={control}
            name="recovery_rating"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Recovery Quality (1-10)</ThemedText>
                <ThemedText style={styles.helperText}>
                  How well did you recover between workouts this week?
                </ThemedText>
                <RatingSelector
                  value={value}
                  onChange={onChange}
                  error={errors.recovery_rating?.message}
                />
              </View>
            )}
          />

          {/* Energy Levels Rating */}
          <Controller
            control={control}
            name="energy_levels_rating"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Energy Levels (1-10)</ThemedText>
                <ThemedText style={styles.helperText}>
                  How were your overall energy levels throughout the week?
                </ThemedText>
                <RatingSelector
                  value={value}
                  onChange={onChange}
                  error={errors.energy_levels_rating?.message}
                />
              </View>
            )}
          />

          {/* Additional Notes */}
          <Controller
            control={control}
            name="additional_notes_for_coach"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <View style={styles.labelContainer}>
                  <MessageSquare size={16} color={colors.primary} />
                  <ThemedText style={styles.label}>Additional Notes (Optional)</ThemedText>
                </View>
                <ThemedText style={styles.helperText}>
                  Anything else you'd like to share with your coach?
                </ThemedText>
                <ThemedInput
                  value={value}
                  onChangeText={onChange}
                  placeholder="Questions, concerns, or other information..."
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  style={styles.textArea}
                />
              </View>
            )}
          />

          <ThemedButton
            title={isSubmitting ? "Submitting..." : "Submit Check-in"}
            onPress={handleSubmit(onSubmit)}
            disabled={isSubmitting}
            style={[styles.submitButton, { backgroundColor: colors.primary }]}
          />
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 24,
    opacity: 0.8,
  },
  inputContainer: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
  },
  helperText: {
    fontSize: 14,
    marginBottom: 8,
    opacity: 0.7,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  ratingButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
  },
  submitButton: {
    paddingVertical: 16,
    marginTop: 16,
    marginBottom: 32,
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  successIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  successMessage: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    opacity: 0.8,
    maxWidth: 300,
  },
  closedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  closedCard: {
    padding: 24,
    alignItems: 'center',
  },
  closedTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  closedMessage: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
    opacity: 0.8,
  },
  closedButton: {
    paddingVertical: 14,
    width: '100%',
  },
});