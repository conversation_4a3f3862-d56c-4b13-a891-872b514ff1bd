import { Stack } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';

export default function CheckInLayout() {
  const { colors } = useThemeStore();

  const BackButton = () => (
    <TouchableOpacity
      onPress={() => router.back()}
      style={{ marginLeft: 16 }}
    >
      <ArrowLeft size={24} color={colors.primary} />
    </TouchableOpacity>
  );

  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTitleStyle: {
          fontSize: 18,
          fontWeight: '600',
          color: colors.text,
        },
        headerLeft: () => <BackButton />,
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Weekly Check-ins',
          headerLeft: undefined,
        }} 
      />
      <Stack.Screen 
        name="form" 
        options={{ 
          title: 'Weekly Check-in',
        }} 
      />
      <Stack.Screen 
        name="history" 
        options={{ 
          title: 'Check-in History',
        }} 
      />
      <Stack.Screen 
        name="[id]" 
        options={{ 
          title: 'Check-in Details',
        }} 
      />
      <Stack.Screen 
        name="feedback-history" 
        options={{ 
          title: 'Feedback History',
        }} 
      />
    </Stack>
  );
}