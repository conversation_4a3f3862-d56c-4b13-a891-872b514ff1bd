import React, { useEffect, useState } from 'react';
import { Tabs, Redirect } from 'expo-router';
import { User, Settings, Chrome as Home, Dumbbell, ClipboardCheck } from 'lucide-react-native';
import { useAuthStore } from '@/store/authStore';
import { useThemeStore } from '@/store/themeStore';
import { useFeedbackStore } from '@/store/feedbackStore';
import { View, Text, StyleSheet } from 'react-native';

export default function TabLayout() {
  const { session } = useAuthStore();
  const { colors, currentTheme } = useThemeStore();
  const { unreadFeedbackCount, getUnreadFeedbackCount } = useFeedbackStore();
  const [hasNewFeedback, setHasNewFeedback] = useState(false);

  console.log('TabLayout - Session:', session ? 'authenticated' : 'null');
  console.log('TabLayout - Theme:', currentTheme, 'Primary:', colors.primary);
  console.log('TabLayout - Unread feedback count:', unreadFeedbackCount);

  // Load unread feedback count when tabs are mounted
  useEffect(() => {
    if (session) {
      getUnreadFeedbackCount().catch(console.error);
    }
  }, [session, getUnreadFeedbackCount]);

  // Update badge state based on unread feedback count
  useEffect(() => {
    setHasNewFeedback(unreadFeedbackCount > 0);
  }, [unreadFeedbackCount]);

  // Just render the tabs - let the individual screens handle auth
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: colors.background,
          borderTopWidth: 1,
          borderTopColor: colors.accent,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ size, color }) => (
            <Home size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="workouts"
        options={{
          title: 'Workouts',
          tabBarIcon: ({ size, color }) => (
            <Dumbbell size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="checkin"
        options={{
          title: 'Check-in',
          tabBarIcon: ({ size, color }) => (
            <View style={{ position: 'relative' }}>
              <ClipboardCheck size={size} color={color} />
              {hasNewFeedback && (
                <View style={[styles.badge, { backgroundColor: colors.primary }]}>
                  <Text style={styles.badgeText}>{unreadFeedbackCount}</Text>
                </View>
              )}
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ size, color }) => (
            <User size={size} color={color} />
          ),
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  badge: {
    position: 'absolute',
    top: -5,
    right: -8,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
});