import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useEffect, useState } from 'react';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import { useUserStore } from '@/store/userStore';
import { supabase } from '@/services/supabaseClient';
import { workoutLoggingService } from '@/services/workoutLoggingService';
import { ThemedView, ThemedText, ThemedCard, ThemedButton, ThemedStatusIndicator } from '@/components/ThemedComponents';
import { Calendar, Clock, Target, Dumbbell, CircleCheck as CheckCircle, CircleAlert as AlertCircle } from 'lucide-react-native';
import WeeklyCheckInPrompt from '@/components/WeeklyCheckInPrompt';


interface WorkoutProgram {
  id: string;
  name: string;
  description: string;
  status: string;
  duration_weeks: number;
  generated_by_ai_at: string;
  coach_reviewed_at?: string;
  coach_notes_for_client?: string;
  client_start_date?: string;
  created_at: string;
}

export default function DashboardScreen() {
  const { colors } = useThemeStore();
  const { profile } = useUserStore();
  const [workoutProgram, setWorkoutProgram] = useState<WorkoutProgram | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [workoutStats, setWorkoutStats] = useState({
    totalWorkouts: 0,
    totalDuration: 0,
    averageRpe: 0,
    lastWorkoutDate: undefined as string | undefined,
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  useEffect(() => {
    fetchWorkoutProgram();
    fetchWorkoutStats();
  }, []);

  const fetchWorkoutProgram = async () => {
    try {
      const { data, error } = await supabase
        .from('workout_programs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error) {
        console.error('Error fetching workout program:', error);
        return;
      }

      setWorkoutProgram(data);
    } catch (error) {
      console.error('Error fetching workout program:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchWorkoutStats = async () => {
    try {
      setIsLoadingStats(true);
      const stats = await workoutLoggingService.getWorkoutStats();
      setWorkoutStats({
        totalWorkouts: stats.totalWorkouts,
        totalDuration: stats.totalDuration,
        averageRpe: stats.averageRpe,
        lastWorkoutDate: stats.lastWorkoutDate,
      });
    } catch (error) {
      console.error('Error fetching workout stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'ai_generated_pending_review':
        return {
          icon: <Clock size={20} color={colors.info} />,
          text: 'Your program has been created and now we\'re just putting the finishing touches on it, check back soon',
          description: 'Your coach is personalizing your program to ensure it perfectly matches your goals and preferences.',
          statusType: 'info' as const,
        };
      case 'coach_approved':
        return {
          icon: <CheckCircle size={20} color={colors.success} />,
          text: 'Program Ready to Start',
          description: 'Your program has been approved by a coach and is ready to begin!',
          statusType: 'success' as const,
        };
      case 'active_by_client':
        return {
          icon: <Dumbbell size={20} color={colors.primary} />,
          text: 'Program Active',
          description: 'You are currently following this workout program.',
          statusType: 'primary' as const,
        };
      default:
        return {
          icon: <AlertCircle size={20} color={colors.warning} />,
          text: 'Program Status Unknown',
          description: 'Please contact support for assistance.',
          statusType: 'warning' as const,
        };
    }
  };

  const renderIntakeStatus = () => {
    if (!profile) return null;

    switch (profile.intake_status) {
      case 'not_started':
        return (
          <ThemedCard style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Target size={24} color={colors.primary} />
              <ThemedText style={styles.statusTitle}>Complete Your Fitness Profile</ThemedText>
            </View>
            <ThemedText style={styles.statusDescription}>
              Get started by completing your fitness intake form to receive a personalized workout program.
            </ThemedText>
            <ThemedButton
              title="Start Intake Form"
              onPress={() => router.push('/(intake)/welcome')}
              variant="primary"
              style={styles.actionButton}
            />
          </ThemedCard>
        );

      case 'in_progress':
        return (
          <ThemedCard style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Clock size={24} color={colors.warning} />
              <ThemedText style={styles.statusTitle}>Complete Your Intake</ThemedText>
            </View>
            <ThemedText style={styles.statusDescription}>
              You've started your fitness profile. Complete it to get your personalized program.
            </ThemedText>
            <ThemedButton
              title="Continue Intake"
              onPress={() => router.push('/(intake)/welcome')}
              variant="primary"
              style={[styles.actionButton, { backgroundColor: colors.warning }]}
            />
          </ThemedCard>
        );

      case 'completed':
        if (!workoutProgram) {
          return (
            <ThemedCard style={styles.statusCard}>
              <View style={styles.statusHeader}>
                <ActivityIndicator size="small" color={colors.primary} />
                <ThemedText style={styles.statusTitle}>Putting together your custom plan</ThemedText>
              </View>
              <ThemedText style={styles.statusDescription}>
                Your coach is crafting a personalized program tailored specifically to your goals and preferences. This usually takes a few minutes.
              </ThemedText>
            </ThemedCard>
          );
        }
        break;

      default:
        return null;
    }
  };

  // Calculate next program update date (4 weeks from client_start_date or created_at)
  const getNextProgramUpdateDate = () => {
    if (!workoutProgram) return null;
    
    const startDate = workoutProgram.client_start_date 
      ? new Date(workoutProgram.client_start_date) 
      : new Date(workoutProgram.created_at);
    
    // Add 4 weeks (28 days) to the start date
    const updateDate = new Date(startDate);
    updateDate.setDate(startDate.getDate() + (28));
    
    return updateDate.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes} min`;
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
          <ThemedText style={styles.title}>Dashboard</ThemedText>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Loading your dashboard...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>

      <View style={[styles.header, { backgroundColor: colors.background, borderBottomColor: colors.accent }]}>
        <ThemedText style={styles.title}>Dashboard</ThemedText>
        <ThemedText style={styles.welcomeText}>
          Welcome back, {profile?.full_name || 'User'}!
        </ThemedText>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {profile?.intake_status !== 'completed' && renderIntakeStatus()}

        {/* Your Goals */}
        {profile?.primary_fitness_goal && profile.primary_fitness_goal.length > 0 && (
          <View style={styles.sectionContainer}>
            <ThemedText style={styles.sectionTitle}>Your Goals</ThemedText>
            <ThemedCard style={styles.goalsCard}>
              <View style={styles.goalsList}>
                {profile.primary_fitness_goal.map((goal, index) => (
                  <View key={index} style={[styles.goalItem, { backgroundColor: colors.accent }]}>
                    <Target size={16} color={colors.primary} />
                    <ThemedText style={styles.goalText}>{goal}</ThemedText>
                  </View>
                ))}
              </View>
              
              <View style={styles.goalDetailsContainer}>
                {profile.training_days_per_week && (
                  <View style={styles.goalDetailItem}>
                    <Calendar size={16} color={colors.primary} />
                    <ThemedText style={styles.goalDetailLabel}>Training days:</ThemedText>
                    <ThemedText style={styles.goalDetailValue}>{profile.training_days_per_week} days/week</ThemedText>
                  </View>
                )}
                
                {profile.preferred_session_duration_minutes && (
                  <View style={styles.goalDetailItem}>
                    <Clock size={16} color={colors.primary} />
                    <ThemedText style={styles.goalDetailLabel}>Session duration:</ThemedText>
                    <ThemedText style={styles.goalDetailValue}>{profile.preferred_session_duration_minutes} min</ThemedText>
                  </View>
                )}
              </View>
            </ThemedCard>
          </View>
        )}
        
        {/* Current Training Program */}
        {workoutProgram && (
          <View style={styles.sectionContainer}>
            <ThemedText style={styles.sectionTitle}>Current Training Program</ThemedText>
            <ThemedCard style={styles.programCard}>
              <ThemedText style={styles.programTitle}>
                {workoutProgram.status === 'ai_generated_pending_review'
                  ? 'Program Status: Pending'
                  : workoutProgram.name}
              </ThemedText>
              
              <ThemedStatusIndicator
                status={getStatusInfo(workoutProgram.status).statusType}
                text={getStatusInfo(workoutProgram.status).text}
                icon={getStatusInfo(workoutProgram.status).icon}
                style={styles.statusBadge}
                textStyle={styles.statusBadgeText}
              />
              
              {getNextProgramUpdateDate() && (
                <View style={[styles.updateInfo, { backgroundColor: colors.accent }]}>
                  <Calendar size={16} color={colors.primary} />
                  <ThemedText style={styles.updateInfoText}>
                    Next program update: {getNextProgramUpdateDate()}
                  </ThemedText>
                </View>
              )}

              
              {workoutProgram.coach_notes_for_client && (
                <View style={[styles.coachNotes, { backgroundColor: colors.accent }]}>
                  <ThemedText style={styles.coachNotesTitle}>Coach Notes:</ThemedText>
                  <ThemedText style={styles.coachNotesText}>
                    {workoutProgram.coach_notes_for_client}
                  </ThemedText>
                </View>
              )}

              <ThemedButton
                title="Check Status"
                onPress={() => router.push(`/workouts/${workoutProgram.id}`)}
                variant="primary"
                style={styles.actionButton}
              />
            </ThemedCard>
          </View>
        )}

        {/* Weekly Check-in Prompt */}
        <WeeklyCheckInPrompt />

        {/* Progress Overview */}
        <View style={styles.sectionContainer}>
          <ThemedText style={styles.sectionTitle}>Progress Overview</ThemedText>
          <ThemedText style={styles.sectionSubtitle}>
            These metrics show you your lifetime training metrics and average RPE to gauge your consistency and intensity over time.
          </ThemedText>
          <View style={styles.statsGrid}>
            <ThemedCard style={styles.statCard}>
              <Dumbbell size={24} color={colors.primary} />
              <ThemedText style={styles.statValue}>
                {workoutStats.totalWorkouts}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Total workouts</ThemedText>
            </ThemedCard>

            <ThemedCard style={styles.statCard}>
              <Clock size={24} color={colors.primary} />
              <ThemedText style={styles.statValue}>
                {formatDuration(workoutStats.totalDuration || 0)}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Total training time</ThemedText>
            </ThemedCard>

            <ThemedCard style={styles.statCard}>
              <Target size={24} color={colors.primary} />
              <ThemedText style={styles.statValue}>
                {workoutStats.averageRpe > 0 ? workoutStats.averageRpe.toFixed(1) : '-'}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Avg. RPE</ThemedText>
            </ThemedCard>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  welcomeText: {
    fontSize: 16,
    opacity: 0.7,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  statusCard: {
    padding: 20,
    marginBottom: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  statusDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
    opacity: 0.8,
  },
  actionButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  sectionSubtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 12,
  },
  programCard: {
    padding: 20,
  },
  programTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginBottom: 12,
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
  updateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    gap: 8,
  },
  updateInfoText: {
    fontSize: 14,
    fontWeight: '500',
  },
  coachNotes: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  coachNotesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  coachNotesText: {
    fontSize: 14,
    lineHeight: 18,
  },
  goalsCard: {
    padding: 16,
  },
  goalsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  goalText: {
    fontSize: 14,
    fontWeight: '500',
  },
  goalDetailsContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
    paddingTop: 16,
    gap: 12,
  },
  goalDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  goalDetailLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  goalDetailValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  }
});