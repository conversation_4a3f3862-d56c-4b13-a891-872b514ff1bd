import React, { useEffect, useState, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Platform, TextInput } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { useIntakeFormStore } from '@/store/intakeFormStore';
import { useThemeStore } from '@/store/themeStore';
import { ArrowLeft, Check } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedSelectionOption, ThemedErrorText } from '@/components/ThemedComponents';

const step3Schema = yup.object().shape({
  equipment_access_type: yup
    .string()
    .required('Please select your equipment access')
    .oneOf(['Full Gym', 'Home Gym Basic', 'Bodyweight Only'], 'Please select a valid option'),
  custom_equipment_notes: yup.string(),
});

interface Step3Form {
  equipment_access_type: 'Full Gym' | 'Home Gym Basic' | 'Bodyweight Only';
  custom_equipment_notes?: string;
}

export default function Step3Screen() {
  const { formData, updateFormData, setCurrentStep, saveDraft } = useIntakeFormStore();
  const { colors } = useThemeStore();
  const [selectedEquipment, setSelectedEquipment] = useState(
    formData.available_equipment || {}
  );

  // Refs for input navigation
  const otherEquipmentRef = useRef<TextInput>(null);
  const customNotesRef = useRef<TextInput>(null);

  const { control, handleSubmit, formState: { errors }, watch } = useForm<Step3Form>({
    resolver: yupResolver(step3Schema),
    defaultValues: {
      equipment_access_type: formData.equipment_access_type,
      custom_equipment_notes: formData.custom_equipment_notes || '',
    },
  });

  const equipmentAccessType = watch('equipment_access_type');

  useEffect(() => {
    setCurrentStep(3);
  }, [setCurrentStep]);

  const onSubmit = async (data: Step3Form) => {
    try {
      const stepData = {
        equipment_access_type: data.equipment_access_type,
        available_equipment: equipmentAccessType === 'Home Gym Basic' ? selectedEquipment : {},
        custom_equipment_notes: data.custom_equipment_notes,
      };
      
      updateFormData(stepData);
      await saveDraft(stepData);
      router.push('/(intake)/step4');
    } catch (error) {
      Alert.alert('Error', 'Failed to save your information. Please try again.');
    }
  };

  const equipmentTypes = [
    {
      value: 'Full Gym' as const,
      title: 'Full Gym Access',
      description: 'Complete gym with all equipment available',
    },
    {
      value: 'Home Gym Basic' as const,
      title: 'Home Gym',
      description: 'Limited equipment at home',
    },
    {
      value: 'Bodyweight Only' as const,
      title: 'Bodyweight Only',
      description: 'No equipment, just your body',
    },
  ];

  const homeEquipmentOptions = [
    { key: 'barbell_plates', label: 'Barbell & Plates' },
    { key: 'dumbbells', label: 'Dumbbells' },
    { key: 'resistance_bands', label: 'Resistance Bands' },
    { key: 'yoga_mat', label: 'Yoga Mat' },
  ];

  const toggleEquipment = (key: string) => {
    setSelectedEquipment(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const updateOtherEquipment = (text: string) => {
    setSelectedEquipment(prev => ({
      ...prev,
      other: text,
    }));
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={styles.container}>
        <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
            <ThemedText style={[styles.backButtonText, { color: colors.primary }]}>Back</ThemedText>
          </TouchableOpacity>

          <ThemedText style={styles.title}>What equipment do you have access to?</ThemedText>
          <ThemedText style={styles.subtitle}>
            We'll design your workouts based on the equipment you have available.
          </ThemedText>

          <Controller
            control={control}
            name="equipment_access_type"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Equipment Access</ThemedText>
                <View style={styles.equipmentContainer}>
                  {equipmentTypes.map((type) => (
                    <ThemedSelectionOption
                      key={type.value}
                      selected={value === type.value}
                      onPress={() => onChange(type.value)}
                      style={styles.equipmentOption}
                    >
                      <Text
                        style={[
                          styles.equipmentOptionTitle,
                          { color: value === type.value ? colors.contrastText : colors.text },
                        ]}
                      >
                        {type.title}
                      </Text>
                      <Text
                        style={[
                          styles.equipmentOptionDescription,
                          {
                            color: value === type.value ? colors.contrastText : colors.text,
                            opacity: value === type.value ? 0.9 : 0.8,
                          },
                        ]}
                      >
                        {type.description}
                      </Text>
                    </ThemedSelectionOption>
                  ))}
                </View>
                {errors.equipment_access_type && (
                  <ThemedErrorText style={styles.errorText}>{errors.equipment_access_type.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          {equipmentAccessType === 'Home Gym Basic' && (
            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>Available Equipment</ThemedText>
              <ThemedText style={styles.helperText}>Select all that apply</ThemedText>
              
              <View style={styles.homeEquipmentContainer}>
                {homeEquipmentOptions.map((option) => (
                  <ThemedSelectionOption
                    key={option.key}
                    selected={selectedEquipment[option.key]}
                    onPress={() => toggleEquipment(option.key)}
                    style={styles.equipmentCheckbox}
                  >
                    <View style={[styles.checkboxContainer, { borderColor: colors.accent }]}>
                      {selectedEquipment[option.key] && (
                        <Check size={16} color={colors.contrastText} />
                      )}
                    </View>
                    <Text
                      style={[
                        styles.equipmentCheckboxText,
                        { color: selectedEquipment[option.key] ? colors.contrastText : colors.text },
                      ]}
                    >
                      {option.label}
                    </Text>
                  </ThemedSelectionOption>
                ))}
              </View>

              <View style={styles.otherEquipmentContainer}>
                <ThemedText style={styles.otherLabel}>Other Equipment</ThemedText>
                <ThemedInput
                  ref={otherEquipmentRef}
                  placeholder="List any other equipment you have..."
                  value={selectedEquipment.other || ''}
                  onChangeText={updateOtherEquipment}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  returnKeyType="next"
                  onSubmitEditing={() => customNotesRef.current?.focus()}
                  blurOnSubmit={false}
                />
              </View>
            </View>
          )}

          <Controller
            control={control}
            name="custom_equipment_notes"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Additional Notes (Optional)</ThemedText>
                <ThemedInput
                  ref={customNotesRef}
                  placeholder="Any specific equipment preferences or limitations..."
                  onChangeText={onChange}
                  value={value || ''}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                  returnKeyType="done"
                  onSubmitEditing={handleSubmit(onSubmit)}
                  blurOnSubmit={true}
                />
              </View>
            )}
          />

          <View style={styles.buttonContainer}>
            <ThemedButton
              title="Next"
              onPress={handleSubmit(onSubmit)}
              style={styles.nextButton}
            />
          </View>
        </View>
      </ScrollView>
    </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButtonText: {
    fontSize: 16,
    marginLeft: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 32,
    opacity: 0.8,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  helperText: {
    fontSize: 12,
    marginBottom: 12,
    opacity: 0.7,
  },
  equipmentContainer: {
    gap: 12,
  },
  equipmentOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  equipmentOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  equipmentOptionTitleSelected: {
    // Color is now handled dynamically in the component
  },
  equipmentOptionDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  equipmentOptionDescriptionSelected: {
    // Color and opacity are now handled dynamically in the component
  },
  homeEquipmentContainer: {
    gap: 8,
  },
  equipmentCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  checkboxContainer: {
    width: 20,
    height: 20,
    borderRadius: 4,
    backgroundColor: 'transparent',
    borderWidth: 2,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  equipmentCheckboxText: {
    fontSize: 16,
    flex: 1,
  },
  equipmentCheckboxTextSelected: {
    // Color is now handled dynamically in the component
  },
  otherEquipmentContainer: {
    marginTop: 16,
  },
  otherLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  buttonContainer: {
    marginTop: 32,
    marginBottom: 32,
  },
  nextButton: {
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});