import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { useIntakeFormStore } from '@/store/intakeFormStore';
import { useThemeStore } from '@/store/themeStore';
import { ArrowLeft } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedInput } from '@/components/ThemedComponents';

const step5Schema = yup.object().shape({
  injuries_limitations: yup.string(),
  training_preferences_notes: yup.string(),
});

interface Step5Form {
  injuries_limitations?: string;
  training_preferences_notes?: string;
}

export default function Step5Screen() {
  const { formData, updateFormData, setCurrentStep, submitIntake, isSubmitting } = useIntakeFormStore();
  const { colors } = useThemeStore();

  const { control, handleSubmit, formState: { errors } } = useForm<Step5Form>({
    resolver: yupResolver(step5Schema),
    defaultValues: {
      injuries_limitations: formData.injuries_limitations || '',
      training_preferences_notes: formData.training_preferences_notes || '',
    },
  });

  useEffect(() => {
    setCurrentStep(5);
  }, [setCurrentStep]);

  const onSubmit = async (data: Step5Form) => {
    console.log('🚀 Step5 onSubmit called with data:', data);
    console.log('📋 Current formData before update:', formData);
    
    try {
      console.log('📝 Updating form data with step 5 data...');
      updateFormData(data);
      
      // Get the updated formData after the update
      const updatedFormData = { ...formData, ...data };
      console.log('🔍 Complete formData that will be submitted:', updatedFormData);
      
      // Log each required field individually for easier debugging
      console.log('🔍 Required field analysis:');
      console.log('  - intake_gender:', updatedFormData.intake_gender);
      console.log('  - age:', updatedFormData.age);
      console.log('  - height_cm:', updatedFormData.height_cm);
      console.log('  - weight_kg:', updatedFormData.weight_kg);
      console.log('  - primary_fitness_goal:', updatedFormData.primary_fitness_goal);
      console.log('  - primary_fitness_goal length:', updatedFormData.primary_fitness_goal?.length);
      console.log('  - training_experience_level:', updatedFormData.training_experience_level);
      console.log('  - equipment_access_type:', updatedFormData.equipment_access_type);
      console.log('  - training_days_per_week:', updatedFormData.training_days_per_week);
      console.log('  - preferred_session_duration_minutes:', updatedFormData.preferred_session_duration_minutes);
      
      // Check if all required fields are present
      const requiredFields = {
        intake_gender: updatedFormData.intake_gender,
        age: updatedFormData.age,
        height_cm: updatedFormData.height_cm,
        weight_kg: updatedFormData.weight_kg,
        primary_fitness_goal: updatedFormData.primary_fitness_goal,
        training_experience_level: updatedFormData.training_experience_level,
        equipment_access_type: updatedFormData.equipment_access_type,
        training_days_per_week: updatedFormData.training_days_per_week,
        preferred_session_duration_minutes: updatedFormData.preferred_session_duration_minutes,
      };
      
      const missingFields = Object.entries(requiredFields).filter(([key, value]) => {
        if (key === 'primary_fitness_goal') {
          return !value || !Array.isArray(value) || value.length === 0;
        }
        return value === null || value === undefined;
      });
      
      if (missingFields.length > 0) {
        console.log('❌ Missing required fields:', missingFields.map(([key]) => key));
      } else {
        console.log('✅ All required fields are present');
      }
      
      console.log('💾 Calling submitIntake (EXPLICIT SUBMISSION - will set intake_explicitly_submitted=true)...');
      await submitIntake();
      
      console.log('✅ submitIntake completed successfully, navigating to submit screen');
      router.push('/(intake)/submit');
    } catch (error) {
      console.error('❌ Error in Step5 onSubmit:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      Alert.alert('Error', 'Failed to submit your intake form. Please try again.');
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
            <ThemedText style={[styles.backButtonText, { color: colors.primary }]}>Back</ThemedText>
          </TouchableOpacity>

          <ThemedText style={styles.title}>Final details</ThemedText>
          <ThemedText style={styles.subtitle}>
            Help us create the safest and most effective program for you by sharing any important information.
          </ThemedText>

          <Controller
            control={control}
            name="injuries_limitations"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Injuries or Physical Limitations</ThemedText>
                <ThemedText style={styles.helperText}>
                  Please describe any current or past injuries, physical limitations, or areas of concern
                </ThemedText>
                <ThemedInput
                  placeholder="e.g., Lower back pain, knee injury, shoulder mobility issues..."
                  onChangeText={onChange}
                  value={value || ''}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
                {errors.injuries_limitations && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.injuries_limitations.message}</Text>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="training_preferences_notes"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Training Preferences & Notes</ThemedText>
                <ThemedText style={styles.helperText}>
                  Any specific preferences, dislikes, or additional information you'd like us to know
                </ThemedText>
                <ThemedInput
                  placeholder="e.g., I don't like burpees, I want to focus on core strength..."
                  onChangeText={onChange}
                  value={value || ''}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
                {errors.training_preferences_notes && (
                  <Text style={[styles.errorText, { color: colors.primary }]}>{errors.training_preferences_notes.message}</Text>
                )}
              </View>
            )}
          />

          <View style={[styles.summaryContainer, { backgroundColor: colors.background, borderColor: colors.accent }]}>
            <ThemedText style={styles.summaryTitle}>🎉 You're almost done!</ThemedText>
            <ThemedText style={styles.summaryText}>
              Once you submit this form, our team will analyze your information and create a personalized 4-week workout program just for you. This usually takes 24-48 hrs.
            </ThemedText>
          </View>

          <View style={styles.buttonContainer}>
            <ThemedButton
              title={isSubmitting ? 'Creating Your Program...' : 'Submit'}
              onPress={handleSubmit(onSubmit)}
              disabled={isSubmitting}
              style={[styles.submitButton, { backgroundColor: colors.success }]}
            />
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButtonText: {
    fontSize: 16,
    marginLeft: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 32,
    opacity: 0.8,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  helperText: {
    fontSize: 12,
    marginBottom: 12,
    lineHeight: 16,
    opacity: 0.7,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  summaryContainer: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  buttonContainer: {
    marginBottom: 32,
  },
  submitButton: {
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});