import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { useIntakeFormStore } from '@/store/intakeFormStore';
import { useThemeStore } from '@/store/themeStore';
import { ArrowLeft, Check } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedSelectionOption, ThemedErrorText } from '@/components/ThemedComponents';

const step2Schema = yup.object().shape({
  primary_fitness_goal: yup
    .array()
    .of(yup.string())
    .min(1, 'Please select at least one fitness goal')
    .max(3, 'Please select no more than 3 fitness goals')
    .required('Please select your fitness goals'),
  training_experience_level: yup
    .string()
    .required('Please select your experience level')
    .oneOf(['Beginner', 'Intermediate', 'Advanced'], 'Please select a valid option'),
  goal_timeline_months: yup
    .number()
    .nullable()
    .min(1, 'Timeline must be at least 1 month')
    .max(24, 'Timeline must be less than 24 months'),
  has_specific_event: yup.boolean(),
});

interface Step2Form {
  primary_fitness_goal: string[];
  training_experience_level: 'Beginner' | 'Intermediate' | 'Advanced';
  goal_timeline_months?: number;
  has_specific_event: boolean;
}

export default function Step2Screen() {
  const { formData, updateFormData, setCurrentStep, saveDraft } = useIntakeFormStore();
  const { colors } = useThemeStore();
  const [selectedGoals, setSelectedGoals] = React.useState<string[]>([]);

  const { control, handleSubmit, formState: { errors }, watch, setValue } = useForm<Step2Form>({
    resolver: yupResolver(step2Schema),
    defaultValues: {
      primary_fitness_goal: formData.primary_fitness_goal || [],
      training_experience_level: formData.training_experience_level,
      goal_timeline_months: formData.goal_timeline_months,
      has_specific_event: formData.has_specific_event || false,
    },
  });

  const hasSpecificEvent = watch('has_specific_event');

  useEffect(() => {
    setCurrentStep(2);
    // Initialize selected goals from existing data
    if (formData.primary_fitness_goal && Array.isArray(formData.primary_fitness_goal)) {
      setSelectedGoals(formData.primary_fitness_goal);
      setValue('primary_fitness_goal', formData.primary_fitness_goal);
    }
  }, [setCurrentStep, formData.primary_fitness_goal, setValue]);

  const onSubmit = async (data: Step2Form) => {
    try {
      const stepData = {
        primary_fitness_goal: data.primary_fitness_goal,
        training_experience_level: data.training_experience_level,
        goal_timeline_months: data.goal_timeline_months,
        has_specific_event: data.has_specific_event,
      };
      
      updateFormData(stepData);
      await saveDraft(stepData);
      router.push('/(intake)/step3');
    } catch (error) {
      Alert.alert('Error', 'Failed to save your information. Please try again.');
    }
  };

  const fitnessGoals = [
    'Build Muscle',
    'Lose Weight',
    'Increase Strength',
    'Improve Endurance',
    'General Fitness',
    'Athletic Performance',
    'Rehabilitation',
    'Improve Flexibility',
    'Better Sleep',
    'Stress Relief',
  ];

  const experienceLevels = [
    { label: 'Beginner', value: 'Beginner' as const, description: 'New to working out or returning after a long break' },
    { label: 'Intermediate', value: 'Intermediate' as const, description: 'Regular exercise for 6+ months' },
    { label: 'Advanced', value: 'Advanced' as const, description: 'Consistent training for 2+ years' },
  ];

  const toggleGoal = (goal: string) => {
    let updatedGoals: string[];
    
    if (selectedGoals.includes(goal)) {
      // Remove the goal if it's already selected
      updatedGoals = selectedGoals.filter(g => g !== goal);
    } else {
      // Add the goal if we haven't reached the limit of 3
      if (selectedGoals.length < 3) {
        updatedGoals = [...selectedGoals, goal];
      } else {
        // Show alert if trying to select more than 3
        Alert.alert('Maximum Goals Reached', 'You can select up to 3 fitness goals. Please deselect one to add another.');
        return;
      }
    }
    
    setSelectedGoals(updatedGoals);
    setValue('primary_fitness_goal', updatedGoals);
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
            <ThemedText style={[styles.backButtonText, { color: colors.primary }]}>Back</ThemedText>
          </TouchableOpacity>

          <ThemedText style={styles.title}>What are your fitness goals?</ThemedText>
          <ThemedText style={styles.subtitle}>
            Understanding your goals helps us design the most effective program for you.
          </ThemedText>

          <Controller
            control={control}
            name="primary_fitness_goal"
            render={({ field: { value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Primary Fitness Goals</ThemedText>
                <ThemedText style={styles.helperText}>
                  Select up to 3 goals ({selectedGoals.length}/3 selected)
                </ThemedText>
                <View style={styles.goalsContainer}>
                  {fitnessGoals.map((goal) => (
                    <ThemedSelectionOption
                      key={goal}
                      selected={selectedGoals.includes(goal)}
                      onPress={() => toggleGoal(goal)}
                      style={styles.goalOption}
                    >
                      <View style={styles.goalContent}>
                        <Text
                          style={[
                            styles.goalOptionText,
                            { color: selectedGoals.includes(goal) ? colors.contrastText : colors.text },
                          ]}
                        >
                          {goal}
                        </Text>
                        {selectedGoals.includes(goal) && (
                          <Check size={16} color={colors.contrastText} />
                        )}
                      </View>
                    </ThemedSelectionOption>
                  ))}
                </View>
                {errors.primary_fitness_goal && (
                  <ThemedErrorText style={styles.errorText}>{errors.primary_fitness_goal.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="training_experience_level"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Training Experience</ThemedText>
                <View style={styles.experienceContainer}>
                  {experienceLevels.map((level) => (
                    <ThemedSelectionOption
                      key={level.value}
                      selected={value === level.value}
                      onPress={() => onChange(level.value)}
                      style={styles.experienceOption}
                    >
                      <Text
                        style={[
                          styles.experienceOptionTitle,
                          { color: value === level.value ? colors.contrastText : colors.text },
                        ]}
                      >
                        {level.label}
                      </Text>
                      <Text
                        style={[
                          styles.experienceOptionDescription,
                          {
                            color: value === level.value ? colors.contrastText : colors.text,
                            opacity: value === level.value ? 0.9 : 0.8,
                          },
                        ]}
                      >
                        {level.description}
                      </Text>
                    </ThemedSelectionOption>
                  ))}
                </View>
                {errors.training_experience_level && (
                  <ThemedErrorText style={styles.errorText}>{errors.training_experience_level.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="has_specific_event"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Do you have a specific event or date in mind?</ThemedText>
                <ThemedText style={styles.helperText}>
                  Like a wedding, vacation, competition, or other milestone you're working towards
                </ThemedText>
                <View style={styles.eventContainer}>
                  <ThemedSelectionOption
                    selected={value === true}
                    onPress={() => onChange(true)}
                    style={styles.eventOption}
                  >
                    <Text
                      style={[
                        styles.eventOptionText,
                        { color: value === true ? colors.contrastText : colors.text },
                      ]}
                    >
                      Yes, I have a specific event
                    </Text>
                  </ThemedSelectionOption>
                  <ThemedSelectionOption
                    selected={value === false}
                    onPress={() => onChange(false)}
                    style={styles.eventOption}
                  >
                    <Text
                      style={[
                        styles.eventOptionText,
                        { color: value === false ? colors.contrastText : colors.text },
                      ]}
                    >
                      No, this is an ongoing goal
                    </Text>
                  </ThemedSelectionOption>
                </View>
              </View>
            )}
          />

          {hasSpecificEvent && (
            <Controller
              control={control}
              name="goal_timeline_months"
              render={({ field: { onChange, value } }) => (
                <View style={styles.inputContainer}>
                  <ThemedText style={styles.label}>How many months until your event?</ThemedText>
                  <ThemedInput
                    placeholder="Enter number of months"
                    onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                  />
                  <ThemedText style={styles.helperText}>
                    This helps us create a program with the right intensity and progression
                  </ThemedText>
                  {errors.goal_timeline_months && (
                    <ThemedErrorText style={styles.errorText}>{errors.goal_timeline_months.message}</ThemedErrorText>
                  )}
                </View>
              )}
            />
          )}

          <View style={styles.buttonContainer}>
            <ThemedButton
              title="Next"
              onPress={handleSubmit(onSubmit)}
              style={styles.nextButton}
            />
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButtonText: {
    fontSize: 16,
    marginLeft: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 32,
    opacity: 0.8,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  helperText: {
    fontSize: 12,
    marginBottom: 12,
    opacity: 0.7,
  },
  goalsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  goalOption: {
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  goalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  goalOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  goalOptionTextSelected: {
    // Color is now handled dynamically in the component
  },
  experienceContainer: {
    gap: 12,
  },
  experienceOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  experienceOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  experienceOptionTitleSelected: {
    // Color is now handled dynamically in the component
  },
  experienceOptionDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  experienceOptionDescriptionSelected: {
    // Color and opacity are now handled dynamically in the component
  },
  eventContainer: {
    gap: 12,
  },
  eventOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  eventOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  eventOptionTextSelected: {
    // Color is now handled dynamically in the component
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  buttonContainer: {
    marginTop: 32,
    marginBottom: 32,
  },
  nextButton: {
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});