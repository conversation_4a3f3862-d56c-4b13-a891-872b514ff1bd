import { Stack } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';
import { useThemeStore } from '@/store/themeStore';
import ProgressIndicator from '@/components/ProgressIndicator';
import { useIntakeFormStore } from '@/store/intakeFormStore';

const stepTitles = [
  'Personal Information',
  'Fitness Goals',
  'Equipment Access',
  'Schedule',
  'Preferences'
];

export default function IntakeLayout() {
  const { currentStep } = useIntakeFormStore();
  const { colors } = useThemeStore();

  const BackButton = () => (
    <TouchableOpacity
      onPress={() => router.back()}
      style={{ marginLeft: 16 }}
    >
      <ArrowLeft size={24} color={colors.primary} />
    </TouchableOpacity>
  );

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: true,
          headerStyle: {
            backgroundColor: colors.background,
          },
          headerTitleStyle: {
            fontSize: 18,
            fontWeight: '600',
            color: colors.text,
          },
          headerLeft: () => <BackButton />,
        }}
      >
        <Stack.Screen 
          name="welcome" 
          options={{ 
            title: 'Welcome',
            headerLeft: undefined,
          }} 
        />
        <Stack.Screen 
          name="step1" 
          options={{ 
            title: 'Personal Information',
            header: () => (
              <ProgressIndicator 
                currentStep={1} 
                totalSteps={5} 
                stepTitles={stepTitles}
              />
            ),
          }} 
        />
        <Stack.Screen 
          name="step2" 
          options={{ 
            title: 'Fitness Goals',
            header: () => (
              <ProgressIndicator 
                currentStep={2} 
                totalSteps={5} 
                stepTitles={stepTitles}
              />
            ),
          }} 
        />
        <Stack.Screen 
          name="step3" 
          options={{ 
            title: 'Equipment Access',
            header: () => (
              <ProgressIndicator 
                currentStep={3} 
                totalSteps={5} 
                stepTitles={stepTitles}
              />
            ),
          }} 
        />
        <Stack.Screen 
          name="step4" 
          options={{ 
            title: 'Schedule',
            header: () => (
              <ProgressIndicator 
                currentStep={4} 
                totalSteps={5} 
                stepTitles={stepTitles}
              />
            ),
          }} 
        />
        <Stack.Screen 
          name="step5" 
          options={{ 
            title: 'Preferences',
            header: () => (
              <ProgressIndicator 
                currentStep={5} 
                totalSteps={5} 
                stepTitles={stepTitles}
              />
            ),
          }} 
        />
        <Stack.Screen 
          name="submit" 
          options={{ 
            title: 'Processing',
            headerLeft: undefined,
          }} 
        />
      </Stack>
    </>
  );
}