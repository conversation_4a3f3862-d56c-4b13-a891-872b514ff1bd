import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { useIntakeFormStore } from '@/store/intakeFormStore';
import { useThemeStore } from '@/store/themeStore';
import { ArrowLeft, Check } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedSelectionOption, ThemedErrorText } from '@/components/ThemedComponents';

const step4Schema = yup.object().shape({
  training_days_per_week: yup
    .number()
    .required('Please select how many days per week you want to train')
    .min(2, 'Minimum 2 days per week')
    .max(6, 'Maximum 6 days per week'),
  preferred_session_duration_minutes: yup
    .number()
    .required('Please select your preferred session duration'),
});

interface Step4Form {
  training_days_per_week: number;
  preferred_session_duration_minutes: number;
}

export default function Step4Screen() {
  const { formData, updateFormData, setCurrentStep, saveDraft } = useIntakeFormStore();
  const { colors } = useThemeStore();
  const [selectedDays, setSelectedDays] = useState<string[]>(
    formData.preferred_training_days || []
  );

  const { control, handleSubmit, formState: { errors } } = useForm<Step4Form>({
    resolver: yupResolver(step4Schema),
    defaultValues: {
      training_days_per_week: formData.training_days_per_week,
      preferred_session_duration_minutes: formData.preferred_session_duration_minutes,
    },
  });

  useEffect(() => {
    setCurrentStep(4);
  }, [setCurrentStep]);

  const onSubmit = async (data: Step4Form) => {
    try {
      const stepData = {
        training_days_per_week: data.training_days_per_week,
        preferred_session_duration_minutes: data.preferred_session_duration_minutes,
        preferred_training_days: selectedDays,
      };
      
      updateFormData(stepData);
      await saveDraft(stepData);
      router.push('/(intake)/step5');
    } catch (error) {
      Alert.alert('Error', 'Failed to save your information. Please try again.');
    }
  };

  const daysPerWeekOptions = [2, 3, 4, 5, 6];
  const sessionDurations = [
    { value: 30, label: '30 minutes', description: 'Quick and efficient' },
    { value: 45, label: '45 minutes', description: 'Balanced workout' },
    { value: 60, label: '60 minutes', description: 'Comprehensive session' },
    { value: 90, label: '90 minutes', description: 'Extended training' },
  ];

  const weekDays = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  const toggleDay = (day: string) => {
    setSelectedDays(prev => {
      if (prev.includes(day)) {
        return prev.filter(d => d !== day);
      } else {
        return [...prev, day];
      }
    });
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.primary} />
            <ThemedText style={[styles.backButtonText, { color: colors.primary }]}>Back</ThemedText>
          </TouchableOpacity>

          <ThemedText style={styles.title}>Let's plan your schedule</ThemedText>
          <ThemedText style={styles.subtitle}>
            We'll create a program that fits your lifestyle and availability.
          </ThemedText>

          <Controller
            control={control}
            name="training_days_per_week"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>How many days per week do you want to train?</ThemedText>
                <View style={styles.daysContainer}>
                  {daysPerWeekOptions.map((days) => (
                    <ThemedSelectionOption
                      key={days}
                      selected={value === days}
                      onPress={() => onChange(days)}
                      style={styles.dayOption}
                    >
                      <Text
                        style={[
                          styles.dayOptionText,
                          { color: value === days ? colors.contrastText : colors.text },
                        ]}
                      >
                        {days} {days === 1 ? 'day' : 'days'}
                      </Text>
                    </ThemedSelectionOption>
                  ))}
                </View>
                {errors.training_days_per_week && (
                  <ThemedErrorText style={styles.errorText}>{errors.training_days_per_week.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          <View style={styles.inputContainer}>
            <ThemedText style={styles.label}>Preferred Training Days (Optional)</ThemedText>
            <ThemedText style={styles.helperText}>Select your preferred days to workout</ThemedText>
            <View style={styles.weekDaysContainer}>
              {weekDays.map((day) => (
                <ThemedSelectionOption
                  key={day}
                  selected={selectedDays.includes(day)}
                  onPress={() => toggleDay(day)}
                  style={styles.weekDayOption}
                >
                  <View style={[styles.checkboxContainer, { borderColor: colors.accent }]}>
                    {selectedDays.includes(day) && (
                      <Check size={16} color={colors.contrastText} />
                    )}
                  </View>
                  <Text
                    style={[
                      styles.weekDayText,
                      { color: selectedDays.includes(day) ? colors.contrastText : colors.text },
                    ]}
                  >
                    {day}
                  </Text>
                </ThemedSelectionOption>
              ))}
            </View>
          </View>

          <Controller
            control={control}
            name="preferred_session_duration_minutes"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Preferred Session Duration</ThemedText>
                <View style={styles.durationContainer}>
                  {sessionDurations.map((duration) => (
                    <ThemedSelectionOption
                      key={duration.value}
                      selected={value === duration.value}
                      onPress={() => onChange(duration.value)}
                      style={styles.durationOption}
                    >
                      <Text
                        style={[
                          styles.durationOptionTitle,
                          { color: value === duration.value ? colors.contrastText : colors.text },
                        ]}
                      >
                        {duration.label}
                      </Text>
                      <Text
                        style={[
                          styles.durationOptionDescription,
                          {
                            color: value === duration.value ? colors.contrastText : colors.text,
                            opacity: value === duration.value ? 0.9 : 0.8,
                          },
                        ]}
                      >
                        {duration.description}
                      </Text>
                    </ThemedSelectionOption>
                  ))}
                </View>
                {errors.preferred_session_duration_minutes && (
                  <ThemedErrorText style={styles.errorText}>{errors.preferred_session_duration_minutes.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          <View style={styles.buttonContainer}>
            <ThemedButton
              title="Next"
              onPress={handleSubmit(onSubmit)}
              style={styles.nextButton}
            />
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButtonText: {
    fontSize: 16,
    marginLeft: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 32,
    opacity: 0.8,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  helperText: {
    fontSize: 12,
    marginBottom: 12,
    opacity: 0.7,
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  dayOption: {
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  dayOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dayOptionTextSelected: {
    // Color is now handled dynamically in the component
  },
  weekDaysContainer: {
    gap: 8,
  },
  weekDayOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  checkboxContainer: {
    width: 20,
    height: 20,
    borderRadius: 4,
    backgroundColor: 'transparent',
    borderWidth: 2,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  weekDayText: {
    fontSize: 16,
    flex: 1,
  },
  weekDayTextSelected: {
    // Color is now handled dynamically in the component
  },
  durationContainer: {
    gap: 12,
  },
  durationOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  durationOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  durationOptionTitleSelected: {
    // Color is now handled dynamically in the component
  },
  durationOptionDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  durationOptionDescriptionSelected: {
    // Color and opacity are now handled dynamically in the component
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  buttonContainer: {
    marginTop: 32,
    marginBottom: 32,
  },
  nextButton: {
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});