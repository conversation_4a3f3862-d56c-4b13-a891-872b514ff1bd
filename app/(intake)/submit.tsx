import React, { useEffect } from 'react';
import { View, StyleSheet, Image, ScrollView } from 'react-native';
import { router } from 'expo-router';
import { useIntakeFormStore } from '@/store/intakeFormStore';
import { useThemeStore } from '@/store/themeStore';
import { useUserStore } from '@/store/userStore';
import { CircleCheck as CheckCircle, ArrowRight } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton } from '@/components/ThemedComponents';

export default function SubmitScreen() {
  const { formData, resetForm } = useIntakeFormStore();
  const { setTheme, colors } = useThemeStore();
  const { fetchProfile } = useUserStore();

  useEffect(() => {
    // Update theme based on gender selection from intake form
    if (formData.intake_gender) {
      console.log('🎨 Setting theme from intake completion:', formData.intake_gender);
      setTheme(formData.intake_gender);
    }

    // Refresh profile to get updated data
    fetchProfile().catch(console.error);
  }, [formData.intake_gender, setTheme, fetchProfile]);

  const handleContinue = () => {
    resetForm();
    router.replace('/(tabs)');
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
        <Image
          source={{ uri: 'https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&cs=tinysrgb&w=800' }}
          style={styles.heroImage}
        />
        
        <View style={styles.successContainer}>
          <CheckCircle size={64} color={colors.primary} />
          <ThemedText style={styles.title}>Your personalized program is in the lab being created!</ThemedText>
          <ThemedText variant="primary" style={styles.subtitle}>
           You will receive it within 24-48 hours.
          </ThemedText>
        </View>

        <View style={[styles.detailsContainer, { backgroundColor: colors.background, borderColor: colors.accent }]}>
          <ThemedText style={styles.detailsTitle}>What's Next?</ThemedText>
          <View style={styles.stepContainer}>
            <View style={[styles.stepNumber, { backgroundColor: colors.primary }]}>
              <ThemedText style={styles.stepNumberText}>1</ThemedText>
            </View>
            <ThemedText variant="primary" style={styles.stepText}>
              Your program will be waiting in your dashboard to begin once it's ready.
            </ThemedText>
          </View>
          <View style={styles.stepContainer}>
            <View style={[styles.stepNumber, { backgroundColor: colors.primary }]}>
              <ThemedText style={styles.stepNumberText}>2</ThemedText>
            </View>
            <ThemedText variant="primary" style={styles.stepText}>
              Start your first workout whenever you're ready
            </ThemedText>
          </View>
          <View style={styles.stepContainer}>
            <View style={[styles.stepNumber, { backgroundColor: colors.primary }]}>
              <ThemedText style={styles.stepNumberText}>3</ThemedText>
            </View>
            <ThemedText variant="primary" style={styles.stepText}>
              Track your progress and receive weekly feedback
            </ThemedText>
          </View>
        </View>

        <ThemedButton
          title="Go to Dashboard"
          onPress={handleContinue}
          style={styles.continueButton}
        >
          <View style={styles.buttonContent}>
            <ThemedText style={styles.continueButtonText}>Go to Dashboard</ThemedText>
            <ArrowRight size={20} color={colors.contrastText} />
          </View>
        </ThemedButton>

        <ThemedText style={styles.footerText}>
          🎯 Your fitness journey starts now!
        </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  heroImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 32,
    resizeMode: 'cover',
  },
  successContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  detailsContainer: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stepNumberText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF', // This is fine since it's always on a primary background
  },
  stepText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 18,
  },
  continueButton: {
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF', // This is fine since it's always on a primary button
    marginRight: 8,
  },
  footerText: {
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
});