import React, { useEffect, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { router } from 'expo-router';
import { useIntakeFormStore } from '@/store/intakeFormStore';
import { useThemeStore } from '@/store/themeStore';
import { profileService } from '@/services/profileService';
import { ThemedView, ThemedText, ThemedButton, ThemedInput, ThemedSelectionOption, ThemedErrorText } from '@/components/ThemedComponents';

// Dynamic validation schema based on units
const createStep1Schema = (heightUnit: string, weightUnit: string) => {
  const baseSchema = {
    gender: yup
      .string()
      .required('Please select your gender')
      .oneOf(['Woman', 'Man'], 'Please select a valid option'),
    age: yup
      .number()
      .required('Age is required')
      .min(13, 'You must be at least 13 years old')
      .max(120, 'Please enter a valid age'),
  };

  if (heightUnit === 'cm') {
    baseSchema.height_cm = yup
      .number()
      .required('Height is required')
      .min(100, 'Please enter a valid height')
      .max(250, 'Please enter a valid height');
  } else {
    baseSchema.height_feet = yup
      .number()
      .required('Feet is required')
      .min(3, 'Please enter a valid height')
      .max(8, 'Please enter a valid height');
    baseSchema.height_inches = yup
      .number()
      .required('Inches is required')
      .min(0, 'Inches must be 0 or greater')
      .max(11, 'Inches must be less than 12');
  }

  if (weightUnit === 'kg') {
    baseSchema.weight_kg = yup
      .number()
      .required('Weight is required')
      .min(30, 'Please enter a valid weight')
      .max(300, 'Please enter a valid weight');
  } else {
    baseSchema.weight_lbs = yup
      .number()
      .required('Weight is required')
      .min(66, 'Please enter a valid weight')
      .max(660, 'Please enter a valid weight');
  }

  return yup.object().shape(baseSchema);
};

interface Step1Form {
  gender: 'Woman' | 'Man';
  age: number;
  height_cm?: number;
  height_feet?: number;
  height_inches?: number;
  weight_kg?: number;
  weight_lbs?: number;
}

export default function Step1Screen() {
  const { formData, updateFormData, setCurrentStep, saveDraft } = useIntakeFormStore();
  const { colors } = useThemeStore();
  const [heightUnit, setHeightUnit] = React.useState<'cm' | 'ft'>(formData.height_unit || 'cm');
  const [weightUnit, setWeightUnit] = React.useState<'kg' | 'lbs'>(formData.weight_unit || 'kg');

  // Refs for input navigation
  const ageRef = useRef<TextInput>(null);
  const heightCmRef = useRef<TextInput>(null);
  const heightFeetRef = useRef<TextInput>(null);
  const heightInchesRef = useRef<TextInput>(null);
  const weightRef = useRef<TextInput>(null);

  const step1Schema = createStep1Schema(heightUnit, weightUnit);

  const { control, handleSubmit, formState: { errors }, setValue, watch } = useForm<Step1Form>({
    resolver: yupResolver(step1Schema),
    defaultValues: {
      gender: formData.intake_gender,
      age: formData.age,
      height_cm: formData.height_cm,
      height_feet: formData.height_cm ? profileService.convertCmToFeetInches(formData.height_cm).feet : undefined,
      height_inches: formData.height_cm ? profileService.convertCmToFeetInches(formData.height_cm).inches : undefined,
      weight_kg: formData.weight_kg,
      weight_lbs: formData.weight_kg ? profileService.convertKgToLbs(formData.weight_kg) : undefined,
    },
  });

  useEffect(() => {
    setCurrentStep(1);
  }, [setCurrentStep]);

  const handleHeightUnitChange = (unit: 'cm' | 'ft') => {
    setHeightUnit(unit);
    
    if (unit === 'ft' && formData.height_cm) {
      const { feet, inches } = profileService.convertCmToFeetInches(formData.height_cm);
      setValue('height_feet', feet);
      setValue('height_inches', inches);
    } else if (unit === 'cm' && formData.height_cm) {
      setValue('height_cm', formData.height_cm);
    }
  };

  const handleWeightUnitChange = (unit: 'kg' | 'lbs') => {
    setWeightUnit(unit);
    
    if (unit === 'lbs' && formData.weight_kg) {
      const lbs = profileService.convertKgToLbs(formData.weight_kg);
      setValue('weight_lbs', lbs);
    } else if (unit === 'kg' && formData.weight_kg) {
      setValue('weight_kg', formData.weight_kg);
    }
  };

  const onSubmit = async (data: Step1Form) => {
    try {
      // Convert all measurements to metric for storage
      let height_cm: number;
      let weight_kg: number;

      if (heightUnit === 'ft') {
        height_cm = profileService.convertHeightToCm(data.height_feet!, data.height_inches!);
      } else {
        height_cm = data.height_cm!;
      }

      if (weightUnit === 'lbs') {
        weight_kg = profileService.convertWeightToKg(data.weight_lbs!);
      } else {
        weight_kg = data.weight_kg!;
      }

      const stepData = {
        intake_gender: data.gender,
        age: data.age,
        height_cm,
        weight_kg,
        height_unit: heightUnit,
        weight_unit: weightUnit,
      };

      updateFormData(stepData);
      await saveDraft(stepData);
      router.push('/(intake)/step2');
    } catch (error) {
      Alert.alert('Error', 'Failed to save your information. Please try again.');
    }
  };

  const genderOptions = [
    { label: 'Woman', value: 'Woman' as const },
    { label: 'Man', value: 'Man' as const },
  ];

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={styles.container}>
        <ScrollView style={styles.scrollContainer}>
        <View style={styles.content}>
          <ThemedText style={styles.title}>Tell us about yourself</ThemedText>
          <ThemedText style={styles.subtitle}>
            This information helps us create a safe and effective workout plan tailored to your body.
          </ThemedText>

          <Controller
            control={control}
            name="gender"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Gender</ThemedText>
                <View style={styles.genderContainer}>
                  {genderOptions.map((option) => (
                    <ThemedSelectionOption
                      key={option.value}
                      selected={value === option.value}
                      onPress={() => onChange(option.value)}
                      style={styles.genderOption}
                    >
                      <Text
                        style={[
                          styles.genderOptionText,
                          { color: value === option.value ? colors.contrastText : colors.text },
                        ]}
                      >
                        {option.label}
                      </Text>
                    </ThemedSelectionOption>
                  ))}
                </View>
                {errors.gender && (
                  <ThemedErrorText style={styles.errorText}>{errors.gender.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          <Controller
            control={control}
            name="age"
            render={({ field: { onChange, value } }) => (
              <View style={styles.inputContainer}>
                <ThemedText style={styles.label}>Age</ThemedText>
                <ThemedInput
                  ref={ageRef}
                  placeholder="Enter your age"
                  onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                  value={value ? value.toString() : ''}
                  keyboardType="numeric"
                  returnKeyType="next"
                  onSubmitEditing={() => {
                    if (heightUnit === 'cm') {
                      heightCmRef.current?.focus();
                    } else {
                      heightFeetRef.current?.focus();
                    }
                  }}
                  blurOnSubmit={false}
                />
                {errors.age && (
                  <ThemedErrorText style={styles.errorText}>{errors.age.message}</ThemedErrorText>
                )}
              </View>
            )}
          />

          {/* Height Section */}
          <View style={styles.inputContainer}>
            <ThemedText style={styles.label}>Height</ThemedText>
            
            {/* Unit Toggle */}
            <View style={[styles.unitToggleContainer, { backgroundColor: colors.accent }]}>
              <ThemedSelectionOption
                selected={heightUnit === 'cm'}
                onPress={() => handleHeightUnitChange('cm')}
                style={styles.unitToggle}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: heightUnit === 'cm' ? colors.contrastText : colors.text },
                  ]}
                >
                  cm
                </Text>
              </ThemedSelectionOption>
              <ThemedSelectionOption
                selected={heightUnit === 'ft'}
                onPress={() => handleHeightUnitChange('ft')}
                style={styles.unitToggle}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: heightUnit === 'ft' ? colors.contrastText : colors.text },
                  ]}
                >
                  ft & in
                </Text>
              </ThemedSelectionOption>
            </View>

            {heightUnit === 'cm' ? (
              <Controller
                control={control}
                name="height_cm"
                render={({ field: { onChange, value } }) => (
                  <ThemedInput
                    ref={heightCmRef}
                    placeholder="Enter your height in centimeters"
                    onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                    returnKeyType="next"
                    onSubmitEditing={() => weightRef.current?.focus()}
                    blurOnSubmit={false}
                  />
                )}
              />
            ) : (
              <View style={styles.feetInchesContainer}>
                <Controller
                  control={control}
                  name="height_feet"
                  render={({ field: { onChange, value } }) => (
                    <View style={styles.feetInchesInput}>
                      <ThemedInput
                        ref={heightFeetRef}
                        placeholder="Feet"
                        onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                        value={value ? value.toString() : ''}
                        keyboardType="numeric"
                        returnKeyType="next"
                        onSubmitEditing={() => heightInchesRef.current?.focus()}
                        blurOnSubmit={false}
                      />
                      <Text style={[styles.unitLabel, { color: colors.text }]}>ft</Text>
                    </View>
                  )}
                />
                <Controller
                  control={control}
                  name="height_inches"
                  render={({ field: { onChange, value } }) => (
                    <View style={styles.feetInchesInput}>
                      <ThemedInput
                        ref={heightInchesRef}
                        placeholder="Inches"
                        onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                        value={value ? value.toString() : ''}
                        keyboardType="numeric"
                        returnKeyType="next"
                        onSubmitEditing={() => weightRef.current?.focus()}
                        blurOnSubmit={false}
                      />
                      <Text style={[styles.unitLabel, { color: colors.text }]}>in</Text>
                    </View>
                  )}
                />
              </View>
            )}
            
            {(errors.height_cm || errors.height_feet || errors.height_inches) && (
              <ThemedErrorText style={styles.errorText}>
                {errors.height_cm?.message || errors.height_feet?.message || errors.height_inches?.message}
              </ThemedErrorText>
            )}
          </View>

          {/* Weight Section */}
          <View style={styles.inputContainer}>
            <ThemedText style={styles.label}>Weight</ThemedText>
            
            {/* Unit Toggle */}
            <View style={[styles.unitToggleContainer, { backgroundColor: colors.accent }]}>
              <ThemedSelectionOption
                selected={weightUnit === 'kg'}
                onPress={() => handleWeightUnitChange('kg')}
                style={styles.unitToggle}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: weightUnit === 'kg' ? colors.contrastText : colors.text },
                  ]}
                >
                  kg
                </Text>
              </ThemedSelectionOption>
              <ThemedSelectionOption
                selected={weightUnit === 'lbs'}
                onPress={() => handleWeightUnitChange('lbs')}
                style={styles.unitToggle}
              >
                <Text
                  style={[
                    styles.unitToggleText,
                    { color: weightUnit === 'lbs' ? colors.contrastText : colors.text },
                  ]}
                >
                  lbs
                </Text>
              </ThemedSelectionOption>
            </View>

            {weightUnit === 'kg' ? (
              <Controller
                control={control}
                name="weight_kg"
                render={({ field: { onChange, value } }) => (
                  <ThemedInput
                    ref={weightRef}
                    placeholder="Enter your weight in kilograms"
                    onChangeText={(text) => onChange(text ? parseFloat(text) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                    returnKeyType="done"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    blurOnSubmit={true}
                  />
                )}
              />
            ) : (
              <Controller
                control={control}
                name="weight_lbs"
                render={({ field: { onChange, value } }) => (
                  <ThemedInput
                    ref={weightRef}
                    placeholder="Enter your weight in pounds"
                    onChangeText={(text) => onChange(text ? parseInt(text, 10) : undefined)}
                    value={value ? value.toString() : ''}
                    keyboardType="numeric"
                    returnKeyType="done"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    blurOnSubmit={true}
                  />
                )}
              />
            )}
            
            {(errors.weight_kg || errors.weight_lbs) && (
              <ThemedErrorText style={styles.errorText}>
                {errors.weight_kg?.message || errors.weight_lbs?.message}
              </ThemedErrorText>
            )}
          </View>

          <View style={styles.buttonContainer}>
            <ThemedButton
              title="Next"
              onPress={handleSubmit(onSubmit)}
              style={styles.nextButton}
            />
          </View>
        </View>
      </ScrollView>
    </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 32,
    opacity: 0.8,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  genderContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  genderOption: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  genderOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  genderOptionTextSelected: {
    // Color is now handled dynamically in the component
  },
  unitToggleContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    borderRadius: 8,
    padding: 4,
  },
  unitToggle: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  unitToggleText: {
    fontSize: 14,
    fontWeight: '500',
  },
  unitToggleTextSelected: {
    // Color is now handled dynamically in the component
  },
  feetInchesContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  feetInchesInput: {
    flex: 1,
    position: 'relative',
  },
  unitLabel: {
    position: 'absolute',
    right: 16,
    top: 16,
    fontSize: 16,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  buttonContainer: {
    marginTop: 32,
    marginBottom: 32,
  },
  nextButton: {
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});