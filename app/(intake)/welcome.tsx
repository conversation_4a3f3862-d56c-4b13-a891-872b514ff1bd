import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Image } from 'react-native';
import { router } from 'expo-router';
import { useIntakeFormStore } from '@/store/intakeFormStore';
import { useThemeStore } from '@/store/themeStore';
import { ChevronRight, Target, Dumbbell, Calendar, Heart } from 'lucide-react-native';
import { ThemedView, ThemedText, ThemedButton, ThemedOverlay } from '@/components/ThemedComponents';

export default function IntakeWelcomeScreen() {
  const { loadDraft } = useIntakeFormStore();
  const { colors } = useThemeStore();

  useEffect(() => {
    // Load any existing draft data when the welcome screen loads
    loadDraft().catch(console.error);
  }, [loadDraft]);

  const features = [
    {
      icon: <Target size={24} color={colors.primary} />,
      title: 'Personalized Goals',
      description: 'Tell us what you want to achieve'
    },
    {
      icon: <Dumbbell size={24} color={colors.primary} />,
      title: 'Equipment Matching',
      description: 'Workouts tailored to your available equipment'
    },
    {
      icon: <Calendar size={24} color={colors.primary} />,
      title: 'Flexible Schedule',
      description: 'Programs that fit your lifestyle'
    },
    {
      icon: <Heart size={24} color={colors.primary} />,
      title: 'Safe & Effective',
      description: 'Workouts designed around your limitations'
    }
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View style={styles.content}>
          <View style={styles.heroSection}>
            <Image
              source={{ uri: 'https://images.pexels.com/photos/416778/pexels-photo-416778.jpeg?auto=compress&cs=tinysrgb&w=800' }}
              style={styles.heroImage}
            />
            <ThemedOverlay style={styles.heroOverlay}>
              <Text style={[styles.heroTitle, { color: colors.overlayText }]}>
                Let's Create Your Perfect Workout Plan
              </Text>
              <Text style={[styles.heroSubtitle, { color: colors.overlayText, opacity: 0.9 }]}>
                Answer a few questions to get a personalized fitness program designed just for you
              </Text>
            </ThemedOverlay>
          </View>

          <View style={styles.featuresSection}>
            <ThemedText style={styles.sectionTitle}>What We'll Cover</ThemedText>
            {features.map((feature, index) => (
              <View key={index} style={[styles.featureItem, { backgroundColor: colors.background, borderColor: colors.accent }]}>
                <View style={[styles.featureIcon, { backgroundColor: `${colors.primary}20` }]}>
                  {feature.icon}
                </View>
                <View style={styles.featureContent}>
                  <ThemedText style={styles.featureTitle}>{feature.title}</ThemedText>
                  <ThemedText style={styles.featureDescription}>{feature.description}</ThemedText>
                </View>
              </View>
            ))}
          </View>

          <View style={styles.timeSection}>
            <ThemedText style={styles.timeText}>⏱️ Takes about 3-5 minutes</ThemedText>
            <ThemedText style={styles.timeSubtext}>Your information is secure and private</ThemedText>
          </View>

          <ThemedButton
            title="Get Started"
            onPress={() => router.push('/(intake)/step1')}
            style={styles.startButton}
          >
            <View style={styles.buttonContent}>
              <Text style={[styles.startButtonText, { color: colors.contrastText }]}>Get Started</Text>
              <ChevronRight size={20} color={colors.contrastText} />
            </View>
          </ThemedButton>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    paddingBottom: 40,
  },
  heroSection: {
    position: 'relative',
    height: 280,
    marginBottom: 32,
  },
  heroImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  heroOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 24,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 34,
  },
  heroSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  featuresSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 18,
    opacity: 0.8,
  },
  timeSection: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  timeText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  timeSubtext: {
    fontSize: 14,
    opacity: 0.7,
  },
  startButton: {
    marginHorizontal: 20,
    marginBottom: 40,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
});