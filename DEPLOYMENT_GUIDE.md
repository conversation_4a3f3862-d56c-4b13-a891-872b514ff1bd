# FitAI Deployment Guide

## Prerequisites

### Development Environment
- [ ] Node.js 18+ installed
- [ ] Expo CLI installed (`npm install -g @expo/cli`)
- [ ] EAS CLI installed (`npm install -g eas-cli`)
- [ ] Git repository access
- [ ] Supabase project configured

### App Store Accounts
- [ ] Apple Developer Account ($99/year)
- [ ] Google Play Developer Account ($25 one-time)
- [ ] App Store Connect access
- [ ] Google Play Console access

### Certificates & Keys
- [ ] iOS Distribution Certificate
- [ ] iOS Provisioning Profiles
- [ ] Android Keystore file
- [ ] Google Service Account JSON (for Play Store)

## Environment Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Environment Variables
Create `.env` file with:
```
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_PROJECT_ID=your_expo_project_id
```

### 3. Configure EAS
```bash
eas login
eas build:configure
```

## Build Process

### Development Builds
```bash
# iOS Development
npm run build:development -- --platform ios

# Android Development  
npm run build:development -- --platform android
```

### Preview Builds
```bash
# iOS Preview (TestFlight)
npm run build:preview -- --platform ios

# Android Preview (Internal Testing)
npm run build:preview -- --platform android
```

### Production Builds
```bash
# iOS Production
npm run build:ios

# Android Production
npm run build:android

# Both platforms
npm run build:production
```

## App Store Submission

### iOS App Store

1. **Prepare Assets**
   - [ ] App icon (1024x1024)
   - [ ] Screenshots (various sizes)
   - [ ] App preview videos (optional)

2. **App Store Connect Setup**
   - [ ] Create app listing
   - [ ] Upload metadata from `app-store-metadata/ios/`
   - [ ] Set pricing and availability
   - [ ] Configure App Store Review Information

3. **Submit for Review**
   ```bash
   npm run submit:ios
   ```

4. **Review Checklist**
   - [ ] App follows iOS Human Interface Guidelines
   - [ ] All features work as described
   - [ ] Privacy policy linked
   - [ ] Age rating appropriate
   - [ ] No crashes or major bugs

### Android Play Store

1. **Prepare Assets**
   - [ ] App icon (512x512)
   - [ ] Feature graphic (1024x500)
   - [ ] Screenshots (phone and tablet)
   - [ ] App preview videos (optional)

2. **Play Console Setup**
   - [ ] Create app listing
   - [ ] Upload metadata from `app-store-metadata/android/`
   - [ ] Set content rating
   - [ ] Configure pricing and distribution

3. **Submit for Review**
   ```bash
   npm run submit:android
   ```

4. **Review Checklist**
   - [ ] App follows Material Design guidelines
   - [ ] All permissions justified
   - [ ] Privacy policy linked
   - [ ] Target API level requirements met
   - [ ] No policy violations

## Over-the-Air Updates

### Preview Updates
```bash
npm run update:preview
```

### Production Updates
```bash
npm run update:production
```

## Monitoring & Analytics

### Post-Launch Monitoring
- [ ] Crash reporting (Expo/Sentry)
- [ ] Performance monitoring
- [ ] User analytics
- [ ] App store ratings/reviews
- [ ] Server monitoring (Supabase)

### Key Metrics to Track
- [ ] App store conversion rates
- [ ] User retention (Day 1, 7, 30)
- [ ] Crash-free sessions
- [ ] API response times
- [ ] User engagement metrics

## Troubleshooting

### Common Build Issues
1. **iOS Build Fails**
   - Check provisioning profiles
   - Verify bundle identifier
   - Update Xcode if needed

2. **Android Build Fails**
   - Check keystore configuration
   - Verify package name
   - Update Android SDK

3. **Submission Rejected**
   - Review rejection reasons
   - Fix issues and resubmit
   - Update metadata if needed

### Support Resources
- [Expo Documentation](https://docs.expo.dev/)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
- [Google Play Policy](https://play.google.com/about/developer-content-policy/)

## Release Checklist

### Pre-Release
- [ ] All features tested on physical devices
- [ ] Performance testing completed
- [ ] Security audit passed
- [ ] Accessibility testing done
- [ ] Legal review completed (privacy policy, terms)
- [ ] Marketing materials prepared

### Release Day
- [ ] Final build uploaded
- [ ] App store listings updated
- [ ] Press release prepared
- [ ] Social media posts scheduled
- [ ] Support documentation updated
- [ ] Team notified of release

### Post-Release
- [ ] Monitor crash reports
- [ ] Track user feedback
- [ ] Respond to app store reviews
- [ ] Plan next update cycle
- [ ] Analyze performance metrics

## Version Management

### Version Numbering
- **iOS**: Use semantic versioning (1.0.0, 1.0.1, 1.1.0)
- **Android**: Auto-increment version codes
- **Updates**: Use EAS Update for non-native changes

### Release Branches
- `main`: Production-ready code
- `develop`: Development branch
- `release/x.x.x`: Release preparation
- `hotfix/x.x.x`: Critical fixes

## Security Considerations

### Pre-Deployment Security
- [ ] API keys secured (not in client code)
- [ ] Database RLS policies tested
- [ ] Authentication flows validated
- [ ] Data encryption verified
- [ ] Third-party integrations audited

### Ongoing Security
- [ ] Regular dependency updates
- [ ] Security monitoring enabled
- [ ] Incident response plan ready
- [ ] Data backup procedures tested
- [ ] Compliance requirements met

---

**Last Updated**: January 11, 2025
**Version**: 1.0.0
