// Debug script to test workout stats service
// This can be run in the browser console or as a Node.js script

const testWorkoutStatsService = {
  // Simulate the updated service logic
  calculateCurrentStreak(workoutData) {
    const completedWorkouts = workoutData
      .filter(w => w.completed_at !== null)
      .map(w => new Date(w.completed_at).toDateString())
      .filter((date, index, arr) => arr.indexOf(date) === index)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

    let streak = 0;
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString();

    if (completedWorkouts.includes(today) || completedWorkouts.includes(yesterday)) {
      for (let i = 0; i < completedWorkouts.length; i++) {
        const currentDate = new Date(completedWorkouts[i]);
        const expectedDate = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        
        if (currentDate.toDateString() === expectedDate.toDateString()) {
          streak++;
        } else {
          break;
        }
      }
    }

    return streak;
  },

  calculateAchievementsFromWorkouts(workoutData) {
    const completedWorkouts = workoutData.filter(w => w.completed_at !== null);
    let achievements = 0;

    if (completedWorkouts.length >= 1) achievements++;
    if (completedWorkouts.length >= 5) achievements++;
    if (completedWorkouts.length >= 10) achievements++;
    if (completedWorkouts.length >= 25) achievements++;
    if (completedWorkouts.length >= 50) achievements++;

    const currentStreak = this.calculateCurrentStreak(workoutData);
    if (currentStreak >= 3) achievements++;
    if (currentStreak >= 7) achievements++;
    if (currentStreak >= 14) achievements++;

    const totalDuration = completedWorkouts.reduce((sum, w) => sum + (w.duration_seconds || 0), 0);
    const totalHours = totalDuration / 3600;
    if (totalHours >= 10) achievements++;
    if (totalHours >= 25) achievements++;

    return achievements;
  },

  processWorkoutData(workoutData) {
    const totalWorkouts = workoutData?.length || 0;
    const completedWorkouts = workoutData?.filter(w => w.completed_at !== null).length || 0;
    const completionRate = totalWorkouts > 0 ? Math.round((completedWorkouts / totalWorkouts) * 100) : 0;
    const achievements = this.calculateAchievementsFromWorkouts(workoutData || []);
    const currentStreak = this.calculateCurrentStreak(workoutData || []);
    const weeklyAverage = Math.round(totalWorkouts / 4);

    const lastWorkout = workoutData
      ?.filter(w => w.completed_at !== null)
      ?.sort((a, b) => new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime())[0];

    return {
      totalWorkouts,
      completionRate,
      achievements,
      currentStreak,
      weeklyAverage,
      lastWorkoutDate: lastWorkout?.completed_at
    };
  }
};

// Test with sample data that matches the actual database schema
const sampleWorkoutData = [
  {
    id: "708f1620-de3b-4387-95fa-336ea59f979a",
    user_id: "d5e328b3-c955-4a75-a7e2-02ca0a9eabd5",
    completed_at: "2025-06-21T15:20:51.397Z",
    started_at: "2025-06-21T15:20:39.846Z",
    duration_seconds: 11,
    created_at: "2025-06-21T15:20:39.846Z"
  },
  {
    id: "5db0559a-a936-4397-93d6-e665c1a7a04e",
    user_id: "d5e328b3-c955-4a75-a7e2-02ca0a9eabd5",
    completed_at: "2025-01-13T10:30:00Z",
    started_at: "2025-01-13T10:00:00Z",
    duration_seconds: 1800,
    created_at: "2025-01-13T10:00:00Z"
  },
  {
    id: "53bec50e-1fec-42a0-99bb-0238b6017baf",
    user_id: "d5e328b3-c955-4a75-a7e2-02ca0a9eabd5",
    completed_at: "2025-01-14T10:45:00Z",
    started_at: "2025-01-14T10:00:00Z",
    duration_seconds: 2700,
    created_at: "2025-01-14T10:00:00Z"
  }
];

console.log('Testing workout stats service...');
const result = testWorkoutStatsService.processWorkoutData(sampleWorkoutData);
console.log('Result:', result);

// Expected output:
// {
//   totalWorkouts: 3,
//   completionRate: 100, // All 3 have completed_at
//   achievements: 2, // First workout + 5 workouts milestone
//   currentStreak: 0, // No recent consecutive days
//   weeklyAverage: 1, // 3 workouts / 4 weeks
//   lastWorkoutDate: "2025-06-21T15:20:51.397Z"
// }
