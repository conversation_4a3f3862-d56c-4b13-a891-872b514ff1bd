#!/bin/bash

# FitnessApp Production Environment Setup Script
# This script sets up the production environment for the FitnessApp

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local tools=("supabase" "vercel" "eas" "node" "npm")
    local missing_tools=()
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_info "Please install the missing tools and run this script again."
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Setup Supabase production project
setup_supabase_production() {
    log_info "Setting up Supabase production project..."
    
    # Check if already logged in
    if ! supabase projects list &> /dev/null; then
        log_info "Please log in to Supabase CLI"
        supabase login
    fi
    
    # Create production project (if not exists)
    log_info "Creating Supabase production project..."
    
    # Note: This would typically be done through the Supabase dashboard
    # The CLI doesn't support project creation yet
    log_warning "Please create the production project manually in the Supabase dashboard"
    log_info "1. Go to https://supabase.com/dashboard"
    log_info "2. Create a new project named 'fitnessapp-production'"
    log_info "3. Note down the project URL and anon key"
    
    read -p "Press Enter after creating the production project..."
    
    # Link to production project
    read -p "Enter your production project reference ID: " PROJECT_REF
    
    if [ -n "$PROJECT_REF" ]; then
        supabase link --project-ref "$PROJECT_REF"
        log_success "Linked to production Supabase project"
    else
        log_error "Project reference ID is required"
        exit 1
    fi
    
    # Deploy database schema and functions
    log_info "Deploying database schema to production..."
    supabase db push
    
    # Deploy Edge Functions
    log_info "Deploying Edge Functions to production..."
    supabase functions deploy --no-verify-jwt
    
    log_success "Supabase production setup completed"
}

# Setup environment variables
setup_environment_variables() {
    log_info "Setting up environment variables..."
    
    # Create production environment file template
    cat > .env.production.template << EOF
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME=FitnessApp
NEXT_PUBLIC_APP_VERSION=1.0.0

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-domain.com

# External Services
OPENAI_API_KEY=your-openai-api-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Email Service
RESEND_API_KEY=your-resend-api-key
FROM_EMAIL=<EMAIL>

# Analytics
GOOGLE_ANALYTICS_ID=your-ga-id
MIXPANEL_TOKEN=your-mixpanel-token

# Monitoring
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project

# Security
ENCRYPTION_KEY=your-encryption-key
JWT_SECRET=your-jwt-secret

# Rate Limiting
UPSTASH_REDIS_REST_URL=your-upstash-url
UPSTASH_REDIS_REST_TOKEN=your-upstash-token
EOF

    log_success "Environment template created: .env.production.template"
    log_warning "Please fill in the actual values and rename to .env.production"
}

# Setup Vercel production deployment
setup_vercel_production() {
    log_info "Setting up Vercel production deployment..."
    
    # Check if logged in to Vercel
    if ! vercel whoami &> /dev/null; then
        log_info "Please log in to Vercel CLI"
        vercel login
    fi
    
    # Link to Vercel project
    cd web
    vercel link
    
    # Set up environment variables in Vercel
    log_info "Setting up Vercel environment variables..."
    log_warning "Please set the following environment variables in Vercel dashboard:"
    log_info "1. Go to your Vercel project settings"
    log_info "2. Navigate to Environment Variables"
    log_info "3. Add all variables from .env.production.template"
    
    cd ..
    log_success "Vercel production setup completed"
}

# Setup mobile app production builds
setup_mobile_production() {
    log_info "Setting up mobile app production builds..."
    
    cd mobile
    
    # Check if logged in to Expo
    if ! eas whoami &> /dev/null; then
        log_info "Please log in to EAS CLI"
        eas login
    fi
    
    # Configure EAS project
    if [ ! -f "eas.json" ]; then
        log_error "eas.json not found. Please run this script from the project root."
        exit 1
    fi
    
    # Build production apps
    log_info "Building production apps..."
    log_warning "This will trigger production builds. Continue? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        eas build --platform all --profile production --non-interactive
        log_success "Production builds initiated"
    else
        log_info "Skipping production builds"
    fi
    
    cd ..
}

# Setup monitoring and alerting
setup_monitoring() {
    log_info "Setting up monitoring and alerting..."
    
    # Create monitoring configuration
    cat > monitoring-config.json << EOF
{
  "services": {
    "web": {
      "url": "https://your-domain.com",
      "healthcheck": "/api/health",
      "alerts": {
        "uptime": true,
        "performance": true,
        "errors": true
      }
    },
    "api": {
      "url": "https://your-project.supabase.co",
      "healthcheck": "/rest/v1/",
      "alerts": {
        "uptime": true,
        "database": true,
        "functions": true
      }
    }
  },
  "notifications": {
    "email": "<EMAIL>",
    "slack": "your-slack-webhook-url",
    "discord": "your-discord-webhook-url"
  },
  "thresholds": {
    "response_time": 2000,
    "error_rate": 0.05,
    "uptime": 0.99
  }
}
EOF

    log_success "Monitoring configuration created: monitoring-config.json"
}

# Setup security configurations
setup_security() {
    log_info "Setting up security configurations..."
    
    # Create security checklist
    cat > security-checklist.md << EOF
# Production Security Checklist

## Environment Variables
- [ ] All secrets are stored securely (not in code)
- [ ] Environment variables are set in deployment platforms
- [ ] No development keys are used in production

## Database Security
- [ ] Row Level Security (RLS) is enabled on all tables
- [ ] Service role key is kept secure
- [ ] Database backups are configured
- [ ] Connection pooling is configured

## API Security
- [ ] Rate limiting is implemented
- [ ] CORS is properly configured
- [ ] Input validation is in place
- [ ] Authentication is required for protected routes

## Web Security
- [ ] HTTPS is enforced
- [ ] Security headers are configured
- [ ] CSP (Content Security Policy) is set
- [ ] XSS protection is enabled

## Mobile Security
- [ ] App signing certificates are secure
- [ ] API keys are not hardcoded
- [ ] Certificate pinning is implemented
- [ ] Obfuscation is applied

## Monitoring
- [ ] Error tracking is configured
- [ ] Performance monitoring is active
- [ ] Security alerts are set up
- [ ] Log aggregation is configured

## Compliance
- [ ] Privacy policy is updated
- [ ] Terms of service are current
- [ ] GDPR compliance is verified
- [ ] Data retention policies are set
EOF

    log_success "Security checklist created: security-checklist.md"
}

# Main setup function
main() {
    log_info "Starting FitnessApp production environment setup..."
    
    check_prerequisites
    setup_environment_variables
    setup_supabase_production
    setup_vercel_production
    setup_mobile_production
    setup_monitoring
    setup_security
    
    log_success "Production environment setup completed!"
    log_info "Next steps:"
    log_info "1. Fill in environment variables in .env.production.template"
    log_info "2. Set environment variables in Vercel dashboard"
    log_info "3. Configure monitoring services"
    log_info "4. Review and complete security checklist"
    log_info "5. Test the production deployment"
}

# Run main function
main "$@"
