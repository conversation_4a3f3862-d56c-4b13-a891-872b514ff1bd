# Test Auto-Approval Programs Function

## Function Overview
The `auto-approve-programs` Edge Function runs daily via cron job to automatically approve programs that have passed their review deadline and handle program transitions.

## Key Features Implemented

### 1. **Overdue Program Detection**
- Finds programs with `review_deadline_date` <= today
- Targets programs with status `ai_generated_pending_review` or `scheduled_pending_review`
- Orders by deadline date (oldest first) for priority processing

### 2. **Auto-Approval Process**
- Updates program status to `auto_approved`
- Sets `coach_reviewed_at` timestamp
- Adds explanatory note about automatic approval with days overdue
- Logs approval action in `operation_logs` table

### 3. **Smart Program Transitions**
- **Immediate Transition**: For programs with `cycle_status = 'pending_transition'`
- **Scheduled Transition**: For programs that need to wait for current cycle to end
- **Seamless Handoff**: Marks current program as completed, activates new program

### 4. **Comprehensive Tracking**
- Records transitions in `program_transitions` table
- Schedules future transitions using `scheduled_generation_jobs`
- Detailed logging for monitoring and debugging

## Test Scenarios

### 1. **Normal Auto-Approval Operation**
```bash
curl -X POST https://your-project.supabase.co/functions/v1/auto-approve-programs \
  -H "Authorization: Bearer YOUR_SERVICE_KEY"
```

**Expected Response:**
```json
{
  "success": true,
  "approvedPrograms": 3,
  "transitionedPrograms": 1,
  "scheduledTransitions": 2,
  "errors": [],
  "executionTime": 1800,
  "overduePrograms": [...]
}
```

### 2. **No Overdue Programs**
When all programs are reviewed on time:
```json
{
  "success": true,
  "approvedPrograms": 0,
  "transitionedPrograms": 0,
  "scheduledTransitions": 0,
  "errors": [],
  "executionTime": 120
}
```

### 3. **Mixed Results with Errors**
When some operations succeed and others fail:
```json
{
  "success": true,
  "approvedPrograms": 2,
  "transitionedPrograms": 1,
  "scheduledTransitions": 0,
  "errors": ["Program xyz: Failed to schedule transition"],
  "executionTime": 2100
}
```

## Program Transition Logic

### **Immediate Transition** (`cycle_status = 'pending_transition'`)
1. Mark current active program as `completed`
2. Activate new program (`active_by_client`)
3. Set `client_start_date` and `cycle_start_date` to today
4. Record transition in `program_transitions` table

### **Scheduled Transition** (current cycle still active)
1. Calculate when current cycle ends (28 days from start)
2. Schedule transition job for that date
3. Program remains `auto_approved` until transition date

## Database Operations

### **Tables Modified:**
- `workout_programs` - Update status, dates, and cycle information
- `program_transitions` - Record completed transitions
- `scheduled_generation_jobs` - Schedule future transitions
- `operation_logs` - Log all operations for monitoring

### **Status Transitions:**
- `scheduled_pending_review` → `auto_approved`
- `ai_generated_pending_review` → `auto_approved`
- `auto_approved` → `active_by_client` (during transition)

## Cron Job Configuration

This function is designed to run daily after the program scheduler:

```sql
-- Daily auto-approval scheduler (10:00 AM UTC)
SELECT cron.schedule(
  'daily-auto-approval',
  '0 10 * * *',
  $$
  SELECT net.http_post(
    url := 'https://your-project.supabase.co/functions/v1/auto-approve-programs',
    headers := '{"Authorization": "Bearer YOUR_SERVICE_KEY"}',
    body := '{}'
  );
  $$
);
```

## Key Benefits

1. **Automatic Coach Workflow**: Programs don't get stuck waiting for coach review
2. **Seamless User Experience**: Users always have access to their next program
3. **Flexible Timing**: Handles both immediate and scheduled transitions
4. **Comprehensive Logging**: Full audit trail of all auto-approvals and transitions
5. **Error Resilience**: Individual failures don't affect other programs

## Monitoring Points

- **Approval Rate**: How many programs are auto-approved vs coach-reviewed
- **Transition Success**: Success rate of program transitions
- **Error Patterns**: Common failure modes for debugging
- **Execution Time**: Performance monitoring for optimization

## Coach Notification Integration

The function sets descriptive `coach_notes_for_client` that can be used to:
- Inform clients about automatic approval
- Explain the review process
- Provide context for the program transition

Example note: "Automatically approved - no coach review within deadline (3 days overdue)"
