{"cli": {"version": ">= 5.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"resourceClass": "medium", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true}, "android": {"resourceClass": "medium", "buildType": "apk"}}, "production": {"ios": {"resourceClass": "m-medium", "autoIncrement": "buildNumber"}, "android": {"resourceClass": "medium", "autoIncrement": "versionCode"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "production"}}, "preview": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}}