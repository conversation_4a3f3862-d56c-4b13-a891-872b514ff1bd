-- Restore exercise video URLs with high-quality demonstration videos
-- Run this AFTER running the fix_exercises_table.sql script

-- Update exercises with curated video URLs from reputable fitness channels
UPDATE exercises SET video_url = CASE name
  -- BODYWEIGHT EXERCISES
  WHEN 'Push-up' THEN 'https://www.youtube.com/watch?v=IODxDxX7oi4'
  WHEN 'Bodyweight Squat' THEN 'https://www.youtube.com/watch?v=aclHkVaku9U'
  WHEN 'Plank' THEN 'https://www.youtube.com/watch?v=ASdvN_XEl_c'
  WHEN 'Lunges' THEN 'https://www.youtube.com/watch?v=QE_hU5kWmZs'
  WHEN 'Mountain Climbers' THEN 'https://www.youtube.com/watch?v=nmwgirgXLYM'
  WHEN 'Burpee' THEN 'https://www.youtube.com/watch?v=auBLPXO8Fww'
  WHEN 'Pull-up' THEN 'https://www.youtube.com/watch?v=eGo4IYlbE5g'
  WHEN 'Dips' THEN 'https://www.youtube.com/watch?v=2z8JmcrW-As'
  WHEN 'Wall Sit' THEN 'https://www.youtube.com/watch?v=y-wV4Venusw'
  WHEN 'Jumping Jacks' THEN 'https://www.youtube.com/watch?v=c4DAnQ6DtF8'
  WHEN 'High Knees' THEN 'https://www.youtube.com/watch?v=8opcQdC-V-U'
  WHEN 'Butt Kicks' THEN 'https://www.youtube.com/watch?v=1Zg0SkdCjzs'
  WHEN 'Side Plank' THEN 'https://www.youtube.com/watch?v=K2VljzCC16g'
  WHEN 'Glute Bridge' THEN 'https://www.youtube.com/watch?v=OUgsJ8-Vi0E'
  WHEN 'Calf Raises' THEN 'https://www.youtube.com/watch?v=gwLzBJYoWlI'
  WHEN 'Tricep Dips' THEN 'https://www.youtube.com/watch?v=0326dy_-CzM'
  WHEN 'Superman' THEN 'https://www.youtube.com/watch?v=cc6UVRS7PW4'
  WHEN 'Bicycle Crunches' THEN 'https://www.youtube.com/watch?v=9FGilxCbdz8'
  WHEN 'Bear Crawl' THEN 'https://www.youtube.com/watch?v=Azrg8Kt2SfU'
  WHEN 'Inchworm' THEN 'https://www.youtube.com/watch?v=qvqLqH27BdU'
  WHEN 'Single Leg Deadlift' THEN 'https://www.youtube.com/watch?v=p3q4pAHIJXk'
  WHEN 'Step-ups' THEN 'https://www.youtube.com/watch?v=5MYiM4ZbO-I'
  WHEN 'Pike Push-up' THEN 'https://www.youtube.com/watch?v=spoSDx8P7sM'
  WHEN 'Reverse Lunge' THEN 'https://www.youtube.com/watch?v=xXc4v1sQQXo'
  WHEN 'Squat Jump' THEN 'https://www.youtube.com/watch?v=A-cFYWvaHr0'
  WHEN 'Push-up to T' THEN 'https://www.youtube.com/watch?v=3sKqzNGGBhM'
  WHEN 'Hollow Body Hold' THEN 'https://www.youtube.com/watch?v=LlDNef_Ztsc'
  WHEN 'V-ups' THEN 'https://www.youtube.com/watch?v=7UVgs18Y1P4'

  -- DUMBBELL EXERCISES
  WHEN 'Dumbbell Bench Press' THEN 'https://www.youtube.com/watch?v=QsYre__-aro'
  WHEN 'Dumbbell Rows' THEN 'https://www.youtube.com/watch?v=roCP6wCXPqo'
  WHEN 'Dumbbell Shoulder Press' THEN 'https://www.youtube.com/watch?v=qEwKCR5JCog'
  WHEN 'Dumbbell Bicep Curls' THEN 'https://www.youtube.com/watch?v=ykJmrZ5v0Oo'
  WHEN 'Dumbbell Goblet Squat' THEN 'https://www.youtube.com/watch?v=MeIiIdhvXT4'
  WHEN 'Dumbbell Deadlift' THEN 'https://www.youtube.com/watch?v=lJ3QwaXNJfw'
  WHEN 'Dumbbell Thrusters' THEN 'https://www.youtube.com/watch?v=joF6NjhvVhE'

  -- BARBELL EXERCISES
  WHEN 'Barbell Bench Press' THEN 'https://www.youtube.com/watch?v=rT7DgCr-3pg'
  WHEN 'Barbell Back Squat' THEN 'https://www.youtube.com/watch?v=ultWZbUMPL8'
  WHEN 'Barbell Deadlift' THEN 'https://www.youtube.com/watch?v=ytGaGIn3SjE'
  WHEN 'Barbell Rows' THEN 'https://www.youtube.com/watch?v=9efgcAjQe7E'
  WHEN 'Overhead Press' THEN 'https://www.youtube.com/watch?v=2yjwXTZQDDI'
  WHEN 'Barbell Hip Thrust' THEN 'https://www.youtube.com/watch?v=xDmFkJxPzeM'

  -- MACHINE EXERCISES
  WHEN 'Lat Pulldown' THEN 'https://www.youtube.com/watch?v=CAwf7n6Luuc'
  WHEN 'Leg Press' THEN 'https://www.youtube.com/watch?v=IZxyjW7MPJQ'
  WHEN 'Chest Press Machine' THEN 'https://www.youtube.com/watch?v=xUm0BiZCWlQ'
  WHEN 'Leg Curl' THEN 'https://www.youtube.com/watch?v=ELOCsoDSmrg'
  WHEN 'Leg Extension' THEN 'https://www.youtube.com/watch?v=YyvSfVjQeL0'
  WHEN 'Cable Rows' THEN 'https://www.youtube.com/watch?v=xQNrFHEMhI4'

  -- CARDIO EXERCISES
  WHEN 'Treadmill Running' THEN 'https://www.youtube.com/watch?v=wRkeBVMQSgg'
  WHEN 'Stationary Bike' THEN 'https://www.youtube.com/watch?v=1VYlOKUdylM'
  WHEN 'Rowing Machine' THEN 'https://www.youtube.com/watch?v=zQPYXdeqvSA'
  WHEN 'Elliptical' THEN 'https://www.youtube.com/watch?v=TqtnpntJBIE'

  -- FUNCTIONAL/CORE EXERCISES
  WHEN 'Russian Twists' THEN 'https://www.youtube.com/watch?v=wkD8rjkodUI'
  WHEN 'Dead Bug' THEN 'https://www.youtube.com/watch?v=hv8mVm4_jU8'
  WHEN 'Bird Dog' THEN 'https://www.youtube.com/watch?v=wiFNA3sqjCA'
  WHEN 'Farmer''s Walk' THEN 'https://www.youtube.com/watch?v=p3G2BjrcbAw'
  WHEN 'Turkish Get-up' THEN 'https://www.youtube.com/watch?v=0bWRPC49-KI'

  -- KETTLEBELL EXERCISES
  WHEN 'Kettlebell Swing' THEN 'https://www.youtube.com/watch?v=YSxHifyI6s8'
  WHEN 'Kettlebell Goblet Squat' THEN 'https://www.youtube.com/watch?v=NIbCyzF4dYs'
  WHEN 'Kettlebell Press' THEN 'https://www.youtube.com/watch?v=TRqHbQzP5_Y'

  -- STRETCHING/MOBILITY
  WHEN 'Cat-Cow Stretch' THEN 'https://www.youtube.com/watch?v=K9bK0BwKFjs'
  WHEN 'Child''s Pose' THEN 'https://www.youtube.com/watch?v=2MTOWdKOiAw'
  WHEN 'Downward Dog' THEN 'https://www.youtube.com/watch?v=Rnfg_dVZFmw'

  ELSE video_url
END;

-- Verify video URLs were added
SELECT name, 
       CASE 
         WHEN video_url IS NOT NULL AND video_url != '' THEN '✅ Has Video'
         ELSE '❌ No Video'
       END as video_status
FROM exercises 
ORDER BY name;

-- Count exercises with and without videos
SELECT 
  COUNT(CASE WHEN video_url IS NOT NULL AND video_url != '' THEN 1 END) as exercises_with_videos,
  COUNT(CASE WHEN video_url IS NULL OR video_url = '' THEN 1 END) as exercises_without_videos,
  COUNT(*) as total_exercises
FROM exercises;
