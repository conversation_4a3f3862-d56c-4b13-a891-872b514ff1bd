# 🔧 Technical Specifications - Hybrid AI Workout System

## 📋 **Database Schema Changes**

### **New Tables**

#### 1. Program Transitions Table
```sql
CREATE TABLE program_transitions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  from_program_id UUID REFERENCES workout_programs(id),
  to_program_id UUID NOT NULL REFERENCES workout_programs(id),
  transition_date DATE NOT NULL,
  transition_status TEXT CHECK (transition_status IN ('scheduled', 'completed', 'failed')) DEFAULT 'scheduled',
  transition_type TEXT CHECK (transition_type IN ('automatic', 'manual', 'coach_initiated')) DEFAULT 'automatic',
  created_at TIMESTAMPTZ DEFAULT now(),
  completed_at TIMESTAMPTZ,
  error_message TEXT
);

-- Indexes
CREATE INDEX idx_program_transitions_user_id ON program_transitions (user_id);
CREATE INDEX idx_program_transitions_date ON program_transitions (transition_date);
CREATE INDEX idx_program_transitions_status ON program_transitions (transition_status);
```

#### 2. Scheduled Generation Jobs Table
```sql
CREATE TABLE scheduled_generation_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  scheduled_date DATE NOT NULL,
  execution_status TEXT CHECK (execution_status IN ('pending', 'running', 'completed', 'failed')) DEFAULT 'pending',
  job_type TEXT CHECK (job_type IN ('program_generation', 'auto_approval', 'transition')) NOT NULL,
  cycle_number INTEGER,
  created_at TIMESTAMPTZ DEFAULT now(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  
  -- Prevent duplicate jobs
  UNIQUE(user_id, scheduled_date, job_type)
);

-- Indexes
CREATE INDEX idx_scheduled_jobs_date_status ON scheduled_generation_jobs (scheduled_date, execution_status);
CREATE INDEX idx_scheduled_jobs_user_type ON scheduled_generation_jobs (user_id, job_type);
CREATE INDEX idx_scheduled_jobs_pending ON scheduled_generation_jobs (execution_status) WHERE execution_status = 'pending';
```

#### 3. Operation Logs Table
```sql
CREATE TABLE operation_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  operation_type TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  program_id UUID REFERENCES workout_programs(id),
  operation_data JSONB,
  execution_status TEXT CHECK (execution_status IN ('success', 'error', 'warning')) NOT NULL,
  execution_time_ms INTEGER,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes
CREATE INDEX idx_operation_logs_type_status ON operation_logs (operation_type, execution_status);
CREATE INDEX idx_operation_logs_created_at ON operation_logs (created_at);
CREATE INDEX idx_operation_logs_user_id ON operation_logs (user_id) WHERE user_id IS NOT NULL;
```

### **Modified Tables**

#### Enhanced Workout Programs Table
```sql
-- Add new columns to existing workout_programs table
ALTER TABLE workout_programs ADD COLUMN IF NOT EXISTS cycle_number INTEGER DEFAULT 1;
ALTER TABLE workout_programs ADD COLUMN IF NOT EXISTS cycle_start_date DATE;
ALTER TABLE workout_programs ADD COLUMN IF NOT EXISTS cycle_status TEXT CHECK (cycle_status IN ('active', 'pending_transition', 'completed', 'archived')) DEFAULT 'active';
ALTER TABLE workout_programs ADD COLUMN IF NOT EXISTS auto_generated_at TIMESTAMPTZ;
ALTER TABLE workout_programs ADD COLUMN IF NOT EXISTS review_deadline_date DATE;
ALTER TABLE workout_programs ADD COLUMN IF NOT EXISTS generation_type TEXT CHECK (generation_type IN ('initial', 'scheduled', 'manual')) DEFAULT 'initial';
ALTER TABLE workout_programs ADD COLUMN IF NOT EXISTS previous_program_id UUID REFERENCES workout_programs(id);

-- Update status constraint to include new values
ALTER TABLE workout_programs DROP CONSTRAINT IF EXISTS workout_programs_status_check;
ALTER TABLE workout_programs ADD CONSTRAINT workout_programs_status_check 
CHECK (status IN (
  'ai_generated_pending_review',
  'scheduled_pending_review',
  'coach_approved',
  'auto_approved',
  'coach_edited',
  'active_by_client',
  'completed_by_client',
  'archived'
));

-- Add new indexes
CREATE INDEX IF NOT EXISTS idx_workout_programs_cycle ON workout_programs (user_id, cycle_number);
CREATE INDEX IF NOT EXISTS idx_workout_programs_review_deadline ON workout_programs (review_deadline_date) WHERE review_deadline_date IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_workout_programs_cycle_status ON workout_programs (cycle_status);
CREATE INDEX IF NOT EXISTS idx_workout_programs_generation_type ON workout_programs (generation_type);
```

### **Database Views**

#### Client Program Access Views
```sql
-- Current active program for client
CREATE OR REPLACE VIEW client_current_program AS
SELECT 
  wp.*,
  'current' as program_type,
  CASE 
    WHEN wp.cycle_start_date IS NOT NULL 
    THEN wp.cycle_start_date + INTERVAL '28 days'
    ELSE wp.created_at::date + INTERVAL '28 days'
  END as next_transition_date
FROM workout_programs wp
WHERE wp.cycle_status = 'active'
  AND wp.status IN ('coach_approved', 'auto_approved', 'active_by_client');

-- Program history for client (current + previous)
CREATE OR REPLACE VIEW client_program_history AS
SELECT 
  wp.*,
  CASE 
    WHEN wp.cycle_status = 'active' THEN 'current'
    WHEN wp.cycle_number = (
      SELECT MAX(cycle_number) - 1 
      FROM workout_programs wp2 
      WHERE wp2.user_id = wp.user_id 
        AND wp2.cycle_status IN ('completed', 'active')
    ) THEN 'previous'
    ELSE 'historical'
  END as program_type,
  CASE 
    WHEN wp.cycle_start_date IS NOT NULL 
    THEN wp.cycle_start_date + INTERVAL '28 days'
    ELSE wp.created_at::date + INTERVAL '28 days'
  END as cycle_end_date
FROM workout_programs wp
WHERE wp.status NOT IN ('archived')
ORDER BY wp.user_id, wp.cycle_number DESC;

-- Coach pending reviews with priority
CREATE OR REPLACE VIEW coach_pending_reviews AS
SELECT 
  wp.*,
  p.full_name as client_name,
  CASE 
    WHEN wp.review_deadline_date <= CURRENT_DATE THEN 'overdue'
    WHEN wp.review_deadline_date <= CURRENT_DATE + INTERVAL '2 days' THEN 'urgent'
    WHEN wp.review_deadline_date <= CURRENT_DATE + INTERVAL '5 days' THEN 'normal'
    ELSE 'low'
  END as priority,
  (wp.review_deadline_date - CURRENT_DATE) as days_remaining
FROM workout_programs wp
JOIN profiles p ON wp.user_id = p.id
WHERE wp.status IN ('ai_generated_pending_review', 'scheduled_pending_review')
ORDER BY 
  CASE 
    WHEN wp.review_deadline_date <= CURRENT_DATE THEN 1
    WHEN wp.review_deadline_date <= CURRENT_DATE + INTERVAL '2 days' THEN 2
    WHEN wp.review_deadline_date <= CURRENT_DATE + INTERVAL '5 days' THEN 3
    ELSE 4
  END,
  wp.review_deadline_date ASC;
```

---

## ⚡ **Edge Function Specifications**

### **1. Enhanced Generate-Workout-Program Function**

#### Input Interface
```typescript
interface GenerationRequest {
  userId: string;
  generationType: 'initial' | 'scheduled' | 'manual';
  cycleNumber?: number;
  previousProgramId?: string;
  coachNotes?: string;
  isRegeneration?: boolean;
}

interface GenerationResponse {
  success: boolean;
  programId?: string;
  message?: string;
  cycleNumber?: number;
  reviewDeadline?: string;
  error?: string;
}
```

#### Key Logic Changes
```typescript
// Enhanced duplicate prevention
const checkGenerationEligibility = async (userId: string, generationType: string) => {
  if (generationType === 'initial') {
    // Only 5-minute race condition protection
    return await checkRaceCondition(userId, 5);
  } else if (generationType === 'scheduled') {
    // Check if user is eligible for 21-day generation
    return await checkScheduledEligibility(userId);
  } else if (generationType === 'manual') {
    // Coach override - always allowed
    return { eligible: true, reason: 'manual_override' };
  }
};

// Cycle management
const determineCycleNumber = async (userId: string) => {
  const { data } = await supabaseClient
    .from('workout_programs')
    .select('cycle_number')
    .eq('user_id', userId)
    .order('cycle_number', { ascending: false })
    .limit(1);
  
  return data?.[0]?.cycle_number ? data[0].cycle_number + 1 : 1;
};

// Program storage with cycle info
const storeWorkoutProgramWithCycle = async (programData, cycleInfo) => {
  const reviewDeadline = new Date();
  reviewDeadline.setDate(reviewDeadline.getDate() + 7);
  
  const { data: programData, error } = await supabaseClient
    .from('workout_programs')
    .insert({
      ...programData,
      cycle_number: cycleInfo.cycleNumber,
      cycle_start_date: cycleInfo.startDate,
      cycle_status: 'pending_transition',
      generation_type: cycleInfo.generationType,
      review_deadline_date: reviewDeadline,
      auto_generated_at: new Date(),
      status: cycleInfo.generationType === 'scheduled' ? 'scheduled_pending_review' : 'ai_generated_pending_review'
    })
    .select()
    .single();
    
  return programData;
};
```

### **2. Daily Program Scheduler Function**

#### Function Structure
```typescript
// File: supabase/functions/daily-program-scheduler/index.ts

interface SchedulerResponse {
  success: boolean;
  processedUsers: number;
  scheduledJobs: number;
  errors: string[];
}

const serve = async (req: Request): Promise<Response> => {
  try {
    console.log('DailyScheduler.start', { timestamp: new Date().toISOString() });
    
    // Find users eligible for 21-day generation
    const eligibleUsers = await findEligibleUsers();
    console.log('DailyScheduler.eligibleUsers', { count: eligibleUsers.length });
    
    // Process each eligible user
    const results = await processEligibleUsers(eligibleUsers);
    
    // Log summary
    await logSchedulerExecution(results);
    
    return new Response(JSON.stringify(results), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('DailyScheduler.error', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

const findEligibleUsers = async () => {
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() - 21); // 21 days ago
  
  const { data, error } = await supabaseClient
    .from('workout_programs')
    .select(`
      user_id,
      cycle_number,
      cycle_start_date,
      created_at,
      profiles(full_name)
    `)
    .eq('cycle_status', 'active')
    .eq('status', 'active_by_client')
    .or(`cycle_start_date.eq.${targetDate.toISOString().split('T')[0]},and(cycle_start_date.is.null,created_at.gte.${targetDate.toISOString()},created_at.lt.${new Date(targetDate.getTime() + 24*60*60*1000).toISOString()})`)
    .not('user_id', 'in', `(
      SELECT user_id FROM scheduled_generation_jobs 
      WHERE job_type = 'program_generation' 
      AND scheduled_date = CURRENT_DATE
      AND execution_status IN ('pending', 'running', 'completed')
    )`);
  
  if (error) throw error;
  return data || [];
};

const processEligibleUsers = async (users) => {
  const results = {
    success: true,
    processedUsers: 0,
    scheduledJobs: 0,
    errors: []
  };
  
  for (const user of users) {
    try {
      await scheduleUserProgramGeneration(user);
      results.processedUsers++;
      results.scheduledJobs++;
    } catch (error) {
      console.error('DailyScheduler.userError', { userId: user.user_id, error });
      results.errors.push(`User ${user.user_id}: ${error.message}`);
    }
  }
  
  return results;
};

const scheduleUserProgramGeneration = async (user) => {
  // Create scheduled job record
  const { error: jobError } = await supabaseClient
    .from('scheduled_generation_jobs')
    .insert({
      user_id: user.user_id,
      scheduled_date: new Date().toISOString().split('T')[0],
      job_type: 'program_generation',
      cycle_number: user.cycle_number + 1,
      execution_status: 'pending'
    });
  
  if (jobError) throw jobError;
  
  // Trigger program generation
  const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/generate-workout-program`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      userId: user.user_id,
      generationType: 'scheduled',
      cycleNumber: user.cycle_number + 1,
      previousProgramId: user.id
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to generate program: ${response.statusText}`);
  }
  
  // Update job status
  await supabaseClient
    .from('scheduled_generation_jobs')
    .update({ 
      execution_status: 'completed',
      completed_at: new Date()
    })
    .eq('user_id', user.user_id)
    .eq('scheduled_date', new Date().toISOString().split('T')[0])
    .eq('job_type', 'program_generation');
};
```

### **3. Auto-Approval Function**

#### Function Structure
```typescript
// File: supabase/functions/auto-approve-programs/index.ts

const serve = async (req: Request): Promise<Response> => {
  try {
    console.log('AutoApproval.start', { timestamp: new Date().toISOString() });
    
    // Find programs past review deadline
    const overduePrograms = await findOverduePrograms();
    console.log('AutoApproval.overduePrograms', { count: overduePrograms.length });
    
    // Process auto-approvals
    const results = await processAutoApprovals(overduePrograms);
    
    return new Response(JSON.stringify(results), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('AutoApproval.error', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

const findOverduePrograms = async () => {
  const { data, error } = await supabaseClient
    .from('workout_programs')
    .select(`
      *,
      profiles(full_name)
    `)
    .in('status', ['ai_generated_pending_review', 'scheduled_pending_review'])
    .lte('review_deadline_date', new Date().toISOString().split('T')[0]);
  
  if (error) throw error;
  return data || [];
};

const processAutoApprovals = async (programs) => {
  const results = {
    success: true,
    approvedPrograms: 0,
    transitionedPrograms: 0,
    errors: []
  };
  
  for (const program of programs) {
    try {
      await autoApproveAndTransition(program);
      results.approvedPrograms++;
      
      if (program.cycle_status === 'pending_transition') {
        await transitionUserProgram(program.user_id, program.id);
        results.transitionedPrograms++;
      }
    } catch (error) {
      console.error('AutoApproval.programError', { programId: program.id, error });
      results.errors.push(`Program ${program.id}: ${error.message}`);
    }
  }
  
  return results;
};

const autoApproveAndTransition = async (program) => {
  // Auto-approve the program
  const { error: approvalError } = await supabaseClient
    .from('workout_programs')
    .update({
      status: 'auto_approved',
      coach_reviewed_at: new Date(),
      coach_notes_for_client: 'Automatically approved - no coach review within 7-day deadline'
    })
    .eq('id', program.id);
  
  if (approvalError) throw approvalError;
  
  // Log the auto-approval
  await supabaseClient
    .from('operation_logs')
    .insert({
      operation_type: 'auto_approval',
      user_id: program.user_id,
      program_id: program.id,
      operation_data: {
        original_deadline: program.review_deadline_date,
        auto_approved_at: new Date()
      },
      execution_status: 'success'
    });
};

const transitionUserProgram = async (userId, newProgramId) => {
  // Mark current program as completed
  await supabaseClient
    .from('workout_programs')
    .update({ cycle_status: 'completed' })
    .eq('user_id', userId)
    .eq('cycle_status', 'active');
  
  // Activate new program
  await supabaseClient
    .from('workout_programs')
    .update({
      cycle_status: 'active',
      status: 'active_by_client',
      client_start_date: new Date()
    })
    .eq('id', newProgramId);
  
  // Record transition
  await supabaseClient
    .from('program_transitions')
    .insert({
      user_id: userId,
      to_program_id: newProgramId,
      transition_date: new Date(),
      transition_status: 'completed',
      transition_type: 'automatic'
    });
};
```

---

## 🔄 **Cron Job Configuration**

### **Supabase pg_cron Setup**
```sql
-- Enable pg_cron extension
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Daily program generation scheduler (9:00 AM UTC)
SELECT cron.schedule(
  'daily-program-generation',
  '0 9 * * *',
  $$
  SELECT net.http_post(
    url := 'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/daily-program-scheduler',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '", "Content-Type": "application/json"}',
    body := '{}'
  );
  $$
);

-- Daily auto-approval scheduler (10:00 AM UTC)
SELECT cron.schedule(
  'daily-auto-approval',
  '0 10 * * *',
  $$
  SELECT net.http_post(
    url := 'https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/auto-approve-programs',
    headers := '{"Authorization": "Bearer ' || current_setting('app.settings.service_role_key') || '", "Content-Type": "application/json"}',
    body := '{}'
  );
  $$
);

-- Cleanup old logs (weekly, Sunday 2:00 AM UTC)
SELECT cron.schedule(
  'weekly-log-cleanup',
  '0 2 * * 0',
  $$
  DELETE FROM operation_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  DELETE FROM scheduled_generation_jobs 
  WHERE created_at < NOW() - INTERVAL '90 days'
  AND execution_status IN ('completed', 'failed');
  $$
);
```

---

## 📊 **Monitoring & Alerting**

### **Health Check Endpoints**
```typescript
// File: supabase/functions/health-check/index.ts

const healthCheck = async () => {
  const checks = {
    database: await checkDatabaseHealth(),
    scheduledJobs: await checkScheduledJobsHealth(),
    edgeFunctions: await checkEdgeFunctionsHealth(),
    cronJobs: await checkCronJobsHealth()
  };
  
  const overallHealth = Object.values(checks).every(check => check.status === 'healthy');
  
  return {
    status: overallHealth ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks
  };
};

const checkScheduledJobsHealth = async () => {
  // Check for failed jobs in last 24 hours
  const { data: failedJobs } = await supabaseClient
    .from('scheduled_generation_jobs')
    .select('count')
    .eq('execution_status', 'failed')
    .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000));
  
  const failedCount = failedJobs?.[0]?.count || 0;
  
  return {
    status: failedCount < 5 ? 'healthy' : 'unhealthy',
    failedJobs: failedCount,
    message: failedCount >= 5 ? 'High number of failed scheduled jobs' : 'Scheduled jobs running normally'
  };
};
```

This comprehensive technical specification provides the foundation for implementing the hybrid AI workout system with automatic updates and coach oversight. The system ensures zero duplicates while providing seamless program progression and robust error handling.
