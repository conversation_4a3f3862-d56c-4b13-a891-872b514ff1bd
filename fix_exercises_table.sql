-- Fix exercises table structure and populate with essential exercises
-- Run this in your Supabase SQL editor

-- First, drop the table if it exists (to start fresh)
DROP TABLE IF EXISTS exercises CASCADE;

-- Recreate the exercises table with proper structure
CREATE TABLE exercises (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  description text,
  video_url text,
  target_muscles_primary text[] DEFAULT '{}',
  target_muscles_secondary text[] DEFAULT '{}',
  equipment_required text[] DEFAULT '{}',
  difficulty_level text CHECK (difficulty_level IN ('Beginner', 'Intermediate', 'Advanced')) DEFAULT 'Beginner',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE exercises ENABLE ROW LEVEL SECURITY;

-- Create policies for exercises table
CREATE POLICY "Authenticated users can read exercises"
  ON exercises
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Service role can manage exercises"
  ON exercises
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_exercises_updated
  BEFORE UPDATE ON exercises
  FOR EACH ROW
  EXECUTE FUNCTION handle_updated_at();

-- Insert essential exercises for the app to function
INSERT INTO exercises (name, description, target_muscles_primary, target_muscles_secondary, equipment_required, difficulty_level) VALUES

-- BODYWEIGHT EXERCISES (Beginner-friendly)
('Push-up', 'Classic bodyweight chest exercise that builds upper body strength', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders', 'Core'], ARRAY['Bodyweight'], 'Beginner'),
('Bodyweight Squat', 'Basic lower body exercise targeting quads and glutes', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Calves'], ARRAY['Bodyweight'], 'Beginner'),
('Plank', 'Core stability exercise that strengthens the entire midsection', ARRAY['Core'], ARRAY['Shoulders', 'Glutes'], ARRAY['Bodyweight'], 'Beginner'),
('Lunges', 'Single-leg exercise for lower body strength and stability', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Calves'], ARRAY['Bodyweight'], 'Beginner'),
('Mountain Climbers', 'Dynamic cardio exercise that targets core and legs', ARRAY['Core', 'Cardio'], ARRAY['Shoulders', 'Legs'], ARRAY['Bodyweight'], 'Beginner'),
('Burpee', 'Full body conditioning exercise combining squat, plank, and jump', ARRAY['Full Body'], ARRAY['Cardio'], ARRAY['Bodyweight'], 'Intermediate'),
('Pull-up', 'Upper body pulling exercise targeting back and biceps', ARRAY['Lats', 'Biceps'], ARRAY['Rhomboids', 'Rear Delts'], ARRAY['Pull-up Bar'], 'Intermediate'),
('Dips', 'Bodyweight exercise targeting triceps and chest', ARRAY['Triceps', 'Chest'], ARRAY['Shoulders'], ARRAY['Parallel Bars'], 'Intermediate'),

-- DUMBBELL EXERCISES
('Dumbbell Bench Press', 'Chest exercise using dumbbells for unilateral strength', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders'], ARRAY['Dumbbells', 'Bench'], 'Beginner'),
('Dumbbell Rows', 'Back exercise targeting lats and rhomboids', ARRAY['Lats', 'Rhomboids'], ARRAY['Biceps', 'Rear Delts'], ARRAY['Dumbbells', 'Bench'], 'Beginner'),
('Dumbbell Shoulder Press', 'Overhead pressing movement for shoulder development', ARRAY['Shoulders'], ARRAY['Triceps', 'Core'], ARRAY['Dumbbells'], 'Beginner'),
('Dumbbell Bicep Curls', 'Isolation exercise for bicep development', ARRAY['Biceps'], ARRAY['Forearms'], ARRAY['Dumbbells'], 'Beginner'),
('Dumbbell Goblet Squat', 'Squat variation holding dumbbell at chest', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Core'], ARRAY['Dumbbells'], 'Beginner'),
('Dumbbell Deadlift', 'Hip hinge movement targeting posterior chain', ARRAY['Hamstrings', 'Glutes'], ARRAY['Lower Back', 'Traps'], ARRAY['Dumbbells'], 'Intermediate'),
('Dumbbell Thrusters', 'Compound movement combining squat and press', ARRAY['Full Body'], ARRAY['Cardio'], ARRAY['Dumbbells'], 'Intermediate'),

-- BARBELL EXERCISES
('Barbell Bench Press', 'Classic chest exercise for upper body strength', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders'], ARRAY['Barbell', 'Bench'], 'Intermediate'),
('Barbell Back Squat', 'Fundamental lower body exercise with barbell', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Core'], ARRAY['Barbell', 'Squat Rack'], 'Intermediate'),
('Barbell Deadlift', 'King of exercises - full body strength movement', ARRAY['Hamstrings', 'Glutes'], ARRAY['Lower Back', 'Traps', 'Lats'], ARRAY['Barbell'], 'Intermediate'),
('Barbell Rows', 'Horizontal pulling exercise for back development', ARRAY['Lats', 'Rhomboids'], ARRAY['Biceps', 'Rear Delts'], ARRAY['Barbell'], 'Intermediate'),
('Overhead Press', 'Standing shoulder press with barbell', ARRAY['Shoulders'], ARRAY['Triceps', 'Core'], ARRAY['Barbell'], 'Intermediate'),
('Barbell Hip Thrust', 'Glute-focused exercise for posterior chain', ARRAY['Glutes'], ARRAY['Hamstrings', 'Core'], ARRAY['Barbell', 'Bench'], 'Intermediate'),

-- MACHINE EXERCISES
('Lat Pulldown', 'Machine exercise for lat development', ARRAY['Lats'], ARRAY['Biceps', 'Rhomboids'], ARRAY['Cable Machine'], 'Beginner'),
('Leg Press', 'Machine-based lower body exercise', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Calves'], ARRAY['Leg Press Machine'], 'Beginner'),
('Chest Press Machine', 'Machine-based chest exercise', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders'], ARRAY['Chest Press Machine'], 'Beginner'),
('Leg Curl', 'Isolation exercise for hamstring development', ARRAY['Hamstrings'], ARRAY[], ARRAY['Leg Curl Machine'], 'Beginner'),
('Leg Extension', 'Isolation exercise for quadriceps', ARRAY['Quads'], ARRAY[], ARRAY['Leg Extension Machine'], 'Beginner'),
('Cable Rows', 'Seated rowing exercise using cable machine', ARRAY['Lats', 'Rhomboids'], ARRAY['Biceps', 'Rear Delts'], ARRAY['Cable Machine'], 'Beginner'),

-- CARDIO EXERCISES
('Treadmill Running', 'Cardiovascular exercise on treadmill', ARRAY['Cardio'], ARRAY['Legs'], ARRAY['Treadmill'], 'Beginner'),
('Stationary Bike', 'Low-impact cardio exercise', ARRAY['Cardio'], ARRAY['Legs'], ARRAY['Stationary Bike'], 'Beginner'),
('Rowing Machine', 'Full-body cardio exercise', ARRAY['Cardio', 'Back'], ARRAY['Legs', 'Arms'], ARRAY['Rowing Machine'], 'Beginner'),
('Elliptical', 'Low-impact full-body cardio', ARRAY['Cardio'], ARRAY['Full Body'], ARRAY['Elliptical Machine'], 'Beginner'),

-- FUNCTIONAL/CORE EXERCISES
('Russian Twists', 'Core rotation exercise', ARRAY['Core'], ARRAY['Obliques'], ARRAY['Bodyweight'], 'Beginner'),
('Dead Bug', 'Core stability exercise', ARRAY['Core'], ARRAY['Hip Flexors'], ARRAY['Bodyweight'], 'Beginner'),
('Bird Dog', 'Core and stability exercise', ARRAY['Core', 'Glutes'], ARRAY['Shoulders'], ARRAY['Bodyweight'], 'Beginner'),
('Farmer''s Walk', 'Functional strength and grip exercise', ARRAY['Forearms', 'Traps'], ARRAY['Core', 'Legs'], ARRAY['Dumbbells'], 'Intermediate'),
('Turkish Get-up', 'Complex full-body movement', ARRAY['Full Body'], ARRAY['Core', 'Shoulders'], ARRAY['Kettlebell'], 'Advanced'),

-- KETTLEBELL EXERCISES
('Kettlebell Swing', 'Dynamic hip hinge movement', ARRAY['Glutes', 'Hamstrings'], ARRAY['Core', 'Shoulders'], ARRAY['Kettlebell'], 'Intermediate'),
('Kettlebell Goblet Squat', 'Squat holding kettlebell at chest', ARRAY['Quads', 'Glutes'], ARRAY['Core'], ARRAY['Kettlebell'], 'Beginner'),
('Kettlebell Press', 'Overhead pressing with kettlebell', ARRAY['Shoulders'], ARRAY['Core', 'Triceps'], ARRAY['Kettlebell'], 'Intermediate'),

-- STRETCHING/MOBILITY
('Cat-Cow Stretch', 'Spinal mobility exercise', ARRAY['Mobility'], ARRAY['Core'], ARRAY['Bodyweight'], 'Beginner'),
('Child''s Pose', 'Relaxation and hip mobility stretch', ARRAY['Mobility'], ARRAY['Hips', 'Back'], ARRAY['Bodyweight'], 'Beginner'),
('Downward Dog', 'Full body stretch and strength pose', ARRAY['Shoulders', 'Hamstrings'], ARRAY['Core', 'Calves'], ARRAY['Bodyweight'], 'Beginner'),

-- ADDITIONAL ESSENTIAL EXERCISES
('Wall Sit', 'Isometric lower body exercise', ARRAY['Quads', 'Glutes'], ARRAY['Calves'], ARRAY['Bodyweight'], 'Beginner'),
('Jumping Jacks', 'Full body cardio exercise', ARRAY['Cardio'], ARRAY['Full Body'], ARRAY['Bodyweight'], 'Beginner'),
('High Knees', 'Dynamic cardio movement', ARRAY['Cardio'], ARRAY['Hip Flexors'], ARRAY['Bodyweight'], 'Beginner'),
('Butt Kicks', 'Dynamic hamstring activation', ARRAY['Hamstrings'], ARRAY['Cardio'], ARRAY['Bodyweight'], 'Beginner'),
('Side Plank', 'Lateral core stability exercise', ARRAY['Core', 'Obliques'], ARRAY['Shoulders'], ARRAY['Bodyweight'], 'Intermediate'),
('Glute Bridge', 'Hip extension exercise for glute activation', ARRAY['Glutes'], ARRAY['Hamstrings'], ARRAY['Bodyweight'], 'Beginner'),
('Calf Raises', 'Isolation exercise for calf development', ARRAY['Calves'], ARRAY[], ARRAY['Bodyweight'], 'Beginner'),
('Tricep Dips', 'Bodyweight tricep exercise using chair or bench', ARRAY['Triceps'], ARRAY['Shoulders'], ARRAY['Bodyweight'], 'Beginner'),
('Superman', 'Lower back strengthening exercise', ARRAY['Lower Back'], ARRAY['Glutes'], ARRAY['Bodyweight'], 'Beginner'),
('Bicycle Crunches', 'Dynamic core exercise targeting obliques', ARRAY['Core', 'Obliques'], ARRAY[], ARRAY['Bodyweight'], 'Beginner'),
('Bear Crawl', 'Full body stability and strength exercise', ARRAY['Core', 'Shoulders'], ARRAY['Full Body'], ARRAY['Bodyweight'], 'Intermediate'),
('Inchworm', 'Dynamic full body movement', ARRAY['Core', 'Shoulders'], ARRAY['Hamstrings'], ARRAY['Bodyweight'], 'Intermediate'),
('Single Leg Deadlift', 'Unilateral balance and strength exercise', ARRAY['Hamstrings', 'Glutes'], ARRAY['Core'], ARRAY['Bodyweight'], 'Intermediate'),
('Step-ups', 'Unilateral lower body exercise', ARRAY['Quads', 'Glutes'], ARRAY['Calves'], ARRAY['Bodyweight'], 'Beginner'),
('Pike Push-up', 'Bodyweight shoulder exercise', ARRAY['Shoulders'], ARRAY['Triceps'], ARRAY['Bodyweight'], 'Intermediate'),
('Reverse Lunge', 'Backward stepping lunge variation', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings'], ARRAY['Bodyweight'], 'Beginner'),
('Squat Jump', 'Plyometric lower body exercise', ARRAY['Quads', 'Glutes'], ARRAY['Calves'], ARRAY['Bodyweight'], 'Intermediate'),
('Push-up to T', 'Push-up with rotation for core and shoulders', ARRAY['Chest', 'Core'], ARRAY['Shoulders'], ARRAY['Bodyweight'], 'Intermediate'),
('Hollow Body Hold', 'Isometric core strengthening exercise', ARRAY['Core'], ARRAY['Hip Flexors'], ARRAY['Bodyweight'], 'Intermediate'),
('V-ups', 'Dynamic core exercise', ARRAY['Core'], ARRAY['Hip Flexors'], ARRAY['Bodyweight'], 'Intermediate');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS exercises_name_idx ON exercises(name);
CREATE INDEX IF NOT EXISTS exercises_difficulty_idx ON exercises(difficulty_level);
CREATE INDEX IF NOT EXISTS exercises_equipment_idx ON exercises USING GIN(equipment_required);
CREATE INDEX IF NOT EXISTS exercises_primary_muscles_idx ON exercises USING GIN(target_muscles_primary);

-- Verify the data was inserted
SELECT COUNT(*) as total_exercises FROM exercises;
SELECT difficulty_level, COUNT(*) as count FROM exercises GROUP BY difficulty_level ORDER BY difficulty_level;
SELECT UNNEST(equipment_required) as equipment, COUNT(*) as exercise_count 
FROM exercises 
GROUP BY equipment 
ORDER BY exercise_count DESC;
