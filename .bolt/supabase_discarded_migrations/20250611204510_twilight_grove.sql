/*
  # Seed exercises table with common exercises

  1. Seed Data
    - Add common exercises for different equipment types
    - Include bodyweight, dumbbell, barbell, and machine exercises
    - Set appropriate difficulty levels and muscle targets
*/

INSERT INTO exercises (name, description, target_muscles_primary, target_muscles_secondary, equipment_required, difficulty_level) VALUES
-- Bodyweight Exercises
('Push-up', 'Classic bodyweight chest exercise', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders', 'Core'], ARRAY['Bodyweight'], 'Beginner'),
('Bodyweight Squat', 'Basic lower body bodyweight exercise', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Calves'], ARRAY['Bodyweight'], 'Beginner'),
('Plank', 'Core stability exercise', ARRAY['Core'], ARRAY['Shoulders', 'Glutes'], ARRAY['Bodyweight'], 'Beginner'),
('Burpee', 'Full body conditioning exercise', ARRAY['Full Body'], ARRAY['Cardio'], ARRAY['Bodyweight'], 'Intermediate'),
('Mountain Climbers', 'Dynamic core and cardio exercise', ARRAY['Core', 'Cardio'], ARRAY['Shoulders', 'Legs'], ARRAY['Bodyweight'], 'Beginner'),
('Jumping Jacks', 'Cardio and coordination exercise', ARRAY['Cardio'], ARRAY['Legs', 'Shoulders'], ARRAY['Bodyweight'], 'Beginner'),
('Lunges', 'Single leg strength exercise', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Calves'], ARRAY['Bodyweight'], 'Beginner'),
('Pull-up', 'Upper body pulling exercise', ARRAY['Lats', 'Biceps'], ARRAY['Rhomboids', 'Middle Traps'], ARRAY['Pull-up Bar'], 'Intermediate'),

-- Dumbbell Exercises
('Dumbbell Bench Press', 'Chest pressing exercise with dumbbells', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders'], ARRAY['Dumbbells', 'Bench'], 'Intermediate'),
('Dumbbell Row', 'Back pulling exercise with dumbbells', ARRAY['Lats', 'Rhomboids'], ARRAY['Biceps', 'Middle Traps'], ARRAY['Dumbbells'], 'Beginner'),
('Dumbbell Shoulder Press', 'Overhead pressing exercise', ARRAY['Shoulders', 'Triceps'], ARRAY['Core'], ARRAY['Dumbbells'], 'Beginner'),
('Dumbbell Bicep Curl', 'Isolation exercise for biceps', ARRAY['Biceps'], ARRAY['Forearms'], ARRAY['Dumbbells'], 'Beginner'),
('Dumbbell Goblet Squat', 'Squat variation holding dumbbell', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Core'], ARRAY['Dumbbells'], 'Beginner'),
('Dumbbell Deadlift', 'Hip hinge movement with dumbbells', ARRAY['Hamstrings', 'Glutes'], ARRAY['Lower Back', 'Traps'], ARRAY['Dumbbells'], 'Intermediate'),

-- Barbell Exercises
('Barbell Squat', 'Compound lower body exercise', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings', 'Core'], ARRAY['Barbell', 'Rack'], 'Intermediate'),
('Barbell Deadlift', 'Full body pulling exercise', ARRAY['Hamstrings', 'Glutes'], ARRAY['Lower Back', 'Traps', 'Lats'], ARRAY['Barbell'], 'Advanced'),
('Barbell Bench Press', 'Horizontal pushing exercise', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders'], ARRAY['Barbell', 'Bench'], 'Intermediate'),
('Barbell Row', 'Horizontal pulling exercise', ARRAY['Lats', 'Rhomboids'], ARRAY['Biceps', 'Middle Traps'], ARRAY['Barbell'], 'Intermediate'),
('Overhead Press', 'Vertical pushing exercise', ARRAY['Shoulders', 'Triceps'], ARRAY['Core'], ARRAY['Barbell'], 'Intermediate'),

-- Resistance Band Exercises
('Resistance Band Chest Press', 'Chest exercise using resistance bands', ARRAY['Chest', 'Triceps'], ARRAY['Shoulders'], ARRAY['Resistance Bands'], 'Beginner'),
('Resistance Band Row', 'Back exercise using resistance bands', ARRAY['Lats', 'Rhomboids'], ARRAY['Biceps'], ARRAY['Resistance Bands'], 'Beginner'),
('Resistance Band Squat', 'Squat with resistance band', ARRAY['Quads', 'Glutes'], ARRAY['Hamstrings'], ARRAY['Resistance Bands'], 'Beginner'),
('Resistance Band Lateral Raise', 'Shoulder isolation with bands', ARRAY['Shoulders'], ARRAY[], ARRAY['Resistance Bands'], 'Beginner'),

-- Core Exercises
('Dead Bug', 'Core stability exercise', ARRAY['Core'], ARRAY[], ARRAY['Bodyweight'], 'Beginner'),
('Bird Dog', 'Core and stability exercise', ARRAY['Core', 'Glutes'], ARRAY['Shoulders'], ARRAY['Bodyweight'], 'Beginner'),
('Russian Twist', 'Rotational core exercise', ARRAY['Core'], ARRAY[], ARRAY['Bodyweight'], 'Beginner'),
('Bicycle Crunches', 'Dynamic core exercise', ARRAY['Core'], ARRAY[], ARRAY['Bodyweight'], 'Beginner'),

-- Flexibility/Mobility
('Cat-Cow Stretch', 'Spinal mobility exercise', ARRAY['Mobility'], ARRAY[], ARRAY['Yoga Mat'], 'Beginner'),
('Child''s Pose', 'Relaxation and hip mobility', ARRAY['Mobility'], ARRAY[], ARRAY['Yoga Mat'], 'Beginner'),
('Downward Dog', 'Full body stretch', ARRAY['Mobility'], ARRAY['Shoulders', 'Hamstrings'], ARRAY['Yoga Mat'], 'Beginner');