/*
  # Add INSERT policy for profiles table

  1. Security
    - Add policy to allow authenticated users to insert their own profile data
    - This enables the handle_new_user trigger to successfully create profile entries during user registration

  2. Changes
    - CREATE POLICY for INSERT operations on public.profiles table
    - Policy ensures users can only insert data for their own user ID (auth.uid() = id)
*/

-- Add INSERT policy for profiles table to allow new user registration
CREATE POLICY "Users can insert own profile"
  ON public.profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);