#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read the function source code
const functionPath = path.join(__dirname, 'supabase/functions/generate-workout-program/index.ts');
const functionCode = fs.readFileSync(functionPath, 'utf8');

// Create a simple deployment payload
const deploymentPayload = {
  name: 'generate-workout-program',
  source: functionCode,
  verify_jwt: false
};

console.log('Function code length:', functionCode.length);
console.log('First 200 characters:', functionCode.substring(0, 200));

// For now, just output the payload structure
console.log('\nDeployment payload structure:');
console.log('- Name:', deploymentPayload.name);
console.log('- Source length:', deploymentPayload.source.length);
console.log('- Verify JWT:', deploymentPayload.verify_jwt);

console.log('\nTo deploy manually:');
console.log('1. Go to Supabase Dashboard > Edge Functions');
console.log('2. Select generate-workout-program function');
console.log('3. Replace the code with the updated version');
console.log('4. Deploy the function');
