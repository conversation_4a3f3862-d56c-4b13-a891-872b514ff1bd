# Test Enhanced Generate-Workout-Program Function

## Test Cases for Enhanced Function

### 1. Initial Generation (Legacy Support)
```json
{
  "userId": "test-user-id",
  "generationType": "initial"
}
```
**Expected**: Creates cycle 1 program with status 'ai_generated_pending_review'

### 2. Scheduled Generation
```json
{
  "userId": "test-user-id", 
  "generationType": "scheduled",
  "cycleNumber": 2,
  "previousProgramId": "previous-program-id"
}
```
**Expected**: Creates cycle 2 program with status 'scheduled_pending_review' and 7-day review deadline

### 3. Manual Generation (Coach Override)
```json
{
  "userId": "test-user-id",
  "generationType": "manual",
  "cycleNumber": 3
}
```
**Expected**: Creates cycle 3 program regardless of timing constraints

### 4. Legacy Webhook Format (Backward Compatibility)
```json
{
  "type": "UPDATE",
  "table": "profiles", 
  "record": { "id": "test-user-id" }
}
```
**Expected**: Treated as initial generation, maintains backward compatibility

## Key Enhancements Implemented

✅ **Enhanced Request Handling**: Supports both legacy webhook and new API formats
✅ **Generation Type Support**: initial, scheduled, manual with different logic
✅ **Cycle Management**: Automatic cycle number determination and tracking
✅ **Enhanced Duplicate Prevention**: Type-specific eligibility checking
✅ **Review Deadline Management**: Automatic 7-day deadlines for scheduled programs
✅ **Job Tracking Integration**: Records completion in scheduled_generation_jobs table
✅ **Improved Response Format**: Returns cycle info and review deadlines

## Database Integration

- Uses new `cycle_number`, `generation_type`, `review_deadline_date` columns
- Integrates with `scheduled_generation_jobs` table for job tracking
- Supports program transitions with `previous_program_id` linking
- Enhanced status workflow with `scheduled_pending_review` status

## Backward Compatibility

- Legacy webhook payloads still work (treated as initial generation)
- Existing programs continue to function normally
- No breaking changes to existing API contracts
