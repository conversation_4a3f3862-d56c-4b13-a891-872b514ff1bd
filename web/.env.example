# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=FitnessApp

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Analytics (Optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_google_analytics_id
NEXT_PUBLIC_MIXPANEL_TOKEN=your_mixpanel_token

# Error Tracking (Optional)
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# Email Service (Optional)
RESEND_API_KEY=your_resend_api_key
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password

# File Upload
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,video/mp4

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_PWA=true
NEXT_PUBLIC_ENABLE_OFFLINE_MODE=true

# Development
NODE_ENV=development
NEXT_PUBLIC_DEBUG=false

# SEO
GOOGLE_SITE_VERIFICATION=your_google_site_verification
NEXT_PUBLIC_SITE_NAME=FitnessApp
NEXT_PUBLIC_SITE_DESCRIPTION=Transform your fitness journey with personalized workout programs

# Social Media
NEXT_PUBLIC_TWITTER_HANDLE=@fitnessapp
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id

# API Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# Security
CORS_ORIGIN=http://localhost:3000
CSP_REPORT_URI=https://your-csp-report-endpoint.com

# Monitoring
NEXT_PUBLIC_UPTIME_ROBOT_KEY=your_uptime_robot_key
HEALTH_CHECK_TOKEN=your_health_check_token
