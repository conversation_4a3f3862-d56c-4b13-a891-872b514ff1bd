#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan');
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return false;
  }
}

function checkPrerequisites() {
  log('🔍 Checking prerequisites...', 'blue');
  
  const checks = [
    { cmd: 'vercel --version', name: 'Vercel CLI' },
    { cmd: 'node --version', name: 'Node.js' },
    { cmd: 'npm --version', name: 'npm' },
  ];

  for (const check of checks) {
    try {
      execSync(check.cmd, { stdio: 'pipe' });
      log(`✅ ${check.name} is installed`, 'green');
    } catch (error) {
      log(`❌ ${check.name} is not installed or not in PATH`, 'red');
      log(`Install with: npm install -g vercel`, 'yellow');
      return false;
    }
  }

  return true;
}

function validateEnvironment() {
  log('🔧 Validating environment...', 'blue');
  
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_APP_URL',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    log(`❌ Missing environment variables: ${missingVars.join(', ')}`, 'red');
    log('Create a .env.local file with the required variables', 'yellow');
    return false;
  }

  log('✅ Environment variables validated', 'green');
  return true;
}

function runTests() {
  log('🧪 Running tests...', 'blue');
  
  const testCommands = [
    'npm run lint',
    'npm run type-check',
    'npm run test:ci',
  ];

  for (const cmd of testCommands) {
    if (!runCommand(cmd, `Running ${cmd}`)) {
      return false;
    }
  }

  return true;
}

function buildApp() {
  log('🏗️ Building application...', 'blue');
  
  return runCommand('npm run build', 'Building Next.js application');
}

function deployToVercel(environment = 'preview') {
  log(`🚀 Deploying to Vercel (${environment})...`, 'blue');
  
  let command = 'vercel';
  
  if (environment === 'production') {
    command += ' --prod';
  }
  
  command += ' --yes';
  
  return runCommand(command, `Deploying to ${environment}`);
}

function generateDeploymentReport(environment, deploymentUrl) {
  const timestamp = new Date().toISOString();
  const reportPath = path.join(__dirname, `../deployment-report-${timestamp.split('T')[0]}.md`);
  
  const report = `# Deployment Report

**Date:** ${timestamp}
**Environment:** ${environment}
**Status:** ✅ Success
**URL:** ${deploymentUrl || 'Check Vercel dashboard'}

## Deployment Configuration

- Platform: Vercel
- Framework: Next.js 14
- Node.js: ${process.version}
- Build Command: npm run build
- Output Directory: .next

## Environment Variables

- NEXT_PUBLIC_SUPABASE_URL: ✅ Set
- NEXT_PUBLIC_SUPABASE_ANON_KEY: ✅ Set
- NEXT_PUBLIC_APP_URL: ✅ Set

## Features Deployed

- ✅ Server-side rendering (SSR)
- ✅ Static site generation (SSG)
- ✅ API routes
- ✅ Progressive Web App (PWA)
- ✅ Service Worker
- ✅ SEO optimization
- ✅ Security headers
- ✅ Performance optimization

## Performance

- Lighthouse Score: Run audit after deployment
- Core Web Vitals: Monitor in production
- Bundle Size: Optimized with Next.js

## Monitoring

- Error Tracking: Vercel Analytics
- Performance: Vercel Speed Insights
- Uptime: Vercel monitoring

## Next Steps

1. Test the deployment thoroughly
2. Run Lighthouse audit
3. Monitor error rates and performance
4. Update DNS if needed for custom domain
5. Set up monitoring alerts

## Links

- [Vercel Dashboard](https://vercel.com/dashboard)
- [Deployment URL](${deploymentUrl || 'TBD'})
- [Analytics](https://vercel.com/analytics)
`;

  fs.writeFileSync(reportPath, report);
  log(`📄 Deployment report generated: ${reportPath}`, 'blue');
}

function setupCustomDomain(domain) {
  log(`🌐 Setting up custom domain: ${domain}...`, 'blue');
  
  return runCommand(`vercel domains add ${domain}`, 'Adding custom domain');
}

function main() {
  const args = process.argv.slice(2);
  const environment = args[0] || 'preview'; // 'preview' or 'production'
  const customDomain = args.find(arg => arg.startsWith('--domain='))?.split('=')[1];
  const skipTests = args.includes('--skip-tests');

  log('🚀 FitnessApp Web Deployment Script', 'bright');
  log('===================================', 'bright');

  if (!['preview', 'production'].includes(environment)) {
    log('❌ Please specify environment: preview or production', 'red');
    log('Usage: node deploy.js <environment> [--domain=example.com] [--skip-tests]', 'yellow');
    process.exit(1);
  }

  // Check prerequisites
  if (!checkPrerequisites()) {
    process.exit(1);
  }

  // Validate environment
  if (!validateEnvironment()) {
    process.exit(1);
  }

  // Run tests (unless skipped)
  if (!skipTests) {
    if (!runTests()) {
      log('❌ Tests failed. Use --skip-tests to deploy anyway.', 'red');
      process.exit(1);
    }
  } else {
    log('⚠️ Skipping tests as requested', 'yellow');
  }

  // Build application
  if (!buildApp()) {
    process.exit(1);
  }

  // Deploy to Vercel
  if (!deployToVercel(environment)) {
    process.exit(1);
  }

  // Setup custom domain if provided
  if (customDomain) {
    setupCustomDomain(customDomain);
  }

  // Generate deployment report
  generateDeploymentReport(environment);

  log('\n🎉 Deployment completed successfully!', 'green');
  log('Check the Vercel dashboard for the deployment URL.', 'blue');
  
  if (environment === 'production') {
    log('🔍 Don\'t forget to:', 'yellow');
    log('  1. Test the production deployment', 'yellow');
    log('  2. Run a Lighthouse audit', 'yellow');
    log('  3. Monitor error rates and performance', 'yellow');
    log('  4. Update any external services with the new URL', 'yellow');
  }
}

if (require.main === module) {
  main();
}

module.exports = { 
  deployToVercel, 
  checkPrerequisites, 
  validateEnvironment, 
  buildApp,
  generateDeploymentReport 
};
