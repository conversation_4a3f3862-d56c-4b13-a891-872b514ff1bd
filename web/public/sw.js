const CACHE_NAME = 'fitnessapp-v1.0.0';
const STATIC_CACHE_NAME = 'fitnessapp-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'fitnessapp-dynamic-v1.0.0';

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/dashboard',
  '/auth/login',
  '/auth/register',
  '/offline',
  '/manifest.json',
  // Add critical CSS and JS files
  '/_next/static/css/app.css',
  '/_next/static/chunks/main.js',
  '/_next/static/chunks/webpack.js',
  // Icons
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/favicon.ico',
];

// Routes that should always be fetched from network
const NETWORK_ONLY_ROUTES = [
  '/api/',
  '/auth/callback',
  '/_next/webpack-hmr',
];

// Routes that should be cached with network-first strategy
const NETWORK_FIRST_ROUTES = [
  '/dashboard/',
  '/api/workouts',
  '/api/progress',
  '/api/messages',
];

// Routes that should be cached with cache-first strategy
const CACHE_FIRST_ROUTES = [
  '/_next/static/',
  '/icons/',
  '/images/',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Failed to cache static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName.startsWith('fitnessapp-')) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests with different strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip cross-origin requests
  if (url.origin !== location.origin) {
    return;
  }
  
  // Network-only routes
  if (NETWORK_ONLY_ROUTES.some(route => url.pathname.startsWith(route))) {
    event.respondWith(fetch(request));
    return;
  }
  
  // Cache-first strategy for static assets
  if (CACHE_FIRST_ROUTES.some(route => url.pathname.startsWith(route))) {
    event.respondWith(cacheFirst(request));
    return;
  }
  
  // Network-first strategy for dynamic content
  if (NETWORK_FIRST_ROUTES.some(route => url.pathname.startsWith(route))) {
    event.respondWith(networkFirst(request));
    return;
  }
  
  // Default: Stale-while-revalidate
  event.respondWith(staleWhileRevalidate(request));
});

// Cache-first strategy
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Cache-first strategy failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

// Network-first strategy
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/offline');
    }
    
    return new Response('Offline', { status: 503 });
  }
}

// Stale-while-revalidate strategy
async function staleWhileRevalidate(request) {
  const cache = await caches.open(DYNAMIC_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  const fetchPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => {
    // Network failed, return cached version if available
    return cachedResponse;
  });
  
  // Return cached version immediately if available, otherwise wait for network
  return cachedResponse || fetchPromise;
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'workout-sync') {
    event.waitUntil(syncWorkoutData());
  } else if (event.tag === 'message-sync') {
    event.waitUntil(syncMessageData());
  } else if (event.tag === 'progress-sync') {
    event.waitUntil(syncProgressData());
  }
});

// Sync workout data when back online
async function syncWorkoutData() {
  try {
    console.log('Service Worker: Syncing workout data');
    
    // Get pending workout logs from IndexedDB
    const pendingWorkouts = await getPendingData('workouts');
    
    for (const workout of pendingWorkouts) {
      try {
        const response = await fetch('/api/workouts/log', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(workout.data),
        });
        
        if (response.ok) {
          await removePendingData('workouts', workout.id);
          console.log('Service Worker: Workout synced successfully');
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync workout', error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Workout sync failed', error);
  }
}

// Sync message data when back online
async function syncMessageData() {
  try {
    console.log('Service Worker: Syncing message data');
    
    const pendingMessages = await getPendingData('messages');
    
    for (const message of pendingMessages) {
      try {
        const response = await fetch('/api/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(message.data),
        });
        
        if (response.ok) {
          await removePendingData('messages', message.id);
          console.log('Service Worker: Message synced successfully');
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync message', error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Message sync failed', error);
  }
}

// Sync progress data when back online
async function syncProgressData() {
  try {
    console.log('Service Worker: Syncing progress data');
    
    const pendingProgress = await getPendingData('progress');
    
    for (const progress of pendingProgress) {
      try {
        const response = await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(progress.data),
        });
        
        if (response.ok) {
          await removePendingData('progress', progress.id);
          console.log('Service Worker: Progress synced successfully');
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync progress', error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Progress sync failed', error);
  }
}

// Helper functions for IndexedDB operations
async function getPendingData(storeName) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('FitnessAppOffline', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = () => resolve(getAllRequest.result);
      getAllRequest.onerror = () => reject(getAllRequest.error);
    };
  });
}

async function removePendingData(storeName, id) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('FitnessAppOffline', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const deleteRequest = store.delete(id);
      
      deleteRequest.onsuccess = () => resolve();
      deleteRequest.onerror = () => reject(deleteRequest.error);
    };
  });
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  if (!event.data) {
    return;
  }
  
  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    image: data.image,
    data: data.data,
    actions: data.actions || [],
    requireInteraction: data.requireInteraction || false,
    silent: data.silent || false,
    tag: data.tag,
    renotify: data.renotify || false,
    vibrate: data.vibrate || [200, 100, 200],
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  const data = event.notification.data;
  let url = '/dashboard';
  
  if (data && data.url) {
    url = data.url;
  } else if (data && data.type) {
    switch (data.type) {
      case 'workout':
        url = '/dashboard/workouts';
        break;
      case 'message':
        url = '/dashboard/messages';
        break;
      case 'progress':
        url = '/dashboard/progress';
        break;
      default:
        url = '/dashboard';
    }
  }
  
  event.waitUntil(
    clients.matchAll({ type: 'window' }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === url && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no window/tab is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(url);
      }
    })
  );
});

// Message handling from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
