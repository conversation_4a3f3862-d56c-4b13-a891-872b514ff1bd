{"buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "framework": "nextjs", "regions": ["iad1", "sfo1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/icons/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/login", "destination": "/auth/login", "permanent": true}, {"source": "/register", "destination": "/auth/register", "permanent": true}, {"source": "/signup", "destination": "/auth/register", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "env": {"NEXT_PUBLIC_APP_URL": "@next_public_app_url", "NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key"}, "build": {"env": {"NODE_ENV": "production"}}, "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/cron/analytics", "schedule": "0 1 * * *"}]}