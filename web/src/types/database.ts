export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          date_of_birth: string | null
          gender: string | null
          height: number | null
          weight: number | null
          fitness_level: string | null
          goals: Json | null
          preferences: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          date_of_birth?: string | null
          gender?: string | null
          height?: number | null
          weight?: number | null
          fitness_level?: string | null
          goals?: Json | null
          preferences?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          date_of_birth?: string | null
          gender?: string | null
          height?: number | null
          weight?: number | null
          fitness_level?: string | null
          goals?: Json | null
          preferences?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      workout_programs: {
        Row: {
          id: string
          name: string
          description: string | null
          duration_weeks: number
          difficulty_level: string
          program_type: string
          created_by: string
          is_template: boolean
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          duration_weeks: number
          difficulty_level: string
          program_type: string
          created_by: string
          is_template?: boolean
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          duration_weeks?: number
          difficulty_level?: string
          program_type?: string
          created_by?: string
          is_template?: boolean
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      workouts: {
        Row: {
          id: string
          program_id: string
          name: string
          description: string | null
          week_number: number
          day_number: number
          estimated_duration: number | null
          workout_data: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          program_id: string
          name: string
          description?: string | null
          week_number: number
          day_number: number
          estimated_duration?: number | null
          workout_data: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          program_id?: string
          name?: string
          description?: string | null
          week_number?: number
          day_number?: number
          estimated_duration?: number | null
          workout_data?: Json
          created_at?: string
          updated_at?: string
        }
      }
      user_programs: {
        Row: {
          id: string
          user_id: string
          program_id: string
          started_at: string
          completed_at: string | null
          current_week: number
          current_day: number
          is_active: boolean
          progress_data: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          program_id: string
          started_at?: string
          completed_at?: string | null
          current_week?: number
          current_day?: number
          is_active?: boolean
          progress_data?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          program_id?: string
          started_at?: string
          completed_at?: string | null
          current_week?: number
          current_day?: number
          is_active?: boolean
          progress_data?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      workout_logs: {
        Row: {
          id: string
          user_id: string
          workout_id: string
          completed_at: string
          duration_minutes: number | null
          exercises_completed: Json
          notes: string | null
          rpe_rating: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          workout_id: string
          completed_at?: string
          duration_minutes?: number | null
          exercises_completed: Json
          notes?: string | null
          rpe_rating?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          workout_id?: string
          completed_at?: string
          duration_minutes?: number | null
          exercises_completed?: Json
          notes?: string | null
          rpe_rating?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      progress_tracking: {
        Row: {
          id: string
          user_id: string
          tracking_type: string
          date: string
          value: number
          unit: string | null
          notes: string | null
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          tracking_type: string
          date: string
          value: number
          unit?: string | null
          notes?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          tracking_type?: string
          date?: string
          value?: number
          unit?: string | null
          notes?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          participant_1_id: string
          participant_2_id: string
          last_message_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          participant_1_id: string
          participant_2_id: string
          last_message_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          participant_1_id?: string
          participant_2_id?: string
          last_message_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          conversation_id: string
          sender_id: string
          content: string
          message_type: string
          metadata: Json | null
          is_read: boolean
          read_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          sender_id: string
          content: string
          message_type?: string
          metadata?: Json | null
          is_read?: boolean
          read_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          sender_id?: string
          content?: string
          message_type?: string
          metadata?: Json | null
          is_read?: boolean
          read_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
