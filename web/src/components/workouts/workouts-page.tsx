'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  Filter,
  Plus,
  Calendar,
  Clock,
  Target,
  Dumbbell,
  Play,
  MoreHorizontal,
  Star,
  TrendingUp,
} from 'lucide-react';
import { WorkoutCard } from '@/components/dashboard/workout-card';
import { WorkoutFilters } from '@/components/workouts/workout-filters';
import { CreateWorkoutModal } from '@/components/workouts/create-workout-modal';

const workoutPrograms = [
  {
    id: '1',
    name: 'Push Pull Legs',
    description: 'A classic 3-day split focusing on push, pull, and leg movements',
    duration: 12,
    difficulty: 'Intermediate',
    type: 'Strength',
    workoutsPerWeek: 6,
    currentWeek: 3,
    progress: 45,
    isActive: true,
    image: '/workout-images/ppl.jpg',
  },
  {
    id: '2',
    name: 'Full Body Strength',
    description: 'Complete full-body workouts for maximum efficiency',
    duration: 8,
    difficulty: 'Beginner',
    type: 'Strength',
    workoutsPerWeek: 3,
    currentWeek: 0,
    progress: 0,
    isActive: false,
    image: '/workout-images/fullbody.jpg',
  },
  {
    id: '3',
    name: 'HIIT Cardio Blast',
    description: 'High-intensity interval training for fat loss and conditioning',
    duration: 6,
    difficulty: 'Advanced',
    type: 'Cardio',
    workoutsPerWeek: 4,
    currentWeek: 0,
    progress: 0,
    isActive: false,
    image: '/workout-images/hiit.jpg',
  },
];

const todaysWorkouts = [
  {
    id: '1',
    name: 'Push Day - Chest & Triceps',
    duration: 45,
    exercises: 8,
    difficulty: 'Intermediate',
    type: 'Strength',
    completed: false,
    scheduledTime: '6:00 PM',
    program: 'Push Pull Legs',
  },
  {
    id: '2',
    name: 'Morning Cardio',
    duration: 30,
    exercises: 5,
    difficulty: 'Beginner',
    type: 'Cardio',
    completed: true,
    scheduledTime: '7:00 AM',
    program: 'HIIT Cardio Blast',
  },
];

const recentWorkouts = [
  {
    id: '1',
    name: 'Pull Day - Back & Biceps',
    completedAt: '2024-01-15T18:30:00Z',
    duration: 52,
    exercises: 7,
    rating: 4,
    program: 'Push Pull Legs',
  },
  {
    id: '2',
    name: 'Leg Day - Quads Focus',
    completedAt: '2024-01-13T17:15:00Z',
    duration: 48,
    exercises: 6,
    rating: 5,
    program: 'Push Pull Legs',
  },
  {
    id: '3',
    name: 'HIIT Circuit',
    completedAt: '2024-01-12T08:00:00Z',
    duration: 25,
    exercises: 8,
    rating: 4,
    program: 'HIIT Cardio Blast',
  },
];

export function WorkoutsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'today' | 'programs' | 'history'>('today');
  const [filters, setFilters] = useState({
    difficulty: '',
    type: '',
    duration: '',
  });

  const tabs = [
    { id: 'today', label: 'Today', icon: Calendar },
    { id: 'programs', label: 'Programs', icon: Target },
    { id: 'history', label: 'History', icon: TrendingUp },
  ];

  const filteredPrograms = workoutPrograms.filter((program) => {
    const matchesSearch = program.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         program.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDifficulty = !filters.difficulty || program.difficulty === filters.difficulty;
    const matchesType = !filters.type || program.type === filters.type;
    
    return matchesSearch && matchesDifficulty && matchesType;
  });

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900 dark:text-white">
            Workouts
          </h1>
          <p className="mt-2 text-secondary-600 dark:text-secondary-400">
            Manage your workout programs and track your progress
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center rounded-lg bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Workout
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Search className="h-5 w-5 text-secondary-400" />
            </div>
            <input
              type="text"
              placeholder="Search workouts and programs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full rounded-lg border border-secondary-300 bg-white py-2 pl-10 pr-3 text-sm placeholder-secondary-500 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 dark:border-secondary-600 dark:bg-secondary-700 dark:text-white dark:placeholder-secondary-400"
            />
          </div>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="inline-flex items-center rounded-lg border border-secondary-300 bg-white px-4 py-2 text-sm font-medium text-secondary-700 shadow-sm hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:border-secondary-600 dark:bg-secondary-700 dark:text-secondary-300 dark:hover:bg-secondary-600"
        >
          <Filter className="mr-2 h-4 w-4" />
          Filters
        </button>
      </div>

      {/* Filters */}
      {showFilters && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
        >
          <WorkoutFilters filters={filters} onFiltersChange={setFilters} />
        </motion.div>
      )}

      {/* Tabs */}
      <div className="border-b border-secondary-200 dark:border-secondary-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  isActive
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300 dark:text-secondary-400 dark:hover:text-secondary-300'
                }`}
              >
                <tab.icon className="mr-2 h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'today' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div>
              <h2 className="text-xl font-semibold text-secondary-900 dark:text-white mb-4">
                Today's Workouts
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {todaysWorkouts.map((workout) => (
                  <WorkoutCard
                    key={workout.id}
                    workout={workout}
                    showStartButton
                  />
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'programs' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div>
              <h2 className="text-xl font-semibold text-secondary-900 dark:text-white mb-4">
                Workout Programs
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredPrograms.map((program) => (
                  <div
                    key={program.id}
                    className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                          {program.name}
                        </h3>
                        <p className="text-sm text-secondary-600 dark:text-secondary-400 mt-1">
                          {program.description}
                        </p>
                      </div>
                      <button className="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300">
                        <MoreHorizontal className="h-5 w-5" />
                      </button>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-secondary-600 dark:text-secondary-400 mb-4">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {program.duration} weeks
                      </div>
                      <div className="flex items-center">
                        <Target className="h-4 w-4 mr-1" />
                        {program.difficulty}
                      </div>
                      <div className="flex items-center">
                        <Dumbbell className="h-4 w-4 mr-1" />
                        {program.workoutsPerWeek}/week
                      </div>
                    </div>

                    {program.isActive && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span className="text-secondary-600 dark:text-secondary-400">
                            Week {program.currentWeek} of {program.duration}
                          </span>
                          <span className="text-primary-600 dark:text-primary-400 font-medium">
                            {program.progress}%
                          </span>
                        </div>
                        <div className="w-full bg-secondary-200 rounded-full h-2 dark:bg-secondary-700">
                          <div
                            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${program.progress}%` }}
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          program.isActive
                            ? 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400'
                            : 'bg-secondary-100 text-secondary-800 dark:bg-secondary-700 dark:text-secondary-300'
                        }`}
                      >
                        {program.isActive ? 'Active' : 'Available'}
                      </span>
                      <button
                        className={`inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                          program.isActive
                            ? 'bg-primary-600 text-white hover:bg-primary-700'
                            : 'bg-secondary-100 text-secondary-700 hover:bg-secondary-200 dark:bg-secondary-700 dark:text-secondary-300 dark:hover:bg-secondary-600'
                        }`}
                      >
                        {program.isActive ? (
                          <>
                            <Play className="mr-1 h-3 w-3" />
                            Continue
                          </>
                        ) : (
                          'Start Program'
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        )}

        {activeTab === 'history' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div>
              <h2 className="text-xl font-semibold text-secondary-900 dark:text-white mb-4">
                Workout History
              </h2>
              <div className="bg-white rounded-2xl shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700">
                <div className="divide-y divide-secondary-200 dark:divide-secondary-700">
                  {recentWorkouts.map((workout) => (
                    <div key={workout.id} className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="h-12 w-12 rounded-lg bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center">
                            <Dumbbell className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                          </div>
                          <div>
                            <h3 className="font-medium text-secondary-900 dark:text-white">
                              {workout.name}
                            </h3>
                            <p className="text-sm text-secondary-500 dark:text-secondary-400">
                              {workout.program} • {workout.duration} min • {workout.exercises} exercises
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < workout.rating
                                    ? 'text-warning-400 fill-current'
                                    : 'text-secondary-300 dark:text-secondary-600'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm text-secondary-500 dark:text-secondary-400">
                            {new Date(workout.completedAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Create Workout Modal */}
      {showCreateModal && (
        <CreateWorkoutModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
        />
      )}
    </div>
  );
}
