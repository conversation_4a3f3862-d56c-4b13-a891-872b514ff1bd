'use client';

import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Target,
  Clock,
  Dumbbell,
  Fire,
  Award,
} from 'lucide-react';

interface ProgressStatsProps {
  timeRange: string;
}

const stats = [
  {
    name: 'Total Workouts',
    value: '24',
    change: '+8',
    changeType: 'positive' as const,
    icon: Activity,
    color: 'blue',
    description: 'Completed this period',
  },
  {
    name: 'Workout Streak',
    value: '12 days',
    change: '+3',
    changeType: 'positive' as const,
    icon: Fire,
    color: 'orange',
    description: 'Current active streak',
  },
  {
    name: 'Total Volume',
    value: '48,250 lbs',
    change: '+12%',
    changeType: 'positive' as const,
    icon: Dumbbell,
    color: 'green',
    description: 'Weight lifted',
  },
  {
    name: 'Avg Duration',
    value: '52 min',
    change: '-3 min',
    changeType: 'negative' as const,
    icon: Clock,
    color: 'purple',
    description: 'Per workout',
  },
  {
    name: 'Goals Achieved',
    value: '8/10',
    change: '+2',
    changeType: 'positive' as const,
    icon: Target,
    color: 'indigo',
    description: 'This period',
  },
  {
    name: 'PRs Set',
    value: '15',
    change: '+5',
    changeType: 'positive' as const,
    icon: Award,
    color: 'yellow',
    description: 'Personal records',
  },
];

const colorClasses = {
  blue: {
    bg: 'bg-blue-50 dark:bg-blue-900/20',
    icon: 'text-blue-600 dark:text-blue-400',
    border: 'border-blue-200 dark:border-blue-800',
  },
  orange: {
    bg: 'bg-orange-50 dark:bg-orange-900/20',
    icon: 'text-orange-600 dark:text-orange-400',
    border: 'border-orange-200 dark:border-orange-800',
  },
  green: {
    bg: 'bg-green-50 dark:bg-green-900/20',
    icon: 'text-green-600 dark:text-green-400',
    border: 'border-green-200 dark:border-green-800',
  },
  purple: {
    bg: 'bg-purple-50 dark:bg-purple-900/20',
    icon: 'text-purple-600 dark:text-purple-400',
    border: 'border-purple-200 dark:border-purple-800',
  },
  indigo: {
    bg: 'bg-indigo-50 dark:bg-indigo-900/20',
    icon: 'text-indigo-600 dark:text-indigo-400',
    border: 'border-indigo-200 dark:border-indigo-800',
  },
  yellow: {
    bg: 'bg-yellow-50 dark:bg-yellow-900/20',
    icon: 'text-yellow-600 dark:text-yellow-400',
    border: 'border-yellow-200 dark:border-yellow-800',
  },
};

export function ProgressStats({ timeRange }: ProgressStatsProps) {
  const getTimeRangeLabel = (range: string) => {
    switch (range) {
      case '7d': return 'last 7 days';
      case '30d': return 'last 30 days';
      case '90d': return 'last 3 months';
      case '1y': return 'last year';
      case 'all': return 'all time';
      default: return 'selected period';
    }
  };

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {stats.map((stat, index) => {
        const colorClass = colorClasses[stat.color as keyof typeof colorClasses];
        
        return (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`relative overflow-hidden rounded-2xl bg-white p-6 shadow-sm border ${colorClass.border} dark:bg-secondary-800`}
          >
            {/* Background decoration */}
            <div className={`absolute -top-4 -right-4 h-16 w-16 rounded-full ${colorClass.bg} opacity-50`} />
            
            <div className="relative">
              {/* Icon */}
              <div className={`inline-flex h-12 w-12 items-center justify-center rounded-lg ${colorClass.bg}`}>
                <stat.icon className={`h-6 w-6 ${colorClass.icon}`} />
              </div>

              {/* Content */}
              <div className="mt-4">
                <div className="flex items-baseline justify-between">
                  <h3 className="text-2xl font-bold text-secondary-900 dark:text-white">
                    {stat.value}
                  </h3>
                  <div className="flex items-center">
                    {stat.changeType === 'positive' ? (
                      <TrendingUp className="h-4 w-4 text-success-500 mr-1" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-error-500 mr-1" />
                    )}
                    <span
                      className={`text-sm font-medium ${
                        stat.changeType === 'positive'
                          ? 'text-success-600 dark:text-success-400'
                          : 'text-error-600 dark:text-error-400'
                      }`}
                    >
                      {stat.change}
                    </span>
                  </div>
                </div>
                
                <p className="text-sm font-medium text-secondary-900 dark:text-white mt-1">
                  {stat.name}
                </p>
                
                <p className="text-xs text-secondary-500 dark:text-secondary-400 mt-1">
                  {stat.description}
                </p>
                
                <p className="text-xs text-secondary-400 dark:text-secondary-500 mt-2">
                  vs {getTimeRangeLabel(timeRange)}
                </p>
              </div>
            </div>
          </motion.div>
        );
      })}
    </div>
  );
}
