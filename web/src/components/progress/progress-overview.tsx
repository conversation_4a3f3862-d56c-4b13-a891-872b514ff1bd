'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from 'recharts';
import {
  TrendingUp,
  Activity,
  Target,
  Calendar,
  MoreHorizontal,
} from 'lucide-react';

interface ProgressOverviewProps {
  timeRange: string;
}

// Sample data - in real app, this would come from API
const workoutVolumeData = [
  { date: '2024-01-01', volume: 12500, workouts: 3 },
  { date: '2024-01-08', volume: 14200, workouts: 4 },
  { date: '2024-01-15', volume: 13800, workouts: 3 },
  { date: '2024-01-22', volume: 15600, workouts: 4 },
  { date: '2024-01-29', volume: 16200, workouts: 4 },
  { date: '2024-02-05', volume: 17100, workouts: 5 },
  { date: '2024-02-12', volume: 16800, workouts: 4 },
];

const strengthProgressData = [
  { exercise: 'Bench Press', current: 185, previous: 175, improvement: 5.7 },
  { exercise: 'Squat', current: 225, previous: 210, improvement: 7.1 },
  { exercise: 'Deadlift', current: 275, previous: 260, improvement: 5.8 },
  { exercise: 'Overhead Press', current: 125, previous: 120, improvement: 4.2 },
];

const workoutTypeData = [
  { name: 'Strength', value: 65, color: '#3B82F6' },
  { name: 'Cardio', value: 20, color: '#EF4444' },
  { name: 'Flexibility', value: 10, color: '#10B981' },
  { name: 'Sports', value: 5, color: '#F59E0B' },
];

const weeklyActivityData = [
  { day: 'Mon', workouts: 1, duration: 45 },
  { day: 'Tue', workouts: 0, duration: 0 },
  { day: 'Wed', workouts: 1, duration: 52 },
  { day: 'Thu', workouts: 0, duration: 0 },
  { day: 'Fri', workouts: 1, duration: 48 },
  { day: 'Sat', workouts: 1, duration: 60 },
  { day: 'Sun', workouts: 0, duration: 0 },
];

export function ProgressOverview({ timeRange }: ProgressOverviewProps) {
  const [selectedChart, setSelectedChart] = useState<'volume' | 'strength' | 'activity'>('volume');

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-secondary-800 p-3 rounded-lg shadow-lg border border-secondary-200 dark:border-secondary-700">
          <p className="text-sm font-medium text-secondary-900 dark:text-white">
            {label}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.dataKey === 'volume' && ' lbs'}
              {entry.dataKey === 'duration' && ' min'}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-8">
      {/* Main Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Workout Volume Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                Workout Volume Trend
              </h3>
              <p className="text-sm text-secondary-500 dark:text-secondary-400">
                Total weight lifted over time
              </p>
            </div>
            <button className="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300">
              <MoreHorizontal className="h-5 w-5" />
            </button>
          </div>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={workoutVolumeData}>
                <defs>
                  <linearGradient id="volumeGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={formatDate}
                  stroke="#6B7280"
                  fontSize={12}
                />
                <YAxis stroke="#6B7280" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="volume"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  fill="url(#volumeGradient)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Strength Progress Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                Strength Progress
              </h3>
              <p className="text-sm text-secondary-500 dark:text-secondary-400">
                1RM improvements by exercise
              </p>
            </div>
            <button className="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300">
              <MoreHorizontal className="h-5 w-5" />
            </button>
          </div>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={strengthProgressData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis type="number" stroke="#6B7280" fontSize={12} />
                <YAxis 
                  type="category" 
                  dataKey="exercise" 
                  stroke="#6B7280" 
                  fontSize={12}
                  width={80}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="current" fill="#10B981" radius={[0, 4, 4, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Weekly Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                Weekly Activity
              </h3>
              <p className="text-sm text-secondary-500 dark:text-secondary-400">
                Workouts per day this week
              </p>
            </div>
            <button className="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300">
              <MoreHorizontal className="h-5 w-5" />
            </button>
          </div>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyActivityData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis dataKey="day" stroke="#6B7280" fontSize={12} />
                <YAxis stroke="#6B7280" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="workouts" fill="#8B5CF6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Workout Type Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                Workout Types
              </h3>
              <p className="text-sm text-secondary-500 dark:text-secondary-400">
                Distribution by category
              </p>
            </div>
            <button className="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300">
              <MoreHorizontal className="h-5 w-5" />
            </button>
          </div>
          
          <div className="h-64 flex items-center">
            <div className="w-1/2">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={workoutTypeData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {workoutTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="w-1/2 space-y-3">
              {workoutTypeData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-3"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm font-medium text-secondary-900 dark:text-white">
                      {item.name}
                    </span>
                  </div>
                  <span className="text-sm text-secondary-500 dark:text-secondary-400">
                    {item.value}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Recent Achievements */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
            Recent Achievements
          </h3>
          <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
            View All
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { title: 'New PR', description: 'Bench Press: 185 lbs', date: '2 days ago', icon: TrendingUp, color: 'text-success-600' },
            { title: '10 Day Streak', description: 'Consistent workouts', date: '1 week ago', icon: Activity, color: 'text-orange-600' },
            { title: 'Goal Achieved', description: 'Monthly volume target', date: '2 weeks ago', icon: Target, color: 'text-primary-600' },
            { title: 'Milestone', description: '100 total workouts', date: '3 weeks ago', icon: Calendar, color: 'text-purple-600' },
          ].map((achievement, index) => (
            <div
              key={index}
              className="p-4 rounded-lg bg-secondary-50 dark:bg-secondary-700/50"
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg bg-white dark:bg-secondary-800 ${achievement.color}`}>
                  <achievement.icon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-secondary-900 dark:text-white">
                    {achievement.title}
                  </p>
                  <p className="text-sm text-secondary-500 dark:text-secondary-400">
                    {achievement.description}
                  </p>
                  <p className="text-xs text-secondary-400 dark:text-secondary-500 mt-1">
                    {achievement.date}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}
