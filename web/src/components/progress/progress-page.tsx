'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Calendar,
  Target,
  Camera,
  Scale,
  Ruler,
  Activity,
  Award,
  Plus,
  Filter,
  Download,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'lucide-react';
import { ProgressOverview } from '@/components/progress/progress-overview';
import { WorkoutHistory } from '@/components/progress/workout-history';
import { BodyMeasurements } from '@/components/progress/body-measurements';
import { ProgressPhotos } from '@/components/progress/progress-photos';
import { StrengthProgress } from '@/components/progress/strength-progress';
import { GoalsTracking } from '@/components/progress/goals-tracking';
import { ProgressStats } from '@/components/progress/progress-stats';

const tabs = [
  { id: 'overview', label: 'Overview', icon: BarChart3 },
  { id: 'workouts', label: 'Workouts', icon: Activity },
  { id: 'strength', label: 'Strength', icon: TrendingUp },
  { id: 'measurements', label: 'Measurements', icon: Ruler },
  { id: 'photos', label: 'Photos', icon: Camera },
  { id: 'goals', label: 'Goals', icon: Target },
];

const timeRanges = [
  { id: '7d', label: '7 Days' },
  { id: '30d', label: '30 Days' },
  { id: '90d', label: '3 Months' },
  { id: '1y', label: '1 Year' },
  { id: 'all', label: 'All Time' },
];

export function ProgressPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('30d');
  const [showFilters, setShowFilters] = useState(false);

  const handleExportData = () => {
    // Implement data export functionality
    console.log('Exporting data for time range:', timeRange);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900 dark:text-white">
            Progress Tracking
          </h1>
          <p className="mt-2 text-secondary-600 dark:text-secondary-400">
            Monitor your fitness journey with detailed analytics and insights
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center rounded-lg border border-secondary-300 bg-white px-4 py-2 text-sm font-medium text-secondary-700 shadow-sm hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:border-secondary-600 dark:bg-secondary-700 dark:text-secondary-300 dark:hover:bg-secondary-600"
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </button>
          <button
            onClick={handleExportData}
            className="inline-flex items-center rounded-lg border border-secondary-300 bg-white px-4 py-2 text-sm font-medium text-secondary-700 shadow-sm hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:border-secondary-600 dark:bg-secondary-700 dark:text-secondary-300 dark:hover:bg-secondary-600"
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </button>
        </div>
      </div>

      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-1 rounded-lg bg-secondary-100 p-1 dark:bg-secondary-800">
          {timeRanges.map((range) => (
            <button
              key={range.id}
              onClick={() => setTimeRange(range.id)}
              className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                timeRange === range.id
                  ? 'bg-white text-secondary-900 shadow-sm dark:bg-secondary-700 dark:text-white'
                  : 'text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-white'
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>

      {/* Progress Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <ProgressStats timeRange={timeRange} />
      </motion.div>

      {/* Tabs */}
      <div className="border-b border-secondary-200 dark:border-secondary-700">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  isActive
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300 dark:text-secondary-400 dark:hover:text-secondary-300'
                }`}
              >
                <tab.icon className="mr-2 h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ProgressOverview timeRange={timeRange} />
          </motion.div>
        )}

        {activeTab === 'workouts' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <WorkoutHistory timeRange={timeRange} />
          </motion.div>
        )}

        {activeTab === 'strength' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <StrengthProgress timeRange={timeRange} />
          </motion.div>
        )}

        {activeTab === 'measurements' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <BodyMeasurements timeRange={timeRange} />
          </motion.div>
        )}

        {activeTab === 'photos' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ProgressPhotos timeRange={timeRange} />
          </motion.div>
        )}

        {activeTab === 'goals' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <GoalsTracking timeRange={timeRange} />
          </motion.div>
        )}
      </div>
    </div>
  );
}
