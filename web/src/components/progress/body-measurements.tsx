'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import {
  Plus,
  Scale,
  Ruler,
  TrendingUp,
  TrendingDown,
  Edit,
  Calendar,
} from 'lucide-react';

interface BodyMeasurementsProps {
  timeRange: string;
}

// Sample data - in real app, this would come from API
const weightData = [
  { date: '2024-01-01', weight: 180.5 },
  { date: '2024-01-08', weight: 179.8 },
  { date: '2024-01-15', weight: 179.2 },
  { date: '2024-01-22', weight: 178.9 },
  { date: '2024-01-29', weight: 178.1 },
  { date: '2024-02-05', weight: 177.8 },
  { date: '2024-02-12', weight: 177.3 },
];

const bodyFatData = [
  { date: '2024-01-01', bodyFat: 15.2 },
  { date: '2024-01-15', bodyFat: 14.8 },
  { date: '2024-01-29', bodyFat: 14.5 },
  { date: '2024-02-12', bodyFat: 14.1 },
];

const measurements = [
  {
    name: 'Weight',
    current: 177.3,
    previous: 180.5,
    unit: 'lbs',
    change: -3.2,
    icon: Scale,
    color: 'blue',
    data: weightData,
    dataKey: 'weight',
  },
  {
    name: 'Body Fat',
    current: 14.1,
    previous: 15.2,
    unit: '%',
    change: -1.1,
    icon: TrendingDown,
    color: 'green',
    data: bodyFatData,
    dataKey: 'bodyFat',
  },
  {
    name: 'Chest',
    current: 42.5,
    previous: 42.0,
    unit: 'in',
    change: 0.5,
    icon: Ruler,
    color: 'purple',
    data: [],
    dataKey: 'chest',
  },
  {
    name: 'Waist',
    current: 32.0,
    previous: 33.5,
    unit: 'in',
    change: -1.5,
    icon: Ruler,
    color: 'orange',
    data: [],
    dataKey: 'waist',
  },
  {
    name: 'Arms',
    current: 15.8,
    previous: 15.5,
    unit: 'in',
    change: 0.3,
    icon: Ruler,
    color: 'indigo',
    data: [],
    dataKey: 'arms',
  },
  {
    name: 'Thighs',
    current: 24.2,
    previous: 23.8,
    unit: 'in',
    change: 0.4,
    icon: Ruler,
    color: 'pink',
    data: [],
    dataKey: 'thighs',
  },
];

const colorClasses = {
  blue: 'text-blue-600 dark:text-blue-400',
  green: 'text-green-600 dark:text-green-400',
  purple: 'text-purple-600 dark:text-purple-400',
  orange: 'text-orange-600 dark:text-orange-400',
  indigo: 'text-indigo-600 dark:text-indigo-400',
  pink: 'text-pink-600 dark:text-pink-400',
};

export function BodyMeasurements({ timeRange }: BodyMeasurementsProps) {
  const [selectedMeasurement, setSelectedMeasurement] = useState(measurements[0]);
  const [showAddModal, setShowAddModal] = useState(false);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-secondary-800 p-3 rounded-lg shadow-lg border border-secondary-200 dark:border-secondary-700">
          <p className="text-sm font-medium text-secondary-900 dark:text-white">
            {formatDate(label)}
          </p>
          <p className="text-sm" style={{ color: payload[0].color }}>
            {selectedMeasurement.name}: {payload[0].value} {selectedMeasurement.unit}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-8">
      {/* Add Measurement Button */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-secondary-900 dark:text-white">
            Body Measurements
          </h2>
          <p className="text-secondary-600 dark:text-secondary-400">
            Track your body composition and measurements over time
          </p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="inline-flex items-center rounded-lg bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Measurement
        </button>
      </div>

      {/* Measurements Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {measurements.map((measurement, index) => (
          <motion.div
            key={measurement.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            onClick={() => setSelectedMeasurement(measurement)}
            className={`bg-white rounded-2xl p-6 shadow-sm border cursor-pointer transition-all hover:shadow-md ${
              selectedMeasurement.name === measurement.name
                ? 'border-primary-500 ring-2 ring-primary-500 ring-opacity-20'
                : 'border-secondary-200 dark:border-secondary-700'
            } dark:bg-secondary-800`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-secondary-50 dark:bg-secondary-700">
                  <measurement.icon className={`h-5 w-5 ${colorClasses[measurement.color as keyof typeof colorClasses]}`} />
                </div>
                <h3 className="ml-3 text-lg font-semibold text-secondary-900 dark:text-white">
                  {measurement.name}
                </h3>
              </div>
              <button className="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300">
                <Edit className="h-4 w-4" />
              </button>
            </div>

            <div className="space-y-2">
              <div className="flex items-baseline justify-between">
                <span className="text-3xl font-bold text-secondary-900 dark:text-white">
                  {measurement.current}
                </span>
                <span className="text-sm text-secondary-500 dark:text-secondary-400">
                  {measurement.unit}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-secondary-500 dark:text-secondary-400">
                  Previous: {measurement.previous} {measurement.unit}
                </span>
                <div className="flex items-center">
                  {measurement.change > 0 ? (
                    <TrendingUp className="h-4 w-4 text-success-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-error-500 mr-1" />
                  )}
                  <span
                    className={`text-sm font-medium ${
                      measurement.change > 0
                        ? 'text-success-600 dark:text-success-400'
                        : 'text-error-600 dark:text-error-400'
                    }`}
                  >
                    {measurement.change > 0 ? '+' : ''}{measurement.change} {measurement.unit}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Chart */}
      {selectedMeasurement.data.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                {selectedMeasurement.name} Trend
              </h3>
              <p className="text-sm text-secondary-500 dark:text-secondary-400">
                Progress over the selected time period
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-secondary-400" />
              <span className="text-sm text-secondary-500 dark:text-secondary-400">
                {timeRange === '7d' && 'Last 7 days'}
                {timeRange === '30d' && 'Last 30 days'}
                {timeRange === '90d' && 'Last 3 months'}
                {timeRange === '1y' && 'Last year'}
                {timeRange === 'all' && 'All time'}
              </span>
            </div>
          </div>

          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={selectedMeasurement.data}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={formatDate}
                  stroke="#6B7280"
                  fontSize={12}
                />
                <YAxis 
                  stroke="#6B7280" 
                  fontSize={12}
                  domain={['dataMin - 1', 'dataMax + 1']}
                />
                <Tooltip content={<CustomTooltip />} />
                <Line
                  type="monotone"
                  dataKey={selectedMeasurement.dataKey}
                  stroke="#3B82F6"
                  strokeWidth={3}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, stroke: '#3B82F6', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      )}

      {/* Recent Entries */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
            Recent Entries
          </h3>
          <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
            View All
          </button>
        </div>

        <div className="space-y-4">
          {[
            { date: '2024-02-12', type: 'Weight', value: '177.3 lbs', change: '-0.5 lbs' },
            { date: '2024-02-12', type: 'Body Fat', value: '14.1%', change: '-0.4%' },
            { date: '2024-02-10', type: 'Chest', value: '42.5 in', change: '+0.2 in' },
            { date: '2024-02-08', type: 'Waist', value: '32.0 in', change: '-0.3 in' },
          ].map((entry, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 rounded-lg bg-secondary-50 dark:bg-secondary-700/50"
            >
              <div className="flex items-center space-x-4">
                <div className="p-2 rounded-lg bg-white dark:bg-secondary-800">
                  <Scale className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                </div>
                <div>
                  <p className="font-medium text-secondary-900 dark:text-white">
                    {entry.type}
                  </p>
                  <p className="text-sm text-secondary-500 dark:text-secondary-400">
                    {new Date(entry.date).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium text-secondary-900 dark:text-white">
                  {entry.value}
                </p>
                <p className={`text-sm ${
                  entry.change.startsWith('-') 
                    ? 'text-error-600 dark:text-error-400' 
                    : 'text-success-600 dark:text-success-400'
                }`}>
                  {entry.change}
                </p>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}
