'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  WifiOff,
  RefreshCw,
  Home,
  Activity,
  Calendar,
  TrendingUp,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import Link from 'next/link';

export function OfflinePage() {
  const [isOnline, setIsOnline] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [pendingSync, setPendingSync] = useState({
    workouts: 0,
    messages: 0,
    progress: 0,
  });

  useEffect(() => {
    // Check online status
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    updateOnlineStatus();
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Check for pending sync data
    checkPendingSync();

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  const checkPendingSync = async () => {
    try {
      // Check IndexedDB for pending sync data
      const request = indexedDB.open('FitnessAppOffline', 1);
      
      request.onsuccess = () => {
        const db = request.result;
        const stores = ['workouts', 'messages', 'progress'];
        const counts = { workouts: 0, messages: 0, progress: 0 };
        
        stores.forEach(storeName => {
          const transaction = db.transaction([storeName], 'readonly');
          const store = transaction.objectStore(storeName);
          const countRequest = store.count();
          
          countRequest.onsuccess = () => {
            counts[storeName as keyof typeof counts] = countRequest.result;
            setPendingSync({ ...counts });
          };
        });
      };
    } catch (error) {
      console.error('Failed to check pending sync data:', error);
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  const offlineFeatures = [
    {
      icon: Activity,
      title: 'View Cached Workouts',
      description: 'Access your recently viewed workout programs and exercises',
      available: true,
    },
    {
      icon: Calendar,
      title: 'Log Workouts Offline',
      description: 'Record your workout progress - it will sync when you\'re back online',
      available: true,
    },
    {
      icon: TrendingUp,
      title: 'View Progress History',
      description: 'Check your cached progress data and workout history',
      available: true,
    },
    {
      icon: CheckCircle,
      title: 'Complete Exercises',
      description: 'Mark exercises as complete - data will be saved locally',
      available: true,
    },
  ];

  return (
    <div className="min-h-screen bg-secondary-50 dark:bg-secondary-900 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-8"
        >
          {/* Offline Icon */}
          <div className="mx-auto w-24 h-24 bg-secondary-200 dark:bg-secondary-700 rounded-full flex items-center justify-center mb-6">
            <WifiOff className="w-12 h-12 text-secondary-600 dark:text-secondary-400" />
          </div>

          {/* Title and Description */}
          <h1 className="text-3xl font-bold text-secondary-900 dark:text-white mb-4">
            You're Offline
          </h1>
          <p className="text-lg text-secondary-600 dark:text-secondary-400 mb-8">
            No internet connection detected. Don't worry - you can still use many features of FitnessApp!
          </p>

          {/* Connection Status */}
          <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
            isOnline 
              ? 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400'
              : 'bg-error-100 text-error-800 dark:bg-error-900/20 dark:text-error-400'
          }`}>
            <div className={`w-2 h-2 rounded-full mr-2 ${
              isOnline ? 'bg-success-500' : 'bg-error-500'
            }`} />
            {isOnline ? 'Connection restored!' : 'No internet connection'}
          </div>
        </motion.div>

        {/* Pending Sync Status */}
        {(pendingSync.workouts > 0 || pendingSync.messages > 0 || pendingSync.progress > 0) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-2xl p-6 mb-8"
          >
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-warning-600 dark:text-warning-400 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-semibold text-warning-800 dark:text-warning-300 mb-2">
                  Data Waiting to Sync
                </h3>
                <p className="text-sm text-warning-700 dark:text-warning-400 mb-3">
                  You have offline data that will be synced when your connection is restored:
                </p>
                <div className="space-y-1 text-sm text-warning-700 dark:text-warning-400">
                  {pendingSync.workouts > 0 && (
                    <div>• {pendingSync.workouts} workout log{pendingSync.workouts !== 1 ? 's' : ''}</div>
                  )}
                  {pendingSync.messages > 0 && (
                    <div>• {pendingSync.messages} message{pendingSync.messages !== 1 ? 's' : ''}</div>
                  )}
                  {pendingSync.progress > 0 && (
                    <div>• {pendingSync.progress} progress entr{pendingSync.progress !== 1 ? 'ies' : 'y'}</div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Available Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-white dark:bg-secondary-800 rounded-2xl p-6 mb-8"
        >
          <h2 className="text-xl font-semibold text-secondary-900 dark:text-white mb-6">
            Available Offline Features
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {offlineFeatures.map((feature, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 ${
                  feature.available
                    ? 'border-success-200 bg-success-50 dark:border-success-800 dark:bg-success-900/20'
                    : 'border-secondary-200 bg-secondary-50 dark:border-secondary-700 dark:bg-secondary-700/50'
                }`}
              >
                <div className="flex items-start">
                  <div className={`p-2 rounded-lg mr-3 ${
                    feature.available
                      ? 'bg-success-100 dark:bg-success-900/50'
                      : 'bg-secondary-100 dark:bg-secondary-600'
                  }`}>
                    <feature.icon className={`w-5 h-5 ${
                      feature.available
                        ? 'text-success-600 dark:text-success-400'
                        : 'text-secondary-600 dark:text-secondary-400'
                    }`} />
                  </div>
                  <div>
                    <h3 className={`font-medium mb-1 ${
                      feature.available
                        ? 'text-success-900 dark:text-success-100'
                        : 'text-secondary-900 dark:text-white'
                    }`}>
                      {feature.title}
                    </h3>
                    <p className={`text-sm ${
                      feature.available
                        ? 'text-success-700 dark:text-success-300'
                        : 'text-secondary-600 dark:text-secondary-400'
                    }`}>
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <button
            onClick={handleRetry}
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <RefreshCw className="w-5 h-5 mr-2" />
            Try Again {retryCount > 0 && `(${retryCount})`}
          </button>
          
          <Link
            href="/dashboard"
            className="inline-flex items-center justify-center px-6 py-3 border border-secondary-300 dark:border-secondary-600 text-base font-medium rounded-lg text-secondary-700 dark:text-secondary-300 bg-white dark:bg-secondary-800 hover:bg-secondary-50 dark:hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <Home className="w-5 h-5 mr-2" />
            Go to Dashboard
          </Link>
        </motion.div>

        {/* Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-8 text-center"
        >
          <div className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-primary-900 dark:text-primary-100 mb-2">
              💡 Offline Tips
            </h3>
            <p className="text-sm text-primary-700 dark:text-primary-300">
              Your workout data is automatically saved locally and will sync when you're back online. 
              You can continue using the app normally!
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
