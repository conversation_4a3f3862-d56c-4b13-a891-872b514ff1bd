'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '@/types/database';

// Gender-based theme types (matching mobile app)
export type GenderThemeType = 'Woman' | 'Man' | 'Neutral';

interface GenderThemeColors {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  accent: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  overlayText: string;
  overlayBackground: string;
  contrastText: string;
}

interface GenderThemeContextType {
  currentTheme: GenderThemeType;
  colors: GenderThemeColors;
  isInitialized: boolean;
  setGenderTheme: (theme: GenderThemeType) => void;
  initializeFromProfile: () => Promise<void>;
}

// Theme configurations (matching mobile app)
const genderThemeConfigs: Record<GenderThemeType, GenderThemeColors> = {
  Neutral: {
    primary: '#508C9B',
    secondary: '#D0D0D0',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#ECECEC',
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
  Woman: {
    primary: '#B37399',
    secondary: '#2b3568',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#F0E6F7',
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
  Man: {
    primary: '#353E43',
    secondary: '#FFFFFF',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#F5F5F5',
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
};

const GenderThemeContext = createContext<GenderThemeContextType | undefined>(undefined);

export function useGenderTheme() {
  const context = useContext(GenderThemeContext);
  if (context === undefined) {
    throw new Error('useGenderTheme must be used within a GenderThemeProvider');
  }
  return context;
}

interface GenderThemeProviderProps {
  children: React.ReactNode;
}

export function GenderThemeProvider({ children }: GenderThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<GenderThemeType>('Neutral');
  const [isInitialized, setIsInitialized] = useState(false);
  const supabase = createClientComponentClient<Database>();

  // Get colors for current theme
  const colors = genderThemeConfigs[currentTheme];

  // Load theme from localStorage on mount
  useEffect(() => {
    console.log('🎨 Web: Gender theme provider mounting...');
    const savedTheme = localStorage.getItem('gender-theme') as GenderThemeType;
    const savedInitialized = localStorage.getItem('gender-theme-initialized') === 'true';

    console.log('🎨 Web: Saved theme from localStorage:', { savedTheme, savedInitialized });

    if (savedTheme && Object.keys(genderThemeConfigs).includes(savedTheme)) {
      console.log('🎨 Web: Loading saved gender theme:', savedTheme);
      setCurrentTheme(savedTheme);
      setIsInitialized(savedInitialized);

      // Apply theme immediately and also after a short delay to ensure DOM is ready
      applyThemeToDOM(savedTheme);
      setTimeout(() => {
        console.log('🎨 Web: Re-applying theme after delay to ensure DOM is ready');
        applyThemeToDOM(savedTheme);
      }, 500);
    } else {
      console.log('🎨 Web: No valid saved theme found, will initialize from profile');
    }
  }, []);

  // Apply theme colors to CSS custom properties
  const applyThemeToDOM = (theme: GenderThemeType) => {
    console.log('🎨 Web: Applying theme to DOM:', theme);

    const themeColors = genderThemeConfigs[theme];
    const root = document.documentElement;

    // Convert hex to RGB for CSS custom properties
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    };

    const primaryRgb = hexToRgb(themeColors.primary);
    const accentRgb = hexToRgb(themeColors.accent);

    console.log('🎨 Web: Color conversion:', {
      primary: themeColors.primary,
      primaryRgb,
      accent: themeColors.accent,
      accentRgb
    });

    if (primaryRgb) {
      const primaryValue = `${primaryRgb.r} ${primaryRgb.g} ${primaryRgb.b}`;
      // Update both primary and gender-primary
      root.style.setProperty('--primary', primaryValue);
      root.style.setProperty('--gender-primary', primaryValue);
      root.style.setProperty('--ring', primaryValue);

      console.log('🎨 Web: Set primary CSS variables to:', primaryValue);
    }
    if (accentRgb) {
      const accentValue = `${accentRgb.r} ${accentRgb.g} ${accentRgb.b}`;
      // Update both accent and gender-accent
      root.style.setProperty('--accent', accentValue);
      root.style.setProperty('--gender-accent', accentValue);

      console.log('🎨 Web: Set accent CSS variables to:', accentValue);
    }

    // Set data attribute for CSS-based theme switching
    root.setAttribute('data-gender-theme', theme);

    // Force a repaint to ensure changes are applied
    root.style.display = 'none';
    root.offsetHeight; // Trigger reflow
    root.style.display = '';

    console.log('🎨 Web: Applied theme colors to DOM:', theme, {
      primary: themeColors.primary,
      accent: themeColors.accent,
      appliedPrimary: primaryRgb ? `${primaryRgb.r} ${primaryRgb.g} ${primaryRgb.b}` : 'none',
      appliedAccent: accentRgb ? `${accentRgb.r} ${accentRgb.g} ${accentRgb.b}` : 'none',
      dataAttribute: root.getAttribute('data-gender-theme')
    });
  };

  // Set gender theme
  const setGenderTheme = (theme: GenderThemeType) => {
    console.log('🎨 Web: Setting gender theme to:', theme);
    setCurrentTheme(theme);
    setIsInitialized(true);
    
    // Save to localStorage
    localStorage.setItem('gender-theme', theme);
    localStorage.setItem('gender-theme-initialized', 'true');
    
    // Apply to DOM
    applyThemeToDOM(theme);
  };

  // Initialize theme from user profile
  const initializeFromProfile = async () => {
    try {
      console.log('🎨 Web: Fetching user profile for theme initialization...');

      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('🎨 Web: Error getting user:', userError);
        return;
      }

      if (!user) {
        console.log('🎨 Web: No user found, keeping neutral theme');
        return;
      }

      console.log('🎨 Web: User found, fetching profile for user ID:', user.id);

      const { data: profile, error } = await supabase
        .from('profiles')
        .select('gender_preference')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('🎨 Web: Error fetching profile:', error);
        return;
      }

      console.log('🎨 Web: Profile fetched:', profile);

      if (profile?.gender_preference) {
        const profileTheme = profile.gender_preference as GenderThemeType;

        console.log('🎨 Web: Profile gender preference:', profileTheme);
        console.log('🎨 Web: Current theme state:', { currentTheme, isInitialized });

        // Only initialize if not already initialized or if theme is currently Neutral or different from profile
        const shouldInitialize =
          !isInitialized ||
          currentTheme === 'Neutral' ||
          currentTheme !== profileTheme;

        console.log('🎨 Web: Should initialize theme?', shouldInitialize);

        if (shouldInitialize) {
          console.log('🎨 Web: Initializing theme from profile:', profileTheme);
          setGenderTheme(profileTheme);
        } else {
          console.log('🎨 Web: Theme already matches profile:', currentTheme);
        }
      } else {
        console.log('🎨 Web: No gender preference found in profile, keeping current theme');
      }
    } catch (error) {
      console.error('🎨 Web: Error initializing theme from profile:', error);
    }
  };

  // Initialize theme when user changes
  useEffect(() => {
    const initializeTheme = async () => {
      console.log('🎨 Web: Starting theme initialization...');

      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('🎨 Web: Error getting session:', error);
          return;
        }

        console.log('🎨 Web: Session check result:', session ? 'authenticated' : 'no session');

        if (session) {
          console.log('🎨 Web: User session found, initializing theme from profile');
          await initializeFromProfile();
        } else {
          console.log('🎨 Web: No session, resetting to neutral theme');
          setGenderTheme('Neutral');
        }
      } catch (error) {
        console.error('🎨 Web: Error during theme initialization:', error);
      }
    };

    // Add a small delay to ensure DOM is ready
    setTimeout(initializeTheme, 100);

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🎨 Web: Auth state change:', event, session ? 'authenticated' : 'no session');

        if (event === 'SIGNED_IN' && session) {
          console.log('🎨 Web: User signed in, initializing theme from profile');
          await initializeFromProfile();
        } else if (event === 'SIGNED_OUT') {
          console.log('🎨 Web: User signed out, resetting to neutral theme');
          setGenderTheme('Neutral');
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Debug function for browser console
  useEffect(() => {
    (window as any).debugGenderTheme = () => {
      console.log('🎨 Web: Gender Theme Debug Info:', {
        currentTheme,
        isInitialized,
        colors,
        localStorage: {
          savedTheme: localStorage.getItem('gender-theme'),
          savedInitialized: localStorage.getItem('gender-theme-initialized')
        },
        cssVariables: {
          primary: getComputedStyle(document.documentElement).getPropertyValue('--primary'),
          accent: getComputedStyle(document.documentElement).getPropertyValue('--accent'),
          genderPrimary: getComputedStyle(document.documentElement).getPropertyValue('--gender-primary'),
          genderAccent: getComputedStyle(document.documentElement).getPropertyValue('--gender-accent')
        },
        dataAttribute: document.documentElement.getAttribute('data-gender-theme')
      });
    };
  }, [currentTheme, isInitialized, colors]);

  const value = {
    currentTheme,
    colors,
    isInitialized,
    setGenderTheme,
    initializeFromProfile,
  };

  return (
    <GenderThemeContext.Provider value={value}>
      {children}
    </GenderThemeContext.Provider>
  );
}
