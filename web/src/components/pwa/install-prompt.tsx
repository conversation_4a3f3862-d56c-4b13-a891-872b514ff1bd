'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Download, Smartphone, Monitor } from 'lucide-react';
import { useOffline } from '@/components/providers';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export function InstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  const { isInstallable, installApp } = useOffline();

  useEffect(() => {
    // Check if running on iOS
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    setIsIOS(iOS);

    // Check if already installed (standalone mode)
    const standalone = window.matchMedia('(display-mode: standalone)').matches;
    setIsStandalone(standalone);

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Show prompt after a delay if not dismissed before
      setTimeout(() => {
        const dismissed = localStorage.getItem('pwa-install-dismissed');
        const lastShown = localStorage.getItem('pwa-install-last-shown');
        const now = Date.now();
        
        // Don't show if dismissed permanently or shown recently (within 7 days)
        if (dismissed === 'permanent' || 
            (lastShown && now - parseInt(lastShown) < 7 * 24 * 60 * 60 * 1000)) {
          return;
        }
        
        setShowPrompt(true);
        localStorage.setItem('pwa-install-last-shown', now.toString());
      }, 3000);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstall = async () => {
    if (deferredPrompt) {
      try {
        await installApp();
        setShowPrompt(false);
        setDeferredPrompt(null);
      } catch (error) {
        console.error('Failed to install PWA:', error);
      }
    }
  };

  const handleDismiss = (permanent = false) => {
    setShowPrompt(false);
    if (permanent) {
      localStorage.setItem('pwa-install-dismissed', 'permanent');
    } else {
      localStorage.setItem('pwa-install-dismissed', 'temporary');
    }
  };

  // Don't show if already installed or not installable
  if (isStandalone || !isInstallable || (!deferredPrompt && !isIOS)) {
    return null;
  }

  return (
    <AnimatePresence>
      {showPrompt && (
        <motion.div
          initial={{ opacity: 0, y: 100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 100 }}
          transition={{ duration: 0.3 }}
          className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm"
        >
          <div className="bg-white dark:bg-secondary-800 rounded-2xl shadow-2xl border border-secondary-200 dark:border-secondary-700 p-6">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/50 rounded-xl flex items-center justify-center mr-3">
                  {isIOS ? (
                    <Smartphone className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                  ) : (
                    <Monitor className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                  )}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                    Install FitnessApp
                  </h3>
                  <p className="text-sm text-secondary-600 dark:text-secondary-400">
                    Get the full app experience
                  </p>
                </div>
              </div>
              <button
                onClick={() => handleDismiss(false)}
                className="text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Benefits */}
            <div className="mb-6">
              <ul className="space-y-2 text-sm text-secondary-600 dark:text-secondary-400">
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3" />
                  Faster loading and offline access
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3" />
                  Push notifications for workouts
                </li>
                <li className="flex items-center">
                  <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3" />
                  Native app-like experience
                </li>
              </ul>
            </div>

            {/* iOS Instructions */}
            {isIOS && (
              <div className="mb-6 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                <p className="text-sm text-primary-800 dark:text-primary-200 mb-2 font-medium">
                  To install on iOS:
                </p>
                <ol className="text-sm text-primary-700 dark:text-primary-300 space-y-1">
                  <li>1. Tap the Share button in Safari</li>
                  <li>2. Scroll down and tap "Add to Home Screen"</li>
                  <li>3. Tap "Add" to install</li>
                </ol>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
              {!isIOS && (
                <button
                  onClick={handleInstall}
                  className="flex-1 flex items-center justify-center px-4 py-2.5 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Install
                </button>
              )}
              <button
                onClick={() => handleDismiss(false)}
                className="px-4 py-2.5 text-secondary-600 dark:text-secondary-400 hover:text-secondary-800 dark:hover:text-secondary-200 transition-colors font-medium"
              >
                Later
              </button>
              <button
                onClick={() => handleDismiss(true)}
                className="px-4 py-2.5 text-secondary-500 dark:text-secondary-500 hover:text-secondary-700 dark:hover:text-secondary-300 transition-colors text-sm"
              >
                Don't ask again
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
