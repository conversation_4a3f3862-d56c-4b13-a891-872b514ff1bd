'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Calendar,
  TrendingUp,
  Target,
  Clock,
  Dumbbell,
  Award,
  Fire,
  Activity,
  ChevronRight,
  Play,
} from 'lucide-react';
import { StatsCard } from '@/components/dashboard/stats-card';
import { WorkoutCard } from '@/components/dashboard/workout-card';
import { ProgressChart } from '@/components/dashboard/progress-chart';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { UpcomingWorkouts } from '@/components/dashboard/upcoming-workouts';

const stats = [
  {
    name: 'Workouts This Week',
    value: '4',
    change: '+2',
    changeType: 'positive' as const,
    icon: Dumbbell,
    color: 'blue',
  },
  {
    name: 'Current Streak',
    value: '12 days',
    change: '+1',
    changeType: 'positive' as const,
    icon: Fire,
    color: 'orange',
  },
  {
    name: 'Total Workouts',
    value: '127',
    change: '+4',
    changeType: 'positive' as const,
    icon: Activity,
    color: 'green',
  },
  {
    name: 'Goals Achieved',
    value: '8/10',
    change: '+1',
    changeType: 'positive' as const,
    icon: Target,
    color: 'purple',
  },
];

const todayWorkout = {
  id: '1',
  name: 'Push Day - Upper Body',
  duration: 45,
  exercises: 8,
  difficulty: 'Intermediate',
  type: 'Strength',
  completed: false,
  scheduledTime: '6:00 PM',
};

const recentWorkouts = [
  {
    id: '1',
    name: 'Pull Day - Back & Biceps',
    completedAt: '2024-01-15T18:30:00Z',
    duration: 52,
    exercises: 7,
    rating: 4,
  },
  {
    id: '2',
    name: 'Leg Day - Quads Focus',
    completedAt: '2024-01-13T17:15:00Z',
    duration: 48,
    exercises: 6,
    rating: 5,
  },
  {
    id: '3',
    name: 'Push Day - Chest & Triceps',
    completedAt: '2024-01-11T19:00:00Z',
    duration: 44,
    exercises: 8,
    rating: 4,
  },
];

export function DashboardOverview() {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">
                {getGreeting()}, John! 👋
              </h1>
              <p className="mt-2 text-primary-100">
                Ready to crush your fitness goals today?
              </p>
            </div>
            <div className="hidden sm:block">
              <div className="text-right">
                <p className="text-sm text-primary-200">Today</p>
                <p className="text-2xl font-semibold">
                  {currentTime.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'short',
                    day: 'numeric',
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4"
      >
        {stats.map((stat, index) => (
          <StatsCard
            key={stat.name}
            {...stat}
            delay={index * 0.1}
          />
        ))}
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-8">
          {/* Today's Workout */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-secondary-900 dark:text-white">
                Today's Workout
              </h2>
              <button className="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center">
                View Schedule
                <ChevronRight className="ml-1 h-4 w-4" />
              </button>
            </div>
            
            <WorkoutCard
              workout={todayWorkout}
              showStartButton
              className="mb-6"
            />
          </motion.div>

          {/* Progress Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                  Weekly Progress
                </h3>
                <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                  View Details
                </button>
              </div>
              <ProgressChart />
            </div>
          </motion.div>

          {/* Recent Workouts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-secondary-900 dark:text-white">
                  Recent Workouts
                </h3>
                <button className="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center">
                  View All
                  <ChevronRight className="ml-1 h-4 w-4" />
                </button>
              </div>
              
              <div className="space-y-4">
                {recentWorkouts.map((workout) => (
                  <div
                    key={workout.id}
                    className="flex items-center justify-between p-4 rounded-lg bg-secondary-50 dark:bg-secondary-700/50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 rounded-lg bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center">
                        <Dumbbell className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                      </div>
                      <div>
                        <h4 className="font-medium text-secondary-900 dark:text-white">
                          {workout.name}
                        </h4>
                        <p className="text-sm text-secondary-500 dark:text-secondary-400">
                          {workout.duration} min • {workout.exercises} exercises
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Award
                            key={i}
                            className={`h-4 w-4 ${
                              i < workout.rating
                                ? 'text-warning-400 fill-current'
                                : 'text-secondary-300 dark:text-secondary-600'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-xs text-secondary-500 dark:text-secondary-400">
                        {new Date(workout.completedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Right Column */}
        <div className="space-y-8">
          {/* Upcoming Workouts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <UpcomingWorkouts />
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <RecentActivity />
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700">
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-white mb-4">
                Quick Actions
              </h3>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-between p-3 rounded-lg bg-primary-50 hover:bg-primary-100 dark:bg-primary-900/20 dark:hover:bg-primary-900/30 transition-colors">
                  <div className="flex items-center">
                    <Play className="h-5 w-5 text-primary-600 dark:text-primary-400 mr-3" />
                    <span className="font-medium text-primary-700 dark:text-primary-300">
                      Start Quick Workout
                    </span>
                  </div>
                  <ChevronRight className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                </button>
                
                <button className="w-full flex items-center justify-between p-3 rounded-lg bg-secondary-50 hover:bg-secondary-100 dark:bg-secondary-700/50 dark:hover:bg-secondary-700 transition-colors">
                  <div className="flex items-center">
                    <TrendingUp className="h-5 w-5 text-secondary-600 dark:text-secondary-400 mr-3" />
                    <span className="font-medium text-secondary-700 dark:text-secondary-300">
                      Log Progress
                    </span>
                  </div>
                  <ChevronRight className="h-4 w-4 text-secondary-600 dark:text-secondary-400" />
                </button>
                
                <button className="w-full flex items-center justify-between p-3 rounded-lg bg-secondary-50 hover:bg-secondary-100 dark:bg-secondary-700/50 dark:hover:bg-secondary-700 transition-colors">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-secondary-600 dark:text-secondary-400 mr-3" />
                    <span className="font-medium text-secondary-700 dark:text-secondary-300">
                      Schedule Workout
                    </span>
                  </div>
                  <ChevronRight className="h-4 w-4 text-secondary-600 dark:text-secondary-400" />
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
