'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import {
  Bell,
  Search,
  Sun,
  Moon,
  User,
  Settings,
  LogOut,
  ChevronDown,
  Palette,
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { createClientSupabase } from '@/lib/supabase';
import { useApp } from '@/components/providers';
import { useGenderTheme } from '@/hooks/use-gender-theme';

export function DashboardHeader() {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const { theme, setTheme } = useTheme();
  const { currentTheme: genderTheme, colors: genderColors } = useGenderTheme();
  const { user, signOut } = useApp();
  const router = useRouter();
  const supabase = createClientSupabase();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success('Signed out successfully');
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <header className="bg-white shadow-sm border-b border-secondary-200 dark:bg-secondary-800 dark:border-secondary-700">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Search */}
          <div className="flex flex-1 items-center lg:ml-6">
            <div className="w-full max-w-lg lg:max-w-xs">
              <label htmlFor="search" className="sr-only">
                Search
              </label>
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <Search className="h-5 w-5 text-secondary-400" aria-hidden="true" />
                </div>
                <input
                  id="search"
                  name="search"
                  className="block w-full rounded-md border border-secondary-300 bg-white py-2 pl-10 pr-3 text-sm placeholder-secondary-500 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 dark:border-secondary-600 dark:bg-secondary-700 dark:text-white dark:placeholder-secondary-400"
                  placeholder="Search workouts, exercises..."
                  type="search"
                />
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="ml-4 flex items-center space-x-4">
            {/* Gender theme indicator */}
            <div
              className="flex items-center space-x-2 rounded-full px-3 py-1 text-sm border-2"
              style={{
                backgroundColor: genderColors.accent,
                borderColor: genderColors.primary,
                color: genderColors.text
              }}
            >
              <Palette
                className="h-4 w-4"
                style={{ color: genderColors.primary }}
                aria-hidden="true"
              />
              <span
                className="font-medium"
                style={{ color: genderColors.primary }}
              >
                {genderTheme} Theme
              </span>
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: genderColors.primary }}
                title={`Primary color: ${genderColors.primary}`}
              />
            </div>

            {/* Theme toggle */}
            <button
              type="button"
              onClick={toggleTheme}
              className="rounded-full bg-white p-1 text-secondary-400 hover:text-secondary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:bg-secondary-800 dark:hover:text-secondary-300"
            >
              <span className="sr-only">Toggle theme</span>
              {theme === 'dark' ? (
                <Sun className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Moon className="h-6 w-6" aria-hidden="true" />
              )}
            </button>

            {/* Notifications */}
            <div className="relative">
              <button
                type="button"
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
                className="rounded-full bg-white p-1 text-secondary-400 hover:text-secondary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:bg-secondary-800 dark:hover:text-secondary-300"
              >
                <span className="sr-only">View notifications</span>
                <Bell className="h-6 w-6" aria-hidden="true" />
                {/* Notification badge */}
                <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-error-500 text-xs text-white flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Notifications dropdown */}
              <AnimatePresence>
                {isNotificationsOpen && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    className="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-secondary-800 dark:ring-secondary-700"
                  >
                    <div className="px-4 py-2 border-b border-secondary-200 dark:border-secondary-700">
                      <h3 className="text-sm font-medium text-secondary-900 dark:text-white">
                        Notifications
                      </h3>
                    </div>
                    <div className="max-h-64 overflow-y-auto">
                      {/* Sample notifications */}
                      {[
                        {
                          id: 1,
                          title: 'Workout reminder',
                          message: 'Your Push Day workout is scheduled for today',
                          time: '5 minutes ago',
                          unread: true,
                        },
                        {
                          id: 2,
                          title: 'Coach feedback',
                          message: 'Your coach left feedback on your last workout',
                          time: '1 hour ago',
                          unread: true,
                        },
                        {
                          id: 3,
                          title: 'Goal achieved',
                          message: 'Congratulations! You completed your weekly goal',
                          time: '2 hours ago',
                          unread: false,
                        },
                      ].map((notification) => (
                        <div
                          key={notification.id}
                          className={`px-4 py-3 hover:bg-secondary-50 dark:hover:bg-secondary-700 ${
                            notification.unread ? 'bg-primary-50 dark:bg-primary-900/20' : ''
                          }`}
                        >
                          <div className="flex items-start">
                            <div className="flex-1">
                              <p className="text-sm font-medium text-secondary-900 dark:text-white">
                                {notification.title}
                              </p>
                              <p className="text-sm text-secondary-500 dark:text-secondary-400">
                                {notification.message}
                              </p>
                              <p className="text-xs text-secondary-400 dark:text-secondary-500 mt-1">
                                {notification.time}
                              </p>
                            </div>
                            {notification.unread && (
                              <div className="ml-2 h-2 w-2 rounded-full bg-primary-500" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="px-4 py-2 border-t border-secondary-200 dark:border-secondary-700">
                      <button className="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400">
                        View all notifications
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Profile dropdown */}
            <div className="relative">
              <button
                type="button"
                onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                className="flex max-w-xs items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:bg-secondary-800"
              >
                <span className="sr-only">Open user menu</span>
                <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center dark:bg-primary-900/50">
                  <User className="h-4 w-4 text-primary-600 dark:text-primary-400" />
                </div>
                <span className="ml-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hidden sm:block">
                  {user?.user_metadata?.first_name || 'User'}
                </span>
                <ChevronDown className="ml-1 h-4 w-4 text-secondary-400" />
              </button>

              {/* Profile dropdown menu */}
              <AnimatePresence>
                {isProfileMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-secondary-800 dark:ring-secondary-700"
                  >
                    <a
                      href="/dashboard/profile"
                      className="flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 dark:text-secondary-300 dark:hover:bg-secondary-700"
                    >
                      <User className="mr-3 h-4 w-4" />
                      Your Profile
                    </a>
                    <a
                      href="/dashboard/settings"
                      className="flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 dark:text-secondary-300 dark:hover:bg-secondary-700"
                    >
                      <Settings className="mr-3 h-4 w-4" />
                      Settings
                    </a>
                    <button
                      onClick={handleSignOut}
                      className="flex w-full items-center px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 dark:text-secondary-300 dark:hover:bg-secondary-700"
                    >
                      <LogOut className="mr-3 h-4 w-4" />
                      Sign out
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
