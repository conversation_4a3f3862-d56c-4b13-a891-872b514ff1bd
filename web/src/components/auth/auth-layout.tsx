'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>bell } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: string;
  showBackButton?: boolean;
}

export function AuthLayout({ children, title, subtitle, showBackButton = false }: AuthLayoutProps) {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-secondary-900 dark:via-secondary-800 dark:to-secondary-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />
      
      <div className="relative flex min-h-screen">
        {/* Left Side - Form */}
        <div className="flex w-full flex-col justify-center px-4 py-12 sm:px-6 lg:w-1/2 lg:px-20 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            {/* Header */}
            <div className="mb-8">
              {showBackButton && (
                <button
                  onClick={() => router.back()}
                  className="mb-6 inline-flex items-center text-sm font-medium text-secondary-600 hover:text-secondary-900 dark:text-secondary-400 dark:hover:text-secondary-100"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </button>
              )}
              
              <Link href="/" className="inline-flex items-center">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary-600">
                  <Dumbbell className="h-6 w-6 text-white" />
                </div>
                <span className="ml-3 text-xl font-bold text-secondary-900 dark:text-white">
                  FitnessApp
                </span>
              </Link>
            </div>

            {/* Title */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-3xl font-bold tracking-tight text-secondary-900 dark:text-white">
                {title}
              </h2>
              <p className="mt-2 text-sm text-secondary-600 dark:text-secondary-400">
                {subtitle}
              </p>
            </motion.div>

            {/* Form */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mt-8"
            >
              {children}
            </motion.div>
          </div>
        </div>

        {/* Right Side - Hero Image/Content */}
        <div className="hidden lg:flex lg:w-1/2 lg:flex-col lg:justify-center lg:bg-gradient-to-br lg:from-primary-600 lg:to-primary-800">
          <div className="relative px-12 py-24">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="text-center"
            >
              <div className="mb-8 inline-flex h-20 w-20 items-center justify-center rounded-full bg-white/10 backdrop-blur-sm">
                <Dumbbell className="h-10 w-10 text-white" />
              </div>
              
              <h3 className="text-3xl font-bold text-white">
                Transform Your Fitness Journey
              </h3>
              
              <p className="mt-4 text-lg text-primary-100">
                Join thousands of users who have achieved their fitness goals with personalized programs and expert coaching.
              </p>

              {/* Features */}
              <div className="mt-12 space-y-4">
                {[
                  'Personalized workout programs',
                  'Expert coaching and feedback',
                  'Comprehensive progress tracking',
                  'Community support and motivation',
                ].map((feature, index) => (
                  <motion.div
                    key={feature}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                    className="flex items-center text-primary-100"
                  >
                    <div className="mr-3 h-2 w-2 rounded-full bg-primary-300" />
                    {feature}
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Decorative Elements */}
            <div className="absolute -top-4 -left-4 h-24 w-24 rounded-full bg-white/5" />
            <div className="absolute -bottom-8 -right-8 h-32 w-32 rounded-full bg-white/5" />
            <div className="absolute top-1/2 -right-12 h-16 w-16 rounded-full bg-white/10" />
          </div>
        </div>
      </div>
    </div>
  );
}
