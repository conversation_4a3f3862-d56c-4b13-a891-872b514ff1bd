'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Paperclip,
  Image,
  Smile,
  MoreH<PERSON>zontal,
  Check,
  CheckCheck,
} from 'lucide-react';
import { formatTime } from '@/lib/utils';

interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderRole: 'coach' | 'client';
  content: string;
  messageType: 'text' | 'image' | 'file' | 'workout' | 'feedback';
  timestamp: string;
  isRead: boolean;
  metadata?: any;
}

interface ChatWindowProps {
  messages: Message[];
  onSendMessage: (content: string, type?: 'text' | 'image' | 'file') => void;
}

export function ChatWindow({ messages, onSendMessage }: ChatWindowProps) {
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      onSendMessage(newMessage.trim());
      setNewMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNewMessage(e.target.value);
    
    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [key: string]: Message[] } = {};
    
    messages.forEach(message => {
      const date = new Date(message.timestamp).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });
    
    return groups;
  };

  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  };

  const renderMessage = (message: Message, isLast: boolean) => {
    const isOwnMessage = message.senderRole === 'client';
    
    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-4`}
      >
        <div className={`flex items-end space-x-2 max-w-xs lg:max-w-md ${
          isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''
        }`}>
          {/* Avatar */}
          {!isOwnMessage && (
            <div className="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-medium text-primary-600 dark:text-primary-400">
                {message.senderName.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
          )}
          
          {/* Message bubble */}
          <div className={`relative px-4 py-2 rounded-2xl ${
            isOwnMessage
              ? 'bg-primary-600 text-white'
              : 'bg-secondary-100 dark:bg-secondary-700 text-secondary-900 dark:text-white'
          }`}>
            {/* Message content */}
            <div className="break-words">
              {message.messageType === 'text' && (
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content}
                </p>
              )}
              
              {message.messageType === 'workout' && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Workout Shared</p>
                  <div className="p-3 rounded-lg bg-black/10 dark:bg-white/10">
                    <p className="text-xs">{message.content}</p>
                  </div>
                </div>
              )}
              
              {message.messageType === 'feedback' && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Feedback</p>
                  <div className="p-3 rounded-lg bg-black/10 dark:bg-white/10">
                    <p className="text-xs">{message.content}</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Message time and status */}
            <div className={`flex items-center justify-end mt-1 space-x-1 ${
              isOwnMessage ? 'text-primary-200' : 'text-secondary-500 dark:text-secondary-400'
            }`}>
              <span className="text-xs">
                {formatTime(message.timestamp)}
              </span>
              {isOwnMessage && (
                <div className="flex items-center">
                  {message.isRead ? (
                    <CheckCheck className="h-3 w-3" />
                  ) : (
                    <Check className="h-3 w-3" />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  const messageGroups = groupMessagesByDate(messages);

  return (
    <div className="flex flex-col h-full">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {Object.entries(messageGroups).map(([date, dateMessages]) => (
          <div key={date}>
            {/* Date header */}
            <div className="flex justify-center mb-4">
              <div className="px-3 py-1 bg-secondary-100 dark:bg-secondary-700 rounded-full">
                <span className="text-xs font-medium text-secondary-600 dark:text-secondary-400">
                  {formatDateHeader(date)}
                </span>
              </div>
            </div>
            
            {/* Messages for this date */}
            {dateMessages.map((message, index) => 
              renderMessage(message, index === dateMessages.length - 1)
            )}
          </div>
        ))}
        
        {/* Typing indicator */}
        <AnimatePresence>
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="flex justify-start"
            >
              <div className="flex items-end space-x-2">
                <div className="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center">
                  <span className="text-xs font-medium text-primary-600 dark:text-primary-400">
                    CS
                  </span>
                </div>
                <div className="bg-secondary-100 dark:bg-secondary-700 rounded-2xl px-4 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className="border-t border-secondary-200 dark:border-secondary-700 p-4">
        <div className="flex items-end space-x-3">
          {/* Attachment buttons */}
          <div className="flex space-x-1">
            <button className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400 transition-colors">
              <Paperclip className="h-4 w-4" />
            </button>
            <button className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400 transition-colors">
              <Image className="h-4 w-4" />
            </button>
          </div>

          {/* Text input */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={newMessage}
              onChange={handleTextareaChange}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-4 py-2 pr-12 border border-secondary-300 rounded-2xl bg-white text-secondary-900 placeholder-secondary-500 focus:border-primary-500 focus:ring-1 focus:ring-primary-500 dark:border-secondary-600 dark:bg-secondary-700 dark:text-white dark:placeholder-secondary-400 resize-none"
              rows={1}
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />
            
            {/* Emoji button */}
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-600 text-secondary-600 dark:text-secondary-400 transition-colors">
              <Smile className="h-4 w-4" />
            </button>
          </div>

          {/* Send button */}
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="p-2 rounded-lg bg-primary-600 text-white hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
