'use client';

import { motion } from 'framer-motion';
import {
  Star,
  Award,
  Calendar,
  MapPin,
  Phone,
  Mail,
  MessageSquare,
  Video,
  Clock,
  Target,
} from 'lucide-react';

interface Coach {
  name: string;
  role: string;
  experience: string;
  specialties: string[];
  bio: string;
  isOnline: boolean;
  rating?: number;
  totalClients?: number;
  location?: string;
  email?: string;
  phone?: string;
}

interface CoachInfoProps {
  coach: Coach;
}

export function CoachInfo({ coach }: CoachInfoProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className="h-full overflow-y-auto p-6 space-y-6"
    >
      {/* Coach Profile */}
      <div className="text-center">
        <div className="relative inline-block">
          <div className="w-20 h-20 rounded-full bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center mx-auto mb-4">
            <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
              {coach.name.split(' ').map(n => n[0]).join('')}
            </span>
          </div>
          {coach.isOnline && (
            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-success-500 rounded-full border-4 border-white dark:border-secondary-800" />
          )}
        </div>
        
        <h3 className="text-lg font-bold text-secondary-900 dark:text-white">
          {coach.name}
        </h3>
        <p className="text-sm text-secondary-600 dark:text-secondary-400">
          {coach.role}
        </p>
        
        {coach.rating && (
          <div className="flex items-center justify-center mt-2 space-x-1">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${
                    i < Math.floor(coach.rating!)
                      ? 'text-warning-400 fill-current'
                      : 'text-secondary-300 dark:text-secondary-600'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-secondary-600 dark:text-secondary-400">
              {coach.rating.toFixed(1)}
            </span>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3">
        <button className="flex items-center justify-center p-3 rounded-lg bg-primary-600 text-white hover:bg-primary-700 transition-colors">
          <MessageSquare className="h-4 w-4 mr-2" />
          <span className="text-sm font-medium">Message</span>
        </button>
        <button className="flex items-center justify-center p-3 rounded-lg border border-secondary-300 text-secondary-700 hover:bg-secondary-50 dark:border-secondary-600 dark:text-secondary-300 dark:hover:bg-secondary-700 transition-colors">
          <Video className="h-4 w-4 mr-2" />
          <span className="text-sm font-medium">Video Call</span>
        </button>
      </div>

      {/* Bio */}
      <div>
        <h4 className="text-sm font-semibold text-secondary-900 dark:text-white mb-2">
          About
        </h4>
        <p className="text-sm text-secondary-600 dark:text-secondary-400 leading-relaxed">
          {coach.bio}
        </p>
      </div>

      {/* Experience & Stats */}
      <div className="space-y-4">
        <h4 className="text-sm font-semibold text-secondary-900 dark:text-white">
          Experience & Stats
        </h4>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Award className="h-4 w-4 text-secondary-400 mr-2" />
              <span className="text-sm text-secondary-600 dark:text-secondary-400">
                Experience
              </span>
            </div>
            <span className="text-sm font-medium text-secondary-900 dark:text-white">
              {coach.experience}
            </span>
          </div>
          
          {coach.totalClients && (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Target className="h-4 w-4 text-secondary-400 mr-2" />
                <span className="text-sm text-secondary-600 dark:text-secondary-400">
                  Total Clients
                </span>
              </div>
              <span className="text-sm font-medium text-secondary-900 dark:text-white">
                {coach.totalClients}+
              </span>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="h-4 w-4 text-secondary-400 mr-2" />
              <span className="text-sm text-secondary-600 dark:text-secondary-400">
                Response Time
              </span>
            </div>
            <span className="text-sm font-medium text-secondary-900 dark:text-white">
              ~2 hours
            </span>
          </div>
        </div>
      </div>

      {/* Specialties */}
      <div>
        <h4 className="text-sm font-semibold text-secondary-900 dark:text-white mb-3">
          Specialties
        </h4>
        <div className="flex flex-wrap gap-2">
          {coach.specialties.map((specialty, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400"
            >
              {specialty}
            </span>
          ))}
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-4">
        <h4 className="text-sm font-semibold text-secondary-900 dark:text-white">
          Contact Information
        </h4>
        
        <div className="space-y-3">
          {coach.email && (
            <div className="flex items-center">
              <Mail className="h-4 w-4 text-secondary-400 mr-3" />
              <span className="text-sm text-secondary-600 dark:text-secondary-400">
                {coach.email}
              </span>
            </div>
          )}
          
          {coach.phone && (
            <div className="flex items-center">
              <Phone className="h-4 w-4 text-secondary-400 mr-3" />
              <span className="text-sm text-secondary-600 dark:text-secondary-400">
                {coach.phone}
              </span>
            </div>
          )}
          
          {coach.location && (
            <div className="flex items-center">
              <MapPin className="h-4 w-4 text-secondary-400 mr-3" />
              <span className="text-sm text-secondary-600 dark:text-secondary-400">
                {coach.location}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Availability */}
      <div>
        <h4 className="text-sm font-semibold text-secondary-900 dark:text-white mb-3">
          Availability
        </h4>
        <div className="space-y-2">
          {[
            { day: 'Monday', hours: '9:00 AM - 6:00 PM' },
            { day: 'Tuesday', hours: '9:00 AM - 6:00 PM' },
            { day: 'Wednesday', hours: '9:00 AM - 6:00 PM' },
            { day: 'Thursday', hours: '9:00 AM - 6:00 PM' },
            { day: 'Friday', hours: '9:00 AM - 5:00 PM' },
            { day: 'Saturday', hours: '10:00 AM - 2:00 PM' },
            { day: 'Sunday', hours: 'Unavailable' },
          ].map((schedule, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-sm text-secondary-600 dark:text-secondary-400">
                {schedule.day}
              </span>
              <span className={`text-sm ${
                schedule.hours === 'Unavailable'
                  ? 'text-secondary-400 dark:text-secondary-500'
                  : 'text-secondary-900 dark:text-white'
              }`}>
                {schedule.hours}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Schedule Session */}
      <div className="pt-4 border-t border-secondary-200 dark:border-secondary-700">
        <button className="w-full flex items-center justify-center p-3 rounded-lg bg-secondary-100 text-secondary-700 hover:bg-secondary-200 dark:bg-secondary-700 dark:text-secondary-300 dark:hover:bg-secondary-600 transition-colors">
          <Calendar className="h-4 w-4 mr-2" />
          <span className="text-sm font-medium">Schedule Session</span>
        </button>
      </div>
    </motion.div>
  );
}
