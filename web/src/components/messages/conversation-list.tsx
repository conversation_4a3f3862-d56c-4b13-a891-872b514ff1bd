'use client';

import { motion } from 'framer-motion';
import { formatRelativeTime } from '@/lib/utils';

interface Conversation {
  id: string;
  participantName: string;
  participantRole: 'coach' | 'client';
  participantAvatar?: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  isOnline: boolean;
  lastSeen?: string;
}

interface ConversationListProps {
  conversations: Conversation[];
  selectedConversation: Conversation | null;
  onConversationSelect: (conversation: Conversation) => void;
}

export function ConversationList({
  conversations,
  selectedConversation,
  onConversationSelect,
}: ConversationListProps) {
  if (conversations.length === 0) {
    return (
      <div className="p-6 text-center">
        <p className="text-secondary-500 dark:text-secondary-400">
          No conversations found
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-secondary-200 dark:divide-secondary-700">
      {conversations.map((conversation, index) => (
        <motion.button
          key={conversation.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
          onClick={() => onConversationSelect(conversation)}
          className={`w-full p-4 text-left hover:bg-secondary-50 dark:hover:bg-secondary-800/50 transition-colors ${
            selectedConversation?.id === conversation.id
              ? 'bg-primary-50 dark:bg-primary-900/20 border-r-2 border-primary-500'
              : ''
          }`}
        >
          <div className="flex items-start space-x-3">
            {/* Avatar */}
            <div className="relative flex-shrink-0">
              <div className="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center">
                {conversation.participantAvatar ? (
                  <img
                    src={conversation.participantAvatar}
                    alt={conversation.participantName}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                    {conversation.participantName.split(' ').map(n => n[0]).join('')}
                  </span>
                )}
              </div>
              
              {/* Online indicator */}
              {conversation.isOnline && (
                <div className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-success-500 rounded-full border-2 border-white dark:border-secondary-900" />
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h4 className="text-sm font-semibold text-secondary-900 dark:text-white truncate">
                  {conversation.participantName}
                </h4>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-secondary-500 dark:text-secondary-400">
                    {formatRelativeTime(conversation.lastMessageTime)}
                  </span>
                  {conversation.unreadCount > 0 && (
                    <div className="w-5 h-5 bg-primary-600 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-white">
                        {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              <p className={`text-sm truncate ${
                conversation.unreadCount > 0
                  ? 'text-secondary-900 dark:text-white font-medium'
                  : 'text-secondary-500 dark:text-secondary-400'
              }`}>
                {conversation.lastMessage}
              </p>

              {/* Role badge */}
              <div className="mt-2">
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  conversation.participantRole === 'coach'
                    ? 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400'
                    : 'bg-secondary-100 text-secondary-800 dark:bg-secondary-700 dark:text-secondary-300'
                }`}>
                  {conversation.participantRole === 'coach' ? 'Coach' : 'Client'}
                </span>
              </div>
            </div>
          </div>
        </motion.button>
      ))}
    </div>
  );
}
