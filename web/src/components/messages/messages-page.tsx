'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  MessageSquare,
  Search,
  Plus,
  Filter,
  MoreHorizontal,
  Phone,
  Video,
  Info,
} from 'lucide-react';
import { ConversationList } from '@/components/messages/conversation-list';
import { ChatWindow } from '@/components/messages/chat-window';
import { CoachInfo } from '@/components/messages/coach-info';
import { NewMessageModal } from '@/components/messages/new-message-modal';

interface Conversation {
  id: string;
  participantName: string;
  participantRole: 'coach' | 'client';
  participantAvatar?: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  isOnline: boolean;
  lastSeen?: string;
}

interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderRole: 'coach' | 'client';
  content: string;
  messageType: 'text' | 'image' | 'file' | 'workout' | 'feedback';
  timestamp: string;
  isRead: boolean;
  metadata?: any;
}

// Sample data - in real app, this would come from API
const sampleConversations: Conversation[] = [
  {
    id: '1',
    participantName: 'Coach Sarah',
    participantRole: 'coach',
    participantAvatar: '/avatars/coach-sarah.jpg',
    lastMessage: 'Great job on your workout today! Your form on squats has really improved.',
    lastMessageTime: '2024-02-15T14:30:00Z',
    unreadCount: 2,
    isOnline: true,
  },
  {
    id: '2',
    participantName: 'Coach Mike',
    participantRole: 'coach',
    participantAvatar: '/avatars/coach-mike.jpg',
    lastMessage: 'Let\'s adjust your program for next week based on your progress.',
    lastMessageTime: '2024-02-14T18:45:00Z',
    unreadCount: 0,
    isOnline: false,
    lastSeen: '2024-02-15T09:00:00Z',
  },
];

const sampleMessages: Message[] = [
  {
    id: '1',
    conversationId: '1',
    senderId: 'coach-sarah',
    senderName: 'Coach Sarah',
    senderRole: 'coach',
    content: 'Hi John! I reviewed your workout from yesterday and I\'m really impressed with your progress.',
    messageType: 'text',
    timestamp: '2024-02-15T13:00:00Z',
    isRead: true,
  },
  {
    id: '2',
    conversationId: '1',
    senderId: 'user-john',
    senderName: 'John',
    senderRole: 'client',
    content: 'Thank you! I felt really strong during the deadlifts. The form cues you gave me last week really helped.',
    messageType: 'text',
    timestamp: '2024-02-15T13:15:00Z',
    isRead: true,
  },
  {
    id: '3',
    conversationId: '1',
    senderId: 'coach-sarah',
    senderName: 'Coach Sarah',
    senderRole: 'coach',
    content: 'That\'s exactly what I wanted to hear! Your hip hinge has improved significantly. Ready for today\'s workout?',
    messageType: 'text',
    timestamp: '2024-02-15T14:00:00Z',
    isRead: true,
  },
  {
    id: '4',
    conversationId: '1',
    senderId: 'coach-sarah',
    senderName: 'Coach Sarah',
    senderRole: 'coach',
    content: 'Great job on your workout today! Your form on squats has really improved.',
    messageType: 'text',
    timestamp: '2024-02-15T14:30:00Z',
    isRead: false,
  },
];

export function MessagesPage() {
  const [conversations, setConversations] = useState<Conversation[]>(sampleConversations);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(conversations[0]);
  const [messages, setMessages] = useState<Message[]>(sampleMessages);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewMessageModal, setShowNewMessageModal] = useState(false);
  const [showCoachInfo, setShowCoachInfo] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const filteredConversations = conversations.filter(conversation =>
    conversation.participantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const conversationMessages = messages.filter(
    message => message.conversationId === selectedConversation?.id
  );

  const handleSendMessage = (content: string, type: 'text' | 'image' | 'file' = 'text') => {
    if (!selectedConversation) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      conversationId: selectedConversation.id,
      senderId: 'user-john',
      senderName: 'John',
      senderRole: 'client',
      content,
      messageType: type,
      timestamp: new Date().toISOString(),
      isRead: false,
    };

    setMessages(prev => [...prev, newMessage]);

    // Update conversation last message
    setConversations(prev =>
      prev.map(conv =>
        conv.id === selectedConversation.id
          ? { ...conv, lastMessage: content, lastMessageTime: newMessage.timestamp }
          : conv
      )
    );
  };

  const handleConversationSelect = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    
    // Mark messages as read
    setMessages(prev =>
      prev.map(message =>
        message.conversationId === conversation.id && message.senderRole === 'coach'
          ? { ...message, isRead: true }
          : message
      )
    );

    // Update unread count
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversation.id
          ? { ...conv, unreadCount: 0 }
          : conv
      )
    );
  };

  return (
    <div className="h-[calc(100vh-8rem)] flex bg-white dark:bg-secondary-900 rounded-2xl shadow-sm border border-secondary-200 dark:border-secondary-700 overflow-hidden">
      {/* Conversations Sidebar */}
      <div className={`${
        isMobile && selectedConversation ? 'hidden' : 'flex'
      } flex-col w-full md:w-80 lg:w-96 border-r border-secondary-200 dark:border-secondary-700`}>
        {/* Header */}
        <div className="p-6 border-b border-secondary-200 dark:border-secondary-700">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-secondary-900 dark:text-white">
              Messages
            </h1>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowNewMessageModal(true)}
                className="p-2 rounded-lg bg-primary-600 text-white hover:bg-primary-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
              </button>
              <button className="p-2 rounded-lg bg-secondary-100 text-secondary-600 hover:bg-secondary-200 dark:bg-secondary-800 dark:text-secondary-400 dark:hover:bg-secondary-700 transition-colors">
                <Filter className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-4 w-4 text-secondary-400" />
            </div>
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-secondary-300 rounded-lg bg-white text-secondary-900 placeholder-secondary-500 focus:border-primary-500 focus:ring-1 focus:ring-primary-500 dark:border-secondary-600 dark:bg-secondary-700 dark:text-white dark:placeholder-secondary-400"
            />
          </div>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          <ConversationList
            conversations={filteredConversations}
            selectedConversation={selectedConversation}
            onConversationSelect={handleConversationSelect}
          />
        </div>
      </div>

      {/* Chat Area */}
      <div className={`${
        isMobile && !selectedConversation ? 'hidden' : 'flex'
      } flex-1 flex flex-col`}>
        {selectedConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-secondary-200 dark:border-secondary-700 bg-white dark:bg-secondary-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {isMobile && (
                    <button
                      onClick={() => setSelectedConversation(null)}
                      className="p-1 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700"
                    >
                      ←
                    </button>
                  )}
                  <div className="relative">
                    <div className="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900/50 flex items-center justify-center">
                      <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                        {selectedConversation.participantName.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    {selectedConversation.isOnline && (
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-success-500 rounded-full border-2 border-white dark:border-secondary-800" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-secondary-900 dark:text-white">
                      {selectedConversation.participantName}
                    </h3>
                    <p className="text-sm text-secondary-500 dark:text-secondary-400">
                      {selectedConversation.isOnline ? (
                        'Online'
                      ) : selectedConversation.lastSeen ? (
                        `Last seen ${new Date(selectedConversation.lastSeen).toLocaleDateString()}`
                      ) : (
                        'Offline'
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400">
                    <Phone className="h-4 w-4" />
                  </button>
                  <button className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400">
                    <Video className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setShowCoachInfo(!showCoachInfo)}
                    className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400"
                  >
                    <Info className="h-4 w-4" />
                  </button>
                  <button className="p-2 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400">
                    <MoreHorizontal className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Chat Window */}
            <div className="flex-1 flex">
              <div className="flex-1">
                <ChatWindow
                  messages={conversationMessages}
                  onSendMessage={handleSendMessage}
                />
              </div>

              {/* Coach Info Sidebar */}
              {showCoachInfo && !isMobile && (
                <motion.div
                  initial={{ width: 0, opacity: 0 }}
                  animate={{ width: 320, opacity: 1 }}
                  exit={{ width: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-l border-secondary-200 dark:border-secondary-700 bg-secondary-50 dark:bg-secondary-800/50"
                >
                  <CoachInfo
                    coach={{
                      name: selectedConversation.participantName,
                      role: 'Personal Trainer',
                      experience: '5+ years',
                      specialties: ['Strength Training', 'Weight Loss', 'Nutrition'],
                      bio: 'Certified personal trainer with expertise in strength training and nutrition coaching.',
                      isOnline: selectedConversation.isOnline,
                    }}
                  />
                </motion.div>
              )}
            </div>
          </>
        ) : (
          /* Empty State */
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-secondary-100 dark:bg-secondary-800 flex items-center justify-center">
                <MessageSquare className="h-8 w-8 text-secondary-400" />
              </div>
              <h3 className="text-lg font-semibold text-secondary-900 dark:text-white mb-2">
                Select a conversation
              </h3>
              <p className="text-secondary-500 dark:text-secondary-400 max-w-sm">
                Choose a conversation from the sidebar to start messaging with your coach.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* New Message Modal */}
      {showNewMessageModal && (
        <NewMessageModal
          isOpen={showNewMessageModal}
          onClose={() => setShowNewMessageModal(false)}
          onCreateConversation={(conversation) => {
            setConversations(prev => [conversation, ...prev]);
            setSelectedConversation(conversation);
            setShowNewMessageModal(false);
          }}
        />
      )}
    </div>
  );
}
