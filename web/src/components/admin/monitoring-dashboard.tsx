'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Globe,
  MemoryStick,
  Server,
  TrendingUp,
  Users,
  Zap,
} from 'lucide-react';

interface HealthStatus {
  status: 'healthy' | 'warning' | 'unhealthy';
  timestamp: string;
  checks: Record<string, any>;
  responseTime: number;
}

interface MetricData {
  metric_name: string;
  metric_value: number;
  created_at: string;
  tags?: Record<string, any>;
}

export function MonitoringDashboard() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [metrics, setMetrics] = useState<MetricData[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    fetchHealthStatus();
    fetchMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchHealthStatus();
      fetchMetrics();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchHealthStatus = async () => {
    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      setHealthStatus(data);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch health status:', error);
    }
  };

  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/metrics?hours=1&limit=100');
      const data = await response.json();
      setMetrics(data.metrics || []);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch metrics:', error);
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-success-600 bg-success-100';
      case 'warning': return 'text-warning-600 bg-warning-100';
      case 'unhealthy': return 'text-error-600 bg-error-100';
      default: return 'text-secondary-600 bg-secondary-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'unhealthy': return AlertTriangle;
      default: return Activity;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatusCard
          title="Overall Status"
          status={healthStatus?.status || 'unknown'}
          icon={Activity}
          value={healthStatus?.status || 'Unknown'}
        />
        <StatusCard
          title="Response Time"
          status="healthy"
          icon={Clock}
          value={`${healthStatus?.responseTime || 0}ms`}
        />
        <StatusCard
          title="Database"
          status={healthStatus?.checks?.database?.status || 'unknown'}
          icon={Database}
          value={healthStatus?.checks?.database?.status || 'Unknown'}
        />
        <StatusCard
          title="External APIs"
          status={healthStatus?.checks?.supabase?.status || 'unknown'}
          icon={Globe}
          value={healthStatus?.checks?.supabase?.status || 'Unknown'}
        />
      </div>

      {/* Detailed Health Checks */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-secondary-800 rounded-2xl p-6 shadow-sm"
      >
        <h3 className="text-lg font-semibold text-secondary-900 dark:text-white mb-4">
          Health Checks
        </h3>
        
        <div className="space-y-4">
          {healthStatus?.checks && Object.entries(healthStatus.checks).map(([name, check]) => (
            <div key={name} className="flex items-center justify-between p-4 rounded-lg bg-secondary-50 dark:bg-secondary-700">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${getStatusColor(check.status)}`}>
                  {React.createElement(getStatusIcon(check.status), { className: 'h-4 w-4' })}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-secondary-900 dark:text-white capitalize">
                    {name.replace('_', ' ')}
                  </p>
                  {check.responseTime && (
                    <p className="text-xs text-secondary-500 dark:text-secondary-400">
                      Response: {check.responseTime}ms
                    </p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(check.status)}`}>
                  {check.status}
                </span>
                {check.error && (
                  <p className="text-xs text-error-600 mt-1">{check.error}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* System Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white dark:bg-secondary-800 rounded-2xl p-6 shadow-sm"
      >
        <h3 className="text-lg font-semibold text-secondary-900 dark:text-white mb-4">
          System Metrics
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {healthStatus?.checks?.memory && (
            <MetricCard
              title="Memory Usage"
              icon={MemoryStick}
              value={`${healthStatus.checks.memory.usage.heapUsed}MB`}
              subtitle={`of ${healthStatus.checks.memory.usage.heapTotal}MB`}
              status={healthStatus.checks.memory.status}
            />
          )}
          
          {healthStatus?.checks?.environment && (
            <MetricCard
              title="Uptime"
              icon={Server}
              value={formatUptime(healthStatus.checks.environment.uptime)}
              subtitle={`Node ${healthStatus.checks.environment.nodeVersion}`}
              status="healthy"
            />
          )}
          
          <MetricCard
            title="Active Users"
            icon={Users}
            value="1,234"
            subtitle="Last 24 hours"
            status="healthy"
          />
        </div>
      </motion.div>

      {/* Recent Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white dark:bg-secondary-800 rounded-2xl p-6 shadow-sm"
      >
        <h3 className="text-lg font-semibold text-secondary-900 dark:text-white mb-4">
          Recent Metrics
        </h3>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-secondary-200 dark:divide-secondary-700">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                  Metric
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 dark:text-secondary-400 uppercase tracking-wider">
                  Time
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-secondary-200 dark:divide-secondary-700">
              {metrics.slice(0, 10).map((metric, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-secondary-900 dark:text-white">
                    {metric.metric_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500 dark:text-secondary-400">
                    {formatMetricValue(metric.metric_name, metric.metric_value)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-secondary-500 dark:text-secondary-400">
                    {new Date(metric.created_at).toLocaleTimeString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Last Update */}
      <div className="text-center text-sm text-secondary-500 dark:text-secondary-400">
        Last updated: {lastUpdate.toLocaleTimeString()}
      </div>
    </div>
  );
}

function StatusCard({ title, status, icon: Icon, value }: {
  title: string;
  status: string;
  icon: any;
  value: string;
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-success-600 bg-success-100';
      case 'warning': return 'text-warning-600 bg-warning-100';
      case 'unhealthy': return 'text-error-600 bg-error-100';
      default: return 'text-secondary-600 bg-secondary-100';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="bg-white dark:bg-secondary-800 rounded-2xl p-6 shadow-sm"
    >
      <div className="flex items-center">
        <div className={`p-3 rounded-lg ${getStatusColor(status)}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-secondary-600 dark:text-secondary-400">
            {title}
          </p>
          <p className="text-2xl font-bold text-secondary-900 dark:text-white">
            {value}
          </p>
        </div>
      </div>
    </motion.div>
  );
}

function MetricCard({ title, icon: Icon, value, subtitle, status }: {
  title: string;
  icon: any;
  value: string;
  subtitle: string;
  status: string;
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-success-600';
      case 'warning': return 'text-warning-600';
      case 'unhealthy': return 'text-error-600';
      default: return 'text-secondary-600';
    }
  };

  return (
    <div className="p-4 rounded-lg bg-secondary-50 dark:bg-secondary-700">
      <div className="flex items-center">
        <Icon className={`h-5 w-5 ${getStatusColor(status)}`} />
        <h4 className="ml-2 text-sm font-medium text-secondary-900 dark:text-white">
          {title}
        </h4>
      </div>
      <p className="mt-2 text-lg font-bold text-secondary-900 dark:text-white">
        {value}
      </p>
      <p className="text-xs text-secondary-500 dark:text-secondary-400">
        {subtitle}
      </p>
    </div>
  );
}

function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) return `${days}d ${hours}h`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
}

function formatMetricValue(metricName: string, value: number): string {
  if (metricName.includes('time') || metricName.includes('duration')) {
    return `${value}ms`;
  }
  if (metricName.includes('bytes') || metricName.includes('size')) {
    return `${(value / 1024 / 1024).toFixed(2)}MB`;
  }
  if (metricName.includes('rate') || metricName.includes('ratio')) {
    return `${(value * 100).toFixed(2)}%`;
  }
  return value.toString();
}
