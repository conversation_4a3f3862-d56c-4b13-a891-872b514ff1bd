import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  FitnessCenter as FitnessCenterIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';
import { adminService } from '@/services/adminService';

interface AdminStats {
  total_users: number;
  active_users: number;
  premium_users: number;
  total_workouts: number;
  total_revenue: number;
  churn_rate: number;
  growth_rate: number;
  support_tickets: number;
}

interface User {
  id: string;
  email: string;
  full_name: string;
  subscription_status: string;
  created_at: string;
  last_active: string;
  total_workouts: number;
  status: 'active' | 'suspended' | 'banned';
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  title: string;
  message: string;
  created_at: string;
  resolved: boolean;
}

export function AdminDashboard() {
  const [currentTab, setCurrentTab] = useState(0);
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userDialogOpen, setUserDialogOpen] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [statsData, usersData, alertsData] = await Promise.all([
        adminService.getSystemStats(),
        adminService.getUsers({ limit: 100 }),
        adminService.getSystemAlerts(),
      ]);
      
      setStats(statsData);
      setUsers(usersData);
      setAlerts(alertsData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (userId: string, action: 'suspend' | 'activate' | 'ban') => {
    try {
      await adminService.updateUserStatus(userId, action);
      await loadDashboardData(); // Refresh data
    } catch (error) {
      console.error(`Failed to ${action} user:`, error);
    }
  };

  const handleResolveAlert = async (alertId: string) => {
    try {
      await adminService.resolveAlert(alertId);
      setAlerts(alerts.map(alert => 
        alert.id === alertId ? { ...alert, resolved: true } : alert
      ));
    } catch (error) {
      console.error('Failed to resolve alert:', error);
    }
  };

  const renderOverviewTab = () => (
    <Box>
      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Users
                  </Typography>
                  <Typography variant="h4">
                    {stats?.total_users?.toLocaleString() || 0}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <TrendingUpIcon color="success" fontSize="small" />
                    <Typography variant="body2" color="success.main" ml={0.5}>
                      +{stats?.growth_rate || 0}%
                    </Typography>
                  </Box>
                </Box>
                <PeopleIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active Users
                  </Typography>
                  <Typography variant="h4">
                    {stats?.active_users?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {stats?.total_users ? Math.round((stats.active_users / stats.total_users) * 100) : 0}% of total
                  </Typography>
                </Box>
                <CheckCircleIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Premium Users
                  </Typography>
                  <Typography variant="h4">
                    {stats?.premium_users?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {stats?.total_users ? Math.round((stats.premium_users / stats.total_users) * 100) : 0}% conversion
                  </Typography>
                </Box>
                <TrendingUpIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Monthly Revenue
                  </Typography>
                  <Typography variant="h4">
                    ${stats?.total_revenue?.toLocaleString() || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {stats?.churn_rate || 0}% churn rate
                  </Typography>
                </Box>
                <AnalyticsIcon color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Growth Trend
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={[
                  { month: 'Jan', users: 1200, premium: 240 },
                  { month: 'Feb', users: 1350, premium: 280 },
                  { month: 'Mar', users: 1500, premium: 320 },
                  { month: 'Apr', users: 1680, premium: 370 },
                  { month: 'May', users: 1850, premium: 420 },
                  { month: 'Jun', users: 2100, premium: 480 },
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="users" stroke="#8884d8" strokeWidth={2} />
                  <Line type="monotone" dataKey="premium" stroke="#82ca9d" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                User Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={[
                      { name: 'Free', value: (stats?.total_users || 0) - (stats?.premium_users || 0), fill: '#8884d8' },
                      { name: 'Premium', value: stats?.premium_users || 0, fill: '#82ca9d' },
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  />
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Alerts */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            System Alerts
          </Typography>
          {alerts.filter(alert => !alert.resolved).length === 0 ? (
            <Alert severity="success">No active alerts</Alert>
          ) : (
            alerts.filter(alert => !alert.resolved).map((alert) => (
              <Alert
                key={alert.id}
                severity={alert.type}
                action={
                  <Button
                    color="inherit"
                    size="small"
                    onClick={() => handleResolveAlert(alert.id)}
                  >
                    Resolve
                  </Button>
                }
                sx={{ mb: 1 }}
              >
                <strong>{alert.title}</strong>: {alert.message}
              </Alert>
            ))
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderUsersTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">User Management</Typography>
        <Button variant="contained" color="primary">
          Export Users
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Subscription</TableCell>
              <TableCell>Workouts</TableCell>
              <TableCell>Last Active</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {user.full_name || 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <Chip
                    label={user.subscription_status}
                    color={user.subscription_status === 'active' ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>{user.total_workouts}</TableCell>
                <TableCell>
                  {new Date(user.last_active).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Chip
                    label={user.status}
                    color={
                      user.status === 'active' ? 'success' :
                      user.status === 'suspended' ? 'warning' : 'error'
                    }
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setSelectedUser(user);
                      setUserDialogOpen(true);
                    }}
                  >
                    <EditIcon />
                  </IconButton>
                  {user.status === 'active' ? (
                    <IconButton
                      size="small"
                      color="warning"
                      onClick={() => handleUserAction(user.id, 'suspend')}
                    >
                      <BlockIcon />
                    </IconButton>
                  ) : (
                    <IconButton
                      size="small"
                      color="success"
                      onClick={() => handleUserAction(user.id, 'activate')}
                    >
                      <CheckCircleIcon />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderAnalyticsTab = () => (
    <Box>
      <Typography variant="h5" gutterBottom>
        Analytics & Reports
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Workout Completion Rates
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={[
                  { day: 'Mon', completed: 85, started: 100 },
                  { day: 'Tue', completed: 78, started: 95 },
                  { day: 'Wed', completed: 92, started: 110 },
                  { day: 'Thu', completed: 88, started: 105 },
                  { day: 'Fri', completed: 95, started: 115 },
                  { day: 'Sat', completed: 102, started: 120 },
                  { day: 'Sun', completed: 75, started: 90 },
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="started" fill="#8884d8" name="Started" />
                  <Bar dataKey="completed" fill="#82ca9d" name="Completed" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Revenue Trends
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={[
                  { month: 'Jan', revenue: 12000, subscriptions: 240 },
                  { month: 'Feb', revenue: 14000, subscriptions: 280 },
                  { month: 'Mar', revenue: 16000, subscriptions: 320 },
                  { month: 'Apr', revenue: 18500, subscriptions: 370 },
                  { month: 'May', revenue: 21000, subscriptions: 420 },
                  { month: 'Jun', revenue: 24000, subscriptions: 480 },
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="revenue" stroke="#8884d8" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderSystemTab = () => (
    <Box>
      <Typography variant="h5" gutterBottom>
        System Administration
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Health
              </Typography>
              <Box mb={2}>
                <Typography variant="body2" gutterBottom>
                  Database Performance
                </Typography>
                <LinearProgress variant="determinate" value={85} color="success" />
                <Typography variant="caption">85% - Good</Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" gutterBottom>
                  API Response Time
                </Typography>
                <LinearProgress variant="determinate" value={92} color="success" />
                <Typography variant="caption">92ms avg - Excellent</Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" gutterBottom>
                  Storage Usage
                </Typography>
                <LinearProgress variant="determinate" value={65} color="warning" />
                <Typography variant="caption">65% - Monitor</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Button variant="outlined" startIcon={<SecurityIcon />}>
                  Run Security Scan
                </Button>
                <Button variant="outlined" startIcon={<AnalyticsIcon />}>
                  Generate Report
                </Button>
                <Button variant="outlined" startIcon={<SettingsIcon />}>
                  System Maintenance
                </Button>
                <Button variant="outlined" color="warning">
                  Clear Cache
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Admin Dashboard
      </Typography>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        <Tab icon={<DashboardIcon />} label="Overview" />
        <Tab icon={<PeopleIcon />} label="Users" />
        <Tab icon={<AnalyticsIcon />} label="Analytics" />
        <Tab icon={<SettingsIcon />} label="System" />
      </Tabs>

      {currentTab === 0 && renderOverviewTab()}
      {currentTab === 1 && renderUsersTab()}
      {currentTab === 2 && renderAnalyticsTab()}
      {currentTab === 3 && renderSystemTab()}

      {/* User Edit Dialog */}
      <Dialog open={userDialogOpen} onClose={() => setUserDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit User</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box display="flex" flexDirection="column" gap={2} mt={1}>
              <TextField
                label="Full Name"
                value={selectedUser.full_name || ''}
                fullWidth
              />
              <TextField
                label="Email"
                value={selectedUser.email}
                fullWidth
                disabled
              />
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select value={selectedUser.status}>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="suspended">Suspended</MenuItem>
                  <MenuItem value="banned">Banned</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserDialogOpen(false)}>Cancel</Button>
          <Button variant="contained">Save Changes</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
