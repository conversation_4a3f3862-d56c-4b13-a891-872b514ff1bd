'use client';

import { generateStructuredData } from '@/lib/seo';

interface StructuredDataProps {
  type: string;
  data: any;
}

export function StructuredData({ type, data }: StructuredDataProps) {
  const structuredData = generateStructuredData(type, data);

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

// Specific structured data components
export function WebApplicationStructuredData() {
  return (
    <StructuredData
      type="WebApplication"
      data={{
        name: 'FitnessApp',
        description: 'Transform your fitness journey with personalized workout programs, expert coaching, and comprehensive progress tracking.',
        url: process.env.NEXT_PUBLIC_APP_URL,
        applicationCategory: 'HealthApplication',
        operatingSystem: 'Web, iOS, Android',
        offers: {
          '@type': 'Offer',
          category: 'Fitness & Health',
          price: '0',
          priceCurrency: 'USD',
          availability: 'https://schema.org/InStock',
        },
        author: {
          '@type': 'Organization',
          name: 'FitnessApp Team',
        },
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: '4.8',
          ratingCount: '1250',
          bestRating: '5',
          worstRating: '1',
        },
        screenshot: [
          `${process.env.NEXT_PUBLIC_APP_URL}/screenshots/desktop-1.png`,
          `${process.env.NEXT_PUBLIC_APP_URL}/screenshots/mobile-1.png`,
        ],
        featureList: [
          'Personalized workout programs',
          'AI-powered exercise recommendations',
          'Progress tracking and analytics',
          'Coach communication platform',
          'Offline workout capability',
          'Cross-platform synchronization',
        ],
      }}
    />
  );
}

export function OrganizationStructuredData() {
  return (
    <StructuredData
      type="Organization"
      data={{
        name: 'FitnessApp',
        url: process.env.NEXT_PUBLIC_APP_URL,
        logo: `${process.env.NEXT_PUBLIC_APP_URL}/logo.png`,
        description: 'Leading fitness technology company providing AI-powered workout programs and coaching.',
        foundingDate: '2024',
        sameAs: [
          'https://twitter.com/fitnessapp',
          'https://facebook.com/fitnessapp',
          'https://instagram.com/fitnessapp',
          'https://linkedin.com/company/fitnessapp',
        ],
        contactPoint: [
          {
            '@type': 'ContactPoint',
            contactType: 'Customer Service',
            email: '<EMAIL>',
            availableLanguage: ['English'],
            areaServed: 'Worldwide',
          },
          {
            '@type': 'ContactPoint',
            contactType: 'Technical Support',
            email: '<EMAIL>',
            availableLanguage: ['English'],
          },
        ],
        address: {
          '@type': 'PostalAddress',
          addressCountry: 'US',
          addressRegion: 'CA',
          addressLocality: 'San Francisco',
        },
      }}
    />
  );
}

interface WorkoutProgramStructuredDataProps {
  program: {
    id: string;
    name: string;
    description: string;
    duration: number;
    difficulty: string;
    type: string;
    exercises: string[];
    equipment: string[];
  };
}

export function WorkoutProgramStructuredData({ program }: WorkoutProgramStructuredDataProps) {
  return (
    <StructuredData
      type="Course"
      data={{
        name: program.name,
        description: program.description,
        provider: {
          '@type': 'Organization',
          name: 'FitnessApp',
          url: process.env.NEXT_PUBLIC_APP_URL,
        },
        courseCode: program.id,
        educationalLevel: program.difficulty,
        timeRequired: `P${program.duration}W`,
        learningResourceType: 'Fitness Program',
        teaches: program.exercises,
        coursePrerequisites: program.difficulty === 'Beginner' ? 'None' : 'Basic fitness knowledge',
        hasCourseInstance: {
          '@type': 'CourseInstance',
          courseMode: 'online',
          courseWorkload: `PT${program.duration * 3}H`,
        },
        about: {
          '@type': 'Thing',
          name: program.type,
          description: `${program.type} training program`,
        },
      }}
    />
  );
}

interface ExerciseStructuredDataProps {
  exercise: {
    name: string;
    description: string;
    type: string;
    bodyParts: string[];
    equipment: string[];
    difficulty: string;
    instructions: string[];
  };
}

export function ExerciseStructuredData({ exercise }: ExerciseStructuredDataProps) {
  return (
    <StructuredData
      type="ExerciseAction"
      data={{
        name: exercise.name,
        description: exercise.description,
        exerciseType: exercise.type,
        bodyLocation: exercise.bodyParts,
        equipment: exercise.equipment,
        intensity: exercise.difficulty,
        instructions: exercise.instructions,
        sportsActivityLocation: 'Gym, Home',
      }}
    />
  );
}

interface ReviewStructuredDataProps {
  reviews: Array<{
    author: string;
    rating: number;
    content: string;
    date: string;
  }>;
}

export function ReviewsStructuredData({ reviews }: ReviewStructuredDataProps) {
  return (
    <>
      {reviews.map((review, index) => (
        <StructuredData
          key={index}
          type="Review"
          data={{
            author: review.author,
            rating: review.rating,
            content: review.content,
            date: review.date,
          }}
        />
      ))}
    </>
  );
}

interface FAQStructuredDataProps {
  questions: Array<{
    question: string;
    answer: string;
  }>;
}

export function FAQStructuredData({ questions }: FAQStructuredDataProps) {
  return (
    <StructuredData
      type="FAQPage"
      data={{ questions }}
    />
  );
}

interface BreadcrumbStructuredDataProps {
  items: Array<{
    name: string;
    url: string;
  }>;
}

export function BreadcrumbStructuredData({ items }: BreadcrumbStructuredDataProps) {
  return (
    <StructuredData
      type="BreadcrumbList"
      data={{ items }}
    />
  );
}

// Article structured data for blog posts
interface ArticleStructuredDataProps {
  article: {
    title: string;
    description: string;
    image: string;
    author: string;
    publishedTime: string;
    modifiedTime?: string;
    url: string;
    tags: string[];
    wordCount: number;
  };
}

export function ArticleStructuredData({ article }: ArticleStructuredDataProps) {
  return (
    <StructuredData
      type="Article"
      data={{
        headline: article.title,
        description: article.description,
        image: article.image,
        author: {
          '@type': 'Person',
          name: article.author,
        },
        publisher: {
          '@type': 'Organization',
          name: 'FitnessApp',
          logo: {
            '@type': 'ImageObject',
            url: `${process.env.NEXT_PUBLIC_APP_URL}/logo.png`,
          },
        },
        datePublished: article.publishedTime,
        dateModified: article.modifiedTime || article.publishedTime,
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': article.url,
        },
        keywords: article.tags,
        wordCount: article.wordCount,
        articleSection: 'Fitness',
        inLanguage: 'en-US',
      }}
    />
  );
}
