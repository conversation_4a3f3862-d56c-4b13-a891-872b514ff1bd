'use client';

import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';
import { BreadcrumbStructuredData } from './structured-data';

interface BreadcrumbItem {
  name: string;
  url: string;
  current?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumbs({ items, className = '' }: BreadcrumbsProps) {
  // Always include home as the first item
  const allItems = [
    { name: 'Home', url: '/' },
    ...items,
  ];

  return (
    <>
      {/* Structured Data */}
      <BreadcrumbStructuredData items={allItems} />
      
      {/* Visual Breadcrumbs */}
      <nav
        aria-label="Breadcrumb"
        className={`flex items-center space-x-2 text-sm ${className}`}
      >
        <ol className="flex items-center space-x-2">
          {allItems.map((item, index) => (
            <li key={item.url} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-secondary-400 mx-2" />
              )}
              
              {item.current || index === allItems.length - 1 ? (
                <span
                  className="text-secondary-900 dark:text-white font-medium"
                  aria-current="page"
                >
                  {index === 0 ? (
                    <Home className="h-4 w-4" />
                  ) : (
                    item.name
                  )}
                </span>
              ) : (
                <Link
                  href={item.url}
                  className="text-secondary-600 dark:text-secondary-400 hover:text-secondary-900 dark:hover:text-white transition-colors"
                >
                  {index === 0 ? (
                    <Home className="h-4 w-4" />
                  ) : (
                    item.name
                  )}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}

// Utility function to generate breadcrumbs from pathname
export function generateBreadcrumbsFromPath(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  let currentPath = '';
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Convert segment to readable name
    const name = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    breadcrumbs.push({
      name,
      url: currentPath,
      current: index === segments.length - 1,
    });
  });

  return breadcrumbs;
}

// Predefined breadcrumb configurations for common pages
export const breadcrumbConfigs: Record<string, BreadcrumbItem[]> = {
  '/dashboard': [
    { name: 'Dashboard', url: '/dashboard', current: true },
  ],
  '/dashboard/workouts': [
    { name: 'Dashboard', url: '/dashboard' },
    { name: 'Workouts', url: '/dashboard/workouts', current: true },
  ],
  '/dashboard/progress': [
    { name: 'Dashboard', url: '/dashboard' },
    { name: 'Progress', url: '/dashboard/progress', current: true },
  ],
  '/dashboard/messages': [
    { name: 'Dashboard', url: '/dashboard' },
    { name: 'Messages', url: '/dashboard/messages', current: true },
  ],
  '/dashboard/profile': [
    { name: 'Dashboard', url: '/dashboard' },
    { name: 'Profile', url: '/dashboard/profile', current: true },
  ],
  '/dashboard/settings': [
    { name: 'Dashboard', url: '/dashboard' },
    { name: 'Settings', url: '/dashboard/settings', current: true },
  ],
  '/auth/login': [
    { name: 'Authentication', url: '/auth' },
    { name: 'Sign In', url: '/auth/login', current: true },
  ],
  '/auth/register': [
    { name: 'Authentication', url: '/auth' },
    { name: 'Sign Up', url: '/auth/register', current: true },
  ],
  '/auth/reset-password': [
    { name: 'Authentication', url: '/auth' },
    { name: 'Reset Password', url: '/auth/reset-password', current: true },
  ],
};
