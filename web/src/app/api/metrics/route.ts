import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Rate limiting for metrics endpoint
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

function isRateLimited(ip: string): boolean {
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 100; // 100 requests per minute

  const current = rateLimitMap.get(ip);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    return false;
  }
  
  if (current.count >= maxRequests) {
    return true;
  }
  
  current.count++;
  return false;
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip || 'unknown';
    if (isRateLimited(ip)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { metric, value, timestamp, tags = {} } = body;

    // Validate input
    if (!metric || typeof value !== 'number') {
      return NextResponse.json(
        { error: 'Invalid metric data' },
        { status: 400 }
      );
    }

    // Store metric in database
    const supabase = createClient();
    const { error } = await supabase
      .from('system_metrics')
      .insert({
        metric_name: metric,
        metric_value: value,
        tags: tags,
        created_at: timestamp ? new Date(timestamp).toISOString() : new Date().toISOString(),
      });

    if (error) {
      console.error('Failed to store metric:', error);
      return NextResponse.json(
        { error: 'Failed to store metric' },
        { status: 500 }
      );
    }

    // Send to external monitoring services if configured
    await sendToExternalServices(metric, value, tags);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Metrics endpoint error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const metric = searchParams.get('metric');
    const hours = parseInt(searchParams.get('hours') || '24');
    const limit = parseInt(searchParams.get('limit') || '1000');

    const supabase = createClient();
    let query = supabase
      .from('system_metrics')
      .select('*')
      .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(limit);

    if (metric) {
      query = query.eq('metric_name', metric);
    }

    const { data, error } = await query;

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch metrics' },
        { status: 500 }
      );
    }

    // Calculate aggregations
    const aggregations = calculateAggregations(data || []);

    return NextResponse.json({
      metrics: data,
      aggregations,
      count: data?.length || 0,
    });

  } catch (error) {
    console.error('Metrics fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function sendToExternalServices(metric: string, value: number, tags: any) {
  const promises = [];

  // Send to custom webhook if configured
  if (process.env.METRICS_WEBHOOK_URL) {
    promises.push(
      fetch(process.env.METRICS_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric,
          value,
          tags,
          timestamp: Date.now(),
          source: 'fitnessapp-web',
        }),
      }).catch(console.error)
    );
  }

  // Send to DataDog if configured
  if (process.env.DATADOG_API_KEY) {
    promises.push(
      fetch('https://api.datadoghq.com/api/v1/series', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'DD-API-KEY': process.env.DATADOG_API_KEY,
        },
        body: JSON.stringify({
          series: [{
            metric: `fitnessapp.${metric}`,
            points: [[Math.floor(Date.now() / 1000), value]],
            tags: Object.entries(tags).map(([k, v]) => `${k}:${v}`),
          }],
        }),
      }).catch(console.error)
    );
  }

  await Promise.allSettled(promises);
}

function calculateAggregations(metrics: any[]) {
  if (metrics.length === 0) return {};

  const groupedByMetric = metrics.reduce((acc, metric) => {
    if (!acc[metric.metric_name]) {
      acc[metric.metric_name] = [];
    }
    acc[metric.metric_name].push(metric.metric_value);
    return acc;
  }, {} as Record<string, number[]>);

  const aggregations: Record<string, any> = {};

  for (const [metricName, values] of Object.entries(groupedByMetric)) {
    const sorted = [...values].sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);

    aggregations[metricName] = {
      count: values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: sum / values.length,
      sum,
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
    };
  }

  return aggregations;
}
