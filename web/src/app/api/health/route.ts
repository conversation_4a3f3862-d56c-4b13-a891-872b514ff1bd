import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Initialize health check results
    const healthChecks = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {} as Record<string, any>,
      responseTime: 0,
    };

    // Database health check
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('users')
        .select('count')
        .limit(1)
        .single();
      
      healthChecks.checks.database = {
        status: error ? 'unhealthy' : 'healthy',
        responseTime: Date.now() - startTime,
        error: error?.message,
      };
    } catch (error) {
      healthChecks.checks.database = {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }

    // Memory check
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    };

    healthChecks.checks.memory = {
      status: memoryUsageMB.heapUsed < 512 ? 'healthy' : 'warning',
      usage: memoryUsageMB,
    };

    // Environment check
    healthChecks.checks.environment = {
      status: 'healthy',
      nodeVersion: process.version,
      platform: process.platform,
      uptime: Math.round(process.uptime()),
    };

    // External services check
    const externalChecks = await Promise.allSettled([
      // Check Supabase API
      fetch(process.env.NEXT_PUBLIC_SUPABASE_URL + '/rest/v1/', {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        },
      }),
    ]);

    healthChecks.checks.supabase = {
      status: externalChecks[0].status === 'fulfilled' && 
              (externalChecks[0].value as Response).ok ? 'healthy' : 'unhealthy',
    };

    // Overall status determination
    const allChecks = Object.values(healthChecks.checks);
    const hasUnhealthy = allChecks.some(check => check.status === 'unhealthy');
    const hasWarning = allChecks.some(check => check.status === 'warning');

    if (hasUnhealthy) {
      healthChecks.status = 'unhealthy';
    } else if (hasWarning) {
      healthChecks.status = 'warning';
    }

    healthChecks.responseTime = Date.now() - startTime;

    // Return appropriate status code
    const statusCode = healthChecks.status === 'healthy' ? 200 : 
                      healthChecks.status === 'warning' ? 200 : 503;

    return NextResponse.json(healthChecks, { status: statusCode });

  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: Date.now() - startTime,
    }, { status: 503 });
  }
}
