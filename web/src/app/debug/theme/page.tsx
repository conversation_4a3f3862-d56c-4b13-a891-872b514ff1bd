'use client';

import { useGenderTheme } from '@/hooks/use-gender-theme';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function ThemeDebugPage() {
  const { currentTheme, colors, isInitialized, setGenderTheme, initializeFromProfile } = useGenderTheme();

  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Gender Theme Debug Page</h1>
        <p className="text-muted-foreground">
          This page helps debug the gender-based theme system on the web.
        </p>
      </div>

      {/* Current Theme Status */}
      <Card>
        <CardHeader>
          <CardTitle>Current Theme Status</CardTitle>
          <CardDescription>Shows the current gender theme state</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Current Theme:</strong> 
              <span 
                className="ml-2 px-2 py-1 rounded text-white font-medium"
                style={{ backgroundColor: colors.primary }}
              >
                {currentTheme}
              </span>
            </div>
            <div>
              <strong>Initialized:</strong> 
              <span className={`ml-2 ${isInitialized ? 'text-green-600' : 'text-red-600'}`}>
                {isInitialized ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Colors Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Theme Colors</CardTitle>
          <CardDescription>Preview of current theme colors</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-2"
                style={{ backgroundColor: colors.primary }}
              ></div>
              <p className="text-sm font-medium">Primary</p>
              <p className="text-xs text-muted-foreground">{colors.primary}</p>
            </div>
            <div className="text-center">
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-2 border"
                style={{ backgroundColor: colors.accent }}
              ></div>
              <p className="text-sm font-medium">Accent</p>
              <p className="text-xs text-muted-foreground">{colors.accent}</p>
            </div>
            <div className="text-center">
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-2"
                style={{ backgroundColor: colors.secondary }}
              ></div>
              <p className="text-sm font-medium">Secondary</p>
              <p className="text-xs text-muted-foreground">{colors.secondary}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Theme Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Theme Controls</CardTitle>
          <CardDescription>Manually set or initialize themes</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={() => setGenderTheme('Woman')}
              variant={currentTheme === 'Woman' ? 'default' : 'outline'}
              style={{ 
                backgroundColor: currentTheme === 'Woman' ? '#B37399' : undefined,
                borderColor: '#B37399',
                color: currentTheme === 'Woman' ? 'white' : '#B37399'
              }}
            >
              Set Woman Theme
            </Button>
            <Button 
              onClick={() => setGenderTheme('Man')}
              variant={currentTheme === 'Man' ? 'default' : 'outline'}
              style={{ 
                backgroundColor: currentTheme === 'Man' ? '#353E43' : undefined,
                borderColor: '#353E43',
                color: currentTheme === 'Man' ? 'white' : '#353E43'
              }}
            >
              Set Man Theme
            </Button>
            <Button 
              onClick={() => setGenderTheme('Neutral')}
              variant={currentTheme === 'Neutral' ? 'default' : 'outline'}
              style={{ 
                backgroundColor: currentTheme === 'Neutral' ? '#508C9B' : undefined,
                borderColor: '#508C9B',
                color: currentTheme === 'Neutral' ? 'white' : '#508C9B'
              }}
            >
              Set Neutral Theme
            </Button>
          </div>
          
          <div className="pt-4 border-t">
            <Button 
              onClick={initializeFromProfile}
              variant="secondary"
            >
              Initialize from Profile
            </Button>
            <p className="text-sm text-muted-foreground mt-2">
              This will fetch your profile and set the theme based on your gender preference.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* CSS Variables Preview */}
      <Card>
        <CardHeader>
          <CardTitle>CSS Variables</CardTitle>
          <CardDescription>Current CSS custom properties</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm font-mono">
            <div>
              <strong>--primary:</strong> 
              <span className="ml-2">rgb(var(--primary))</span>
            </div>
            <div>
              <strong>--accent:</strong> 
              <span className="ml-2">rgb(var(--accent))</span>
            </div>
            <div>
              <strong>--gender-primary:</strong> 
              <span className="ml-2">rgb(var(--gender-primary))</span>
            </div>
            <div>
              <strong>--gender-accent:</strong> 
              <span className="ml-2">rgb(var(--gender-accent))</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Elements */}
      <Card>
        <CardHeader>
          <CardTitle>Test Elements</CardTitle>
          <CardDescription>Elements using the theme colors</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            className="w-full"
            style={{ backgroundColor: colors.primary, color: 'white' }}
          >
            Primary Button
          </Button>
          
          <div 
            className="p-4 rounded-lg"
            style={{ backgroundColor: colors.accent, color: colors.text }}
          >
            <p>This box uses the accent background color.</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: colors.success }}
            ></div>
            <span>Success Color</span>
            
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: colors.warning }}
            ></div>
            <span>Warning Color</span>
            
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: colors.error }}
            ></div>
            <span>Error Color</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
