import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '@/types/database';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') ?? '/dashboard';

  if (code) {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    try {
      const { error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('Auth callback error:', error);
        return NextResponse.redirect(
          new URL('/auth/login?error=auth_callback_error', requestUrl.origin)
        );
      }

      // Get the user to create/update profile
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        // Check if user profile exists
        const { data: profile } = await supabase
          .from('users')
          .select('id')
          .eq('id', user.id)
          .single();

        // Create user profile if it doesn't exist
        if (!profile) {
          const { error: profileError } = await supabase
            .from('users')
            .insert({
              id: user.id,
              email: user.email!,
              first_name: user.user_metadata?.first_name || null,
              last_name: user.user_metadata?.last_name || null,
              avatar_url: user.user_metadata?.avatar_url || null,
            });

          if (profileError) {
            console.error('Error creating user profile:', profileError);
          }
        }
      }

      return NextResponse.redirect(new URL(next, requestUrl.origin));
    } catch (error) {
      console.error('Unexpected auth callback error:', error);
      return NextResponse.redirect(
        new URL('/auth/login?error=unexpected_error', requestUrl.origin)
      );
    }
  }

  // Return the user to an error page with instructions
  return NextResponse.redirect(
    new URL('/auth/login?error=no_code_provided', requestUrl.origin)
  );
}
