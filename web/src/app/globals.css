@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-inter: 'Inter', system-ui, sans-serif;

    /* Light theme colors */
    --background: 255 255 255;
    --foreground: 15 23 42;
    --muted: 248 250 252;
    --muted-foreground: 100 116 139;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --border: 226 232 240;
    --input: 226 232 240;
    --primary: 80 140 155; /* Default Neutral theme primary */
    --primary-foreground: 255 255 255;
    --secondary: 248 250 252;
    --secondary-foreground: 15 23 42;
    --accent: 236 236 236; /* Default Neutral theme accent */
    --accent-foreground: 15 23 42;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --ring: 80 140 155; /* Match primary */
    --radius: 0.5rem;

    /* Toast colors */
    --toast-bg: rgb(255 255 255);
    --toast-color: rgb(15 23 42);
    --toast-border: rgb(226 232 240);

    /* Gender theme colors - will be overridden by JavaScript */
    --gender-primary: 80 140 155; /* Neutral */
    --gender-accent: 236 236 236; /* Neutral */
  }

  /* Force gender theme colors to take precedence */
  html[data-gender-theme="Woman"] {
    --primary: 179 115 153 !important; /* Woman theme */
    --accent: 240 230 247 !important;
    --ring: 179 115 153 !important;
  }

  html[data-gender-theme="Man"] {
    --primary: 53 62 67 !important; /* Man theme */
    --accent: 245 245 245 !important;
    --ring: 53 62 67 !important;
  }

  html[data-gender-theme="Neutral"] {
    --primary: 80 140 155 !important; /* Neutral theme */
    --accent: 236 236 236 !important;
    --ring: 80 140 155 !important;
  }

  .dark {
    /* Dark theme colors */
    --background: 15 23 42;
    --foreground: 248 250 252;
    --muted: 30 41 59;
    --muted-foreground: 148 163 184;
    --popover: 15 23 42;
    --popover-foreground: 248 250 252;
    --card: 15 23 42;
    --card-foreground: 248 250 252;
    --border: 51 65 85;
    --input: 51 65 85;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 30 41 59;
    --secondary-foreground: 248 250 252;
    --accent: 30 41 59;
    --accent-foreground: 248 250 252;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --ring: 59 130 246;
    
    /* Toast colors */
    --toast-bg: rgb(30 41 59);
    --toast-color: rgb(248 250 252);
    --toast-border: rgb(51 65 85);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgb(var(--muted-foreground) / 0.3) rgb(var(--muted));
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Remove autofill styles */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px rgb(var(--background)) inset !important;
    -webkit-text-fill-color: rgb(var(--foreground)) !important;
  }

  /* Loading animation */
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  /* Skeleton loading */
  @keyframes skeleton {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .skeleton {
    background: linear-gradient(90deg, rgb(var(--muted)) 25%, rgb(var(--muted-foreground) / 0.1) 50%, rgb(var(--muted)) 75%);
    background-size: 200px 100%;
    animation: skeleton 1.5s infinite;
  }

  /* Fade in animation */
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Slide up animation */
  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Scale in animation */
  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Glass morphism effect */
  .glass {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  .dark .glass {
    background-color: rgba(15, 23, 42, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, rgb(var(--primary)), rgb(var(--primary)) 40%, rgb(var(--accent-foreground)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Button hover effects */
  .btn-hover {
    @apply transition-all duration-200 ease-in-out;
  }

  .btn-hover:hover {
    @apply transform scale-105 shadow-lg;
  }

  .btn-hover:active {
    @apply transform scale-95;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 ease-in-out;
  }

  .card-hover:hover {
    @apply transform -translate-y-1 shadow-xl;
  }

  /* Text balance for better typography */
  .text-balance {
    text-wrap: balance;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Custom checkbox */
  .checkbox {
    @apply relative inline-flex h-5 w-5 items-center justify-center rounded border-2 border-muted-foreground/30 bg-background transition-colors;
  }

  .checkbox:checked {
    @apply border-primary bg-primary;
  }

  .checkbox:checked::after {
    content: '';
    @apply absolute h-2 w-3 border-b-2 border-r-2 border-primary-foreground transform rotate-45 -translate-y-0.5;
  }

  /* Custom radio */
  .radio {
    @apply relative inline-flex h-5 w-5 items-center justify-center rounded-full border-2 border-muted-foreground/30 bg-background transition-colors;
  }

  .radio:checked {
    @apply border-primary;
  }

  .radio:checked::after {
    content: '';
    @apply absolute h-2 w-2 rounded-full bg-primary;
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    body {
      @apply text-black bg-white;
    }
    
    .print-break {
      page-break-before: always;
    }
  }
}
