import { NextResponse } from 'next/server';
import { generateSitemap } from '@/lib/seo';

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://fitnessapp.com';
  
  // Define all public pages
  const staticPages = [
    {
      url: '/',
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1.0,
    },
    {
      url: '/about',
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: '/features',
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: '/pricing',
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: '/contact',
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: '/privacy',
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.5,
    },
    {
      url: '/terms',
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.5,
    },
    {
      url: '/auth/login',
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: '/auth/register',
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
  ];

  // Add dynamic pages (blog posts, workout programs, etc.)
  const dynamicPages = await getDynamicPages();
  
  const allPages = [...staticPages, ...dynamicPages];
  const sitemap = generateSitemap(allPages);

  return new NextResponse(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}

async function getDynamicPages() {
  const pages = [];
  
  try {
    // Add blog posts if they exist
    // const blogPosts = await getBlogPosts();
    // blogPosts.forEach(post => {
    //   pages.push({
    //     url: `/blog/${post.slug}`,
    //     lastModified: new Date(post.updatedAt),
    //     changeFrequency: 'monthly' as const,
    //     priority: 0.7,
    //   });
    // });

    // Add workout programs if they exist
    // const workoutPrograms = await getPublicWorkoutPrograms();
    // workoutPrograms.forEach(program => {
    //   pages.push({
    //     url: `/programs/${program.slug}`,
    //     lastModified: new Date(program.updatedAt),
    //     changeFrequency: 'weekly' as const,
    //     priority: 0.8,
    //   });
    // });

    // Add exercise library if it exists
    // const exercises = await getPublicExercises();
    // exercises.forEach(exercise => {
    //   pages.push({
    //     url: `/exercises/${exercise.slug}`,
    //     lastModified: new Date(exercise.updatedAt),
    //     changeFrequency: 'monthly' as const,
    //     priority: 0.6,
    //   });
    // });

  } catch (error) {
    console.error('Error generating dynamic sitemap pages:', error);
  }

  return pages;
}
