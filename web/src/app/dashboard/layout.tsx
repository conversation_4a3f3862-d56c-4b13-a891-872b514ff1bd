import { Metadata } from 'next';
import { DashboardSidebar } from '@/components/dashboard/sidebar';
import { DashboardHeader } from '@/components/dashboard/header';
import { AuthGuard } from '@/components/auth/auth-guard';

export const metadata: Metadata = {
  title: {
    default: 'Dashboard',
    template: '%s | Dashboard | FitnessApp',
  },
  description: 'Your personal fitness dashboard with workouts, progress tracking, and coaching.',
};

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <AuthGuard>
      <div className="min-h-screen bg-secondary-50 dark:bg-secondary-900">
        {/* Sidebar */}
        <DashboardSidebar />
        
        {/* Main Content */}
        <div className="lg:pl-72">
          {/* Header */}
          <DashboardHeader />
          
          {/* Page Content */}
          <main className="py-6">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </AuthGuard>
  );
}
