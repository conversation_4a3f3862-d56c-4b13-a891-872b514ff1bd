import type { Metada<PERSON>, Viewport } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/providers';
import { Toaster } from 'react-hot-toast';
import { InstallPrompt } from '@/components/pwa/install-prompt';
import { WebApplicationStructuredData, OrganizationStructuredData } from '@/components/seo/structured-data';
import './globals.css';

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: {
    default: 'FitnessApp - Your Personal Fitness Journey',
    template: '%s | FitnessApp',
  },
  description: 'Transform your fitness journey with personalized workout programs, expert coaching, and comprehensive progress tracking.',
  keywords: [
    'fitness',
    'workout',
    'personal trainer',
    'exercise',
    'health',
    'nutrition',
    'coaching',
    'progress tracking',
    'fitness app',
    'workout planner',
  ],
  authors: [{ name: 'FitnessApp Team' }],
  creator: 'FitnessApp',
  publisher: 'FitnessApp',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://fitnessapp.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'FitnessApp - Your Personal Fitness Journey',
    description: 'Transform your fitness journey with personalized workout programs, expert coaching, and comprehensive progress tracking.',
    siteName: 'FitnessApp',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'FitnessApp - Your Personal Fitness Journey',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'FitnessApp - Your Personal Fitness Journey',
    description: 'Transform your fitness journey with personalized workout programs, expert coaching, and comprehensive progress tracking.',
    images: ['/og-image.jpg'],
    creator: '@fitnessapp',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
  category: 'fitness',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
  colorScheme: 'light dark',
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" className={inter.variable} suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="//supabase.co" />
        
        {/* Structured data for SEO */}
        <WebApplicationStructuredData />
        <OrganizationStructuredData />
      </head>
      <body className="min-h-screen bg-white font-sans antialiased dark:bg-secondary-900">
        <Providers>
          {children}
          <InstallPrompt />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--toast-bg)',
                color: 'var(--toast-color)',
                border: '1px solid var(--toast-border)',
              },
              success: {
                iconTheme: {
                  primary: '#22c55e',
                  secondary: '#ffffff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#ffffff',
                },
              },
            }}
          />
        </Providers>
      </body>
    </html>
  );
}
