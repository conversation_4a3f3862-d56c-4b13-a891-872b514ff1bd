import { NextResponse } from 'next/server';
import { generateRobotsTxt } from '@/lib/seo';

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://fitnessapp.com';
  
  const robots = generateRobotsTxt({
    userAgent: '*',
    allow: [
      '/',
      '/about',
      '/features',
      '/pricing',
      '/contact',
      '/auth/login',
      '/auth/register',
      '/blog/',
      '/programs/',
      '/exercises/',
    ],
    disallow: [
      '/api/',
      '/dashboard/',
      '/admin/',
      '/_next/',
      '/auth/callback',
      '/auth/reset-password',
      '/auth/verify-email',
      '/auth/update-password',
      '/offline',
      '/*.json$',
      '/private/',
      '/temp/',
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
  });

  // Add additional directives
  const additionalRobots = `
# Crawl-delay for specific bots
User-agent: Googlebot
Crawl-delay: 1

User-agent: Bingbot
Crawl-delay: 2

# Block specific bots if needed
User-agent: BadBot
Disallow: /

# Allow specific paths for SEO bots
User-agent: Googlebot
Allow: /api/og/
Allow: /_next/static/

User-agent: Bingbot
Allow: /api/og/
Allow: /_next/static/

# Additional sitemaps
Sitemap: ${baseUrl}/sitemap-blog.xml
Sitemap: ${baseUrl}/sitemap-programs.xml
Sitemap: ${baseUrl}/sitemap-exercises.xml`;

  const fullRobots = robots + additionalRobots;

  return new NextResponse(fullRobots, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400',
    },
  });
}
