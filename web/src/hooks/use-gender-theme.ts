'use client';

import { useGenderTheme } from '@/components/gender-theme-provider';

// Re-export the hook for easier importing
export { useGenderTheme };

// Utility function to get theme-aware CSS classes
export function getThemeClasses(baseClasses: string = '') {
  const { currentTheme } = useGenderTheme();
  
  const themeClasses = {
    'Woman': 'theme-woman',
    'Man': 'theme-man', 
    'Neutral': 'theme-neutral'
  };
  
  const themeClass = themeClasses[currentTheme] || 'theme-neutral';
  
  return `${baseClasses} ${themeClass}`.trim();
}

// Utility function to get theme colors as CSS variables
export function getThemeStyles() {
  const { colors } = useGenderTheme();
  
  return {
    '--theme-primary': colors.primary,
    '--theme-secondary': colors.secondary,
    '--theme-accent': colors.accent,
    '--theme-background': colors.background,
    '--theme-text': colors.text,
    '--theme-success': colors.success,
    '--theme-warning': colors.warning,
    '--theme-error': colors.error,
    '--theme-info': colors.info,
  } as React.CSSProperties;
}

// Utility function to check if theme is initialized
export function useThemeInitialized() {
  const { isInitialized } = useGenderTheme();
  return isInitialized;
}
