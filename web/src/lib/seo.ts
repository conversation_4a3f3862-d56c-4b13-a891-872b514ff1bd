import { Metadata } from 'next';

export interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  noFollow?: boolean;
}

const defaultConfig = {
  siteName: 'FitnessApp',
  siteUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://fitnessapp.com',
  defaultTitle: 'FitnessApp - Transform Your Fitness Journey',
  defaultDescription: 'Get personalized workout programs, expert coaching, and comprehensive progress tracking. Transform your fitness journey with AI-powered workout generation.',
  defaultImage: '/og-default.jpg',
  twitterHandle: '@fitnessapp',
  facebookAppId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
};

export function generateMetadata(config: SEOConfig = {}): Metadata {
  const {
    title,
    description = defaultConfig.defaultDescription,
    keywords = [],
    image = defaultConfig.defaultImage,
    url,
    type = 'website',
    publishedTime,
    modifiedTime,
    author,
    section,
    tags = [],
    noIndex = false,
    noFollow = false,
  } = config;

  const fullTitle = title 
    ? `${title} | ${defaultConfig.siteName}`
    : defaultConfig.defaultTitle;

  const fullUrl = url 
    ? `${defaultConfig.siteUrl}${url}`
    : defaultConfig.siteUrl;

  const fullImage = image.startsWith('http') 
    ? image 
    : `${defaultConfig.siteUrl}${image}`;

  const allKeywords = [
    'fitness',
    'workout',
    'personal trainer',
    'exercise',
    'health',
    'nutrition',
    'coaching',
    'progress tracking',
    'AI workout',
    'fitness app',
    ...keywords,
  ];

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: allKeywords,
    authors: author ? [{ name: author }] : [{ name: defaultConfig.siteName }],
    creator: defaultConfig.siteName,
    publisher: defaultConfig.siteName,
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(defaultConfig.siteUrl),
    alternates: {
      canonical: url || '/',
    },
    openGraph: {
      type,
      locale: 'en_US',
      url: fullUrl,
      title: fullTitle,
      description,
      siteName: defaultConfig.siteName,
      images: [
        {
          url: fullImage,
          width: 1200,
          height: 630,
          alt: title || defaultConfig.defaultTitle,
        },
      ],
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(author && { authors: [author] }),
      ...(section && { section }),
      ...(tags.length > 0 && { tags }),
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [fullImage],
      creator: defaultConfig.twitterHandle,
      site: defaultConfig.twitterHandle,
    },
    robots: {
      index: !noIndex,
      follow: !noFollow,
      googleBot: {
        index: !noIndex,
        follow: !noFollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
      yandex: process.env.YANDEX_VERIFICATION,
      bing: process.env.BING_VERIFICATION,
    },
    category: 'fitness',
  };

  return metadata;
}

export function generateStructuredData(type: string, data: any) {
  const baseData = {
    '@context': 'https://schema.org',
    '@type': type,
  };

  switch (type) {
    case 'WebApplication':
      return {
        ...baseData,
        name: defaultConfig.siteName,
        description: defaultConfig.defaultDescription,
        url: defaultConfig.siteUrl,
        applicationCategory: 'HealthApplication',
        operatingSystem: 'Web, iOS, Android',
        offers: {
          '@type': 'Offer',
          category: 'Fitness & Health',
        },
        author: {
          '@type': 'Organization',
          name: defaultConfig.siteName,
        },
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: '4.8',
          ratingCount: '1250',
          bestRating: '5',
          worstRating: '1',
        },
        ...data,
      };

    case 'Organization':
      return {
        ...baseData,
        name: defaultConfig.siteName,
        url: defaultConfig.siteUrl,
        logo: `${defaultConfig.siteUrl}/logo.png`,
        sameAs: [
          'https://twitter.com/fitnessapp',
          'https://facebook.com/fitnessapp',
          'https://instagram.com/fitnessapp',
          'https://linkedin.com/company/fitnessapp',
        ],
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'Customer Service',
          email: '<EMAIL>',
        },
        ...data,
      };

    case 'Article':
      return {
        ...baseData,
        headline: data.title,
        description: data.description,
        image: data.image,
        author: {
          '@type': 'Person',
          name: data.author || defaultConfig.siteName,
        },
        publisher: {
          '@type': 'Organization',
          name: defaultConfig.siteName,
          logo: {
            '@type': 'ImageObject',
            url: `${defaultConfig.siteUrl}/logo.png`,
          },
        },
        datePublished: data.publishedTime,
        dateModified: data.modifiedTime || data.publishedTime,
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': data.url,
        },
        ...data,
      };

    case 'ExerciseAction':
      return {
        ...baseData,
        name: data.name,
        description: data.description,
        exerciseType: data.type,
        bodyLocation: data.bodyParts,
        equipment: data.equipment,
        intensity: data.intensity,
        repetitions: data.repetitions,
        restPeriods: data.restPeriods,
        ...data,
      };

    case 'Course':
      return {
        ...baseData,
        name: data.name,
        description: data.description,
        provider: {
          '@type': 'Organization',
          name: defaultConfig.siteName,
        },
        courseCode: data.id,
        educationalLevel: data.difficulty,
        timeRequired: `P${data.duration}W`,
        coursePrerequisites: data.prerequisites,
        learningResourceType: 'Fitness Program',
        ...data,
      };

    case 'Review':
      return {
        ...baseData,
        itemReviewed: {
          '@type': 'WebApplication',
          name: defaultConfig.siteName,
        },
        author: {
          '@type': 'Person',
          name: data.author,
        },
        reviewRating: {
          '@type': 'Rating',
          ratingValue: data.rating,
          bestRating: '5',
          worstRating: '1',
        },
        reviewBody: data.content,
        datePublished: data.date,
        ...data,
      };

    case 'FAQPage':
      return {
        ...baseData,
        mainEntity: data.questions.map((q: any) => ({
          '@type': 'Question',
          name: q.question,
          acceptedAnswer: {
            '@type': 'Answer',
            text: q.answer,
          },
        })),
        ...data,
      };

    case 'BreadcrumbList':
      return {
        ...baseData,
        itemListElement: data.items.map((item: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.name,
          item: item.url,
        })),
        ...data,
      };

    default:
      return { ...baseData, ...data };
  }
}

export function generateBreadcrumbs(items: Array<{ name: string; url: string }>) {
  return generateStructuredData('BreadcrumbList', { items });
}

export function generateSitemap(pages: Array<{
  url: string;
  lastModified?: Date;
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}>) {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages.map(page => `  <url>
    <loc>${defaultConfig.siteUrl}${page.url}</loc>
    ${page.lastModified ? `<lastmod>${page.lastModified.toISOString()}</lastmod>` : ''}
    ${page.changeFrequency ? `<changefreq>${page.changeFrequency}</changefreq>` : ''}
    ${page.priority ? `<priority>${page.priority}</priority>` : ''}
  </url>`).join('\n')}
</urlset>`;

  return sitemap;
}

export function generateRobotsTxt(options: {
  userAgent?: string;
  allow?: string[];
  disallow?: string[];
  sitemap?: string;
} = {}) {
  const {
    userAgent = '*',
    allow = ['/'],
    disallow = ['/api/', '/admin/', '/_next/'],
    sitemap = `${defaultConfig.siteUrl}/sitemap.xml`,
  } = options;

  const robots = `User-agent: ${userAgent}
${allow.map(path => `Allow: ${path}`).join('\n')}
${disallow.map(path => `Disallow: ${path}`).join('\n')}

Sitemap: ${sitemap}`;

  return robots;
}

// SEO-friendly URL generation
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Meta tag validation
export function validateMetaTags(metadata: Metadata): string[] {
  const errors: string[] = [];

  if (!metadata.title) {
    errors.push('Title is required');
  } else if (typeof metadata.title === 'string' && metadata.title.length > 60) {
    errors.push('Title should be 60 characters or less');
  }

  if (!metadata.description) {
    errors.push('Description is required');
  } else if (metadata.description.length > 160) {
    errors.push('Description should be 160 characters or less');
  }

  if (metadata.openGraph?.images && metadata.openGraph.images.length === 0) {
    errors.push('Open Graph image is recommended');
  }

  return errors;
}
