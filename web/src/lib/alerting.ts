// Comprehensive alerting system for FitnessApp

export interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'critical';
  title: string;
  message: string;
  source: string;
  timestamp: Date;
  metadata?: Record<string, any>;
  resolved?: boolean;
  resolvedAt?: Date;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: (data: any) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cooldown: number; // minutes
  channels: string[];
  enabled: boolean;
}

export class AlertManager {
  private static instance: AlertManager;
  private rules: Map<string, AlertRule> = new Map();
  private recentAlerts: Map<string, Date> = new Map();
  private channels: Map<string, AlertChannel> = new Map();

  static getInstance(): AlertManager {
    if (!AlertManager.instance) {
      AlertManager.instance = new AlertManager();
    }
    return AlertManager.instance;
  }

  // Register alert rule
  registerRule(rule: AlertRule) {
    this.rules.set(rule.id, rule);
  }

  // Register alert channel
  registerChannel(name: string, channel: AlertChannel) {
    this.channels.set(name, channel);
  }

  // Check all rules and trigger alerts
  async checkRules(data: any) {
    for (const [ruleId, rule] of this.rules) {
      if (!rule.enabled) continue;

      try {
        if (rule.condition(data)) {
          await this.triggerAlert(ruleId, rule, data);
        }
      } catch (error) {
        console.error(`Error checking rule ${ruleId}:`, error);
      }
    }
  }

  // Trigger an alert
  private async triggerAlert(ruleId: string, rule: AlertRule, data: any) {
    // Check cooldown
    const lastAlert = this.recentAlerts.get(ruleId);
    if (lastAlert) {
      const cooldownMs = rule.cooldown * 60 * 1000;
      if (Date.now() - lastAlert.getTime() < cooldownMs) {
        return; // Still in cooldown
      }
    }

    const alert: Alert = {
      id: `${ruleId}_${Date.now()}`,
      type: this.severityToType(rule.severity),
      title: rule.name,
      message: this.generateMessage(rule, data),
      source: 'AlertManager',
      timestamp: new Date(),
      metadata: data,
    };

    // Send to configured channels
    const promises = rule.channels.map(channelName => {
      const channel = this.channels.get(channelName);
      return channel?.send(alert);
    });

    await Promise.allSettled(promises);

    // Update cooldown
    this.recentAlerts.set(ruleId, new Date());

    console.log(`Alert triggered: ${alert.title}`);
  }

  private severityToType(severity: string): Alert['type'] {
    switch (severity) {
      case 'critical': return 'critical';
      case 'high': return 'error';
      case 'medium': return 'warning';
      default: return 'info';
    }
  }

  private generateMessage(rule: AlertRule, data: any): string {
    // Generate contextual message based on rule and data
    return `Alert: ${rule.name} - ${JSON.stringify(data)}`;
  }
}

// Alert channel interface
export interface AlertChannel {
  send(alert: Alert): Promise<void>;
}

// Slack alert channel
export class SlackChannel implements AlertChannel {
  constructor(private webhookUrl: string) {}

  async send(alert: Alert): Promise<void> {
    const color = this.getColor(alert.type);
    const payload = {
      text: `🚨 FitnessApp Alert: ${alert.title}`,
      attachments: [{
        color,
        title: alert.title,
        text: alert.message,
        fields: [
          { title: 'Type', value: alert.type, short: true },
          { title: 'Source', value: alert.source, short: true },
          { title: 'Time', value: alert.timestamp.toISOString(), short: true },
        ],
        footer: 'FitnessApp Monitoring',
        ts: Math.floor(alert.timestamp.getTime() / 1000),
      }],
    };

    await fetch(this.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
  }

  private getColor(type: Alert['type']): string {
    switch (type) {
      case 'critical': return '#ff0000';
      case 'error': return '#ff6b6b';
      case 'warning': return '#ffa500';
      default: return '#36a64f';
    }
  }
}

// Discord alert channel
export class DiscordChannel implements AlertChannel {
  constructor(private webhookUrl: string) {}

  async send(alert: Alert): Promise<void> {
    const color = this.getColor(alert.type);
    const payload = {
      embeds: [{
        title: `🚨 ${alert.title}`,
        description: alert.message,
        color,
        fields: [
          { name: 'Type', value: alert.type, inline: true },
          { name: 'Source', value: alert.source, inline: true },
          { name: 'Time', value: alert.timestamp.toISOString(), inline: true },
        ],
        footer: { text: 'FitnessApp Monitoring' },
        timestamp: alert.timestamp.toISOString(),
      }],
    };

    await fetch(this.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
  }

  private getColor(type: Alert['type']): number {
    switch (type) {
      case 'critical': return 0xff0000;
      case 'error': return 0xff6b6b;
      case 'warning': return 0xffa500;
      default: return 0x36a64f;
    }
  }
}

// Email alert channel
export class EmailChannel implements AlertChannel {
  constructor(private apiKey: string, private fromEmail: string) {}

  async send(alert: Alert): Promise<void> {
    const payload = {
      from: this.fromEmail,
      to: process.env.ALERT_EMAIL_RECIPIENTS?.split(',') || [],
      subject: `🚨 FitnessApp Alert: ${alert.title}`,
      html: this.generateEmailHtml(alert),
    };

    await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
  }

  private generateEmailHtml(alert: Alert): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
          <h2 style="color: #dc3545; margin: 0;">🚨 FitnessApp Alert</h2>
          <h3 style="margin: 10px 0;">${alert.title}</h3>
          <p style="margin: 10px 0;">${alert.message}</p>
          
          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Type</td>
              <td style="padding: 8px; border: 1px solid #ddd;">${alert.type}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Source</td>
              <td style="padding: 8px; border: 1px solid #ddd;">${alert.source}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Time</td>
              <td style="padding: 8px; border: 1px solid #ddd;">${alert.timestamp.toISOString()}</td>
            </tr>
          </table>
          
          ${alert.metadata ? `
            <details style="margin: 20px 0;">
              <summary style="cursor: pointer; font-weight: bold;">Additional Details</summary>
              <pre style="background: #f1f3f4; padding: 10px; border-radius: 4px; overflow-x: auto;">
${JSON.stringify(alert.metadata, null, 2)}
              </pre>
            </details>
          ` : ''}
          
          <p style="margin: 20px 0 0 0; font-size: 12px; color: #666;">
            This alert was generated by FitnessApp monitoring system.
          </p>
        </div>
      </div>
    `;
  }
}

// Setup default alert rules
export function setupDefaultAlertRules() {
  const alertManager = AlertManager.getInstance();

  // High error rate
  alertManager.registerRule({
    id: 'high_error_rate',
    name: 'High Error Rate',
    condition: (data) => {
      const errorRate = data.errorRate || 0;
      return errorRate > 0.05; // 5% error rate
    },
    severity: 'high',
    cooldown: 15,
    channels: ['slack', 'email'],
    enabled: true,
  });

  // Slow response time
  alertManager.registerRule({
    id: 'slow_response_time',
    name: 'Slow Response Time',
    condition: (data) => {
      const avgResponseTime = data.avgResponseTime || 0;
      return avgResponseTime > 2000; // 2 seconds
    },
    severity: 'medium',
    cooldown: 10,
    channels: ['slack'],
    enabled: true,
  });

  // Database connection issues
  alertManager.registerRule({
    id: 'database_connection_failure',
    name: 'Database Connection Failure',
    condition: (data) => {
      return data.databaseStatus === 'unhealthy';
    },
    severity: 'critical',
    cooldown: 5,
    channels: ['slack', 'discord', 'email'],
    enabled: true,
  });

  // High memory usage
  alertManager.registerRule({
    id: 'high_memory_usage',
    name: 'High Memory Usage',
    condition: (data) => {
      const memoryUsage = data.memoryUsage || 0;
      return memoryUsage > 0.9; // 90% memory usage
    },
    severity: 'medium',
    cooldown: 20,
    channels: ['slack'],
    enabled: true,
  });

  // Low disk space
  alertManager.registerRule({
    id: 'low_disk_space',
    name: 'Low Disk Space',
    condition: (data) => {
      const diskUsage = data.diskUsage || 0;
      return diskUsage > 0.85; // 85% disk usage
    },
    severity: 'high',
    cooldown: 30,
    channels: ['slack', 'email'],
    enabled: true,
  });
}

// Setup alert channels
export function setupAlertChannels() {
  const alertManager = AlertManager.getInstance();

  if (process.env.SLACK_WEBHOOK_URL) {
    alertManager.registerChannel('slack', new SlackChannel(process.env.SLACK_WEBHOOK_URL));
  }

  if (process.env.DISCORD_WEBHOOK_URL) {
    alertManager.registerChannel('discord', new DiscordChannel(process.env.DISCORD_WEBHOOK_URL));
  }

  if (process.env.RESEND_API_KEY && process.env.ALERT_FROM_EMAIL) {
    alertManager.registerChannel('email', new EmailChannel(
      process.env.RESEND_API_KEY,
      process.env.ALERT_FROM_EMAIL
    ));
  }
}

// Initialize alerting system
export function initializeAlerting() {
  setupDefaultAlertRules();
  setupAlertChannels();
  
  console.log('Alerting system initialized');
}

export { AlertManager };
