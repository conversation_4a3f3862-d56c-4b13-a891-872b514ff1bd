// Service Worker Registration and Management

export interface ServiceWorkerRegistrationResult {
  success: boolean;
  registration?: ServiceWorkerRegistration;
  error?: Error;
}

export class ServiceWorkerManager {
  private static instance: ServiceWorkerManager;
  private registration: ServiceWorkerRegistration | null = null;
  private isSupported: boolean = false;

  constructor() {
    this.isSupported = 'serviceWorker' in navigator;
  }

  static getInstance(): ServiceWorkerManager {
    if (!ServiceWorkerManager.instance) {
      ServiceWorkerManager.instance = new ServiceWorkerManager();
    }
    return ServiceWorkerManager.instance;
  }

  async register(): Promise<ServiceWorkerRegistrationResult> {
    if (!this.isSupported) {
      const error = new Error('Service Worker not supported');
      console.warn('Service Worker not supported in this browser');
      return { success: false, error };
    }

    try {
      console.log('Registering Service Worker...');
      
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none',
      });

      this.registration = registration;

      // Handle updates
      registration.addEventListener('updatefound', () => {
        console.log('Service Worker update found');
        this.handleUpdate(registration);
      });

      // Check for updates periodically
      this.scheduleUpdateCheck();

      console.log('Service Worker registered successfully:', registration);
      return { success: true, registration };
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return { success: false, error: error as Error };
    }
  }

  private handleUpdate(registration: ServiceWorkerRegistration) {
    const newWorker = registration.installing;
    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
        // New service worker is available
        this.showUpdateNotification();
      }
    });
  }

  private showUpdateNotification() {
    // Show a notification to the user about the update
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('App Update Available', {
        body: 'A new version of FitnessApp is available. Refresh to update.',
        icon: '/icons/icon-192x192.png',
        tag: 'app-update',
        requireInteraction: true,
        actions: [
          { action: 'update', title: 'Update Now' },
          { action: 'dismiss', title: 'Later' },
        ],
      });
    } else {
      // Fallback: show in-app notification
      this.dispatchUpdateEvent();
    }
  }

  private dispatchUpdateEvent() {
    window.dispatchEvent(new CustomEvent('sw-update-available', {
      detail: { registration: this.registration }
    }));
  }

  private scheduleUpdateCheck() {
    // Check for updates every hour
    setInterval(() => {
      if (this.registration) {
        this.registration.update();
      }
    }, 60 * 60 * 1000);
  }

  async skipWaiting(): Promise<void> {
    if (!this.registration || !this.registration.waiting) {
      return;
    }

    // Tell the waiting service worker to skip waiting
    this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });

    // Reload the page to activate the new service worker
    window.location.reload();
  }

  async unregister(): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      const result = await this.registration.unregister();
      console.log('Service Worker unregistered:', result);
      return result;
    } catch (error) {
      console.error('Service Worker unregistration failed:', error);
      return false;
    }
  }

  getRegistration(): ServiceWorkerRegistration | null {
    return this.registration;
  }

  isServiceWorkerSupported(): boolean {
    return this.isSupported;
  }
}

// Background Sync Manager
export class BackgroundSyncManager {
  private static instance: BackgroundSyncManager;
  private registration: ServiceWorkerRegistration | null = null;

  static getInstance(): BackgroundSyncManager {
    if (!BackgroundSyncManager.instance) {
      BackgroundSyncManager.instance = new BackgroundSyncManager();
    }
    return BackgroundSyncManager.instance;
  }

  setRegistration(registration: ServiceWorkerRegistration) {
    this.registration = registration;
  }

  async scheduleSync(tag: string): Promise<void> {
    if (!this.registration || !('sync' in window.ServiceWorkerRegistration.prototype)) {
      console.warn('Background Sync not supported');
      return;
    }

    try {
      await this.registration.sync.register(tag);
      console.log(`Background sync scheduled: ${tag}`);
    } catch (error) {
      console.error(`Failed to schedule background sync: ${tag}`, error);
    }
  }

  async scheduleWorkoutSync(): Promise<void> {
    await this.scheduleSync('workout-sync');
  }

  async scheduleMessageSync(): Promise<void> {
    await this.scheduleSync('message-sync');
  }

  async scheduleProgressSync(): Promise<void> {
    await this.scheduleSync('progress-sync');
  }
}

// Push Notification Manager
export class PushNotificationManager {
  private static instance: PushNotificationManager;
  private registration: ServiceWorkerRegistration | null = null;

  static getInstance(): PushNotificationManager {
    if (!PushNotificationManager.instance) {
      PushNotificationManager.instance = new PushNotificationManager();
    }
    return PushNotificationManager.instance;
  }

  setRegistration(registration: ServiceWorkerRegistration) {
    this.registration = registration;
  }

  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('Notifications not supported');
      return 'denied';
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    const permission = await Notification.requestPermission();
    console.log('Notification permission:', permission);
    return permission;
  }

  async subscribeToPush(vapidPublicKey: string): Promise<PushSubscription | null> {
    if (!this.registration) {
      console.error('Service Worker not registered');
      return null;
    }

    if (!('PushManager' in window)) {
      console.warn('Push messaging not supported');
      return null;
    }

    try {
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey),
      });

      console.log('Push subscription created:', subscription);
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  async unsubscribeFromPush(): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      const subscription = await this.registration.pushManager.getSubscription();
      if (subscription) {
        const result = await subscription.unsubscribe();
        console.log('Push subscription removed:', result);
        return result;
      }
      return true;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }
}

// Initialize Service Worker
export async function initializeServiceWorker(): Promise<void> {
  if (typeof window === 'undefined') {
    return; // Skip on server-side
  }

  const swManager = ServiceWorkerManager.getInstance();
  const syncManager = BackgroundSyncManager.getInstance();
  const pushManager = PushNotificationManager.getInstance();

  const result = await swManager.register();
  
  if (result.success && result.registration) {
    syncManager.setRegistration(result.registration);
    pushManager.setRegistration(result.registration);

    // Request notification permission
    await pushManager.requestPermission();

    console.log('Service Worker initialized successfully');
  } else {
    console.error('Failed to initialize Service Worker:', result.error);
  }
}

// Utility function to check if app is running as PWA
export function isPWA(): boolean {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true;
}

// Utility function to check if app can be installed
export function canInstall(): boolean {
  return 'beforeinstallprompt' in window;
}
