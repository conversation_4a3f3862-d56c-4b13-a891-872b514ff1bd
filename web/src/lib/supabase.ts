import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import type { Database } from '@/types/database';

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Client-side Supabase client (for use in client components)
export const createClientSupabase = () => {
  return createClientComponentClient<Database>();
};

// Server-side Supabase client (for use in server components and API routes)
export const createServerSupabase = () => {
  return createServerComponentClient<Database>({ cookies });
};

// Admin Supabase client (for server-side operations that require elevated permissions)
export const createAdminSupabase = () => {
  if (!supabaseServiceKey) {
    throw new Error('Missing Supabase service role key');
  }
  
  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
};

// Default client for general use
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
});

// Utility functions for common operations
export const supabaseUtils = {
  // Get current user
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  // Get user profile
  async getUserProfile(userId?: string) {
    const targetUserId = userId || (await this.getCurrentUser())?.id;
    if (!targetUserId) throw new Error('No user ID provided');

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', targetUserId)
      .single();

    if (error) throw error;
    return data;
  },

  // Update user profile
  async updateUserProfile(updates: Partial<Database['public']['Tables']['users']['Update']>) {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('No authenticated user');

    const { data, error } = await supabase
      .from('users')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', user.id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Get user's active program
  async getUserActiveProgram(userId?: string) {
    const targetUserId = userId || (await this.getCurrentUser())?.id;
    if (!targetUserId) throw new Error('No user ID provided');

    const { data, error } = await supabase
      .from('user_programs')
      .select(`
        *,
        workout_programs (*)
      `)
      .eq('user_id', targetUserId)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
    return data;
  },

  // Get workout logs for a user
  async getUserWorkoutLogs(userId?: string, limit = 10) {
    const targetUserId = userId || (await this.getCurrentUser())?.id;
    if (!targetUserId) throw new Error('No user ID provided');

    const { data, error } = await supabase
      .from('workout_logs')
      .select(`
        *,
        workouts (
          name,
          workout_programs (name)
        )
      `)
      .eq('user_id', targetUserId)
      .order('completed_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data;
  },

  // Get progress tracking data
  async getProgressData(userId?: string, trackingType?: string, limit = 30) {
    const targetUserId = userId || (await this.getCurrentUser())?.id;
    if (!targetUserId) throw new Error('No user ID provided');

    let query = supabase
      .from('progress_tracking')
      .select('*')
      .eq('user_id', targetUserId)
      .order('date', { ascending: false });

    if (trackingType) {
      query = query.eq('tracking_type', trackingType);
    }

    if (limit) {
      query = query.limit(limit);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data;
  },

  // Upload file to storage
  async uploadFile(bucket: string, path: string, file: File) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) throw error;
    return data;
  },

  // Get public URL for a file
  getPublicUrl(bucket: string, path: string) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  },

  // Delete file from storage
  async deleteFile(bucket: string, path: string) {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) throw error;
  },

  // Real-time subscription helper
  subscribeToTable<T = any>(
    table: keyof Database['public']['Tables'],
    callback: (payload: any) => void,
    filter?: string
  ) {
    let channel = supabase
      .channel(`${table}_changes`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: table as string,
          filter,
        },
        callback
      );

    return channel.subscribe();
  },

  // Unsubscribe from real-time updates
  unsubscribe(subscription: any) {
    return supabase.removeChannel(subscription);
  },

  // Batch operations helper
  async batchInsert<T>(
    table: keyof Database['public']['Tables'],
    records: T[],
    batchSize = 100
  ) {
    const results = [];
    
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      const { data, error } = await supabase
        .from(table as any)
        .insert(batch)
        .select();

      if (error) throw error;
      results.push(...(data || []));
    }

    return results;
  },

  // Health check
  async healthCheck() {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .limit(1);

      return { healthy: !error, error };
    } catch (error) {
      return { healthy: false, error };
    }
  },
};

// Error handling utilities
export class SupabaseError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'SupabaseError';
  }
}

export const handleSupabaseError = (error: any): never => {
  if (error?.code) {
    throw new SupabaseError(error.message, error.code, error.details);
  }
  throw new Error(error?.message || 'An unknown error occurred');
};

// Type helpers
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];

// Export types for convenience
export type User = Tables<'users'>;
export type WorkoutProgram = Tables<'workout_programs'>;
export type Workout = Tables<'workouts'>;
export type UserProgram = Tables<'user_programs'>;
export type WorkoutLog = Tables<'workout_logs'>;
export type ProgressTracking = Tables<'progress_tracking'>;
export type Conversation = Tables<'conversations'>;
export type Message = Tables<'messages'>;
