// Comprehensive monitoring and alerting system

import * as Sentry from '@sentry/nextjs';

// Initialize Sentry for error tracking
export function initializeMonitoring() {
  if (process.env.NODE_ENV === 'production') {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 0.1,
      profilesSampleRate: 0.1,
      integrations: [
        new Sentry.BrowserTracing({
          tracePropagationTargets: [
            'localhost',
            /^https:\/\/.*\.fitnessapp\.com/,
          ],
        }),
      ],
      beforeSend(event) {
        // Filter out non-critical errors
        if (event.exception) {
          const error = event.exception.values?.[0];
          if (error?.type === 'ChunkLoadError') {
            return null; // Don't send chunk load errors
          }
        }
        return event;
      },
    });
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Track page load times
  trackPageLoad(pageName: string) {
    if (typeof window !== 'undefined' && window.performance) {
      const loadTime = window.performance.timing.loadEventEnd - 
                      window.performance.timing.navigationStart;
      
      this.recordMetric(`page_load_${pageName}`, loadTime);
      
      // Send to analytics
      if (typeof gtag !== 'undefined') {
        gtag('event', 'page_load_time', {
          page_name: pageName,
          value: loadTime,
        });
      }
    }
  }

  // Track API response times
  trackApiCall(endpoint: string, duration: number, status: number) {
    this.recordMetric(`api_${endpoint}`, duration);
    
    // Track errors
    if (status >= 400) {
      this.recordMetric(`api_error_${endpoint}`, 1);
      
      Sentry.addBreadcrumb({
        message: `API Error: ${endpoint}`,
        level: 'error',
        data: { duration, status },
      });
    }
  }

  // Track user interactions
  trackUserAction(action: string, duration?: number) {
    if (duration) {
      this.recordMetric(`user_action_${action}`, duration);
    }
    
    // Send to analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', 'user_action', {
        action_name: action,
        value: duration || 1,
      });
    }
  }

  // Record custom metrics
  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // Keep only last 100 values
    if (values.length > 100) {
      values.shift();
    }
    
    // Send to external monitoring if configured
    this.sendToExternalMonitoring(name, value);
  }

  // Get metric statistics
  getMetricStats(name: string) {
    const values = this.metrics.get(name) || [];
    if (values.length === 0) return null;
    
    const sorted = [...values].sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);
    
    return {
      count: values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: sum / values.length,
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
    };
  }

  // Send metrics to external monitoring service
  private sendToExternalMonitoring(name: string, value: number) {
    // Send to custom monitoring endpoint
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric: name,
          value,
          timestamp: Date.now(),
        }),
      }).catch(() => {
        // Silently fail to avoid affecting user experience
      });
    }
  }
}

// Health check system
export class HealthChecker {
  private static instance: HealthChecker;
  private checks: Map<string, () => Promise<boolean>> = new Map();
  private lastResults: Map<string, { status: boolean; timestamp: number }> = new Map();

  static getInstance(): HealthChecker {
    if (!HealthChecker.instance) {
      HealthChecker.instance = new HealthChecker();
    }
    return HealthChecker.instance;
  }

  // Register health check
  registerCheck(name: string, checkFn: () => Promise<boolean>) {
    this.checks.set(name, checkFn);
  }

  // Run all health checks
  async runAllChecks(): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {};
    
    for (const [name, checkFn] of this.checks) {
      try {
        const result = await Promise.race([
          checkFn(),
          new Promise<boolean>((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), 5000)
          ),
        ]);
        
        results[name] = result;
        this.lastResults.set(name, { status: result, timestamp: Date.now() });
        
        if (!result) {
          this.alertHealthCheckFailure(name);
        }
      } catch (error) {
        results[name] = false;
        this.lastResults.set(name, { status: false, timestamp: Date.now() });
        this.alertHealthCheckFailure(name, error);
      }
    }
    
    return results;
  }

  // Get last health check results
  getLastResults() {
    const results: { [key: string]: { status: boolean; timestamp: number } } = {};
    for (const [name, result] of this.lastResults) {
      results[name] = result;
    }
    return results;
  }

  // Alert on health check failure
  private alertHealthCheckFailure(name: string, error?: any) {
    Sentry.captureException(new Error(`Health check failed: ${name}`), {
      tags: { healthCheck: name },
      extra: { error },
    });
    
    // Send to alerting system
    this.sendAlert('health_check_failure', {
      check: name,
      error: error?.message,
      timestamp: new Date().toISOString(),
    });
  }

  // Send alert to external systems
  private sendAlert(type: string, data: any) {
    // Send to webhook endpoints
    const webhooks = [
      process.env.SLACK_WEBHOOK_URL,
      process.env.DISCORD_WEBHOOK_URL,
    ].filter(Boolean);
    
    webhooks.forEach(webhook => {
      fetch(webhook!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: `🚨 FitnessApp Alert: ${type}`,
          attachments: [{
            color: 'danger',
            fields: Object.entries(data).map(([key, value]) => ({
              title: key,
              value: String(value),
              short: true,
            })),
          }],
        }),
      }).catch(() => {
        // Silently fail
      });
    });
  }
}

// Error boundary for React components
export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    Sentry.captureException(error, {
      contexts: { react: errorInfo },
    });
    
    PerformanceMonitor.getInstance().recordMetric('react_error', 1);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error!} />;
    }
    
    return this.props.children;
  }
}

// Default error fallback component
function DefaultErrorFallback({ error }: { error: Error }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-8 w-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-gray-800">
              Something went wrong
            </h3>
            <div className="mt-2 text-sm text-gray-500">
              <p>We're sorry, but something unexpected happened. Please try refreshing the page.</p>
            </div>
            <div className="mt-4">
              <button
                onClick={() => window.location.reload()}
                className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Initialize default health checks
export function setupDefaultHealthChecks() {
  const healthChecker = HealthChecker.getInstance();
  
  // Database connectivity check
  healthChecker.registerCheck('database', async () => {
    try {
      const response = await fetch('/api/health/database');
      return response.ok;
    } catch {
      return false;
    }
  });
  
  // External API checks
  healthChecker.registerCheck('supabase', async () => {
    try {
      const response = await fetch(process.env.NEXT_PUBLIC_SUPABASE_URL + '/rest/v1/', {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        },
      });
      return response.ok;
    } catch {
      return false;
    }
  });
  
  // Memory usage check
  healthChecker.registerCheck('memory', async () => {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      return usageRatio < 0.9; // Alert if using more than 90% of available memory
    }
    return true;
  });
}

// Export monitoring utilities
export const monitoring = {
  performance: PerformanceMonitor.getInstance(),
  health: HealthChecker.getInstance(),
  initializeMonitoring,
  setupDefaultHealthChecks,
  ErrorBoundary,
};
