{"name": "<PERSON><PERSON><PERSON><PERSON>", "main": "expo-router/entry", "version": "0.1.0", "private": true, "scripts": {"dev": "expo start", "android": "expo run:android", "ios": "expo run:ios", "build": "expo prebuild", "build:web": "expo export --platform web", "test": "jest", "lint": "expo lint", "build:development": "eas build --profile development", "build:preview": "eas build --profile preview", "build:production": "eas build --profile production", "build:ios": "eas build --platform ios --profile production", "build:android": "eas build --platform android --profile production", "submit:ios": "eas submit --platform ios --profile production", "submit:android": "eas submit --platform android --profile production", "update": "eas update", "update:preview": "eas update --branch preview", "update:production": "eas update --branch production"}, "dependencies": {"@babel/runtime": "^7.27.6", "@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.1.1", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/slider": "^4.5.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.3.16", "@supabase/supabase-js": "^2.50.0", "expo": "^53.0.0", "expo-av": "~15.0.1", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-device": "~6.0.2", "expo-file-system": "~18.0.4", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-image-picker": "~16.1.0", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-media-library": "~17.1.1", "expo-notifications": "~0.30.3", "expo-router": "~5.0.2", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "jest": "^30.0.0", "lucide-react-native": "^0.515.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.0", "react-native": "0.79.1", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "yup": "^1.4.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "supabase": "^1.200.3", "typescript": "~5.8.3"}}