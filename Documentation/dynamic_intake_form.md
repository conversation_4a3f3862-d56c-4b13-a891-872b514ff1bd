**Feature 2: Dynamic Intake Form System**

**Goal:** Comprehensive onboarding questionnaire that captures user goals, equipment access, experience level, schedule constraints, and training preferences to inform AI program generation.

**API relationships:**

* Supabase Database (PostgreSQL):

* `intake_forms` table: CRUD operations for storing intake data.

* `profiles` table: Update `gender_preference` after intake for theming.

**Detailed requirements:**

* Multi-step form with conditional logic.

* Progressive form completion with save/resume capability (save on each step completion).

* Intuitive UI guiding user through questions about goals, experience, available days/time, equipment.

* Equipment selection with custom additions.

* Goal-setting with timeline parameters.

* Schedule availability mapping.

* Data validation and sanitization for all inputs.

* Conditional question flow based on user responses (e.g., detailed equipment list if "Home Gym" selected).

* Data must be securely stored and linked to user accounts (`user_id`).

* Form completion triggers the AI program generation process.

* Gender collected here will also update `profiles.gender_preference` for app theming.

**Implementation guide:**

* **Architecture overview:**

* **Diagram:**

```

Client (React Native App - Intake Screens)

|-- Local State (React Hook Form / Zustand for current step)

|-- AsyncStorage (Optional: for unsaved draft of current step)

V

Supabase Backend

|-- Database (PostgreSQL - 'intake_forms' table)

|-- (Trigger: On intake_forms submission, can initiate Supabase Function for AI program generation)

```

* **Tech-stack justification:**

* React Native: For UI.

* Zustand/Redux/React Context: For managing complex form state across multiple steps, including conditional logic and data persistence between steps. `react-hook-form` for individual step validation.

* Supabase DB: To store structured intake data.

* **Deployment:** Part of the main client app.

* **DB schema:**

* **ER Diagram (Conceptual):**

```

auth.users

id (uuid, pk)

...

public.profiles

id (uuid, pk, fk to auth.users.id)

gender_preference (text ENUM('Woman', 'Man', 'Neutral'))

...

public.intake_forms

id (uuid, pk, default uuid_generate_v4())

user_id (uuid, unique, fk to auth.users.id ON DELETE CASCADE)

created_at (timestampz, default now())

updated_at (timestampz, default now())

submitted_at (timestampz, nullable)

status (text ENUM('draft', 'submitted', 'processing', 'completed') default 'draft')

-- Step 1: Personal Basics (Example)

gender (text ENUM('Woman', 'Man'), not null) -- For AI, separate from theme preference if needed

age (integer, not null, check (age > 0 AND age < 120))

height_cm (integer, not null, check (height_cm > 50 AND height_cm < 300))

weight_kg (numeric(5,2), not null, check (weight_kg > 20 AND weight_kg < 500))

-- Step 2: Fitness Goals & Experience

primary_fitness_goal (text, not null) -- E.g., 'Muscle Gain', 'Fat Loss', 'Strength'

training_experience_level (text ENUM('Beginner', 'Intermediate', 'Advanced'), not null)

goal_timeline_months (integer, nullable) -- E.g., 3, 6, 12, or null for ongoing

-- Step 3: Equipment Access

equipment_access_type (text ENUM('Full Gym', 'Home Gym Basic', 'Bodyweight Only'), not null)

available_equipment (jsonb, nullable) -- E.g., {"barbell": true, "dumbbells": [5,10,15], "bench": true}

custom_equipment_notes (text, nullable)

-- Step 4: Schedule & Availability

training_days_per_week (integer, not null, check (training_days_per_week >= 2 AND training_days_per_week <= 6))

preferred_training_days (text[], nullable) -- E.g., ARRAY['Monday', 'Wednesday', 'Friday']

preferred_session_duration_minutes (integer, not null) -- E.g., 30, 45, 60, 90

-- Step 5: Injuries & Preferences

injuries_limitations (text, nullable)

training_preferences_notes (text, nullable) -- Any other specific notes for AI/Coach

```

* **Table definitions:**

* `intake_forms`:

* `id`: UUID, PK.

* `user_id`: UUID, FK to `auth.users.id`, UNIQUE (one intake form per user).

* `status`: TEXT ENUM ('draft', 'submitted', 'processing', 'completed'). `draft` allows user to save and resume. `submitted` locks the form and triggers AI.

* Fields for each question as detailed above (gender, age, goals, equipment, schedule, injuries).

* `available_equipment`: JSONB to store a structured list of selected equipment.

* `preferred_training_days`: ARRAY of TEXT for selected days.

* **Indexes:**

* `intake_forms(user_id)` (auto by unique constraint).

* **Migrations:** SQL script for creating `intake_forms` table, RLS policies.

```sql

CREATE TABLE public.intake_forms (

id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

user_id UUID UNIQUE NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

created_at TIMESTAMPTZ DEFAULT NOW(),

updated_at TIMESTAMPTZ DEFAULT NOW(),

submitted_at TIMESTAMPTZ,

status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'processing', 'completed')),

gender TEXT NOT NULL CHECK (gender IN ('Woman', 'Man')),

age INTEGER NOT NULL CHECK (age > 0 AND age < 120),

height_cm INTEGER NOT NULL CHECK (height_cm > 50 AND height_cm < 300),

weight_kg NUMERIC(5,2) NOT NULL CHECK (weight_kg > 20 AND weight_kg < 500),

primary_fitness_goal TEXT NOT NULL,

training_experience_level TEXT NOT NULL CHECK (training_experience_level IN ('Beginner', 'Intermediate', 'Advanced')),

goal_timeline_months INTEGER,

equipment_access_type TEXT NOT NULL CHECK (equipment_access_type IN ('Full Gym', 'Home Gym Basic', 'Bodyweight Only')),

available_equipment JSONB,

custom_equipment_notes TEXT,

training_days_per_week INTEGER NOT NULL CHECK (training_days_per_week >= 2 AND training_days_per_week <= 6),

preferred_training_days TEXT[],

preferred_session_duration_minutes INTEGER NOT NULL,

injuries_limitations TEXT,

training_preferences_notes TEXT

);

-- RLS Policies

ALTER TABLE public.intake_forms ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own intake form."

ON public.intake_forms FOR ALL

USING (auth.uid() = user_id)

WITH CHECK (auth.uid() = user_id);

-- Coach policy (if coach needs to read it)

-- CREATE POLICY "Coaches can read intake forms of their clients."

-- ON public.intake_forms FOR SELECT

-- USING (is_coach() AND user_id IN (SELECT client_id FROM coach_clients WHERE coach_id = auth.uid()));

CREATE TRIGGER on_intake_form_updated

BEFORE UPDATE ON public.intake_forms

FOR EACH ROW

EXECUTE PROCEDURE public.handle_updated_at();

-- Trigger to update profiles.gender_preference upon intake submission

CREATE OR REPLACE FUNCTION public.update_profile_gender_from_intake()

RETURNS TRIGGER AS $$

BEGIN

IF NEW.status = 'submitted' AND NEW.gender IS NOT NULL THEN

UPDATE public.profiles

SET gender_preference = NEW.gender -- Make sure NEW.gender is one of 'Woman', 'Man', 'Neutral'

WHERE id = NEW.user_id;

END IF;

RETURN NEW;

END;

$$ LANGUAGE plpgsql;

CREATE TRIGGER on_intake_submitted_update_profile_gender

AFTER UPDATE OF status ON public.intake_forms

FOR EACH ROW

WHEN (OLD.status IS DISTINCT FROM 'submitted' AND NEW.status = 'submitted')

EXECUTE PROCEDURE public.update_profile_gender_from_intake();

```

* **API design:**

* **Get Intake Form Data (for resume):**

* Method: `GET` via Supabase client `supabase.from('intake_forms').select('*').eq('user_id', userId).maybeSingle()`

* Response (Success 200): Intake form data object or `null` if not started.

* **Save/Upsert Intake Form Data (Save progress per step or final submit):**

* Method: `POST` (Upsert) via Supabase client `supabase.from('intake_forms').upsert({ user_id: userId, ...stepData, status: 'draft' }, { onConflict: 'user_id' })`

* Request: Partial or full intake form data.

* Response (Success 200/201): Upserted data.

* **Final Submit Intake Form:**

* Method: `POST` (Update) via Supabase client `supabase.from('intake_forms').update({ ...fullData, status: 'submitted', submitted_at: new Date().toISOString() }).eq('user_id', userId)`

* Request: Full intake form data, setting status to 'submitted'.

* Response (Success 200): Updated data. This action will trigger AI program generation flow.

* **Auth:** User must be authenticated. RLS policies enforce access.

* **Errors:** Standard HTTP errors for validation failures if not caught client-side, or DB errors.

* **Frontend structure:**

* **Component hierarchy:**

* `IntakeStackNavigator.tsx`: Manages flow through `IntakeWelcomeScreen`, `IntakeStep1Screen`, ..., `IntakeReviewSubmitScreen`, `IntakeProcessingScreen`.

* `FormStepLayout.tsx`: Common layout for each step (header, progress, back/next buttons).

* Individual input components (e.g., `GenderSelector`, `EquipmentPicker`, `ScheduleMapper`).

* `ProgressIndicator.tsx`: Visual progress bar/stepper.

* **State management (`intakeFormStore` using Zustand/Redux):**

* Holds all form data, current step, validation status per step.

* `actions.loadDraft()`: Fetches existing data from Supabase on init.

* `actions.updateStepData(step, data)`: Updates store, optionally auto-saves to Supabase as 'draft'.

* `actions.submitForm()`: Validates all data, submits to Supabase with 'submitted' status.

* **Navigation:** `react-navigation`. Navigate between steps. Prevent navigation if current step invalid.

* **CRUD operations (Intake Form - Client Perspective):**

* **Create/Update (Upsert):** User fills form. `intakeService.saveDraft(formData)` on "Next" or "Save & Exit". `intakeService.submitIntake(fullFormData)` on final submission.

* **Read:** On entering intake flow, `intakeService.getDraft()` to pre-fill if exists.

* **Delete:** Not directly by user. Data might be cleared if user resets account (future).

* **Validation:**

* Client-side: Per field (required, type, range) and cross-field within a step. `react-hook-form` + `yup`. Overall validation before final submission.

* Server-side: DB constraints (NOT NULL, CHECKs, ENUMs).

* **Pagination:** N/A (form steps are not traditional pagination).

* **UX flow:**

* Refer to `<other-considerations>`: "Dynamic Intake Form System" screens (Welcome, Step 1-N, Review/Submit).

* "Intake Form Submission & Program Generation Sequence": Screen for "Submitting / Processing Confirmation" providing feedback on next steps (AI generation, coach review, timeline).

* Save progress: Data is saved to backend as `status: 'draft'` upon completing each step and pressing "Next". User can close app and resume later.

* Conditional logic:

```pseudocode

IF equipment_access_type IS "Home Gym" THEN

SHOW detailed_home_equipment_selection

ELSE IF equipment_access_type IS "Full Gym" THEN

SHOW different_detailed_gym_equipment_selection // or assume most things available

ELSE // Bodyweight

HIDE detailed_equipment_selection

END IF

```

* **Security:**

* RLS: Users can only access/modify their own intake form.

* Input Sanitization: Standard client-side (trimming) and rely on Supabase for query sanitization. JSONB fields need careful handling if structured by user text input (prefer selections).

* Data privacy: Intake data is sensitive. Ensure compliance with privacy policy.

* **Testing:**

* **Unit Tests:**

* Validation rules for each field/step.

* Conditional rendering logic for form fields.

* State transitions in `intakeFormStore`.

* **Integration Tests:**

* Saving draft data to Supabase and reloading.

* Full form submission and status change.

* Triggering `update_profile_gender_from_intake`.

* **E2E Tests:**

* User completes entire intake form across all steps, including conditional paths.

* User saves form, closes app, reopens, and resumes.

* Verify submission triggers next state (e.g., "processing" message).

* **Data management:**

* **Caching:** Form state managed by `intakeFormStore`. Persist to Supabase frequently (on step completion).

* **Lifecycle:** Form created once per user. Can be in 'draft' then 'submitted'. After 'submitted', it's generally read-only for the client.

* **Real-time needs:** No, user actively fills form.

* **Logging & error handling:**

* **Structured logs:**

* `Intake.stepCompleted {stepNumber}`

* `Intake.formSubmitted {userId}`

* `Intake.saveDraft.failure {error}`

* `Intake.submitForm.failure {error}`

* **Alerts:** High failure rate on form submission.

* **Recovery:**

* Save draft allows recovery from app crash/closure.

* Clear error messages for validation issues.

* Retry mechanism for saving data if network fails.
