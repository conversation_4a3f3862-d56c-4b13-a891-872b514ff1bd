## Overall System-Wide Considerations (Not tied to a single feature but applicable to many)

These are aspects that need to be consistently applied across features.

**1. Overall Architecture & Tech Stack Justification (Reiteration)**

* **Frontend:** React Native (Expo, TypeScript)

* **Justification:** Cross-platform development (iOS, Android) from a single codebase, strong typing with TypeScript for maintainability, large community, rich ecosystem of libraries. Expo simplifies build processes, updates, and provides useful modules (like `expo-av`, `expo-secure-store`, `expo-sharing`, push notifications).

* **Backend:** Supabase (Auth, PostgreSQL DB, Storage, Functions)

* **Justification:** BaaS significantly reduces backend development overhead for MVP. Provides integrated authentication, a robust PostgreSQL database with RLS, serverless functions for custom logic (AI interaction, CSV export), and file storage. Realtime capabilities can be leveraged later.

* **AI Model:** OpenAI GPT (or similar custom GPTs)

* **Justification:** Powerful LLM for generating personalized workout programs and feedback drafts based on complex user inputs. API accessibility.

* **State Management (Frontend):** Zustand or Redux Toolkit (with React Context for simpler cases)

* **Justification:** Zustand for a simpler, more modern approach to global state. Redux Toolkit if complex state interactions and middleware are heavily anticipated. React Context for localized state sharing.

* **Navigation (Frontend):** `react-navigation`

* **Justification:** De-facto standard for navigation in React Native, feature-rich and customizable.

* **Form Handling (Frontend):** `react-hook-form` with a schema validation library like `yup` or `zod`.

* **Justification:** Performance, ease of use, handles validation effectively.

* **Analytics:** PostHog

* **Justification:** Product analytics, feature flagging, A/B testing capabilities. Self-hosting option available for data control. Good for understanding user behavior.

* **Error Reporting:** Sentry (or similar like Bugsnag)

* **Justification:** Comprehensive error tracking and reporting for frontend and Supabase Functions.

* **Deployment:**

* Frontend: EAS Build (Expo Application Services) for app store submissions. EAS Update for over-the-air updates.

* Backend: Supabase handles its own cloud deployment. Supabase Functions are deployed via CLI.

**2. Global Data Management Strategies**

* **Data Flow:** Client -> Supabase (DB, Auth, Functions) -> OpenAI (for AI features) -> Supabase -> Client.

* **Caching (Client-Side):**

* Use `react-query` or SWR for server state management. This handles caching, background updates, stale-while-revalidate for fetched data like programs, history, profile.

* Securely cache session tokens using `expo-secure-store`.

* Cache user profile and settings in global state (`userStore`, `themeStore`) once fetched.

* **Data Lifecycle & Retention:**

* User account data: Retained as long as account is active.

* Intake form data: Retained, as it's fundamental to program generation.

* Workout programs & logs: Retained, core value of the app.

* Weekly check-ins & feedback: Retained for historical context.

* Personal notes: Retained until deleted by user.

* **Data Deletion Policy:** Upon user account deletion (post-MVP feature), or after 30 days post-subscription cancellation (if subscriptions are implemented), user data should be hard deleted or anonymized where legally required/appropriate, after offering data export.

* **Real-time Needs (MVP vs. Future):**

* MVP: Primarily relies on push notifications for asynchronous updates (new feedback, program ready). Data is generally fetched on navigation or pull-to-refresh.

* Future: Supabase Realtime can be used for live updates (e.g., coach seeing client activity, chat features).

**3. UI/UX Consistency & Theming**

* **Color Schemes:**

* **Gender-Neutral (Pre-Intake/Auth):** White BG (#FFFFFF), Black text (#000000), Neutral Blue Accent (#508C9B), Light Grey (#ECECEC / #D0D0D0 for borders).

* **Gender-Specific (Post-Intake):**

* **Women:** White BG (#FFFFFF), Primary Purple Accent (#B37399), Secondary Deep Purple (#2b3568) or Black text.

* **Men:** White BG (#FFFFFF), Primary Gun Metal Grey Accent (#353E43) with White Text for headers, Black text for body.

* Theme switching logic implemented in `themeStore` and applied globally.

* **Typography:** Consistent font family (e.g., Inter), weights, and scaling across the app. Use `react-native-responsive-fontsize` or similar for adaptability.

* **Layout & Spacing:** Consistent use of padding, margins, and a grid system (even if conceptual) for visual harmony.

* **Component Library:** Develop a set of reusable UI components (Buttons, Inputs, Cards, Modals, etc.) styled according to the theme to ensure consistency.

* **Loading States:** Standardized loading indicators (spinners, skeletons) for network requests and data processing.

* **Error States:** Consistent presentation of error messages (inline for forms, toasts/snackbars for general errors, full-screen for critical failures).

* **Empty States:** Meaningful empty state messages and visuals for lists or sections without data.

* **Accessibility (WCAG AA):**

* Sufficient color contrast for all themes.

* Proper use of ARIA-like props for React Native (`accessible`, `accessibilityLabel`, `accessibilityRole`, `accessibilityState`).

* Touch targets minimum 44x44dp.

* Support for dynamic font sizes (OS-level accessibility settings).

**4. Security (Global Perspective)**

* **Authentication & Authorization:**

* JWTs handled by Supabase Auth. Secure storage of tokens on client.

* RLS (Row Level Security) in PostgreSQL is the cornerstone of data access control. Policies must be meticulously defined for all tables and roles (`anon`, `authenticated`, `coach`).

* Role management within `profiles` table.

* **Input Sanitization & Validation:**

* Client-side: Validate all user inputs before sending to backend (`react-hook-form` + `yup`/`zod`). Sanitize by trimming, etc.

* Server-side: Supabase client libraries use parameterized queries (protects against SQLi). DB constraints (CHECK, NOT NULL, FKs) provide data integrity. Supabase Functions should validate their inputs.

* **API Security:**

* All communication via HTTPS (enforced by Supabase).

* Store external API keys (OpenAI) securely as Supabase Function secrets. Never expose them on the client.

* Rate limiting configured on Supabase and Functions to prevent abuse.

* **Data Privacy:**

* Adhere to relevant data privacy regulations (e.g., GDPR, CCPA).

* Clear privacy policy.

* Anonymize or pseudomynize data sent to third parties (like AI) where possible.

* Secure data at rest (Supabase manages this) and in transit (HTTPS).

* **Dependency Management:** Regularly update libraries (React Native, Expo, Supabase client, etc.) to patch security vulnerabilities. Use tools like `npm audit` or Snyk.

* **OWASP Mobile Top 10:** Consider these during development and testing (e.g., M1: Improper Platform Usage, M2: Insecure Data Storage, M4: Insecure Authentication, M5: Insufficient Cryptography, M7: Client Code Quality, M8: Code Tampering).

**5. Testing Strategy (Global)**

* **Pyramid:**

* **Unit Tests (Jest, React Testing Library):** Largest number. Test individual functions, components, store logic in isolation. Mock dependencies.

* **Integration Tests (Jest, React Testing Library, potentially with MSW for API mocking):** Test interaction between components, services, and mocked backend responses. Test Supabase Function logic with a test DB.

* **E2E Tests (Detox, Maestro):** Smallest number, but crucial. Test full user flows through the actual application UI, interacting with a staging/test Supabase backend.

* **Types of Tests for Key Areas:**

* **Auth:** Login, registration, logout, session refresh, password reset.

* **Forms:** Validation, submission, conditional logic.

* **API Interactions:** Correct request formatting, response handling, error states.

* **State Management:** Correct state updates and selectors.

* **Navigation:** Transitions between screens, deep linking.

* **Permissions (RLS):** While hard to E2E test exhaustively, integration tests for services should cover scenarios assuming different user roles. Backend tests for RLS policies.

* **Performance Testing:**

* Manual profiling using Flipper or React DevTools.

* Automated (Post-MVP): Tools to measure app startup, screen transition times, API response times under load.

* **Security Testing:**

* Manual penetration testing (Post-MVP).

* Code reviews focused on security aspects.

* Automated vulnerability scanning for dependencies.

* **CI/CD:** Integrate tests into CI/CD pipeline (e.g., GitHub Actions, EAS Build) to run automatically on pushes/PRs.

**6. Logging, Monitoring & Error Handling (Global)**

* **Structured Logging:**

* **Frontend (PostHog/Sentry):** User actions, navigation events, key lifecycle events, API call successes/failures (excluding sensitive data in logs).

* **Backend (Supabase Functions - Sentry/Supabase Logs):** Function invocation, parameters (non-sensitive), interactions with external APIs, errors, execution duration.

* **Error Reporting (Sentry):**

* Capture unhandled exceptions in frontend and backend.

* Include stack traces, device info, user context (ID, not PII) for easier debugging.

* **Monitoring & Alerting (Supabase Dashboard, Sentry, custom alerts):**

* **System Health:** Supabase platform status, DB performance (CPU, RAM, connections), Function execution rates/errors.

* **Key Metrics:** Auth success/failure rates, API endpoint error rates, critical feature usage funnels.

* **Alerts:** For critical errors, spikes in error rates, performance degradation.

* **Client-Side Error Handling:**

* Graceful degradation.

* User-friendly error messages (avoiding technical jargon).

* Retry mechanisms for transient network errors.

* "Something went wrong" screens for unrecoverable errors, with option to contact support or retry.

* **Log Levels:** Use standard log levels (DEBUG, INFO, WARN, ERROR) for better filtering and analysis.

**7. Notifications Strategy**

* **Push Notifications (Expo Push Notifications / FCM / APNS):**

* Workout program ready.

* New coach feedback published.

* Weekly check-in window open.

* Weekly check-in reminder.

* (Future) Streaks, achievements, social interactions.

* **In-App Notifications/Badges:**

* Unread feedback.

* Pending check-in.

* New items on dashboard.

* **Management:**

* User settings to control notification preferences (Post-MVP).

* Backend (Supabase Function or scheduled job) to trigger time-based or event-based notifications.

* Store push tokens securely, associated with user ID.

* Handle token refresh and unregistration.

* **Deep Linking:** Notifications should deep link into the relevant part of the app.

**8. Timezone Handling**

* **Storage:** Store all timestamps in Supabase (PostgreSQL `TIMESTAMPTZ`) in UTC.

* **Client-Side Logic:**

* Obtain user's local timezone (e.g., `Intl.DateTimeFormat().resolvedOptions().timeZone` or allow user to set in profile).

* Use a library like `date-fns-tz` or `moment-timezone` for reliable conversions and display of dates/times in the user's local timezone.

* Critical for "Weekly Check-in" window (Sunday 6 AM - 11:59 PM local time). All calculations for availability must be done using the user's local time.

* When sending date/times to backend that are meant to be "local" (like a check-in date for a specific local Sunday), ensure the UTC conversion is handled correctly or the backend understands the intent.

## File System

* Frontend/ (React Native with Expo, TypeScript)

    * src/

        * screens/

            * Auth/

                * SplashScreen.tsx

                * AuthSelectionScreen.tsx

                * RegistrationScreen.tsx

                * LoginScreen.tsx

                * ForgotPasswordScreen.tsx

            * Intake/

                * IntakeWelcomeScreen.tsx

                * IntakeStep1Screen.tsx

                * ...

                * IntakeReviewSubmitScreen.tsx

                * IntakeProcessingScreen.tsx

            * MainApp/

                * Dashboard/

                    * ClientProgressOverviewScreen.tsx

                * Workouts/

                    * WeeklyWorkoutScheduleScreen.tsx

                    * WorkoutDetailsScreen.tsx

                    * ExerciseDetailScreen.tsx

                    * ActiveWorkoutSessionScreen.tsx

                    * WorkoutSummaryScreen.tsx

                * Tracking/

                    * WorkoutHistoryListScreen.tsx

                    * WorkoutHistoryDetailScreen.tsx

                * CheckIn/

                    * WeeklyCheckInPrompt.tsx (as part of Dashboard or a dedicated screen)

                    * WeeklyCheckInFormScreen.tsx

                    * CheckInHistoryListScreen.tsx

                    * CheckInHistoryDetailScreen.tsx

                * Profile/

                    * ProfileManagementScreen.tsx

                    * EditProfileScreen.tsx

                    * ChangePasswordScreen.tsx

                    * DataExportScreen.tsx

                * Notes/

                    * NotesListScreen.tsx

                    * AddEditNoteScreen.tsx

                * Common/ (Shared components across main app screens)

                    * CoachFeedbackDisplayModal.tsx

            * components/

                * UI/ (Buttons, Inputs, Cards, Modals, etc.)

                * Auth/ (AuthForm, SocialLoginButtons)

                * Intake/ (ProgressIndicator, FormStepLayout)

                * Workouts/ (ExerciseListItem, SetLogger, RestTimer)

                * Charts/ (ProgressBar, DonutChart, LineChartWrapper)

                * VideoPlayer/ (EmbeddedVideoPlayer.tsx)

            * navigation/

                * RootNavigator.tsx

                * AuthNavigator.tsx

                * MainAppTabNavigator.tsx

                * IntakeStackNavigator.tsx

            * services/

                * supabaseClient.ts

                * authService.ts

                * profileService.ts

                * intakeService.ts

                * workoutService.ts

                * feedbackService.ts

                * notesService.ts

                * exportService.ts

                * posthog.ts (analytics integration)

            * store/ (State Management - e.g., Zustand or Redux)

                * userStore.ts

                * authStore.ts

                * intakeFormStore.ts

                * workoutStore.ts

                * themeStore.ts (for gender-specific themes)

            * hooks/

                * useAuth.ts

                * useFormValidation.ts

            * assets/

                * images/

                * fonts/

            * theme/

                * colors.ts (base, neutral, gender-specific palettes)

                * typography.ts

                * spacing.ts

                * globalStyles.ts

            * utils/

                * helpers.ts

                * validationSchemas.ts

                * timeZone.ts

            * App.tsx

* Backend/ (Supabase)

    * supabase/

        * migrations/ (SQL for schema changes)

        * functions/

            * generate-workout-program/

                * index.ts (Node.js)

                * openAIPromptBuilder.ts

            * process-weekly-feedback/

                * index.ts (Node.js)

            * export-user-data/

        * index.ts (Node.js)

        * seed.sql (for initial data like exercises)

        * config.toml

        * (No separate backend server code if fully relying on Supabase and Functions)