**Feature 3: AI-Powered Workout Program Generation**

**Goal:** Leverages AI to create personalized 4-week training programs using data from the client's intake form. The generated program is then sent for coach review.

**API relationships:**

* Supabase Database (PostgreSQL):

* Read from `intake_forms` for a user.

* Write to `workout_programs`, `program_weeks`, `workouts`, `workout_exercises`.

* Link to `exercises` (pre-populated exercise library).

* External AI Model API (e.g., OpenAI GPT):

* Send processed intake data as prompt.

* Receive structured workout program data.

* Supabase Functions:

* `generate-workout-program`: Orchestrates fetching intake, calling AI, parsing response, and saving to DB.

**Detailed requirements:**

* System securely sends relevant intake data to the AI model.

* AI generates a structured 4-week training block (exercises, sets, reps, suggested weights/RPE, rest times).

* Generated program is stored, associated with the client, and set to `status: 'ai_generated_pending_review'`.

* Predefined `exercises` library (with video links, target muscles, etc.) for AI to reference. AI should output exercise names that map to this library.

* This process is triggered after intake form submission with `status: 'submitted'`.

* Client is shown a "program being generated" message with an ETA (e.g., 24-48 hours including coach review).

**Implementation guide:**

* **Architecture overview:**

* **Diagram:**

```

1. Client submits Intake Form (status: 'submitted')

|

V

2. Supabase DB ('intake_forms' table)

| (DB Trigger or scheduled task checks for new 'submitted' intake)

V

3. Supabase Function ('generate-workout-program')

| a. Reads intake_forms for user

| b. Constructs prompt for AI

| c. Calls External AI API (e.g., OpenAI)

| d. Receives structured workout data from AI

| e. Parses/validates AI response, maps exercises to 'exercises' table

| f. Writes program to 'workout_programs', 'program_weeks', etc. (status: 'ai_generated_pending_review')

V

4. Supabase DB (program data stored)

|

V

5. Coach Portal (reads programs with status 'ai_generated_pending_review' for review)

```

* **Tech-stack justification:**

* Supabase Functions (Node.js/Deno): Server-side logic for AI interaction, secure API key management.

* OpenAI GPT (or similar): For complex generation logic based on natural language processing and structured input.

* PostgreSQL: For storing the structured program and exercise library.

* **Deployment:** Supabase Function deployed to Supabase cloud. `exercises` table seeded.

* **DB schema:**

* **ER Diagram (Conceptual):**

```

auth.users --< intake_forms >-- workout_programs --< program_weeks --< workouts --< workout_exercises >-- exercises

workout_programs also links to profiles (for coach_id)

```

* **Table definitions:**

* `exercises`:

* `id`: UUID, PK.

* `name`: TEXT, UNIQUE, NOT NULL (e.g., "Barbell Squat", "Push-up").

* `description`: TEXT.

* `video_url`: TEXT (link to YouTube/Vimeo/self-hosted).

* `target_muscles_primary`: TEXT[] (e.g., `{"Quads", "Glutes"}`).

* `target_muscles_secondary`: TEXT[] (e.g., `{"Hamstrings", "Core"}`).

* `equipment_required`: TEXT[] (e.g., `{"Barbell", "Rack"}`).

* `difficulty_level`: TEXT ENUM('Beginner', 'Intermediate', 'Advanced').

* `created_at`, `updated_at`.

* `workout_programs`:

* `id`: UUID, PK.

* `user_id`: UUID, FK to `auth.users.id`.

* `name`: TEXT (e.g., "Client X - 4 Week Hypertrophy Block 1").

* `description`: TEXT (Overall goal of the program).

* `duration_weeks`: INTEGER, default 4.

* `status`: TEXT ENUM('ai_generated_pending_review', 'coach_edited', 'coach_approved', 'active_by_client', 'completed_by_client', 'archived'), default 'ai_generated_pending_review'.

* `ai_prompt_data`: JSONB (stores the exact input/context given to AI for traceability).

* `generated_by_ai_at`: TIMESTAMPTZ.

* `coach_id`: UUID, FK to `public.profiles(id)` (where profile is a coach), nullable.

* `coach_reviewed_at`: TIMESTAMPTZ, nullable.

* `coach_notes_for_client`: TEXT, nullable (added by coach during review).

* `client_start_date`: DATE, nullable (set when client starts the program).

* `created_at`, `updated_at`.

* `program_weeks`:

* `id`: UUID, PK.

* `workout_program_id`: UUID, FK to `workout_programs.id` ON DELETE CASCADE.

* `week_number`: INTEGER (1-4 for a 4-week block).

* `notes`: TEXT (e.g., "Focus on progressive overload", "Deload week").

* UNIQUE (`workout_program_id`, `week_number`).

* `workouts`: (A specific training session)

* `id`: UUID, PK.

* `program_week_id`: UUID, FK to `program_weeks.id` ON DELETE CASCADE.

* `day_identifier`: TEXT (e.g., "Day 1", "Day A", "Monday" - depends on flexibility offered).

* `title`: TEXT (e.g., "Upper Body Push Strength", "Full Body A").

* `description`: TEXT (brief overview of the workout's focus).

* `estimated_duration_minutes`: INTEGER.

* `order_in_week`: INTEGER (for display sequence).

* `workout_exercises`: (An exercise within a workout)

* `id`: UUID, PK.

* `workout_id`: UUID, FK to `workouts.id` ON DELETE CASCADE.

* `exercise_id`: UUID, FK to `exercises.id`. (If AI suggests an unknown exercise, flag for coach or map to similar).

* `order_in_workout`: INTEGER.

* `prescribed_sets`: INTEGER.

* `prescribed_reps_min`: INTEGER, nullable.

* `prescribed_reps_max`: INTEGER, nullable. (e.g. 8-10 reps)

* `prescribed_duration_seconds`: INTEGER, nullable (for timed exercises like planks).

* `prescribed_rir`: INTEGER, nullable (Reps In Reserve).

* `prescribed_rpe`: NUMERIC(3,1), nullable (Rate of Perceived Exertion).

* `prescribed_tempo`: TEXT, nullable (e.g., "2-1-2-1").

* `rest_period_seconds_after_set`: INTEGER.

* `notes`: TEXT (e.g., "Focus on explosive concentric", "Warm-up sets: 2x12").

* **Indexes:** On all FKs, `exercises(name)`, `workout_programs(user_id, status)`.

* **Migrations:** SQL for creating these tables, RLS. Seed `exercises` table.

* **API design (Supabase Function `generate-workout-program`):**

* Trigger: HTTP POST (e.g., called by a DB trigger on `intake_forms` update to `status='submitted'`, or by a scheduled job).

* Request (if HTTP direct): `{ "userId": "uuid_of_user" }`

* Internal Steps:

1. Function fetches `intake_forms` data for `userId`.

2. Function fetches relevant `exercises` from DB to provide context to AI (e.g., names, equipment).

3. Constructs a detailed prompt for OpenAI API.

```json

// Example Prompt Structure Snippet

{

"model": "gpt-4-turbo-preview", // or other suitable model

"messages": [

{"role": "system", "content": "You are an expert fitness program designer. Generate a 4-week structured workout program based on the user's profile. Output should be in JSON format, strictly adhering to the provided schema. Use only exercises from the provided list: [Exercise1, Exercise2,...]. For each exercise, specify sets, reps (as a range like 8-10 or single value), RPE or RIR, and rest time in seconds. Structure workouts per day for each of the 4 weeks."},

{"role": "user", "content": /* JSON string of user_intake_data and exercise_list_context */ }

],

"response_format": { "type": "json_object" }, // if supported

"temperature": 0.5 // Adjust for creativity vs. determinism

}

```

4. Calls OpenAI API: `POST https://api.openai.com/v1/chat/completions`

* Headers: `Authorization: Bearer $OPENAI_API_KEY`, `Content-Type: application/json`.

5. Parses OpenAI JSON response. Validates structure and exercise names against `exercises` table.

6. Inserts data into `workout_programs`, `program_weeks`, `workouts`, `workout_exercises` tables with `status: 'ai_generated_pending_review'`.

7. Updates `intake_forms.status` to `'processing'` or `'completed'` (if generation itself is the completion).

* Response (of Supabase Function if HTTP triggered):

* Success: `{ "success": true, "programId": "uuid_of_program" }`

* Error: `{ "success": false, "error": "message" }`

* **Auth:** Supabase Function invoked with service role key or specific permissions if triggered by authenticated context. OpenAI API key stored as a secret in Supabase Function settings.

* **Rate-Limit:** OpenAI API rate limits. Supabase Function execution limits.

* **Frontend structure:**

* Client shows `IntakeProcessingScreen` (from `<other-considerations>`) after submitting intake. This screen informs the user that the program is being generated and reviewed by a coach, with an ETA of 24-48 hours. No direct interaction with AI generation from client.

* Later, client app will fetch and display the `coach_approved` program (covered in "Workout Display & Interaction").

* **CRUD operations (Workout Programs - AI/System Perspective):**

* **Create:** The Supabase Function creates the initial program record and its child entities.

* **Read:** N/A for client at this stage. Coach reads these.

* **Update:** N/A for client at this stage. Coach updates these.

* **Delete:** N/A.

* **Validation:**

* AI response parsing: Ensure JSON is valid and contains required fields.

* Exercise mapping: Ensure AI-suggested exercises exist in the `exercises` table. If not, flag for coach or attempt fuzzy matching.

* DB constraints ensure data integrity upon insertion.

* **UX flow:**

* Client completes intake, sees "Intake Submission State 1: Submitting / Processing Confirmation" screen.

* Text: "Thank You! Your information has been successfully submitted."

* Expectation Setting Text: "Your personalized training program is being crafted by our AI and reviewed by your coach. It will be ready and uploaded to your app within 24-48 hours. We'll notify you!"

* No further UX for program *generation* for the client. They await notification.

* Coach UX (out of scope for this client spec but for context): Coach sees list of `ai_generated_pending_review` programs, reviews, edits, approves.

* **Security:**

* OpenAI API Key: Stored securely as a Supabase Function secret, not exposed to client.

* Data to AI: Send only necessary, anonymized (if possible) data from intake. Avoid sending raw PII if not strictly needed for generation.

* RLS: `workout_programs` and related tables must have RLS ensuring coaches can access for review and clients can access *their own approved* programs.

* **Testing:**

* **Unit Tests (Supabase Function):**

* Prompt construction logic.

* AI response parsing and validation.

* Mapping AI exercises to DB `exercises`.

* DB insertion logic (mocked DB calls).

* **Integration Tests:**

* Full Supabase Function execution: Trigger -> Fetch Intake -> Call Mocked OpenAI -> Parse -> Store in actual test DB.

* Verify program status is `ai_generated_pending_review`.

* **E2E Tests:** (Difficult for direct AI part, more focused on intake-to-pending_review state change)

* Simulate intake submission and verify program appears in coach's queue (mocked).

* **Data management:**

* `exercises` table: Needs to be comprehensive and well-maintained. Process for adding new exercises.

* Generated programs are stored long-term.

* Consider versioning or archiving if AI re-generates programs for a user.

* AI prompt and (optionally) raw AI response can be stored for debugging/auditing.

* **Logging & error handling (Supabase Function):**

* **Structured logs:**

* `GenerateProgram.start {userId}`

* `GenerateProgram.OpenAI.request {promptSummary}`

* `GenerateProgram.OpenAI.response {status, exerciseCount}`

* `GenerateProgram.OpenAI.failure {error}`

* `GenerateProgram.DBStore.success {programId}`

* `GenerateProgram.DBStore.failure {error}`

* `GenerateProgram.ExerciseMapping.issue {aiExerciseName, mappedTo}`

* **Alerts:**

* Consistent failures in OpenAI API calls.

* High rate of failures in parsing AI response or storing program.

* Function timeouts.

* **Recovery/Retries:**

* Implement retries for OpenAI API calls (with exponential backoff).

* If generation fails, intake form status might revert or go to an error state, notifying coach/admin.

* Graceful handling of malformed AI responses (e.g., attempt to partially save, flag for manual review).

---
