**Goal:** Facilitates regular client feedback through a structured weekly form. Gathers qualitative data on recovery, energy levels, and overall training performance. Also displays coach-published feedback.

**API relationships:**

* Supabase Database (PostgreSQL):

* CRUD for `weekly_checkins` table.

* Read `coach_feedback` table.

* Supabase Functions (Node.js):

* (Potential) For sending notifications/reminders about check-in window (if not handled client-side).

**Detailed requirements:**

* Check-in form with preset questions available every Sunday 6 AM to 11:59 PM client's local time.

* Notification to user when form is available and reminder if not completed.

* Stores historical check-in submissions for user and coach review.

* Displays coach-published feedback.

**Implementation guide:**

* **Architecture overview:**

* **Diagram:**

```

Client (React Native App - Check-In Screens)

|-- Local logic for form availability (time-based)

|-- Interacts with `checkInService`, `feedbackService`

V

Supabase Backend

|-- Database (PostgreSQL - 'weekly_checkins', 'coach_feedback')

|-- (Optional Function for server-side notification scheduling)

```

* **Tech-stack justification:**

* React Native: For UI.

* Supabase DB: Store check-ins and coach feedback.

* Timezone Handling: Critical. Use a library like `date-fns-tz` or `moment-timezone` on client. Store user's timezone preference or derive. Supabase stores timestamps in UTC.

* **Deployment:** Part of client app. Notifications via Expo Push Notifications or Firebase Cloud Messaging (FCM).

* **DB schema:**

* **Table definitions:**

* `weekly_checkins`:

* `id`: UUID, PK.

* `user_id`: UUID, FK to `auth.users.id`.

* `program_week_identifier`: TEXT, nullable (e.g., "Program XYZ - Week 3" or just a week number in current program).

* `checkin_date`: DATE, NOT NULL (The Sunday this check-in pertains to).

* `submitted_at`: TIMESTAMPTZ, NOT NULL.

* `wins`: TEXT, nullable.

* `challenges`: TEXT, nullable.

* `progress_reflection`: TEXT, nullable.

* `training_performance_rating`: INTEGER, nullable (1-10).

* `recovery_rating`: INTEGER, nullable (1-10).

* `energy_levels_rating`: INTEGER, nullable (1-10).

* `additional_notes_for_coach`: TEXT, nullable.

* UNIQUE (`user_id`, `checkin_date`).

* `created_at`, `updated_at`.

* `coach_feedback`: (Could be a general feedback table, or specific to weekly cycle)

* `id`: UUID, PK.

* `user_id`: UUID, FK to `auth.users.id`.

* `coach_id`: UUID, FK to `public.profiles(id)` (where profile is a coach).

* `related_checkin_id`: UUID, FK to `weekly_checkins.id`, nullable.

* `feedback_content`: TEXT, NOT NULL.

* `status`: TEXT ENUM('draft_by_ai', 'draft_by_coach', 'published', 'read_by_client'), default 'draft_by_ai'.

* `published_at`: TIMESTAMPTZ, nullable.

* `read_at`: TIMESTAMPTZ, nullable (by client).

* `created_at`, `updated_at`.

* **Indexes:** `weekly_checkins(user_id, checkin_date DESC)`, `coach_feedback(user_id, status, published_at DESC)`.

* **Migrations:** SQL for these tables, RLS.

* **API design:**

* **Submit Weekly Check-In:**

* Method: `POST` via Supabase client `supabase.from('weekly_checkins').insert({ user_id: userId, ...formData, checkin_date: currentSunday, submitted_at: new Date() })`

* Request: Check-in form data.

* Response (Success 201): Saved check-in data.

* **Fetch Historical Check-Ins:**

* Method: `GET` via Supabase client `supabase.from('weekly_checkins').select('*').eq('user_id', userId).order('checkin_date', { ascending: false })`

* Response: Array of past check-ins.

* **Fetch Published Coach Feedback:**

* Method: `GET` via Supabase client `supabase.from('coach_feedback').select('*').eq('user_id', userId).eq('status', 'published').order('published_at', { ascending: false })`

* Response: Array of published feedback.

* **Mark Feedback as Read:**

* Method: `PATCH` via Supabase client `supabase.from('coach_feedback').update({ status: 'read_by_client', read_at: new Date() }).match({ id: feedbackId, user_id: userId })`

* **Auth:** User authenticated.

* **Frontend structure:**

* **Component hierarchy:**

* `WeeklyCheckInPrompt.tsx`: Card/banner on dashboard when check-in is due.

* `WeeklyCheckInFormScreen.tsx`: The form itself.

* `CheckInHistoryListScreen.tsx` / `CheckInHistoryDetailScreen.tsx`.

* (Re-use) `CoachFeedbackDisplayModal.tsx` / or integrate feedback display into check-in history or progress overview.

* **State management (`checkInStore`, `feedbackStore`):**

* Current check-in form data.

* Historical check-ins.

* Fetched coach feedback.

* Logic to determine if check-in window is open based on user's local time.

* **Navigation:** From prompt to form. Access history from profile/dashboard.

* **CRUD operations (Weekly Check-ins - Client Perspective):**

* **Create:** Client fills and submits form.

* **Read:** Client views past submissions.

* **Update/Delete:** Generally not allowed for submitted check-ins to maintain record integrity.

* **CRUD for Coach Feedback (Client Perspective):** Read-only. Update status to 'read_by_client'.

* **UX flow:**

* Refer to `<other-considerations>`: "Weekly Check-In & Feedback System" screens:

* Screen 1: Weekly Check-In Prompt / Access (Available, Not Available/Submitted).

* Screen 2: Weekly Check-In Form (Answering, Submitting, Confirmation with feedback timeline expectation).

* Screen 3: Historical Check-Ins View (List, Detailed).

* Displaying coach feedback on Progress Dashboard and/or in a dedicated feedback section.

* **Timezone Logic:**

```pseudocode

FUNCTION isCheckInWindowOpen(userLocalTimezone):

// Get current time in user's local timezone

nowInUserTz = getCurrentTimeInTimezone(userLocalTimezone)

dayOfWeek = getDayOfWeek(nowInUserTz) // Sunday = 0 or 7

hour = getHour(nowInUserTz)

IF dayOfWeek IS Sunday AND hour >= 6 AND hour < 24 THEN // 11:59 PM is hour 23

// Check if already submitted for this Sunday

sundayDate = getStartOfDay(nowInUserTz) // Get date for this Sunday

IF hasSubmittedCheckInForDate(sundayDate) THEN

RETURN "Submitted"

ELSE

RETURN "Open"

END IF

ELSE

RETURN "Closed"

END IF

```

* **Notifications:**

* Sunday 6 AM (local): "Weekly Check-In is now open!" (Expo Push Notification).

* Sunday 3 PM (local): "Reminder: Complete your Weekly Check-In by tonight." (if not yet submitted).

* **Security:**

* RLS: Users can only manage their own check-ins and view their own feedback.

* Coaches can read check-ins and manage feedback for their clients.

* **Testing:**

* **Unit Tests:**

* Timezone logic for check-in window availability.

* Form validation.

* Notification scheduling logic (if client-side).

* **Integration Tests:**

* Submitting check-in and verifying it's stored.

* Fetching historical check-ins and feedback.

* **E2E Tests:**

* User receives check-in notification, completes and submits form.

* User views past check-ins and coach feedback.

* **Push Notification Testing:** Using Expo's tools or FCM console.

* **Data management:**

* **Caching:** Historical check-ins and feedback lists can be cached.

* **Lifecycle:** Check-ins are created weekly. Feedback is tied to check-in cycle.

* **Real-time needs:** Notification for new feedback. New check-in availability is time-based.

* **Logging & error handling:**

* **Structured logs:**

* `CheckIn.formOpened {userId}`

* `CheckIn.formSubmitted {checkInId}`

* `CheckIn.submit.failure {error}`

* `CoachFeedback.viewedByClient {feedbackId}`

* `Notification.sent {type, userId}`

* `Notification.failed {type, userId, error}`

* **Alerts:** High failure rate submitting check-ins. Notification delivery issues.

* **Recovery:** Retry submission. Clear error messages.
