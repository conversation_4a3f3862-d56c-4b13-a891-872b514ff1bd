
**Goal:** Provides the coach with a dedicated interface to log in, oversee client activities, and manage feedback. For <PERSON>, this supports a single coach. This spec focuses only on aspects that directly impact the client or necessitate client-side data structures for coach interaction. 

**API relationships (Client data accessed by Coach):**

* Supabase Database (PostgreSQL): Coach role needs read access via RLS to:

* `profiles` (client list, basic info).

* `intake_forms` (client's onboarding data).

* `workout_programs` (AI-generated, coach-edited, approved programs).

* `workout_logs`, `workout_exercise_logs` (client's training history).

* `weekly_checkins` (client's qualitative feedback).

* Coach role needs write/update access to:

* `workout_programs` (to edit AI-generated, approve, add notes).

* `coach_feedback` (to create, edit, publish).

**Detailed requirements (Client Impact):**

* Coach can view a list of their assigned clients (implies a `coach_clients` mapping or similar if multiple coaches were supported; for single coach MVP, coach sees all users or users with a specific flag).

* Coach can select a client to view their detailed progress, workout logs, and check-in submissions.

* Coach actions (approving programs, publishing feedback) trigger state changes and notifications visible to the client.

* MVP supports a single coach; onboarding of coach might be manual (DB record creation with 'coach' role).

**Implementation guide:**

* **Architecture overview:**

* The Coach Dashboard would likely be a different section of a React Native app with role-based views.

* It interacts with the same Supabase backend, but with 'coach' role permissions.

* Client app is only impacted by the data changes the coach makes (e.g., program status, new feedback).

* **DB schema (Key considerations for coach-client relationship):**

* `profiles` table:

* Needs a `role` field (e.g., `TEXT ENUM('client', 'coach') DEFAULT 'client'`).

* For single coach MVP, the coach user can be manually set in DB.

* RLS Policies: Critical for coach access.

```sql

-- Example RLS for a coach to read all clients' workout_logs (single coach MVP)

-- More sophisticated RLS needed for multiple coaches and client assignments.

CREATE POLICY "Coaches can read all client workout logs."

ON public.workout_logs FOR SELECT

USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'coach'));

-- Example RLS for a coach to update a workout_program

CREATE POLICY "Coaches can update workout programs."

ON public.workout_programs FOR UPDATE

USING (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'coach')) -- Coach can update any

WITH CHECK (EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'coach'));

-- Similar RLS for intake_forms, weekly_checkins, coach_feedback (create/update for coach on this table).

```

* `coach_feedback.coach_id` and `workout_programs.coach_id` would store the `profiles.id` of the coach.

* **API design (Coach actions impacting client):**

* **Approve Workout Program:**

* Coach action: Updates `workout_programs.status` to `'coach_approved'`, sets `workout_programs.coach_reviewed_at`, potentially adds `coach_notes_for_client`.

* Client impact: Program becomes visible/startable. Client receives notification: "Your program is ready!".

* **Publish Coach Feedback:**

* Coach action: Updates `coach_feedback.status` to `'published'`, sets `coach_feedback.published_at`.

* Client impact: Feedback becomes visible. Client receives notification: "New coach feedback!".

* **Frontend structure (Client App):**

* No direct UI for coach functionality within the client app for MVP.

* Client app needs to react to data changes made by the coach (e.g., new program status, new feedback). This is handled by fetching data and push notifications.

* **UX flow (Client receiving updates from coach):**

* As described in "AI-Powered Workout Program Generation" (receiving approved program) and "AI Feedback Generation & Coach Review" (receiving published feedback).

* Notifications are key.

* **Security:**

* Robust RLS is paramount to ensure coaches can only access data they are permitted to, and cannot impersonate or modify data outside their scope.

* Coach login must be secure. MFA for coach highly recommended.

* **Testing (Client-side, reacting to coach actions):**

* E2E tests simulating coach actions in the backend (e.g., manually updating DB record status) and verifying client app receives notifications and displays updated data correctly.

* **Data management (Client-side):**

* Client app polls or uses push notifications to become aware of coach-initiated updates.

* `react-query` or similar can help manage cache invalidation and refetching when relevant data changes.

* **Logging & error handling (Client-side):**

* Log when client receives and processes coach-initiated updates (e.g., "ProgramApprovedNotificationReceived", "CoachFeedbackNotificationReceived").
