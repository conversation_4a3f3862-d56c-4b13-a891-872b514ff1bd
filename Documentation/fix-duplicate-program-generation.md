# Fix Duplicate AI Program Generation Issue

## Problem Summary
Two programs are being generated for new users due to:
1. **Database webhook trigger** firing on every profile update (sends empty payload)
2. **Deployed Edge Function** lacking race condition protection

## Solution Steps

### 1. Apply Database Migration
```bash
# Apply the migration to remove the problematic webhook trigger
npx supabase db reset
# OR apply specific migrations:
npx supabase migration up
```

### 2. Deploy Enhanced Edge Function
```bash
# Deploy the updated Edge Function with race condition protection
npx supabase functions deploy generate-workout-program
```

### 3. Verify the Fix
```bash
# Test with a new user signup to ensure only 1 program is generated
# Check the workout_programs table after a new user completes intake
```

## Technical Details

### Database Changes
- **Removed**: `generate_workout_program_webhook` trigger (problematic)
- **Kept**: `on_profile_intake_updated` trigger (proper logic)

### Edge Function Enhancements
- **Added**: Advisory lock protection to prevent simultaneous execution
- **Added**: 5-minute duplicate check for race conditions
- **Added**: Enhanced logging for debugging

### Expected Behavior After Fix
1. User completes intake form
2. `on_profile_intake_updated` trigger sets `intake_status = 'completed'`
3. ~~Webhook trigger no longer fires~~ (removed)
4. Edge Function uses advisory locks to prevent duplicates
5. Only 1 program is generated per user

## Verification Commands
```sql
-- Check for duplicate programs
SELECT user_id, COUNT(*) as program_count 
FROM workout_programs 
GROUP BY user_id 
HAVING COUNT(*) > 1;

-- Check active triggers on profiles table
SELECT trigger_name, event_manipulation, action_statement 
FROM information_schema.triggers 
WHERE event_object_table = 'profiles';
```

## Files Modified
- `supabase/migrations/20250710000001_fix_duplicate_program_generation.sql`
- `supabase/migrations/20250710000002_remove_duplicate_webhook_trigger.sql`
- `supabase/functions/generate-workout-program/index.ts` (enhanced version)
