**Feature 1: Client Account Management**

**Goal:** Enables client users to register, log in, manage their profile, and ensures secure storage and access to their personal information, workout data, and history within the app.

**API relationships:**

* Supabase Auth:

* `signUp()`: User registration.

* `signInWithPassword()`: User login.

* `signOut()`: User logout.

* `getSession()`: Retrieve current session.

* `onAuthStateChange()`: Listen to auth state changes.

* `updateUser()`: Update user attributes (e.g., email, password).

* `resetPasswordForEmail()`: Initiate password reset.

* `refreshSession()`: Handle token refresh (managed by Supabase client library).

* Supabase Database (PostgreSQL):

* `profiles` table: CRUD operations for user profile data.

**Detailed requirements:**

* Secure user registration with email and password.

* Secure user login and session management (JWT based).

* Token refresh mechanism.

* Password hashing and secure storage (handled by Supabase Auth).

* Password reset functionality via email.

* Authenticated clients can access their dashboard and app features.

* CRUD operations for user profiles:

* Clients can create accounts.

* Profile information (e.g., full name, contact info - email via Supabase Auth) can be updated by the client.

* (Future: Account deletion, considering data retention).

* Gender selection during profile setup/intake to influence app theme.

**Implementation guide:**

* **Architecture overview:**

* **Diagram:**

```

Client (React Native App) <--HTTPS--> Supabase Backend

|-- Auth Service (JWT)

|-- Database (PostgreSQL - 'profiles' table)

```

* **Tech-stack justification:**

* React Native (Expo, TypeScript): Cross-platform mobile development, strong typing.

* Supabase: BaaS providing Auth, PostgreSQL DB, and Functions, simplifying backend development for MVP.

* JWT: Standard for stateless session management, handled by Supabase.

* HTTPS: Standard for secure communication, enforced by Supabase.

* **Deployment:**

* Frontend: Expo Application Services (EAS) Build for app store deployment (iOS App Store, Google Play Store).

* Backend: Supabase cloud-hosted platform.

* **DB schema:**

* **ER Diagram (Conceptual):**

```

auth.users (Supabase managed)

id (uuid, pk)

email (text)

encrypted_password (text)

... (other auth fields)

public.profiles

id (uuid, pk, fk to auth.users.id ON DELETE CASCADE)

created_at (timestampz, default now())

updated_at (timestampz, default now())

full_name (text, not null)

-- Gender, Age, Height, Weight will be moved to intake_forms to be captured during onboarding

-- For theme toggle, gender might be stored here redundantly or intake form is primary source

gender_preference (text ENUM('Woman', 'Man', 'Neutral'), nullable) -- Used for app theming

-- Other profile-specific, non-intake preferences can be added here.

```

* **Table definitions:**

* `auth.users`: Managed by Supabase Auth. Contains core authentication details.

* `public.profiles`:

* `id`: UUID, Primary Key, Foreign Key referencing `auth.users.id`.

* `created_at`: TIMESTAMPTZ, default `NOW()`.

* `updated_at`: TIMESTAMPTZ, default `NOW()`. Function to auto-update.

* `full_name`: TEXT, NOT NULL.

* `gender_preference`: TEXT, ENUM ('Woman', 'Man', 'Neutral'). Stores user's choice for app theming. Filled upon intake completion.

* **Indexes:**

* Primary key on `profiles.id` (auto-created).

* Supabase Auth creates necessary indexes on `auth.users`.

* **Migrations:**

* SQL scripts managed via Supabase CLI or dashboard for `profiles` table.

* Initial migration to create `profiles` table and `updated_at` trigger.

```sql

-- Create profiles table

CREATE TABLE public.profiles (

id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,

created_at TIMESTAMPTZ DEFAULT NOW(),

updated_at TIMESTAMPTZ DEFAULT NOW(),

full_name TEXT NOT NULL,

gender_preference TEXT CHECK (gender_preference IN ('Woman', 'Man', 'Neutral'))

);

-- RLS Policy for profiles

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile."

ON public.profiles FOR SELECT

USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile."

ON public.profiles FOR UPDATE

USING (auth.uid() = id)

WITH CHECK (auth.uid() = id);

-- Function to update 'updated_at' timestamp

CREATE OR REPLACE FUNCTION public.handle_updated_at()

RETURNS TRIGGER AS $$

BEGIN

NEW.updated_at = NOW();

RETURN NEW;

END;

$$ LANGUAGE plpgsql;

CREATE TRIGGER on_profile_updated

BEFORE UPDATE ON public.profiles

FOR EACH ROW

EXECUTE PROCEDURE public.handle_updated_at();

-- Function to create a profile entry when a new user signs up

CREATE OR REPLACE FUNCTION public.handle_new_user()

RETURNS TRIGGER AS $$

BEGIN

INSERT INTO public.profiles (id, full_name)

VALUES (NEW.id, COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User')); -- Use full_name from sign_up metadata if available

RETURN NEW;

END;

$$ LANGUAGE plpgsql;

CREATE TRIGGER on_auth_user_created

AFTER INSERT ON auth.users

FOR EACH ROW

EXECUTE PROCEDURE public.handle_new_user();

```

* **API design:**

* **Registration:**

* Endpoint: `POST /auth/v1/signup` (Supabase Auth)

* Request: `{ "email": "<EMAIL>", "password": "securepassword123", "data": { "full_name": "Jane Doe" } }`

* Response (Success 200): `{ "id": "uuid", "aud": "authenticated", "role": "authenticated", "email": "<EMAIL>", ...sessionData }` (Also triggers `handle_new_user` to create profile)

* Response (Error 4xx): `{ "code": 422, "msg": "User already registered" }`

* **Login:**

* Endpoint: `POST /auth/v1/token?grant_type=password` (Supabase Auth)

* Request: `{ "email": "<EMAIL>", "password": "securepassword123" }`

* Response (Success 200): `{ "access_token": "jwt_token", "token_type": "bearer", ...sessionData }`

* Response (Error 400): `{ "error": "invalid_grant", "error_description": "Invalid login credentials" }`

* **Get Profile:**

* Method: `GET` via Supabase client `supabase.from('profiles').select('*').eq('id', userId).single()`

* Response (Success 200): `{ "id": "uuid", "full_name": "Jane Doe", "gender_preference": "Woman", ... }`

* Response (Error 404): If profile not found (shouldn't happen if `handle_new_user` trigger works).

* **Update Profile:**

* Method: `PATCH` via Supabase client `supabase.from('profiles').update({ full_name: "Jane Smith" }).eq('id', userId)`

* Request: `{ "full_name": "Jane Smith" }`

* Response (Success 200/204): Updated profile data or no content.

* **Password Reset Request:**

* Endpoint: `POST /auth/v1/recover` (Supabase Auth)

* Request: `{ "email": "<EMAIL>" }`

* Response (Success 200): `{}` (Even if email doesn't exist, to prevent enumeration)

* **Update Password (after reset link or in-app):**

* Endpoint: `POST /auth/v1/user` (Supabase Auth, using access token)

* Request: `{ "password": "newsecurepassword123" }`

* Response (Success 200): User object.

* **Auth:** Handled by Supabase JWT. Tokens are sent in Authorization header (`Bearer <token>`).

* **Errors:** Standard HTTP status codes. Supabase provides error objects with `message` and `status`.

* **Rate-Limit:** Default Supabase rate limits apply. Custom limits can be configured if needed.

* **Frontend structure:**

* **Component hierarchy:**

* `App.tsx` (Root, sets up Supabase client, navigation, global state providers)

* `RootNavigator.tsx` (Checks auth state, directs to Auth or MainApp)

* `AuthNavigator.tsx`: Stack navigator for `SplashScreen`, `AuthSelectionScreen`, `RegistrationScreen`, `LoginScreen`, `ForgotPasswordScreen`.

* `MainAppTabNavigator.tsx`: Tab navigator for main app sections (Dashboard, Workouts, Profile, etc.) once logged in.

* `ProfileStackNavigator.tsx`: Stack for `ProfileManagementScreen`, `EditProfileScreen`, `ChangePasswordScreen`.

* **State management:**

* `authStore` (Zustand/Context): Manages user session, authentication status, loading states.

* `userStore` (Zustand/Context): Manages fetched user profile data.

* Local component state (`useState`, `react-hook-form`) for form inputs and validation.

* `themeStore` (Zustand/Context): Manages current theme (neutral, gender-specific) based on `gender_preference`.

* **Navigation:** `react-navigation` library. Deep linking for password reset emails.

* **CRUD operations (User Profiles - Client Perspective):**

* **Create:** Via `RegistrationScreen`. `authService.signUp()` call. Profile record auto-created by DB trigger.

* **Read:** `ProfileManagementScreen` fetches data via `profileService.getProfile()`. Data stored in `userStore`.

* **Update:** `EditProfileScreen` form. `profileService.updateProfile()` call.

* **Delete:** (MVP - Deferred) Account deletion would involve `authService.deleteUser()` and DB cascading delete. Requires careful consideration of data retention.

* **Validation:**

* Client-side: `react-hook-form` with `yup` (or similar) for email format, password strength, required fields.

* Server-side: Supabase Auth handles email uniqueness, password policies. DB constraints (e.g., NOT NULL).

* **Pagination:** Not applicable for single user profile.

* **Soft vs. Hard Delete:** For MVP, hard delete through Supabase user deletion is simpler. Soft delete (e.g., `is_active` flag) adds complexity but allows recovery.

* **UX flow:**

* Refer to `<other-considerations>` for detailed screen states:

* Screen 1: Splash/Loading Screen

* Screen 2: Auth Selection Screen

* Screen 3: Registration Screen (Empty, Validation Errors, Loading, Success)

* Screen 4: Login Screen (Empty, Validation Errors, Loading, Success)

* Screen 5: Forgot Password Screen (Email Input, Loading, Confirmation)

* Screen 6: Profile Management Screen (View Profile, Edit Profile Details, Change Password)

* Color scheme toggle logic:

1. App starts in `Gender-Neutral` theme.

2. User completes Intake Form, selects gender. This is saved in `intake_forms.gender` and also copied to `profiles.gender_preference`.

3. `themeStore` updates based on `profiles.gender_preference`.

4. Subsequent themed screens (Dashboard, Workouts, Profile etc.) use the selected `Gender-Specific` theme.

* **Security:**

* **Auth flow:**

1. User enters credentials.

2. Client sends to Supabase Auth.

3. Supabase verifies, returns JWT (access & refresh token).

4. Client stores tokens securely (e.g., `expo-secure-store`).

5. Access token sent with subsequent API requests.

6. Supabase client library handles token refresh automatically.

7. Email verification enabled in Supabase Auth settings.

* **Roles:**

* `anon`: Unauthenticated users (can access public marketing pages, login/signup).

* `authenticated`: Logged-in users (can access their data).

* `coach`: (Future/Separate) Special role for coach dashboard access. RLS policies define data access.

* **Sanitization:**

* Client-side: Sanitize inputs before sending (e.g., trim whitespace).

* Server-side: Supabase and PostgreSQL handle SQL injection protection for parameterized queries. Text inputs should be handled carefully if rendered as HTML (not typical in React Native).

* **OWASP Protections:**

* A1: Broken Access Control: RLS policies in Supabase.

* A2: Cryptographic Failures: HTTPS enforced by Supabase. Passwords hashed by Supabase Auth. Sensitive data in transit encrypted.

* A3: Injection: Use Supabase client libraries which use parameterized queries.

* A5: Security Misconfiguration: Regular review of Supabase settings (RLS, auth policies).

* A7: Identification and Authentication Failures: Strong password policies, MFA (if enabled in Supabase), secure session management.

* A8: Software and Data Integrity Failures: Secure token handling.

* **Testing:**

* **Unit Tests (Jest, React Testing Library):**

* Auth form components (validation logic, submission handlers).

* State management logic in `authStore`, `userStore`.

* `authService`, `profileService` (mocking Supabase client).

* **Integration Tests:**

* Full registration flow with mocked Supabase Auth.

* Login and session persistence.

* Profile update and data refresh.

* Password reset request flow.

* **E2E Tests (Detox/Maestro):**

* User registers, logs out, logs in.

* User updates profile information.

* User initiates password reset.

* **Performance Tests:**

* App startup time.

* Login/registration screen responsiveness.

* Profile screen load time.

* **Data management:**

* **Caching:**

* Session token cached securely.

* User profile data cached in `userStore` (e.g., with `react-query` or Zustand persistence) to avoid re-fetching on every screen. Stale-while-revalidate strategy.

* **Lifecycle:**

* User data (profile) loaded after login and on app start if session exists.

* Refreshed when user updates profile.

* **Real-time needs:** Potentially listen to `onAuthStateChange` for real-time updates to auth status. Profile data itself is unlikely to need real-time updates for MVP unless changed by an admin.

* **Logging & error handling:**

* **Structured logs (PostHog/Sentry/Supabase Logs):**

* `AuthService.signUp.success`, `AuthService.signUp.failure {error}`

* `AuthService.login.success`, `AuthService.login.failure {error}`

* `ProfileService.updateProfile.success`, `ProfileService.updateProfile.failure {error}`

* Unexpected errors in UI components.

* **Alerts (Sentry/Supabase Monitoring):**

* High rate of auth failures.

* Critical errors in profile update flow.

* Supabase service health alerts.

* **Recovery:**

* Clear, user-friendly error messages on forms (e.g., "Invalid email or password", "Email already in use").

* "Forgot Password" flow for account recovery.

* Retry mechanisms for network errors (e.g., using `react-query`).

---
