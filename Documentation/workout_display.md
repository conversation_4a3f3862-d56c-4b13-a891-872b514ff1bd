**Feature 4: Workout Display & Interaction**

**Goal:** Allows clients to view their assigned, coach-approved workouts, access exercise instructions, and play demonstration videos. Clients can navigate their weekly schedule and select workouts flexibly.

**API relationships:**

* Supabase Database (PostgreSQL):

* Read `workout_programs`, `program_weeks`, `workouts`, `workout_exercises`, `exercises` tables for the authenticated user where `workout_programs.status` is 'coach_approved' or 'active_by_client'.

**Detailed requirements:**

* Display of assigned 4-week training program after coach approval.

* Navigation through weeks and days of the program.

* Each workout displays a list of exercises with sets, reps, rest, etc.

* Each exercise links to detailed instructions and an embedded video.

* Embedded media player for YouTube/Vimeo/self-hosted videos.

* Clients can select any workout from their current block to perform on any day (flexibility).

* Consistent naming and formatting for exercise mapping.

* Video streaming optimization for mobile.

**Implementation guide:**

* **Architecture overview:**

* **Diagram:**

```

Client (React Native App - Workout Screens)

|-- Fetches workout program data

V

Supabase Backend

|-- Database (PostgreSQL - program tables, exercises table)

|-- (<PERSON><PERSON> ensures client gets their own approved program)

Client (React Native App - Video Player)

|-- Streams video

V

Video Hosting Service (YouTube/Vimeo/Supabase Storage)

```

* **Tech-stack justification:**

* React Native: For UI.

* React Native Video (or `expo-av`): For embedding video playback.

* Supabase DB: Source of truth for workout program structure and exercise details.

* **Deployment:** Part of main client app. Exercise videos hosted externally or via Supabase Storage.

* **DB schema:**

* Utilizes tables defined in "AI-Powered Workout Program Generation": `workout_programs`, `program_weeks`, `workouts`, `workout_exercises`, `exercises`.

* Key fields for display: `exercises.name`, `exercises.video_url`, `exercises.description`, `workout_exercises.prescribed_sets/reps/rir/rpe/rest`, etc.

* **API design:**

* **Fetch Active/Approved Workout Program:**

* Method: `GET` via Supabase client.

```javascript

// workoutService.ts

async function getActiveProgram(userId) {

const { data, error } = await supabase

.from('workout_programs')

.select(`

id, name, description, status, coach_notes_for_client, client_start_date,

program_weeks (

id, week_number, notes,

workouts (

id, day_identifier, title, description, estimated_duration_minutes, order_in_week,

workout_exercises (

id, order_in_workout, prescribed_sets, prescribed_reps_min, prescribed_reps_max,

prescribed_duration_seconds, prescribed_rir, prescribed_rpe, prescribed_tempo,

rest_period_seconds_after_set, notes,

exercises (

id, name, description, video_url, target_muscles_primary, equipment_required

)

)

)

)

`)

.eq('user_id', userId)

.in('status', ['coach_approved', 'active_by_client']) // Fetch program ready to be started or already started

.order('created_at', { ascending: false }) // Get the latest one if multiple somehow exist

.maybeSingle();

if (error) throw error;

return data;

}

```

* Response (Success 200): Nested JSON object of the user's current workout program.

* **Mark Program as Started (Optional client action):**

* Method: `PATCH` via Supabase client.

* `supabase.from('workout_programs').update({ status: 'active_by_client', client_start_date: new Date().toISOString() }).eq('id', programId)`

* **Frontend structure:**

* **Component hierarchy:**

* `WeeklyWorkoutScheduleScreen.tsx`: Displays tabs for Week 1-4. Each tab lists workouts for that week.

* `WorkoutCard.tsx`: Summarizes a single workout (title, duration, day) in the weekly list.

* `WorkoutDetailsScreen.tsx`: Shows list of exercises for a selected workout. "Start Workout" button.

* `ExerciseListItemDetailed.tsx`: Displays one exercise in `WorkoutDetailsScreen` (name, sets/reps, link to video/details).

* `ExerciseDetailScreen.tsx`: Shows embedded video, instructions, cues for a single exercise.

* `EmbeddedVideoPlayer.tsx`: Component using `react-native-video` or `expo-av`'s Video component.

* **State management (`workoutStore`):**

* Stores the fetched active program structure.

* Current selected week/day/workout for navigation.

* **Navigation:** Stack navigators for drilling down from schedule to workout to exercise. Modal for exercise video/details can be used.

* **CRUD operations (Workout Program Display - Client Perspective):**

* **Read:** Primary operation. Fetch and display program structure.

* **Update:** Client might update program status to 'active_by_client' when they first start it. Logging workouts (separate feature) updates related data.

* **Create/Delete:** Not by client for the program structure itself.

* **UX flow:**

* Refer to `<other-considerations>`:

* "Workout Display & Interaction (Client-Facing - Program Reveal)" -> "Screen 1: Workout Program Overview (First View Post-Notification)" (This is the `WeeklyWorkoutScheduleScreen`).

* "Workout Display & Interaction" -> "Screen 2: Workout Details / Pre-Start Screen".

* "Workout Display & Interaction" -> "Screen 3: Exercise Detail Screen (View Instructions & Video)".

* Flexibility: User can tap on any workout in their current block (e.g., "Week 2, Day 3") and choose to start it, regardless of the actual calendar day.

* **Security:**

* RLS policies on all program-related tables ensure users can only read their own assigned and approved/active programs.

* Video URLs should be to trusted sources. If self-hosting, ensure proper access controls if videos are private.

* **Testing:**

* **Unit Tests:**

* Rendering of workout schedule, workout details, exercise details based on mock data.

* Video player component basic functionality (mocking video source).

* Navigation logic between screens.

* **Integration Tests:**

* Fetching program data from mock Supabase and displaying it correctly.

* **E2E Tests:**

* User navigates from dashboard to weekly schedule, selects a workout, views its exercises, opens an exercise video.

* **Performance Tests:**

* Screen load times for program display.

* Video player startup time and buffering.

* **Data management:**

* **Caching:** Program data can be cached in `workoutStore` (e.g., using `react-query` or Zustand persistence) to avoid re-fetching if unchanged. Cache invalidation if coach makes urgent updates (rare post-approval).

* **Lifecycle:** Program is fetched when user navigates to schedule screen.

* **Real-time needs:** Unlikely for program structure display. If coach could edit live programs (post-MVP), then real-time updates would be needed.

* **Video streaming:** Use adaptive bitrate streaming if possible. Optimize video file sizes. Consider `expo-video-thumbnails` for previews.

* **Logging & error handling:**

* **Structured logs (PostHog):**

* `WorkoutDisplay.programViewed {programId, userId}`

* `WorkoutDisplay.workoutDetailsViewed {workoutId}`

* `WorkoutDisplay.exerciseVideoPlayed {exerciseId, videoUrl}`

* `WorkoutDisplay.fetchProgram.failure {error}`

* `WorkoutDisplay.videoPlayback.error {videoId, error}`

* **Alerts:** High rate of failures fetching programs. Widespread video playback issues.

* **Recovery:**

* Show loading state while fetching.

* Display error message if program can't be fetched, with retry option.

* Graceful error handling for video player (e.g., "Video unavailable").
