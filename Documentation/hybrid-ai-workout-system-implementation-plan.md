# 🎯 Hybrid AI Workout Program Generation System - Implementation Plan

## 📋 **Executive Summary**

This plan implements a hybrid system that combines automatic AI program generation with coach oversight, featuring:
- **Automatic 4-week program updates** triggered on day 21 of each cycle
- **7-day coach review window** (days 21-28) with automatic approval fallback
- **Seamless client experience** with access to current and previous programs
- **Zero duplicate generation** through enhanced race condition protection

## 🏗️ **System Architecture Overview**

```
Day 1-20: Client uses current program
Day 21: Automatic AI generation of next program (status: pending_review)
Day 21-28: Coach review window (7 days)
Day 28: Auto-approval if not reviewed + program transition
Day 29+: Client uses new program, previous remains accessible
```

## 📊 **Implementation Phases & Complexity**

| Phase | Tasks | Estimated Days | Complexity | Dependencies |
|-------|-------|----------------|------------|--------------|
| Phase 1 | 4 tasks | 2-3 days | Low | None |
| Phase 2 | 5 tasks | 5-7 days | Medium | Phase 1 |
| Phase 3 | 5 tasks | 8-10 days | High | Phase 2 |
| Phase 4 | 5 tasks | 6-8 days | High | Phase 3 |
| Phase 5 | 5 tasks | 5-7 days | Medium | Phase 4 |
| Phase 6 | 5 tasks | 6-8 days | Medium | Phase 5 |
| Phase 7 | 6 tasks | 7-10 days | Medium | All phases |
| **Total** | **35 tasks** | **39-53 days** | | |

---

# 🔧 **Phase 1: Fix Current Duplicate Issues** (2-3 days)

**Priority**: CRITICAL - Must be completed first
**Complexity**: Low
**Dependencies**: None

## Tasks Breakdown:

### 1.1: Remove Problematic Database Triggers (0.5 days)
**Files to modify:**
- Apply `supabase/migrations/20250710000002_remove_duplicate_webhook_trigger.sql`

**Implementation:**
```sql
-- Remove the problematic webhook trigger
DROP TRIGGER IF EXISTS generate_workout_program_webhook ON public.profiles;
```

### 1.2: Deploy Enhanced Edge Function (1 day)
**Files to modify:**
- `supabase/functions/generate-workout-program/index.ts`

**Key changes:**
- Deploy version with advisory locks and 5-minute race condition protection
- Verify enhanced duplicate prevention is active

### 1.3: Verify Duplicate Fix (0.5 days)
**Testing requirements:**
- Create test user and complete intake
- Verify only 1 program is generated
- Test concurrent intake completions

### 1.4: Clean Up Existing Duplicates (1 day)
**Implementation:**
```sql
-- Identify and archive duplicate programs
UPDATE workout_programs 
SET status = 'archived' 
WHERE id IN (
  SELECT id FROM (
    SELECT id, ROW_NUMBER() OVER (
      PARTITION BY user_id 
      ORDER BY created_at DESC
    ) as rn
    FROM workout_programs 
    WHERE status IN ('ai_generated_pending_review', 'coach_approved')
  ) t WHERE rn > 1
);
```

---

# 🗄️ **Phase 2: Database Schema Enhancements** (5-7 days)

**Priority**: HIGH
**Complexity**: Medium
**Dependencies**: Phase 1 complete

## Tasks Breakdown:

### 2.1: Add Program Cycle Tracking (1.5 days)
**Migration file:** `20250710000003_add_program_cycles.sql`

```sql
-- Add cycle tracking columns
ALTER TABLE workout_programs ADD COLUMN cycle_number INTEGER DEFAULT 1;
ALTER TABLE workout_programs ADD COLUMN cycle_start_date DATE;
ALTER TABLE workout_programs ADD COLUMN cycle_status TEXT CHECK (cycle_status IN ('active', 'pending_transition', 'completed'));
ALTER TABLE workout_programs ADD COLUMN auto_generated_at TIMESTAMPTZ;
ALTER TABLE workout_programs ADD COLUMN review_deadline_date DATE;

-- Create indexes
CREATE INDEX idx_workout_programs_cycle ON workout_programs (user_id, cycle_number);
CREATE INDEX idx_workout_programs_review_deadline ON workout_programs (review_deadline_date);
```

### 2.2: Create Program Transitions Table (1.5 days)
**Migration file:** `20250710000004_create_program_transitions.sql`

```sql
CREATE TABLE program_transitions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  from_program_id UUID REFERENCES workout_programs(id),
  to_program_id UUID NOT NULL REFERENCES workout_programs(id),
  transition_date DATE NOT NULL,
  transition_status TEXT CHECK (transition_status IN ('scheduled', 'completed', 'failed')),
  created_at TIMESTAMPTZ DEFAULT now(),
  completed_at TIMESTAMPTZ
);
```

### 2.3: Add Scheduled Generation Tracking (1 day)
**Migration file:** `20250710000005_create_scheduled_jobs.sql`

```sql
CREATE TABLE scheduled_generation_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  scheduled_date DATE NOT NULL,
  execution_status TEXT CHECK (execution_status IN ('pending', 'running', 'completed', 'failed')),
  job_type TEXT CHECK (job_type IN ('program_generation', 'auto_approval')),
  created_at TIMESTAMPTZ DEFAULT now(),
  executed_at TIMESTAMPTZ,
  error_message TEXT,
  UNIQUE(user_id, scheduled_date, job_type)
);
```

### 2.4: Update Program Status Enum (0.5 days)
**Migration file:** `20250710000006_update_program_status.sql`

```sql
-- Add new status values
ALTER TABLE workout_programs DROP CONSTRAINT IF EXISTS workout_programs_status_check;
ALTER TABLE workout_programs ADD CONSTRAINT workout_programs_status_check 
CHECK (status IN (
  'ai_generated_pending_review',
  'scheduled_pending_review',
  'coach_approved',
  'auto_approved',
  'coach_edited',
  'active_by_client',
  'completed_by_client',
  'archived'
));
```

### 2.5: Create Program History Views (1.5 days)
**Migration file:** `20250710000007_create_program_views.sql`

```sql
-- View for client's current program
CREATE VIEW client_current_program AS
SELECT wp.*, 'current' as program_type
FROM workout_programs wp
WHERE wp.status IN ('coach_approved', 'auto_approved', 'active_by_client')
  AND wp.cycle_status = 'active';

-- View for client's program history
CREATE VIEW client_program_history AS
SELECT wp.*, 
  CASE 
    WHEN wp.cycle_status = 'active' THEN 'current'
    WHEN wp.cycle_number = (
      SELECT MAX(cycle_number) - 1 
      FROM workout_programs wp2 
      WHERE wp2.user_id = wp.user_id
    ) THEN 'previous'
    ELSE 'historical'
  END as program_type
FROM workout_programs wp
WHERE wp.status NOT IN ('archived');
```

---

# ⚡ **Phase 3: Enhanced Edge Functions** (8-10 days)

**Priority**: HIGH
**Complexity**: High
**Dependencies**: Phase 2 complete

## Tasks Breakdown:

### 3.1: Modify Generate-Workout-Program Function (3 days)
**Files to modify:**
- `supabase/functions/generate-workout-program/index.ts`

**Key enhancements:**
```typescript
interface GenerationRequest {
  userId: string;
  generationType: 'initial' | 'scheduled' | 'manual';
  cycleNumber?: number;
  previousProgramId?: string;
}

// Enhanced duplicate prevention logic
const checkDuplicatePrevention = async (userId: string, generationType: string) => {
  if (generationType === 'initial') {
    // 5-minute race condition protection only
    return checkRecentGeneration(userId, 5);
  } else if (generationType === 'scheduled') {
    // Allow if 21+ days since last generation
    return checkCycleEligibility(userId);
  } else {
    // Manual generation - coach override allowed
    return { allowed: true };
  }
};
```

### 3.2: Create Daily Scheduler Edge Function (2.5 days)
**New file:** `supabase/functions/daily-program-scheduler/index.ts`

```typescript
// Identify users eligible for 21-day generation
const findEligibleUsers = async () => {
  const { data } = await supabaseClient
    .from('workout_programs')
    .select('user_id, cycle_start_date, cycle_number')
    .eq('cycle_status', 'active')
    .lte('cycle_start_date', new Date(Date.now() - 21 * 24 * 60 * 60 * 1000));
  
  return data;
};

// Schedule generation jobs
const scheduleGenerationJobs = async (eligibleUsers) => {
  for (const user of eligibleUsers) {
    await scheduleUserProgramGeneration(user.user_id, user.cycle_number + 1);
  }
};
```

### 3.3: Create Auto-Approval Edge Function (1.5 days)
**New file:** `supabase/functions/auto-approve-programs/index.ts`

```typescript
// Find programs pending review past deadline
const findOverduePrograms = async () => {
  const { data } = await supabaseClient
    .from('workout_programs')
    .select('*')
    .eq('status', 'scheduled_pending_review')
    .lte('review_deadline_date', new Date());
  
  return data;
};

// Auto-approve overdue programs
const autoApprovePrograms = async (programs) => {
  for (const program of programs) {
    await supabaseClient
      .from('workout_programs')
      .update({ 
        status: 'auto_approved',
        coach_reviewed_at: new Date(),
        coach_notes_for_client: 'Automatically approved - no coach review within deadline'
      })
      .eq('id', program.id);
  }
};
```

### 3.4: Add Program Transition Logic (1 day)
**Implementation in existing functions:**

```typescript
const transitionPrograms = async (userId: string, newProgramId: string) => {
  // Mark current program as completed
  await supabaseClient
    .from('workout_programs')
    .update({ cycle_status: 'completed' })
    .eq('user_id', userId)
    .eq('cycle_status', 'active');

  // Activate new program
  await supabaseClient
    .from('workout_programs')
    .update({ 
      cycle_status: 'active',
      status: 'active_by_client',
      client_start_date: new Date()
    })
    .eq('id', newProgramId);

  // Record transition
  await supabaseClient
    .from('program_transitions')
    .insert({
      user_id: userId,
      to_program_id: newProgramId,
      transition_date: new Date(),
      transition_status: 'completed'
    });
};
```

### 3.5: Enhanced Error Handling & Logging (1 day)
**Implementation across all functions:**

```typescript
const logOperation = async (operation: string, data: any, status: 'success' | 'error') => {
  console.log(`[${new Date().toISOString()}] ${operation}`, {
    status,
    data,
    timestamp: Date.now()
  });
  
  // Optional: Store in dedicated logging table
  await supabaseClient
    .from('operation_logs')
    .insert({
      operation,
      data,
      status,
      created_at: new Date()
    });
};
```

---

# ⏰ **Phase 4: Scheduled Job Implementation** (6-8 days)

**Priority**: HIGH
**Complexity**: High
**Dependencies**: Phase 3 complete

## Tasks Breakdown:

### 4.1: Setup Supabase Cron Jobs (1 day)
**Configuration:**
```sql
-- Daily program generation check (runs at 9 AM UTC)
SELECT cron.schedule('daily-program-generation', '0 9 * * *', 
  'SELECT net.http_post(
    url := ''https://your-project.supabase.co/functions/v1/daily-program-scheduler'',
    headers := ''{"Authorization": "Bearer YOUR_SERVICE_KEY"}'',
    body := ''{}''
  );'
);

-- Daily auto-approval check (runs at 10 AM UTC)
SELECT cron.schedule('daily-auto-approval', '0 10 * * *',
  'SELECT net.http_post(
    url := ''https://your-project.supabase.co/functions/v1/auto-approve-programs'',
    headers := ''{"Authorization": "Bearer YOUR_SERVICE_KEY"}'',
    body := ''{}''
  );'
);
```

### 4.2: Implement Generation Scheduler (2 days)
**Enhanced daily-program-scheduler function:**

```typescript
const processScheduledGeneration = async () => {
  try {
    // Find eligible users
    const eligibleUsers = await findEligibleUsers();
    
    // Process each user
    for (const user of eligibleUsers) {
      await processUserGeneration(user);
    }
    
    return { success: true, processed: eligibleUsers.length };
  } catch (error) {
    await logOperation('scheduled-generation', { error: error.message }, 'error');
    throw error;
  }
};
```

### 4.3: Implement Auto-Approval Scheduler (1.5 days)
**Enhanced auto-approve-programs function:**

```typescript
const processAutoApprovals = async () => {
  try {
    const overduePrograms = await findOverduePrograms();
    
    for (const program of overduePrograms) {
      await autoApproveAndTransition(program);
    }
    
    return { success: true, approved: overduePrograms.length };
  } catch (error) {
    await logOperation('auto-approval', { error: error.message }, 'error');
    throw error;
  }
};
```

### 4.4: Add Monitoring & Alerting (1.5 days)
**New file:** `supabase/functions/monitor-scheduled-jobs/index.ts`

```typescript
const checkJobHealth = async () => {
  // Check for failed jobs
  const failedJobs = await supabaseClient
    .from('scheduled_generation_jobs')
    .select('*')
    .eq('execution_status', 'failed')
    .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000));

  // Send alerts if needed
  if (failedJobs.length > 0) {
    await sendAlert('Failed scheduled jobs detected', failedJobs);
  }
};
```

### 4.5: Create Admin Dashboard for Scheduling (2 days)
**New file:** `app/(admin)/scheduled-jobs.tsx`

```typescript
const ScheduledJobsDashboard = () => {
  const [jobs, setJobs] = useState([]);
  const [metrics, setMetrics] = useState({});

  return (
    <View>
      <Text>Scheduled Jobs Dashboard</Text>
      {/* Job status overview */}
      {/* Failed job alerts */}
      {/* Manual job triggers */}
      {/* Performance metrics */}
    </View>
  );
};
```

---

# 👨‍💼 **Phase 5: Coach Dashboard Enhancements** (5-7 days)

**Priority**: MEDIUM
**Complexity**: Medium
**Dependencies**: Phase 4 complete

## Tasks Breakdown:

### 5.1: Pending Reviews Dashboard (1.5 days)
**File:** `app/(coach)/pending-reviews.tsx`

```typescript
const PendingReviewsDashboard = () => {
  const [pendingPrograms, setPendingPrograms] = useState([]);

  const fetchPendingPrograms = async () => {
    const { data } = await supabase
      .from('workout_programs')
      .select('*, profiles(full_name)')
      .eq('status', 'scheduled_pending_review')
      .order('review_deadline_date', { ascending: true });
    
    setPendingPrograms(data);
  };

  return (
    <View>
      {pendingPrograms.map(program => (
        <ProgramReviewCard 
          key={program.id}
          program={program}
          daysRemaining={calculateDaysRemaining(program.review_deadline_date)}
        />
      ))}
    </View>
  );
};
```

### 5.2: Enhanced Program Review Interface (1.5 days)
**File:** `app/(coach)/program/[id].tsx` (enhanced)

```typescript
const EnhancedProgramReview = ({ programId }) => {
  const [program, setProgram] = useState(null);
  const [cycleHistory, setCycleHistory] = useState([]);

  return (
    <ScrollView>
      {/* Cycle information */}
      <CycleInfoCard 
        cycleNumber={program.cycle_number}
        startDate={program.cycle_start_date}
        reviewDeadline={program.review_deadline_date}
      />
      
      {/* Previous cycle comparison */}
      <PreviousCycleComparison history={cycleHistory} />
      
      {/* Enhanced review actions */}
      <ReviewActions 
        programId={programId}
        onApprove={handleApprove}
        onEdit={handleEdit}
        onRegenerate={handleRegenerate}
      />
    </ScrollView>
  );
};
```

### 5.3: Bulk Review Actions (1 day)
**Component:** `BulkReviewActions.tsx`

```typescript
const BulkReviewActions = ({ selectedPrograms }) => {
  const handleBulkApprove = async () => {
    for (const programId of selectedPrograms) {
      await workoutService.updateProgramStatus(programId, 'coach_approved');
    }
  };

  return (
    <View>
      <ThemedButton title="Approve Selected" onPress={handleBulkApprove} />
      <ThemedButton title="Mark as Edited" onPress={handleBulkEdit} />
    </View>
  );
};
```

### 5.4: Coach Notification System (1 day)
**Service:** `src/services/notificationService.ts`

```typescript
const sendCoachNotification = async (coachId: string, type: string, data: any) => {
  // Send push notification
  await sendPushNotification(coachId, {
    title: getNotificationTitle(type),
    body: getNotificationBody(type, data),
    data: { type, ...data }
  });

  // Store in database
  await supabase
    .from('notifications')
    .insert({
      user_id: coachId,
      type,
      data,
      created_at: new Date()
    });
};
```

### 5.5: Program Transition Management (1 day)
**Component:** `ProgramTransitionManager.tsx`

```typescript
const ProgramTransitionManager = ({ userId }) => {
  const [transitions, setTransitions] = useState([]);
  const [upcomingTransitions, setUpcomingTransitions] = useState([]);

  return (
    <View>
      <Text>Upcoming Transitions</Text>
      {upcomingTransitions.map(transition => (
        <TransitionCard 
          key={transition.id}
          transition={transition}
          onManualTransition={handleManualTransition}
        />
      ))}
    </View>
  );
};
```

---

# 📱 **Phase 6: Client Experience Improvements** (6-8 days)

**Priority**: MEDIUM
**Complexity**: Medium
**Dependencies**: Phase 5 complete

## Tasks Breakdown:

### 6.1: Program History Access (2 days)
**File:** `app/(tabs)/workouts/history.tsx`

```typescript
const ProgramHistory = () => {
  const [currentProgram, setCurrentProgram] = useState(null);
  const [previousProgram, setPreviousProgram] = useState(null);
  const [historicalPrograms, setHistoricalPrograms] = useState([]);

  const fetchProgramHistory = async () => {
    const { data } = await supabase
      .from('client_program_history')
      .select('*')
      .eq('user_id', user.id)
      .order('cycle_number', { ascending: false });

    setCurrentProgram(data.find(p => p.program_type === 'current'));
    setPreviousProgram(data.find(p => p.program_type === 'previous'));
    setHistoricalPrograms(data.filter(p => p.program_type === 'historical'));
  };

  return (
    <ScrollView>
      <ProgramCard program={currentProgram} type="current" />
      <ProgramCard program={previousProgram} type="previous" />
      <HistoricalProgramsList programs={historicalPrograms} />
    </ScrollView>
  );
};
```

### 6.2: Program Transition UI (1.5 days)
**Component:** `ProgramTransitionBanner.tsx`

```typescript
const ProgramTransitionBanner = ({ nextProgram, daysUntilTransition }) => {
  if (daysUntilTransition > 7) return null;

  return (
    <View style={styles.banner}>
      <Text>New program ready in {daysUntilTransition} days!</Text>
      <ThemedButton 
        title="Preview New Program" 
        onPress={() => navigateToProgram(nextProgram.id)}
      />
    </View>
  );
};
```

### 6.3: Workout Continuity (1.5 days)
**Service enhancement:** `src/services/workoutService.ts`

```typescript
const getActiveWorkoutProgram = async (userId: string) => {
  // Always return the current active program
  // Even during transitions, ensure continuity
  const { data } = await supabase
    .from('client_current_program')
    .select('*')
    .eq('user_id', userId)
    .single();

  return data;
};

const handleProgramTransition = async (userId: string) => {
  // Seamless transition without interrupting ongoing workouts
  const activeWorkout = await getActiveWorkout(userId);
  
  if (activeWorkout) {
    // Allow completion of current workout before transition
    await scheduleTransitionAfterWorkout(userId, activeWorkout.id);
  } else {
    // Immediate transition
    await executeTransition(userId);
  }
};
```

### 6.4: Progress Tracking Across Cycles (1 day)
**Component:** `CrossCycleProgress.tsx`

```typescript
const CrossCycleProgress = ({ userId }) => {
  const [progressData, setProgressData] = useState([]);

  const fetchCrossCycleProgress = async () => {
    // Aggregate progress across multiple program cycles
    const { data } = await supabase
      .from('workout_logs')
      .select(`
        *,
        workout_exercises(exercise_id, exercises(name)),
        workout_programs(cycle_number)
      `)
      .eq('user_id', userId)
      .order('completed_at', { ascending: true });

    setProgressData(aggregateProgressByCycle(data));
  };

  return (
    <View>
      <ProgressChart data={progressData} />
      <CycleComparisonTable data={progressData} />
    </View>
  );
};
```

### 6.5: Client Notifications (1 day)
**Service:** `src/services/clientNotificationService.ts`

```typescript
const sendClientNotification = async (userId: string, type: string, data: any) => {
  const notificationConfig = {
    'new_program_ready': {
      title: 'New Workout Program Ready!',
      body: 'Your coach has approved your next 4-week program.'
    },
    'program_transition': {
      title: 'Program Updated',
      body: 'You\'re now on your new 4-week program cycle.'
    },
    'coach_feedback': {
      title: 'Coach Feedback Available',
      body: 'Your coach has reviewed your progress.'
    }
  };

  await sendPushNotification(userId, notificationConfig[type]);
};
```

---

# 🧪 **Phase 7: Testing & Deployment** (7-10 days)

**Priority**: CRITICAL
**Complexity**: Medium
**Dependencies**: All previous phases

## Tasks Breakdown:

### 7.1: Unit Testing (2 days)
**Test files to create:**
- `tests/edge-functions/generate-workout-program.test.ts`
- `tests/edge-functions/daily-scheduler.test.ts`
- `tests/edge-functions/auto-approval.test.ts`
- `tests/services/workoutService.test.ts`

```typescript
// Example test
describe('Generate Workout Program', () => {
  test('should prevent duplicates within 5 minutes', async () => {
    const userId = 'test-user-id';
    
    // First generation
    const result1 = await generateWorkoutProgram({ userId, generationType: 'initial' });
    expect(result1.success).toBe(true);
    
    // Second generation within 5 minutes should be blocked
    const result2 = await generateWorkoutProgram({ userId, generationType: 'initial' });
    expect(result2.message).toContain('race condition prevented');
  });
});
```

### 7.2: Integration Testing (2 days)
**Test scenarios:**
- Complete user journey from intake to multiple program cycles
- Coach review workflow with deadlines
- Automatic program transitions
- Scheduled job execution

### 7.3: Load Testing (1 day)
**Test scenarios:**
- 100+ concurrent users completing intake
- Daily scheduled job with 1000+ users
- Coach dashboard with 50+ pending reviews

### 7.4: Staging Deployment (1 day)
**Deployment checklist:**
- Deploy all Edge Functions
- Apply all database migrations
- Configure cron jobs
- Test all workflows end-to-end

### 7.5: Production Deployment (1 day)
**Deployment plan:**
- Blue-green deployment strategy
- Database migration during low-traffic window
- Gradual rollout of scheduled jobs
- Monitoring and alerting setup

### 7.6: Post-Deployment Monitoring (3 days)
**Monitoring checklist:**
- Scheduled job execution success rates
- Program generation performance
- User experience metrics
- Error rates and response times

---

# 📈 **Success Metrics & KPIs**

## **Technical Metrics:**
- **Zero duplicate programs** (current issue resolution)
- **99.9% scheduled job success rate**
- **<2 second response time** for program generation
- **<1% error rate** across all operations

## **Business Metrics:**
- **100% automatic program progression** for active users
- **<24 hour coach review time** (target: within 7-day window)
- **95% client satisfaction** with program transitions
- **50% reduction in manual coach workload**

## **User Experience Metrics:**
- **Seamless program transitions** (no workout interruptions)
- **Clear visibility** of current vs. previous programs
- **Timely notifications** for all stakeholders

---

# 🚀 **Implementation Timeline**

**Total Duration:** 39-53 days (7.8-10.6 weeks)

**Critical Path:**
1. Phase 1 (Fix duplicates) → Phase 2 (Database) → Phase 3 (Edge Functions) → Phase 4 (Scheduling)
2. Phases 5-6 can run in parallel after Phase 4
3. Phase 7 requires all previous phases complete

**Recommended Team:**
- **1 Backend Developer** (Edge Functions, Database)
- **1 Frontend Developer** (Coach/Client UI)
- **1 DevOps/QA Engineer** (Testing, Deployment)

**Risk Mitigation:**
- Start with Phase 1 immediately to fix current issues
- Implement comprehensive testing at each phase
- Use feature flags for gradual rollout
- Maintain rollback plans for each deployment
