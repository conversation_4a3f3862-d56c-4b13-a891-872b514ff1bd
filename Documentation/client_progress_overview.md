**Goal:** Provides clients with a visual summary of their workout consistency and displays coach feedback. Helps clients stay motivated and informed.

**API relationships:**

* Supabase Database (PostgreSQL):

* Read aggregated data from `workout_logs` (e.g., completion rates, streaks).

* Read `coach_feedback` table (where status is 'published').

* (Future) Read `weekly_checkins` for trends.

**Detailed requirements:**

* Dashboard section for clients to view weekly workout completion rates, intensity trends, and streaks.

* Area to display published coach feedback.

* Visuals must be lightweight and mobile-optimized.

* Data must update in "near real-time" as workouts are logged (i.e., refresh on view or pull-to-refresh).

* Overview should be encouraging and easy to understand.

* CSV export capability for user data (covered in separate "User Data Export" feature, but link might be here).

**Implementation guide:**

* **Architecture overview:**

* **Diagram:**

```

Client (React Native App - Progress Dashboard Screen)

|-- Fetches progress data & feedback via `progressService`

V

Supabase Backend

|-- Database (PostgreSQL - 'workout_logs', 'coach_feedback', 'workout_programs')

|-- (Potentially DB Views or Functions for aggregations)

```

* **Tech-stack justification:**

* React Native: For UI.

* React Native Charts (e.g., `react-native-gifted-charts`, `victory-native`): For simple progress bars, donut charts, line charts.

* Supabase DB: Source data. DB views or functions can pre-aggregate data for performance.

* **Deployment:** Part of main client app.

* **DB schema:**

* Utilizes `workout_logs`, `coach_feedback`, `workout_programs`.

* **Potential DB View for Weekly Completion:**

```sql

CREATE OR REPLACE VIEW public.user_weekly_workout_summary AS

SELECT

wl.user_id,

wp.id as workout_program_id,

date_trunc('week', wl.completed_at AT TIME ZONE 'UTC') AS week_start_date, -- Adjust timezone as needed

COUNT(DISTINCT wl.planned_workout_id) AS completed_workouts_in_week,

-- This calculation for total_planned_workouts_in_week is complex and might need

-- to be derived more carefully based on program_weeks and current date relative to client_start_date

-- For simplicity, this might be calculated client-side or via a dedicated function.

-- Placeholder for logic:

(SELECT COUNT(*)

FROM public.workouts w

JOIN public.program_weeks pw ON w.program_week_id = pw.id

WHERE pw.workout_program_id = wp.id

AND pw.week_number = (EXTRACT(EPOCH FROM (wl.completed_at - wp.client_start_date))/604800)::integer + 1

) AS total_planned_workouts_in_week,

AVG(wl.overall_session_rpe) AS avg_rpe_in_week

FROM

public.workout_logs wl

JOIN public.workout_programs wp ON wl.workout_program_id = wp.id AND wp.status IN ('active_by_client', 'completed_by_client')

WHERE wl.planned_workout_id IS NOT NULL AND wp.client_start_date IS NOT NULL

GROUP BY

wl.user_id,

wp.id,

date_trunc('week', wl.completed_at AT TIME ZONE 'UTC');

```

*Note: Calculating `total_planned_workouts_in_week` accurately within a view relative to the dynamic nature of `program_weeks` and the current week of the program is non-trivial. This often requires joining with `program_weeks` and `workouts` based on the `workout_program_id` and then filtering by the current week number, which can be derived from `client_start_date` of the `workout_programs` table.*

* **Streak Calculation:** Best done in application logic by fetching recent `workout_logs` dates and checking for consecutive days/planned workouts.

* **API design:**

* **Fetch Progress Dashboard Data:**

* Method: Single Supabase Edge Function `get-progress-dashboard` or multiple client-side Supabase queries.

* If Edge Function:

* Request: `{ "userId": "uuid" }`

* Response:

```json

{

"weeklyCompletion": { "completed": 3, "total": 4, "percentage": 75 }, // For current week

"currentStreak": 5, // Number of consecutive completed planned workouts or days

"intensityTrend": [ { "week": 1, "avgRpe": 7.5 }, { "week": 2, "avgRpe": 7.8 } ], // Last 4 weeks

"latestCoachFeedback": { "id": "uuid", "content_snippet": "Great job this week...", "published_at": "timestamp", "is_read": false }

}

```

* Queries (if client-side):

1. Get `workout_logs` for current week and `workout_programs` to calculate completion.

2. Get `workout_logs` for streak calculation.

3. Get aggregated `workout_logs` (or view) for intensity trend.

4. Get latest `coach_feedback` `where user_id = userId AND status = 'published'`.

* **Auth:** User authenticated. RLS applied.

* **Frontend structure:**

* **Component hierarchy:**

* `ClientProgressOverviewScreen.tsx`: Main dashboard screen.

* `WorkoutConsistencyCard.tsx`: Displays progress bar/donut for weekly completion.

* `StreakCard.tsx`: Displays current workout streak.

* `IntensityTrendChart.tsx`: Displays line/bar chart for RPE trends.

* `CoachFeedbackSnippetCard.tsx`: Shows latest feedback, tappable to full view.

* (Re-use) `CoachFeedbackDisplayModal.tsx` for full feedback.

* **State management (`progressStore`):**

* Stores fetched dashboard data.

* Loading/error states for data fetching.

* **Navigation:** Main tab. Tapping feedback card opens modal.

* **CRUD operations (Progress Data - Client Perspective):**

* **Read:** Primary operation. Fetch and display aggregated data.

* **Update/Create/Delete:** Not directly by client on this screen. Data is derived from other actions (logging workouts, coach publishing feedback).

* **UX flow:**

* Refer to `<other-considerations>`: "Client Progress Overview" -> "Screen 1: Progress Dashboard" (Default View, Viewing Full Coach Feedback).

* Dashboard is a primary tab, loads on app open (if logged in and intake complete).

* Data refreshes on screen focus or via pull-to-refresh.

* Charts are simple, clear, and motivating.

* **Security:**

* RLS: User can only see their own progress data and feedback.

* **Testing:**

* **Unit Tests:**

* Chart components with mock data.

* Logic for calculating streak/completion from mock log data.

* State updates in `progressStore`.

* **Integration Tests:**

* Fetching data from mock Supabase (or DB view) and populating dashboard.

* **E2E Tests:**

* User logs a workout, then navigates to dashboard and sees updated stats.

* Coach publishes feedback (mocked), user sees it on dashboard.

* **Performance Tests:** Dashboard load time, especially if aggregations are complex.

* **Data management:**

* **Caching:** Dashboard data can be cached (e.g., `react-query` with stale-while-revalidate).

* **Lifecycle:** Data refreshed on view or periodically.

* **Real-time needs:** "Near real-time" - refresh on app open/screen focus is usually sufficient. True real-time (websockets) is overkill for MVP.

* **Logging & error handling:**

* **Structured logs (PostHog):**

* `ProgressDashboard.viewed {userId}`

* `ProgressDashboard.fetchData.failure {error}`

* `ProgressDashboard.coachFeedbackViewed {feedbackId}`

* **Alerts:** High failure rate fetching dashboard data.

* **Recovery:** Show stale data if available while fetching. Clear error message with retry.
