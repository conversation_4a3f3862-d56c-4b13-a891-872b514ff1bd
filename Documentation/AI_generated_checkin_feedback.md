

**Goal:** Automates the creation of weekly feedback for clients using their tracked workout data and check-in responses. This AI-generated draft is then reviewed, edited, and published by the coach. Client receives the finalized feedback.

**API relationships:**

* Supabase Database (PostgreSQL):

* Read `workout_logs`, `weekly_checkins` for a client.

* Write/Update `coach_feedback` table.

* External AI Model API (e.g., OpenAI GPT):

* Send client data for feedback generation.

* Supabase Functions:

* `process-weekly-feedback`: Orchestrates fetching client data, calling AI, saving draft feedback.

**Detailed requirements (Client-facing aspects):**

* Client receives a notification when coach publishes feedback.

* Client can view published feedback by Monday 11:59 PM (coach's time or defined SLA).

* Feedback is personalized based on their logged data and check-in.

**Implementation guide:**

* **Architecture overview:** (Focus on client-relevant parts)

* **Diagram:**

```

(Backend/Coach Process - Simplified)

1. Scheduled Task (e.g., Monday AM)

|

V

2. Supabase Function ('process-weekly-feedback')

| a. Reads client weekly_checkins, workout_logs

| b. Calls OpenAI to draft feedback

| c. Saves draft to 'coach_feedback' (status: 'draft_by_ai')

V

3. Coach Portal

| a. Coach reviews, edits, and 'publishes' feedback

| (Updates 'coach_feedback' status to 'published', sets 'published_at')

V

4. Supabase DB ('coach_feedback' table)

| (DB Trigger on 'coach_feedback' status='published' OR client app polls/refreshes)

V

Client (React Native App)

| a. Receives Push Notification (New Coach Feedback!)

| b. Fetches and displays published feedback

```

* Client app doesn't interact with AI directly for this. It consumes the final output.

* **DB schema:**

* Utilizes `coach_feedback` table (defined in "Weekly Check-In & Feedback System").

* Key fields for client: `feedback_content`, `published_at`, `status`.

* **API design (Client-facing):**

* **Fetch Published Coach Feedback:** (Same as in "Weekly Check-In & Feedback System")

* `supabase.from('coach_feedback').select('*').eq('user_id', userId).eq('status', 'published').order('published_at', { ascending: false })`

* **Mark Feedback as Read:** (Same as in "Weekly Check-In & Feedback System")

* **Frontend structure (Client-facing):**

* **Component hierarchy:**

* Notification handler (App-level).

* Feedback display areas (Progress Dashboard, Check-In History, dedicated Feedback screen).

* `CoachFeedbackDisplayModal.tsx` or similar.

* **State management (`feedbackStore`):**

* Stores fetched feedback.

* Manages unread feedback count for badges.

* **Navigation:** Deep link from push notification to feedback view.

* **CRUD operations (Coach Feedback - Client Perspective):**

* **Read:** Primary client interaction.

* **Update:** Client implicitly updates `status` to `read_by_client` and `read_at` timestamp.

* **UX flow:**

* Refer to `<other-considerations>`: "AI Feedback Generation & Coach Review"

* "Notification: New Coach Feedback Published" (Push & In-App).

* Client taps notification, deep-links to feedback.

* Feedback displayed clearly. "New" badge until read.

* Expectation set (during check-in submission confirmation) that feedback arrives by Monday evening.

* **Security:**

* RLS: Client can only read their own feedback.

* **Testing (Client-facing):**

* **Unit Tests:** Notification handling. Feedback display components.

* **Integration Tests:** Receiving mock push notification, fetching and displaying feedback.

* **E2E Tests:** Full flow: (Mocked) Coach publishes feedback -> client receives notification -> client views feedback.

* **Data management (Client-facing):**

* **Caching:** Feedback list can be cached.

* **Real-time needs:** Push notification for new feedback is key.

* **Logging & error handling (Client-facing):**

* **Structured logs:**

* `Client.CoachFeedback.NotificationReceived {feedbackId}`

* `Client.CoachFeedback.Viewed {feedbackId}`

* `Client.CoachFeedback.Fetch.Failure {error}`

* **Alerts:** Issues with push notification delivery affecting clients.
