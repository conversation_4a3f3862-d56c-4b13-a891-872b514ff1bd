**Goal:** Users log detailed metrics for each session including sets, reps, weight, RPE/intensity, rest times, and optional notes. Workouts are tracked and stored for future reference. This data is used for progress tracking and AI-generated feedback. Optimize for Mobile UI.

**API relationships:**

* Supabase Database (PostgreSQL):

* Read `workout_programs`, `program_weeks`, `workouts`, `workout_exercises`, `exercises` to display the workout being performed.

* Write to `workout_logs` (header for the session) and `workout_exercise_logs` (details for each exercise/set).

* PostHog: Track user engagement with workout features (e.g., how often sets are logged, timers used, notes added).

**Detailed requirements:**

* UI/UX optimized for easy data entry during a workout.

* Input fields for actual sets, reps, weight, RPE/intensity, rest times per exercise.

* Ability to add optional notes per exercise or per set.

* Automatic logging of workout completion date and duration.

* Display historical workout data (covered partly in Client Progress Overview, detailed view here).

* Timer/stopwatch functionality for rest periods.

* Clients rate overall workout intensity (RPE 1-10) at the end of each session.

* All tracked data saved to client's history, viewable by client and coach.

**Implementation guide:**

* **Architecture overview:**

* **Diagram:**

```

Client (React Native App - Active Workout Session)

|-- Local State (current exercise, set data, timer)

|-- Interacts with `workoutService`

V

Supabase Backend

|-- Database (PostgreSQL - 'workout_logs', 'workout_exercise_logs' tables)

|-- (RLS ensures client writes to their own logs)

```

* **Tech-stack justification:**

* React Native: For UI.

* Zustand/Context (`activeWorkoutStore`): To manage state of an ongoing workout session (current exercise, logged sets, timer).

* Supabase DB: To persist logged workout data.

* PostHog: For product analytics on feature usage.

* **Deployment:** Part of main client app.

* **DB schema:**

* **ER Diagram (Conceptual):**

```

auth.users --< workout_logs >-- workout_exercise_logs

workout_logs references workout_programs.workouts (the planned workout ID, if applicable)

workout_exercise_logs references exercises (the actual exercise performed)

```

* **Table definitions:**

* `workout_logs`: (Header for a completed workout session)

* `id`: UUID, PK.

* `user_id`: UUID, FK to `auth.users.id`.

* `workout_program_id`: UUID, FK to `workout_programs.id`, nullable (if it's a planned workout).

* `planned_workout_id`: UUID, FK to `workouts.id`, nullable (specific planned workout from the program).

* `workout_name_actual`: TEXT, NOT NULL (e.g., "Full Body Strength A" or "Ad-hoc Run").

* `started_at`: TIMESTAMPTZ, NOT NULL.

* `completed_at`: TIMESTAMPTZ, NOT NULL.

* `duration_seconds`: INTEGER, NOT NULL (calculated from started_at, completed_at).

* `overall_session_rpe`: INTEGER, nullable (1-10 scale, client-rated).

* `client_notes_for_session`: TEXT, nullable (overall reflection on the session).

* `created_at`, `updated_at`.

* `workout_exercise_logs`: (Log for each exercise performed in a session)

* `id`: UUID, PK.

* `workout_log_id`: UUID, FK to `workout_logs.id` ON DELETE CASCADE.

* `exercise_id`: UUID, FK to `exercises.id`. (The actual exercise done).

* `planned_workout_exercise_id`: UUID, FK to `workout_exercises.id`, nullable (if part of a planned program).

* `exercise_order`: INTEGER (order performed in this session).

* `sets_logged`: JSONB, NOT NULL.

* Example: `[{"set_number": 1, "reps_actual": 10, "weight_kg_actual": 50, "duration_seconds_actual": null, "rir_actual": 2, "rpe_actual": 8, "rest_completed_seconds": 90, "notes": "Felt good"}, ...]`

* `exercise_notes`: TEXT, nullable (overall notes for this exercise in this session).

* **Indexes:**

* `workout_logs(user_id, completed_at DESC)`.

* `workout_exercise_logs(workout_log_id)`.

* `workout_exercise_logs(exercise_id)`.

* **Migrations:** SQL for creating these tables, RLS policies.

* **API design:**

* **Start Workout Session (Client Action, primarily local state change):**

* Client records `started_at` timestamp locally.

* Prepares to create a `workout_logs` entry upon completion.

* **Log Exercise Set (Client Action, data accumulates locally, batch saved or saved on workout complete):**

* Client adds set data (reps, weight, etc.) to a local array for the current exercise.

* **Save Workout Session:**

* Method: `POST` via Supabase client, likely in a transaction if `workout_logs` and `workout_exercise_logs` are created together.

```javascript

// workoutService.ts

async function saveCompletedWorkout(userId, workoutLogData, exerciseLogsData) {

// workoutLogData: { planned_workout_id, workout_name_actual, started_at, completed_at, duration_seconds, overall_session_rpe, client_notes_for_session }

// exerciseLogsData: array of { exercise_id, planned_workout_exercise_id, exercise_order, sets_logged, exercise_notes }

// Option 1: Single function call (Supabase Edge Function)

// const { data, error } = await supabase.functions.invoke('save-workout-log', {

// body: { workoutLogData, exerciseLogsData }

// });

// Option 2: Client-side transaction (more complex error handling)

// 1. Insert into workout_logs

const { data: log, error: logError } = await supabase

.from('workout_logs')

.insert({ user_id: userId, ...workoutLogData })

.select()

.single();

if (logError) throw logError;

// 2. Add workout_log_id to each exercise log and insert

const logsToInsert = exerciseLogsData.map(exLog => ({

...exLog,

workout_log_id: log.id

}));

const { error: exLogError } = await supabase

.from('workout_exercise_logs')

.insert(logsToInsert);

if (exLogError) {

// Attempt to roll back workout_logs insert or mark as incomplete

// This is where a backend function is simpler for atomicity

throw exLogError;

}

return log;

}

```

* Request: JSON object containing `workout_log_data` and an array of `exercise_logs_data`.

* Response (Success 201): `{ "id": "workout_log_id", ... }`.

* **Fetch Workout History (List):**

* Method: `GET` via Supabase client.

* `supabase.from('workout_logs').select('id, workout_name_actual, completed_at, duration_seconds, overall_session_rpe').eq('user_id', userId).order('completed_at', { ascending: false }).limit(pageSize).offset(pageOffset)`

* Response: Array of workout log summaries.

* **Fetch Detailed Workout Log:**

* Method: `GET` via Supabase client.

* `supabase.from('workout_logs').select('*, workout_exercise_logs(*, exercises(name, video_url))').eq('id', logId).eq('user_id', userId).single()`

* Response: Full workout log with nested exercise logs and exercise details.

* **Auth:** User authenticated. RLS ensures data segregation.

* **Frontend structure:**

* **Component hierarchy:**

* `ActiveWorkoutSessionScreen.tsx`: Main screen for an ongoing workout. Displays current exercise, set logger.

* `CurrentExerciseDisplay.tsx`: Shows prescribed/details of current exercise.

* `SetInputRow.tsx`: Component for logging one set (reps, weight, RPE inputs).

* `RestTimerModal.tsx`: Full-screen or prominent modal for rest timer.

* `WorkoutSummaryScreen.tsx`: Shown after workout completion for RPE rating and notes.

* `WorkoutHistoryListScreen.tsx`: Lists past workouts.

* `WorkoutHistoryDetailScreen.tsx`: Shows full details of a past logged workout.

* **State management (`activeWorkoutStore`):**

* `currentWorkoutPlan`: (if started from a plan) The structured workout being performed.

* `currentExerciseIndex`: Index of the exercise being logged.

* `loggedData`: Accumulates `workout_exercise_logs` data as user progresses.

* `sessionStartTime`: Timestamp.

* `restTimer`: { `isActive`, `duration`, `remaining` }.

* **Navigation:** From `WorkoutDetailsScreen` ("Start Workout") -> `ActiveWorkoutSessionScreen`. After completion -> `WorkoutSummaryScreen`, then back to dashboard or workout schedule.

* **CRUD operations (Workout Logs - Client Perspective):**

* **Create:** User performs workout, logs sets. On "Finish Workout", `workoutService.saveCompletedWorkout()` is called.

* **Read:** User views past logs in `WorkoutHistoryListScreen` and `WorkoutHistoryDetailScreen`.

* **Update:** Generally, past logs are immutable to maintain data integrity. Edits could be a post-MVP feature with strict auditing.

* **Delete:** Deleting past logs might be allowed, with confirmation.

* **Validation:**

* Client-side: Inputs for reps/weight are numeric, within sensible ranges. RPE is 1-10.

* Server-side: DB constraints.

* **Pagination:** For `WorkoutHistoryListScreen`.

* **UX flow:**

* Refer to `<other-considerations>`: "Custom Workout Tracking & Logging" screens:

* Screen 1: Active Workout Session (Logging Interface - Exercise In Progress, Rest Timer Active)

* Screen 1 State 3: Workout Summary / Completion Screen (Rate overall RPE, add notes).

* Screen 2: Workout History View (List View, Detailed Past Workout Log View).

* Workflow:

1. User selects a workout from their plan (or starts an ad-hoc workout - post-MVP).

2. `ActiveWorkoutSessionScreen` loads. First exercise shown.

3. User performs set, inputs data into `SetInputRow` (reps, weight, RPE for set).

4. User taps "Log Set". Data saved to local state. Rest timer auto-starts.

5. After rest, repeat for next set or tap "Next Exercise".

6. After last exercise, tap "Finish Workout".

7. `WorkoutSummaryScreen` appears. User rates overall session RPE (1-10), adds optional session notes.

8. Tap "Save". All data (header log, exercise logs) sent to `workoutService.saveCompletedWorkout()`.

9. PostHog events: `workout_started`, `set_logged`, `exercise_completed`, `rest_timer_used`, `workout_completed`.

* **Security:**

* RLS: Users can only create, read, update (if allowed), delete their own workout logs.

* Coaches can read workout logs of their assigned clients.

* **Testing:**

* **Unit Tests:**

* Set logging logic, input validation.

* Rest timer functionality.

* `activeWorkoutStore` state transitions.

* `workoutService` (mocking Supabase calls).

* **Integration Tests:**

* Saving a full workout session (header + multiple exercise logs) to mock Supabase.

* Fetching and displaying workout history.

* **E2E Tests:**

* User starts a workout, logs several sets for multiple exercises, uses rest timer, finishes workout, rates RPE.

* User views their workout history and a detailed log.

* **Performance Tests:** Responsiveness of `ActiveWorkoutSessionScreen` during logging. Save time for full workout data.

* **Data management:**

* **Caching:** Workout history list can be cached with pagination. Individual detailed logs fetched on demand.

* **Lifecycle:** Logged data is generally permanent unless explicitly deleted.

* **Real-time needs:** No, logging is user-driven.

* **Offline Support (Post-MVP):** Queue logged data locally if offline, sync when connection restored. This significantly increases complexity.

* **Logging & error handling:**

* **Structured logs (PostHog for analytics, Sentry for errors):**

* `WorkoutLogging.saveSession.success {workoutLogId, exerciseCount}`

* `WorkoutLogging.saveSession.failure {error}`

* `WorkoutLogging.fetchHistory.failure {error}`

* `PostHog: workout_started, set_logged, exercise_notes_added, workout_completed, session_rpe_rated`

* **Alerts:** High failure rate saving workout logs.

* **Recovery:**

* If saving fails, retain data locally and allow retry.

* Clear error messages for input validation.
