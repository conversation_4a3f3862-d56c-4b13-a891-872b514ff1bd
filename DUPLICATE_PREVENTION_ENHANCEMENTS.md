# Workout Program Generation - Duplicate Prevention Enhancements

## Overview
I have implemented robust duplicate prevention mechanisms in the `generate-workout-program` Edge Function to handle race conditions that occur when multiple webhook triggers fire in quick succession during user intake completion.

## Enhancements Made

### 1. Enhanced Advisory Locking
- **Improved lock acquisition logging**: Added detailed timestamps and lock ID tracking
- **Better lock release handling**: Enhanced error handling for lock release with proper cleanup
- **Lock busy response**: Returns success (not error) when lock is busy to prevent webhook retries

### 2. Enhanced Eligibility Checking
- **5-minute race condition prevention**: Checks for any programs created in the last 5 minutes before checking other eligibility criteria
- **Improved logging**: Added detailed logging for all eligibility checks with timestamps
- **Better response data**: Enhanced response includes more context about why generation was skipped

### 3. Generation Status Tracking (Optional)
- **Tracking table**: Created `generation_tracking` table to monitor generation processes
- **In-progress status**: Tracks when generation starts and cleans up when complete
- **Non-critical implementation**: If tracking table doesn't exist, function continues normally

### 4. Enhanced Logging Throughout
- **Detailed timestamps**: All log entries now include ISO timestamps
- **Process tracking**: Better visibility into each step of the generation process
- **Error context**: Enhanced error messages with more context for debugging

## Key Race Condition Protections

### Primary Protection: Advisory Locks
```typescript
const lockId = parseInt(userId.replace(/-/g, '').substring(0, 8), 16)
const { data: lockResult } = await supabaseClient.rpc('pg_try_advisory_lock', { key: lockId })

if (!lockResult) {
  return new Response(JSON.stringify({
    success: true, // Important: return success to prevent webhook retries
    message: 'Program generation already in progress for this user'
  }))
}
```

### Secondary Protection: 5-Minute Window Check
```typescript
const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString()
const { data: recentPrograms } = await supabaseClient
  .from('workout_programs')
  .select('*')
  .eq('user_id', userId)
  .gte('created_at', fiveMinutesAgo)

if (recentPrograms && recentPrograms.length > 0) {
  return { eligible: false, reason: 'Recent program already exists (race condition prevented)' }
}
```

### Tertiary Protection: Generation Tracking (Optional)
- Creates temporary tracking records during generation
- Provides additional visibility into concurrent generation attempts
- Automatically cleans up old records

## Database Changes

### New Table: generation_tracking
```sql
CREATE TABLE generation_tracking (
  id TEXT PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  generation_type TEXT NOT NULL CHECK (generation_type IN ('initial', 'scheduled', 'manual')),
  cycle_number INTEGER,
  status TEXT NOT NULL CHECK (status IN ('in_progress', 'completed', 'failed')),
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

## Testing Instructions

### 1. Deploy the Enhanced Function
```bash
npx supabase functions deploy generate-workout-program
```

### 2. Test Race Condition Scenario
To test the duplicate prevention:

1. **Create a test user** and complete their intake up to the last step
2. **Simulate rapid webhook triggers** by making multiple concurrent calls:
   ```bash
   # Terminal 1
   curl -X POST https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/generate-workout-program \
     -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
     -H "Content-Type: application/json" \
     -d '{"userId": "TEST_USER_ID", "generationType": "initial"}'

   # Terminal 2 (run immediately after Terminal 1)
   curl -X POST https://uejvzxziybtvtgdbkffq.supabase.co/functions/v1/generate-workout-program \
     -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
     -H "Content-Type: application/json" \
     -d '{"userId": "TEST_USER_ID", "generationType": "initial"}'
   ```

3. **Expected Results**:
   - First call: Should succeed and generate a program
   - Second call: Should return success with message "Program generation already in progress" or "Recent program already exists"
   - Database: Should contain exactly ONE workout program for the user

### 3. Monitor Logs
Check the Edge Function logs for detailed tracking:
- Look for `GenerateProgram.lockBusy` entries
- Check for `GenerateProgram.recentProgramFound` entries
- Verify `GenerateProgram.success` appears only once per user

### 4. Verify Database State
```sql
-- Check for duplicate programs
SELECT user_id, COUNT(*) as program_count, 
       MIN(created_at) as first_created, 
       MAX(created_at) as last_created
FROM workout_programs 
WHERE user_id = 'TEST_USER_ID'
GROUP BY user_id
HAVING COUNT(*) > 1;

-- Check generation tracking (if table exists)
SELECT * FROM generation_tracking 
WHERE user_id = 'TEST_USER_ID' 
ORDER BY started_at DESC;
```

## Expected Behavior

### Successful Duplicate Prevention
- **First webhook**: Acquires lock, generates program successfully
- **Second webhook**: Finds lock busy OR recent program exists, returns success without generating
- **Result**: Exactly one program created, no errors, no webhook retries

### Log Patterns to Look For
```
GenerateProgram.start [userId] - First invocation starts
GenerateProgram.acquiringLock [userId] - Lock acquisition attempt
GenerateProgram.lockAcquired [userId] - First process gets lock
GenerateProgram.start [userId] - Second invocation starts
GenerateProgram.lockBusy [userId] - Second process finds lock busy
GenerateProgram.success [userId] - First process completes
GenerateProgram.lockReleased [userId] - Lock released
```

## Rollback Plan
If issues occur, the previous version can be restored by reverting the changes to the Edge Function. The database changes are non-breaking and can remain in place.

## Files Modified
- `supabase/functions/generate-workout-program/index.ts` - Enhanced duplicate prevention
- `supabase/migrations/20250712000002_create_generation_tracking_table.sql` - Optional tracking table

## Next Steps
1. Deploy the enhanced function
2. Test with the race condition scenario
3. Monitor logs for the first few real user registrations
4. Verify no duplicate programs are created
5. Consider adding automated monitoring alerts for duplicate detection
