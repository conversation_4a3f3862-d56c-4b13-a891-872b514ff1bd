name: Mobile App Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'mobile/**'
      - '.github/workflows/mobile-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'mobile/**'
      - '.github/workflows/mobile-tests.yml'

jobs:
  test:
    name: Test Mobile App
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
        
    - name: Install dependencies
      working-directory: ./mobile
      run: npm ci
      
    - name: Run linter
      working-directory: ./mobile
      run: npm run lint
      
    - name: Run type check
      working-directory: ./mobile
      run: npm run type-check
      
    - name: Run tests
      working-directory: ./mobile
      run: npm run test:ci
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./mobile/coverage/lcov.info
        directory: ./mobile/coverage/
        flags: mobile
        name: mobile-coverage
        fail_ci_if_error: false
        
    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.node-version }}
        path: |
          mobile/coverage/
          mobile/test-report.md
          
  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
        
    - name: Install dependencies
      working-directory: ./mobile
      run: npm ci
      
    - name: Run security audit
      working-directory: ./mobile
      run: npm audit --audit-level=moderate
      
    - name: Run dependency check
      working-directory: ./mobile
      run: npx audit-ci --moderate
      
  build:
    name: Build Check
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
        
    - name: Install dependencies
      working-directory: ./mobile
      run: npm ci
      
    - name: Setup Expo CLI
      run: npm install -g @expo/cli
      
    - name: Expo prebuild
      working-directory: ./mobile
      run: expo prebuild --no-install
      
    - name: Check bundle size
      working-directory: ./mobile
      run: |
        expo export --platform web --output-dir dist
        du -sh dist/
        
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
        
    - name: Install dependencies
      working-directory: ./mobile
      run: npm ci
      
    - name: Run performance tests
      working-directory: ./mobile
      run: |
        # Add performance testing commands here
        echo "Performance tests would run here"
        
    - name: Bundle analysis
      working-directory: ./mobile
      run: |
        # Analyze bundle size and performance
        npx expo export --platform web --output-dir dist
        npx bundlesize
        
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [test, security, build, performance]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.test.result == 'success' && needs.security.result == 'success' && needs.build.result == 'success'
      run: |
        echo "✅ All mobile app tests passed!"
        
    - name: Notify failure
      if: needs.test.result == 'failure' || needs.security.result == 'failure' || needs.build.result == 'failure'
      run: |
        echo "❌ Mobile app tests failed!"
        exit 1
