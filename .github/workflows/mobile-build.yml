name: Mobile App Build & Deploy

on:
  push:
    branches: [ main ]
    paths:
      - 'mobile/**'
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to build'
        required: true
        default: 'all'
        type: choice
        options:
          - ios
          - android
          - all
      profile:
        description: 'Build profile'
        required: true
        default: 'production'
        type: choice
        options:
          - development
          - preview
          - production
      submit:
        description: 'Submit to app stores'
        required: false
        default: false
        type: boolean

jobs:
  build:
    name: EAS Build
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json
        
    - name: Setup Expo CLI
      run: npm install -g @expo/cli@latest
      
    - name: Setup EAS CLI
      run: npm install -g eas-cli@latest
      
    - name: Install dependencies
      working-directory: ./mobile
      run: npm ci
      
    - name: Run tests
      working-directory: ./mobile
      run: npm run test:ci
      
    - name: Verify EAS project
      working-directory: ./mobile
      run: eas whoami
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        
    - name: Build iOS app
      if: github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all' || github.ref_type == 'tag'
      working-directory: ./mobile
      run: |
        eas build --platform ios --profile ${{ github.event.inputs.profile || 'production' }} --non-interactive
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.EXPO_PUBLIC_SUPABASE_ANON_KEY }}
        
    - name: Build Android app
      if: github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all' || github.ref_type == 'tag'
      working-directory: ./mobile
      run: |
        eas build --platform android --profile ${{ github.event.inputs.profile || 'production' }} --non-interactive
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.EXPO_PUBLIC_SUPABASE_ANON_KEY }}
        
    - name: Submit iOS to App Store
      if: (github.event.inputs.submit == 'true' || github.ref_type == 'tag') && (github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all')
      working-directory: ./mobile
      run: |
        eas submit --platform ios --latest --non-interactive
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        EXPO_APPLE_ID: ${{ secrets.EXPO_APPLE_ID }}
        EXPO_APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.EXPO_APPLE_APP_SPECIFIC_PASSWORD }}
        
    - name: Submit Android to Google Play
      if: (github.event.inputs.submit == 'true' || github.ref_type == 'tag') && (github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all')
      working-directory: ./mobile
      run: |
        eas submit --platform android --latest --non-interactive
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        GOOGLE_SERVICE_ACCOUNT_KEY: ${{ secrets.GOOGLE_SERVICE_ACCOUNT_KEY }}
        
    - name: Notify success
      if: success()
      run: |
        echo "✅ Mobile app build completed successfully!"
        echo "Check the EAS dashboard for download links."
        
    - name: Notify failure
      if: failure()
      run: |
        echo "❌ Mobile app build failed!"
        exit 1

  update-version:
    name: Update Version
    runs-on: ubuntu-latest
    if: github.ref_type == 'tag'
    needs: build
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        
    - name: Extract version from tag
      id: version
      run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
      
    - name: Update package.json version
      working-directory: ./mobile
      run: |
        npm version ${{ steps.version.outputs.VERSION }} --no-git-tag-version
        
    - name: Commit version update
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add mobile/package.json mobile/app.json
        git commit -m "chore: update version to ${{ steps.version.outputs.VERSION }}" || exit 0
        git push

  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    if: github.ref_type == 'tag'
    needs: [build, update-version]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: FitnessApp Mobile ${{ github.ref }}
        body: |
          ## 🚀 FitnessApp Mobile Release
          
          ### What's New
          - Bug fixes and performance improvements
          - Enhanced user experience
          - Updated dependencies
          
          ### Download
          - iOS: Available on the App Store
          - Android: Available on Google Play Store
          
          ### Build Information
          - Built with EAS Build
          - Tested on CI/CD pipeline
          - Automated deployment
          
          For technical details, see the [EAS Build dashboard](https://expo.dev).
        draft: false
        prerelease: false
