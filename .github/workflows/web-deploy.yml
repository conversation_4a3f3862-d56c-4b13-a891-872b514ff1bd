name: Web App Deploy

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'web/**'
      - '.github/workflows/web-deploy.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'web/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - production

jobs:
  test:
    name: Test Web App
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: web/package-lock.json
        
    - name: Install dependencies
      working-directory: ./web
      run: npm ci
      
    - name: Run linter
      working-directory: ./web
      run: npm run lint
      
    - name: Run type check
      working-directory: ./web
      run: npm run type-check
      
    - name: Run tests
      working-directory: ./web
      run: npm run test:ci
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
        NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
        
    - name: Build application
      working-directory: ./web
      run: npm run build
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
        NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: web-build
        path: web/.next/
        retention-days: 1

  deploy-preview:
    name: Deploy Preview
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'pull_request' || (github.event_name == 'push' && github.ref == 'refs/heads/develop') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'preview')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: web/package-lock.json
        
    - name: Install dependencies
      working-directory: ./web
      run: npm ci
      
    - name: Deploy to Vercel Preview
      working-directory: ./web
      run: |
        npm install -g vercel@latest
        vercel --token ${{ secrets.VERCEL_TOKEN }} --yes
      env:
        VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
        VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
        VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
        NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
        
    - name: Comment PR with preview URL
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const { data: deployments } = await github.rest.repos.listDeployments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            ref: context.payload.pull_request.head.sha,
            per_page: 1
          });
          
          if (deployments.length > 0) {
            const deployment = deployments[0];
            const { data: statuses } = await github.rest.repos.listDeploymentStatuses({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: deployment.id
            });
            
            const successStatus = statuses.find(status => status.state === 'success');
            if (successStatus) {
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `🚀 **Preview deployment ready!**\n\n✅ Preview: ${successStatus.target_url}\n\nThe latest changes are deployed and ready for review.`
              });
            }
          }

  deploy-production:
    name: Deploy Production
    runs-on: ubuntu-latest
    needs: test
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: web/package-lock.json
        
    - name: Install dependencies
      working-directory: ./web
      run: npm ci
      
    - name: Deploy to Vercel Production
      working-directory: ./web
      run: |
        npm install -g vercel@latest
        vercel --prod --token ${{ secrets.VERCEL_TOKEN }} --yes
      env:
        VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
        VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
        VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
        NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
        
    - name: Run post-deployment tests
      working-directory: ./web
      run: |
        # Add post-deployment smoke tests here
        echo "Running post-deployment verification..."
        curl -f ${{ secrets.NEXT_PUBLIC_APP_URL }} || exit 1
        curl -f ${{ secrets.NEXT_PUBLIC_APP_URL }}/api/health || exit 1
        
    - name: Notify deployment success
      run: |
        echo "✅ Production deployment successful!"
        echo "URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}"

  lighthouse:
    name: Lighthouse Audit
    runs-on: ubuntu-latest
    needs: deploy-production
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        urls: |
          ${{ secrets.NEXT_PUBLIC_APP_URL }}
          ${{ secrets.NEXT_PUBLIC_APP_URL }}/auth/login
          ${{ secrets.NEXT_PUBLIC_APP_URL }}/dashboard
        configPath: './web/lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true
        
    - name: Comment PR with Lighthouse results
      if: github.event_name == 'pull_request'
      uses: treosh/lighthouse-ci-action@v10
      with:
        urls: |
          ${{ secrets.NEXT_PUBLIC_APP_URL }}
        uploadArtifacts: true
        temporaryPublicStorage: true
