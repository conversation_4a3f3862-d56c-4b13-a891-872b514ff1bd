{"expo": {"name": "FitAI - AI Workout Coach", "slug": "<PERSON><PERSON>-workout-coach", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "description": "AI-powered personalized workout programs with coach support. Get custom 4-week training plans, track progress, and communicate with certified trainers.", "keywords": ["fitness", "workout", "AI", "personal trainer", "exercise", "health"], "privacy": "public", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "notification": {"icon": "./assets/notification-icon.png", "color": "#000000", "iosDisplayInForeground": true, "androidMode": "default", "androidCollapsedTitle": "New notification"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.fitai.workoutcoach", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to take progress photos for your fitness journey.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to save and view progress photos.", "NSMicrophoneUsageDescription": "This app may use the microphone for video messages with your coach.", "NSUserNotificationsUsageDescription": "This app sends notifications for workout reminders and coach messages."}, "config": {"usesNonExemptEncryption": false}, "associatedDomains": ["applinks:fitai-workout.com"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.fitai.workoutcoach", "versionCode": 1, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "RECORD_AUDIO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK"], "intentFilters": [{"action": "VIEW", "data": [{"scheme": "https", "host": "fitai-workout.com"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "defaultChannel": "default"}], ["expo-camera", {"cameraPermission": "Allow FitAI to access your camera to take progress photos."}]], "scheme": "fitai", "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "uejvzxziybtvtgdbkffq"}}, "updates": {"fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/uejvzxziybtvtgdbkffq"}, "runtimeVersion": {"policy": "sdkVersion"}}}