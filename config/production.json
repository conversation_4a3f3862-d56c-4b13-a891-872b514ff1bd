{"app": {"name": "FitnessApp", "version": "1.0.0", "environment": "production", "url": "https://fitnessapp.com", "api_url": "https://fitnessapp.com/api"}, "database": {"supabase": {"url": "${NEXT_PUBLIC_SUPABASE_URL}", "anon_key": "${NEXT_PUBLIC_SUPABASE_ANON_KEY}", "service_role_key": "${SUPABASE_SERVICE_ROLE_KEY}", "jwt_secret": "${SUPABASE_JWT_SECRET}"}, "connection_pool": {"max_connections": 20, "idle_timeout": 30000, "connection_timeout": 10000}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 30}}, "authentication": {"providers": {"email": true, "google": true, "apple": true}, "session": {"duration": 86400, "refresh_threshold": 3600}, "security": {"password_min_length": 8, "require_email_verification": true, "max_login_attempts": 5, "lockout_duration": 900}}, "api": {"rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_limit": 200}, "cors": {"origins": ["https://fitnessapp.com", "https://www.fitnessapp.com"], "methods": ["GET", "POST", "PUT", "DELETE", "PATCH"], "credentials": true}, "timeout": 30000}, "storage": {"supabase": {"bucket": "fitness-app-storage", "max_file_size": 10485760, "allowed_types": ["image/jpeg", "image/png", "image/webp", "video/mp4", "application/pdf"]}, "cdn": {"enabled": true, "cache_duration": 31536000}}, "email": {"provider": "resend", "from_address": "<EMAIL>", "from_name": "FitnessApp", "templates": {"welcome": "welcome-template", "password_reset": "password-reset-template", "workout_reminder": "workout-reminder-template"}}, "notifications": {"push": {"enabled": true, "providers": {"expo": true, "fcm": true, "apns": true}}, "email": {"enabled": true, "digest_frequency": "daily"}}, "analytics": {"google_analytics": {"enabled": true, "tracking_id": "${GOOGLE_ANALYTICS_ID}"}, "mixpanel": {"enabled": true, "token": "${MIXPANEL_TOKEN}"}, "custom_events": {"workout_completed": true, "program_started": true, "goal_achieved": true}}, "monitoring": {"sentry": {"enabled": true, "dsn": "${SENTRY_DSN}", "environment": "production", "sample_rate": 0.1}, "uptime": {"enabled": true, "check_interval": 60, "timeout": 10}, "performance": {"enabled": true, "sample_rate": 0.1}}, "security": {"headers": {"hsts": {"max_age": 31536000, "include_subdomains": true, "preload": true}, "csp": {"default_src": ["'self'"], "script_src": ["'self'", "'unsafe-inline'", "https://www.googletagmanager.com"], "style_src": ["'self'", "'unsafe-inline'"], "img_src": ["'self'", "data:", "https:"], "connect_src": ["'self'", "https://*.supabase.co"]}, "x_frame_options": "DENY", "x_content_type_options": "nosniff", "referrer_policy": "strict-origin-when-cross-origin"}, "encryption": {"algorithm": "aes-256-gcm", "key": "${ENCRYPTION_KEY}"}}, "caching": {"redis": {"enabled": true, "url": "${UPSTASH_REDIS_REST_URL}", "token": "${UPSTASH_REDIS_REST_TOKEN}", "ttl": 3600}, "cdn": {"enabled": true, "max_age": 31536000}}, "logging": {"level": "info", "format": "json", "destinations": ["console", "file", "sentry"], "retention_days": 30}, "features": {"ai_workout_generation": true, "coach_messaging": true, "progress_tracking": true, "social_features": false, "premium_features": true}, "limits": {"max_workouts_per_user": 1000, "max_programs_per_user": 50, "max_photos_per_user": 100, "max_file_size": 10485760}, "maintenance": {"enabled": false, "message": "FitnessApp is currently undergoing maintenance. We'll be back shortly!", "allowed_ips": []}}