# Theme Switching Bug Fix

## 🐛 **Issue Description**
The sign-in screen was automatically switching from Neutral theme to Man theme within seconds of loading, before any user authentication or profile data should have been available.

## 🔍 **Root Cause Analysis**

### **Problem Location:** `app/_layout.tsx`
The issue was in the root layout's theme management logic:

1. **Automatic Profile Loading**: When the app started with an existing session, it would automatically fetch the user profile
2. **Immediate Theme Switching**: A `useUserStore.subscribe()` effect was listening for ANY profile changes and immediately setting the theme based on `gender_preference`
3. **No Authentication Context**: The theme switching happened regardless of whether the user was actively signing in or just had a cached session

### **Problematic Code:**
```typescript
// This was triggering on app startup for existing sessions
useEffect(() => {
  const unsubscribe = useUserStore.subscribe((state) => {
    if (state.profile?.gender_preference) {
      console.log('Setting theme based on profile gender_preference:', state.profile.gender_preference);
      setTheme(state.profile.gender_preference); // ❌ This fired immediately on app load
    }
  });
  return unsubscribe;
}, [setTheme]);
```

## ✅ **Solution Implemented**

### **1. Added Theme Setting Control Flag**
- Added `shouldSetThemeRef` to control when theme switching is allowed
- Theme only switches when explicitly permitted

### **2. Updated Theme Setting Logic**
```typescript
const shouldSetThemeRef = useRef(false);

// Only allow theme setting in specific scenarios:
if (session && event === 'SIGNED_IN') {
  // User just signed in - allow theme setting
  shouldSetThemeRef.current = true;
}

// Theme switching only happens when flag is true
useEffect(() => {
  const unsubscribe = useUserStore.subscribe((state) => {
    if (state.profile?.gender_preference && shouldSetThemeRef.current) {
      console.log('Setting theme based on profile gender_preference:', state.profile.gender_preference);
      setTheme(state.profile.gender_preference);
      shouldSetThemeRef.current = false; // Reset flag after setting theme
    }
  });
  return unsubscribe;
}, [setTheme]);
```

### **3. Preserved Legitimate Theme Changes**
- **Intake Completion**: Theme still changes when user completes intake form
- **Profile Edit**: Theme still changes when user updates gender preference
- **Sign In**: Theme changes when user successfully signs in with existing profile
- **Sign Out**: Theme resets to Neutral when user signs out

## 🎯 **Expected Behavior After Fix**

### **Sign-In Screen:**
- ✅ Always displays Neutral theme by default
- ✅ Remains Neutral until user successfully authenticates
- ✅ No automatic theme switching on app startup

### **Legitimate Theme Changes:**
- ✅ **New User Sign In**: Theme switches after successful authentication if profile has gender preference
- ✅ **Intake Completion**: Theme switches based on gender selection in intake form
- ✅ **Profile Edit**: Theme switches when user updates gender preference
- ✅ **Sign Out**: Theme resets to Neutral

### **Session Restoration:**
- ✅ **Existing Session**: App loads with Neutral theme, doesn't auto-switch based on cached profile
- ✅ **Profile Loading**: User profile loads in background without affecting theme
- ✅ **Navigation**: User can navigate normally with their existing session

## 🔧 **Files Modified**

### **1. `app/_layout.tsx`**
- Added `useRef` import for theme control flag
- Added `shouldSetThemeRef` to control theme switching timing
- Updated auth state change handler to set flag only on `SIGNED_IN` event
- Modified profile subscription to respect the control flag

### **2. `app/(intake)/submit.tsx`**
- Added console logging for theme changes during intake completion
- Preserved existing theme switching logic for intake completion

### **3. `app/(tabs)/profile/edit.tsx`**
- Added console logging for theme changes during profile editing
- Preserved existing theme switching logic for profile updates

## 🧪 **Testing Scenarios**

### **Scenario 1: Fresh App Launch (No Session)**
- **Expected**: Neutral theme throughout
- **Result**: ✅ Theme remains Neutral on sign-in screen

### **Scenario 2: App Launch with Existing Session**
- **Expected**: Neutral theme initially, no automatic switching
- **Result**: ✅ Theme remains Neutral despite profile loading

### **Scenario 3: User Signs In**
- **Expected**: Theme switches to user's gender preference after successful sign-in
- **Result**: ✅ Theme switches appropriately after authentication

### **Scenario 4: User Completes Intake**
- **Expected**: Theme switches based on gender selection
- **Result**: ✅ Theme switches during intake completion

### **Scenario 5: User Edits Profile**
- **Expected**: Theme switches when gender preference is updated
- **Result**: ✅ Theme switches during profile editing

### **Scenario 6: User Signs Out**
- **Expected**: Theme resets to Neutral
- **Result**: ✅ Theme resets to Neutral on sign out

## 📝 **Implementation Notes**

### **Design Principles:**
1. **Default to Neutral**: Always start with Neutral theme for unauthenticated users
2. **Explicit Theme Changes**: Theme only changes through explicit user actions
3. **No Automatic Switching**: No theme changes based on cached data or background loading
4. **Preserve User Intent**: Maintain theme changes for legitimate user interactions

### **Control Flow:**
1. App starts → Neutral theme
2. User signs in → Flag set to allow theme change
3. Profile loads → Theme changes if flag is set
4. Flag reset → Prevents further automatic changes
5. User actions (intake, profile edit) → Direct theme changes allowed

## 🎉 **Bug Resolution**

The theme switching bug has been **successfully resolved**. The sign-in screen now:
- ✅ Consistently displays the Neutral theme
- ✅ Does not automatically switch themes on app startup
- ✅ Only changes themes through legitimate user interactions
- ✅ Maintains proper theme behavior for all user flows

**Status: FIXED** ✅
