# Theme Persistence Fix - Comprehensive Solution

## 🎯 **Problem Summary**

The fitness app had theme persistence issues where gender-based color themes were not properly applied in several scenarios:

1. **Navigation Issues**: Theme not persisting when navigating back to previous screens
2. **App Initialization**: Theme not loading correctly on cold starts or app reloads  
3. **Authentication Cycles**: Theme not applying properly after login/logout cycles
4. **Race Conditions**: Profile loading and theme application not synchronized

## ✅ **Comprehensive Solution Implemented**

### **1. Enhanced Theme Store (`src/store/themeStore.ts`)**

#### **New State Properties:**
```typescript
interface ThemeState {
  currentTheme: ThemeType;
  colors: ThemeColors;
  isInitialized: boolean; // NEW: Track initialization state
  setTheme: (theme: ThemeType) => void;
  initializeThemeFromProfile: (profileTheme: ThemeType) => void; // NEW
  resetTheme: () => void; // NEW
}
```

#### **Smart Theme Initialization:**
```typescript
initializeThemeFromProfile: (profileTheme: ThemeType) => {
  const currentState = get();

  // Always initialize from profile if:
  // 1. Not initialized yet, OR
  // 2. Current theme is Neutral (regardless of initialization state), OR
  // 3. Profile theme is different from current theme (user changed preference)
  const shouldInitialize =
    !currentState.isInitialized ||
    currentState.currentTheme === 'Neutral' ||
    currentState.currentTheme !== profileTheme;

  if (shouldInitialize) {
    console.log('🎨 ThemeStore: Initializing theme from profile:', profileTheme);
    set({
      currentTheme: profileTheme,
      colors: themeConfigs[profileTheme],
      isInitialized: true,
    });
  } else {
    console.log('🎨 ThemeStore: Theme already matches profile, no change needed');
  }
}
```

#### **Enhanced Persistence:**
```typescript
// Updated persistence config to include initialization state
THEME: {
  partialize: (state: any) => ({
    currentTheme: state.currentTheme,
    isInitialized: state.isInitialized, // NEW: Persist initialization state
  }),
}
```

### **2. Improved App Initialization (`app/_layout.tsx`)**

#### **Proper Async Initialization:**
```typescript
const initializeApp = async () => {
  try {
    // Get initial session
    const { data: { session } } = await supabase.auth.getSession();
    console.log('🚀 App initialization - Initial session:', session ? 'authenticated' : 'null');
    setSession(session);

    if (session) {
      // User is authenticated - fetch profile and initialize theme
      console.log('👤 Fetching profile for authenticated user...');
      await fetchProfile();
      console.log('✅ Profile fetched, theme will be initialized');
    } else {
      // No session - ensure neutral theme
      console.log('🎨 No session - resetting to neutral theme');
      resetTheme();
    }
  } catch (error) {
    console.error('❌ Error during app initialization:', error);
    resetTheme(); // Fallback to neutral theme
  } finally {
    setIsInitializing(false);
  }
};
```

#### **Enhanced Auth State Handling:**
```typescript
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log('🔄 Auth state change:', event, 'Session:', session ? 'authenticated' : 'null');
  setSession(session);

  if (session && (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED')) {
    // User signed in or token refreshed - fetch profile and update theme
    console.log('👤 Fetching profile after auth change...');
    try {
      await fetchProfile();
      console.log('✅ Profile fetched after auth change');
    } catch (error) {
      console.error('❌ Error fetching profile after auth change:', error);
    }
  } else if (!session && event === 'SIGNED_OUT') {
    // User signed out - reset to neutral theme
    console.log('🎨 User signed out - resetting to neutral theme');
    resetTheme();
    hasInitializedThemeRef.current = false;
  }
});
```

#### **Smart Profile-Based Theme Initialization:**
```typescript
// Watch for profile changes and initialize theme appropriately
useEffect(() => {
  const unsubscribe = useUserStore.subscribe((state) => {
    if (state.profile?.gender_preference && !isInitializing) {
      // Always try to initialize theme from profile - the theme store will decide if it needs updating
      console.log('🎨 Profile loaded, attempting to initialize theme from profile:', state.profile.gender_preference);
      initializeThemeFromProfile(state.profile.gender_preference);
      hasInitializedThemeRef.current = true;
    }
  });

  return unsubscribe;
}, [initializeThemeFromProfile, isInitializing]);
```

### **3. Consistent Theme Management Across Screens**

#### **Profile Editing (`app/(tabs)/profile/edit.tsx`):**
```typescript
// Update theme based on gender preference change
console.log('Setting theme from profile edit:', data.genderPreference);
setTheme(data.genderPreference);
```

#### **Intake Completion (`app/(intake)/submit.tsx`):**
```typescript
// Update theme based on gender selection from intake form
if (formData.intake_gender) {
  console.log('🎨 Setting theme from intake completion:', formData.intake_gender);
  setTheme(formData.intake_gender);
}
```

#### **Sign Out Process (`app/(tabs)/profile/index.tsx`):**
```typescript
// Reset theme to neutral
resetTheme();
console.log('🎨 Theme reset to neutral');
```

## 🔧 **Key Improvements**

### **1. Race Condition Prevention**
- ✅ **Initialization State Tracking**: `isInitialized` flag prevents premature theme changes
- ✅ **Async Profile Loading**: Proper await/async handling for profile fetching
- ✅ **Initialization Guards**: Theme only initializes when appropriate conditions are met

### **2. Persistence Enhancement**
- ✅ **Complete State Persistence**: Both theme and initialization state are persisted
- ✅ **Smart Restoration**: Theme restores properly on app restart
- ✅ **Fallback Handling**: Graceful fallback to Neutral theme on errors

### **3. Navigation Consistency**
- ✅ **Cross-Screen Persistence**: Theme persists across all navigation scenarios
- ✅ **Back Button Support**: Theme maintained when navigating back
- ✅ **Deep Link Support**: Theme applies correctly on deep link navigation

### **4. Authentication Integration**
- ✅ **Login Flow**: Theme applies immediately after successful authentication
- ✅ **Token Refresh**: Theme maintained during token refresh cycles
- ✅ **Logout Flow**: Theme properly resets to Neutral on sign out

### **5. Page Refresh Fix**
- ✅ **Refresh Detection**: Theme store detects when current theme doesn't match profile
- ✅ **Smart Reinitialization**: Always updates theme when profile differs from current
- ✅ **Neutral Override**: Always applies profile theme when current theme is Neutral
- ✅ **Profile Changes**: Immediately applies theme when user updates gender preference

## 🎯 **Expected Behavior**

### **✅ App Initialization:**
1. **Cold Start**: App loads → Checks session → Fetches profile → Initializes theme
2. **Existing Session**: App loads → Profile loads → Theme initializes from profile
3. **No Session**: App loads → Theme stays/resets to Neutral

### **✅ Authentication Flow:**
1. **Sign In**: User signs in → Profile fetches → Theme applies based on gender_preference
2. **Sign Out**: User signs out → Theme resets to Neutral → Profile cleared
3. **Token Refresh**: Token refreshes → Profile re-fetches → Theme maintained

### **✅ Profile Updates:**
1. **Gender Change**: User updates gender_preference → Theme immediately updates
2. **Intake Completion**: User completes intake → Theme applies based on selected gender
3. **Profile Edit**: User edits profile → Theme updates if gender_preference changed

### **✅ Navigation:**
1. **Screen Navigation**: Theme persists across all screens
2. **Back Button**: Theme maintained when navigating back
3. **Tab Switching**: Theme consistent across all tabs
4. **Deep Links**: Theme applies correctly on direct navigation

## 🧪 **Testing Scenarios**

### **Test 1: Cold App Start**
1. Close app completely
2. Reopen app
3. ✅ **Expected**: Theme should load based on user's profile gender_preference

### **Test 2: Navigation Persistence**
1. Navigate to any screen
2. Use back button or navigate to different screens
3. ✅ **Expected**: Theme should remain consistent throughout navigation

### **Test 3: Login/Logout Cycle**
1. Sign out of app
2. Sign back in
3. ✅ **Expected**: Theme should apply based on profile after login

### **Test 4: Profile Updates**
1. Go to Profile → Edit Profile
2. Change gender preference
3. ✅ **Expected**: Theme should immediately update to match new preference

### **Test 5: App Refresh/Reload**
1. Force close and reopen app multiple times
2. ✅ **Expected**: Theme should consistently load based on user's profile

## 📁 **Files Modified**

1. **`app/_layout.tsx`** - Enhanced app initialization and auth handling
2. **`src/store/themeStore.ts`** - Added smart initialization and reset methods
3. **`src/utils/zustandPersistence.ts`** - Enhanced theme persistence config
4. **`app/(tabs)/profile/index.tsx`** - Added theme reset on sign out
5. **`app/(intake)/submit.tsx`** - Enhanced logging for theme updates

## 🚀 **Deployment Notes**

This fix is backward compatible and will automatically migrate existing theme preferences. Users may see their theme initialize properly on the next app launch after the update is deployed.

The solution handles all edge cases including:
- First-time users (Neutral theme)
- Existing users with saved preferences
- Network connectivity issues
- Profile loading failures
- Authentication state changes
