# Theme System Refactoring Implementation Plan

## Phase 1: Extend the Theme System

### Step 1: Add Semantic Colors to ThemeStore

```typescript
export type ThemeType = 'Woman' | 'Man' | 'Neutral';

interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  accent: string;
  // New semantic colors
  success: string;
  warning: string;
  error: string;
  info: string;
  overlayText: string;
  overlayBackground: string;
  contrastText: string; // For text on colored backgrounds
}

const themeConfigs: Record<ThemeType, ThemeColors> = {
  Neutral: {
    primary: '#508C9B',
    secondary: '#D0D0D0',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#ECECEC',
    // New semantic colors
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
  Woman: {
    primary: '#B37399',
    secondary: '#2b3568',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#F0E6F7',
    // New semantic colors
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
  Man: {
    primary: '#353E43',
    secondary: '#FFFFFF',
    background: '#FFFFFF',
    text: '#000000',
    accent: '#F5F5F5',
    // New semantic colors
    success: '#28A745',
    warning: '#FF9500',
    error: '#DC3545',
    info: '#0A84FF',
    overlayText: '#FFFFFF',
    overlayBackground: 'rgba(0, 0, 0, 0.6)',
    contrastText: '#FFFFFF',
  },
};
```

### Step 2: Create New Themed Components for Common Patterns

```typescript
// Add these new components to ThemedComponents.tsx

interface ThemedErrorTextProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export function ThemedErrorText({ children, style }: ThemedErrorTextProps) {
  const { colors } = useThemeStore();
  
  return (
    <Text style={[{ color: colors.error }, style]}>
      {children}
    </Text>
  );
}

interface ThemedStatusIndicatorProps {
  status: 'success' | 'warning' | 'error' | 'info' | 'primary';
  text: string;
  icon?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export function ThemedStatusIndicator({ 
  status, 
  text, 
  icon, 
  style, 
  textStyle 
}: ThemedStatusIndicatorProps) {
  const { colors } = useThemeStore();
  
  const getStatusColor = () => {
    switch (status) {
      case 'success': return colors.success;
      case 'warning': return colors.warning;
      case 'error': return colors.error;
      case 'info': return colors.info;
      case 'primary': return colors.primary;
      default: return colors.primary;
    }
  };
  
  return (
    <View style={[{ flexDirection: 'row', alignItems: 'center' }, style]}>
      {icon && <View style={{ marginRight: 8 }}>{icon}</View>}
      <Text style={[{ color: getStatusColor() }, textStyle]}>{text}</Text>
    </View>
  );
}

interface ThemedOverlayProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function ThemedOverlay({ children, style }: ThemedOverlayProps) {
  const { colors } = useThemeStore();
  
  return (
    <View style={[{ backgroundColor: colors.overlayBackground }, style]}>
      {children}
    </View>
  );
}

interface ThemedSelectionOptionProps {
  selected: boolean;
  onPress: () => void;
  children: React.ReactNode;
  style?: ViewStyle;
}

export function ThemedSelectionOption({ 
  selected, 
  onPress, 
  children, 
  style 
}: ThemedSelectionOptionProps) {
  const { colors } = useThemeStore();
  
  return (
    <TouchableOpacity
      style={[
        { 
          borderWidth: 1, 
          borderRadius: 8, 
          padding: 12,
          backgroundColor: colors.background, 
          borderColor: colors.accent 
        },
        selected && { 
          backgroundColor: colors.primary, 
          borderColor: colors.primary 
        },
        style
      ]}
      onPress={onPress}
    >
      {children}
    </TouchableOpacity>
  );
}
```

## Phase 2: Refactor Critical Components

### Step 1: Status Indicators in Dashboard and Workouts

**Before:**

```typescript
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ai_generated_pending_review':
      return {
        icon: <Clock size={20} color={colors.primary} />,
        text: 'Program Generated - Pending Coach Review',
        description: 'Your personalized program has been created and is being reviewed by a coach.',
        color: colors.primary,
      };
    case 'coach_approved':
      return {
        icon: <CheckCircle size={20} color="#28A745" />,
        text: 'Program Ready to Start',
        description: 'Your program has been approved by a coach and is ready to begin!',
        color: '#28A745',
      };
    // Other cases...
  }
};

// Usage
const statusInfo = getStatusInfo(programStatus);
return (
  <View style={styles.statusContainer}>
    <View style={styles.statusIconContainer}>
      {statusInfo.icon}
    </View>
    <View style={styles.statusTextContainer}>
      <Text style={[styles.statusText, { color: statusInfo.color }]}>{statusInfo.text}</Text>
      <Text style={styles.statusDescription}>{statusInfo.description}</Text>
    </View>
  </View>
);
```

**After:**

```typescript
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ai_generated_pending_review':
      return {
        icon: <Clock size={20} color={colors.info} />,
        text: 'Program Generated - Pending Coach Review',
        description: 'Your personalized program has been created and is being reviewed by a coach.',
        statusType: 'info' as const,
      };
    case 'coach_approved':
      return {
        icon: <CheckCircle size={20} color={colors.success} />,
        text: 'Program Ready to Start',
        description: 'Your program has been approved by a coach and is ready to begin!',
        statusType: 'success' as const,
      };
    // Other cases...
  }
};

// Usage
const statusInfo = getStatusInfo(programStatus);
return (
  <View style={styles.statusContainer}>
    <View style={styles.statusIconContainer}>
      {statusInfo.icon}
    </View>
    <View style={styles.statusTextContainer}>
      <ThemedStatusIndicator 
        status={statusInfo.statusType}
        text={statusInfo.text}
        textStyle={styles.statusText}
      />
      <ThemedText style={styles.statusDescription}>{statusInfo.description}</ThemedText>
    </View>
  </View>
);
```

### Step 2: Refactor Confirmation Modal

**Before:**

```typescript
export default function ConfirmationModal({
  isVisible,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  confirmButtonColor = '#DC3545',
}: ConfirmationModalProps) {
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.title}>{title}</Text>
            <Text style={styles.message}>{message}</Text>
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={onCancel}
              >
                <Text style={styles.buttonText}>{cancelText}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, { backgroundColor: confirmButtonColor }]}
                onPress={onConfirm}
              >
                <Text style={[styles.buttonText, { color: '#FFFFFF' }]}>{confirmText}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
}
```

**After:**

```typescript
import { useThemeStore } from '@/store/themeStore';
import { ThemedText, ThemedView, ThemedButton } from '@/components/ThemedComponents';

export default function ConfirmationModal({
  isVisible,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  confirmButtonVariant = 'primary',
}: ConfirmationModalProps) {
  const { colors } = useThemeStore();
  
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <ThemedOverlay style={styles.overlay}>
        <ThemedView style={styles.modalContainer} variant="card">
          <View style={styles.modalContent}>
            <ThemedText style={styles.title}>{title}</ThemedText>
            <ThemedText style={styles.message}>{message}</ThemedText>
            <View style={styles.buttonContainer}>
              <ThemedButton
                title={cancelText}
                onPress={onCancel}
                variant="outline"
                style={styles.button}
              />
              <ThemedButton
                title={confirmText}
                onPress={onConfirm}
                variant={confirmButtonVariant}
                style={styles.button}
              />
            </View>
          </View>
        </ThemedView>
      </ThemedOverlay>
    </Modal>
  );
}
```

## Phase 3: Refactor Form Elements

### Step 1: Selection Options in Intake Forms

**Before:**

```typescript
<View style={styles.weekDaysContainer}>
  {weekDays.map((day) => (
    <TouchableOpacity
      key={day}
      style={[
        styles.weekDayOption,
        { backgroundColor: colors.background, borderColor: colors.accent },
        selectedDays.includes(day) && { backgroundColor: colors.primary, borderColor: colors.primary },
      ]}
      onPress={() => toggleDay(day)}
    >
      <Text
        style={[
          styles.weekDayText,
          { color: colors.text },
          selectedDays.includes(day) && { color: '#FFFFFF' },
        ]}
      >
        {day}
      </Text>
    </TouchableOpacity>
  ))}
</View>
```

**After:**

```typescript
<View style={styles.weekDaysContainer}>
  {weekDays.map((day) => (
    <ThemedSelectionOption
      key={day}
      selected={selectedDays.includes(day)}
      onPress={() => toggleDay(day)}
      style={styles.weekDayOption}
    >
      <Text
        style={[
          styles.weekDayText,
          { color: selectedDays.includes(day) ? colors.contrastText : colors.text },
        ]}
      >
        {day}
      </Text>
    </ThemedSelectionOption>
  ))}
</View>
```

### Step 2: Error Messages in Forms

**Before:**

```typescript
{errors.training_days_per_week && (
  <Text style={[styles.errorText, { color: colors.primary }]}>
    {errors.training_days_per_week.message}
  </Text>
)}
```

**After:**

```typescript
{errors.training_days_per_week && (
  <ThemedErrorText style={styles.errorText}>
    {errors.training_days_per_week.message}
  </ThemedErrorText>
)}
```

## Phase 4: Refactor Overlay Components

### Step 1: Hero Overlays in Welcome Screen

**Before:**

```typescript
<View style={styles.heroContainer}>
  <Image
    source={{ uri: 'https://example.com/hero-image.jpg' }}
    style={styles.heroImage}
  />
  <View style={styles.heroOverlay}>
    <Text style={styles.heroTitle}>Welcome to FitApp</Text>
    <Text style={styles.heroSubtitle}>
      Let's create your personalized fitness plan
    </Text>
  </View>
</View>

const styles = StyleSheet.create({
  heroOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 24,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    lineHeight: 34,
  },
  heroSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    lineHeight: 22,
  },
});
```

**After:**

```typescript
<View style={styles.heroContainer}>
  <Image
    source={{ uri: 'https://example.com/hero-image.jpg' }}
    style={styles.heroImage}
  />
  <ThemedOverlay style={styles.heroOverlay}>
    <Text style={[styles.heroTitle, { color: colors.overlayText }]}>
      Welcome to FitApp
    </Text>
    <Text style={[styles.heroSubtitle, { color: colors.overlayText, opacity: 0.9 }]}>
      Let's create your personalized fitness plan
    </Text>
  </ThemedOverlay>
</View>

const styles = StyleSheet.create({
  heroOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 24,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 34,
  },
  heroSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
});
```

## Phase 5: Prioritized File Refactoring List

Here's a prioritized list of files to refactor, based on visibility and impact:

1. **High Priority (User-Facing Screens)**
   - `app/(tabs)/index.tsx` - Dashboard with status indicators
   - `app/(tabs)/workouts.tsx` - Workout list with status indicators
   - `app/(intake)/submit.tsx` - Submission confirmation screen
   - `src/components/ConfirmationModal.tsx` - Used across the app

2. **Medium Priority (Form Components)**
   - `app/(intake)/step1.tsx` - Gender selection (theme trigger)
   - `app/(intake)/step2.tsx` - Basic info form
   - `app/(intake)/step3.tsx` - Equipment selection
   - `app/(intake)/step4.tsx` - Schedule selection
   - `app/(tabs)/profile/edit.tsx` - Profile editing
   - `app/(tabs)/profile/intakeDetails.tsx` - Intake details editing

3. **Lower Priority (Less Visible Components)**
   - `src/components/ProgressIndicator.tsx` - Progress bars
   - `app/(auth)/login.tsx` - Login screen
   - `app/(auth)/register.tsx` - Registration screen
   - `app/(auth)/forgot-password.tsx` - Password recovery

## Phase 6: Testing and Validation

After completing the refactoring for each file:

1. **Visual Testing**:
   - Check each screen in all three themes (Woman, Man, Neutral)
   - Verify that all components use the correct theme colors
   - Ensure text remains readable in all themes

2. **Functional Testing**:
   - Verify theme switching works correctly when:
     - Completing the intake form
     - Editing profile preferences
     - Logging in/out

3. **Edge Case Testing**:
   - Test with very long text content
   - Test with different device sizes
   - Test with accessibility features enabled

## Implementation Timeline

1. **Phase 1 (Theme System Extension)**: 1 day
2. **Phase 2 (Critical Components)**: 2 days
3. **Phase 3 (Form Elements)**: 2 days
4. **Phase 4 (Overlay Components)**: 1 day
5. **Phase 5 (Remaining Files)**: 3 days
6. **Phase 6 (Testing and Validation)**: 2 days

**Total Estimated Time**: 11 days

This implementation plan provides a systematic approach to eliminating hardcoded colors throughout the codebase while maintaining visual consistency and improving the maintainability of the theme system.